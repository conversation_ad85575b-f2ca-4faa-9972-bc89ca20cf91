SET NAMES utf8mb4;

-- ----------------------------
-- Table structure for t_ind267_doc
-- ----------------------------
DROP TABLE IF EXISTS `t_ind267_doc`;
CREATE TABLE `t_ind267_doc`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `project_id` int(10) UNSIGNED NOT NULL COMMENT '项目ID',
  `trial_main_type` tinyint(3) UNSIGNED NOT NULL COMMENT '试验主类型 1单次2重复3遗传毒性',
  `trial_subtype` tinyint(3) NOT NULL COMMENT '试验子类型 1单次2重复关键3重复非关键4回复5染色体6微核',
  `trial_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '试验标题',
  `trial_institution` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '试验机构',
  `trial_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '试验编号',
  `species` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '种属',
  `solvent_and_dosage_form` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '溶媒与剂型',
  `glp_compliance` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'GLP依从性',
  `administration_method` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '给药方法',
  `administration_unit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '给药计量单位',
  `dosing_regimen` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '给药周期',
  `administration_dosage` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '给药剂量',
  `test_product` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '受试物',
  `applicant` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '申请人',
  `doc_file_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '文档地址',
  `data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT 'data数据',
  `table_of_contents` longtext COLLATE utf8mb4_unicode_ci COMMENT '目录',
  `cover_paragraphs` text COLLATE utf8mb4_unicode_ci COMMENT '封面段落数据',
  `cover_tables` text COLLATE utf8mb4_unicode_ci COMMENT '封面表格数据',
  `cover_tables_md` text COLLATE utf8mb4_unicode_ci COMMENT '封面表格MD格式数据',
  `is_active` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '文件是否启用',
  `chunk_status` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '切片状态 \r\n0：未开始\r\n1：切片中\r\n2：切片完成\r\n3：切片失败',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `project_id`(`project_id`) USING BTREE
) CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_ind267_doc_chunk
-- ----------------------------
DROP TABLE IF EXISTS `t_ind267_doc_chunk`;
CREATE TABLE `t_ind267_doc_chunk`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `parent_id` int(11) NOT NULL COMMENT '父亲ID',
  `project_id` int(11) NOT NULL COMMENT '项目ID',
  `doc_id` int(11) NOT NULL COMMENT '文档ID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模板数据',
  `numbering` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '自动编号的序号',
  `paragraph` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '段落数组JSON字符串',
  `is_delete` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `doc_id`(`doc_id`) USING BTREE,
  INDEX `project_id`(`project_id`) USING BTREE,
  INDEX `parent_id`(`parent_id`) USING BTREE
) CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_ind267_project
-- ----------------------------
DROP TABLE IF EXISTS `t_ind267_project`;
CREATE TABLE `t_ind267_project`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `project_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '项目名称',
  `template` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '模板名称',
  `version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '版本号',
  `data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT 'data数据',
  `builder_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT 'builder data数据',
  `files` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '项目存储文件集合',
  `doc_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '项目文件名称',
  `doc_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '项目文件路径',
  `status` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '【项目表】\n项目状态\r\n\n0: 上传文件触发切片，创建backlog项目【隐藏状态】\n\r\n1: 保存项目完成，项目正式创建，未生成【项目显示】\r\n\n2: 生成中，通过点击【下一步】后触发\r\n\n3: 完成生成\n\r\n4: 生成失败',
  `creator_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '项目创建者',
  `creator_id` int(11) NOT NULL COMMENT '项目创建者id',
  `is_delete` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已删除',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for t_ind267_table_chunk
-- ----------------------------
DROP TABLE IF EXISTS `t_ind267_table_chunk`;
CREATE TABLE `t_ind267_table_chunk`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT ' ',
  `project_id` int(11) NOT NULL COMMENT '项目ID',
  `doc_id` int(10) UNSIGNED NOT NULL COMMENT '文档ID',
  `doc_chunk_id` int(11) NOT NULL COMMENT '文档切片ID',
  `header` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '表格的表头段落数组JSON字符串',
  `footer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '表格的表尾段落数组JSON字符串',
  `content_md` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '表格markdown格式字符串',
  `content_json` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '表格json格式字符串',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '章节标题',
  `table_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '表格标题',
  `is_delete` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `doc_chunk_id`(`doc_chunk_id`) USING BTREE,
  INDEX `project_id`(`project_id`) USING BTREE,
  INDEX `doc_id`(`doc_id`) USING BTREE
) CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;