import glob
import os
from pathlib import Path
from shutil import rmtree

from Cython.Build import cythonize
from setuptools import setup

patterns = [
    'app/clients/*.py',
    # 'app/controllers/*.py',
    'app/extractors/*.py',
    'app/models/*.py',
    'app/schemas/*.py',
    'app/tasks/**/*.py',
    'app/utils/**/*.py',
]

# exclude = ['app/tasks/__init__.py', 'app/tasks/extractor/__init__.py', 'app/utils/azure_openai.py']
include = ['app/models/__init__.py']
exclude = ['app/utils/azure_openai.py']

module_list = []
for pattern in patterns:
    for py_file in glob.iglob(pattern, recursive=True):
        path = Path(py_file)
        posix_path = path.as_posix()
        if posix_path in exclude:
            continue
        if posix_path in include:
            module_list.append(py_file)
        elif posix_path.endswith('__init__.py'):
            continue
        else:
            module_list.append(py_file)

setup(
    ext_modules=cythonize(
        module_list,
        build_dir='/tmp/build',
        compiler_directives={
            'language_level': 3,
        },
    )
)

# 删除编译前的 py 文件
for py_file in module_list:
    try:
        os.remove(py_file)
    except Exception:
        pass

# 清除垃圾文件
for path in glob.iglob('app/**/__pycache__', recursive=True):
    try:
        rmtree(path)
    except Exception:
        pass
