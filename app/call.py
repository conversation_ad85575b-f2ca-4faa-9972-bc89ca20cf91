from asyncio import run

from sqlmodel import col

from app.clients.mysql import get_session
from app.models.doc import Doc
from app.models.generated_field import GeneratedField
from app.models.project import Project
from app.schemas.project import ProjectStatus
from app.task import broker
from app.tasks.project import get_all_fields_for_project


async def main():
    project_id = 279
    await broker.startup()
    async with get_session() as mysql:
        doc_ids = await Doc.get_by_project(mysql, project_id, columns=col(Doc.id))
        await Project.update_by_id(
            mysql, project_id, {Project.status.name: ProjectStatus.GENERATING, Project.builder_data.name: ''}
        )
        await mysql.commit()

    await GeneratedField.delete(project_id, doc_ids)
    task = await get_all_fields_for_project.kiq(project_id)
    await task.wait_result()
    await broker.shutdown()


if __name__ == '__main__':
    run(main())
