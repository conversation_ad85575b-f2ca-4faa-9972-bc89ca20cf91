from pydantic import AmqpDsn, AnyUrl, MySQLDsn, RedisDsn
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    model_config = SettingsConfigDict(env_file='.env', env_file_encoding='utf-8', extra='ignore')

    APP_PORT: int = 3000
    RELOAD: bool = False

    MYSQL_DSN: MySQLDsn = MySQLDsn('mysql+asyncmy://dev:123456@127.0.0.1:3306/kanglong?charset=utf8mb4')
    MYSQL_POOL_SIZE: int = 10
    MYSQL_MAX_OVERFLOW: int = 10
    MYSQL_POOL_TIMEOUT: int = 2
    MYSQL_POOL_RECYCLE: int = 3600

    REDIS_DSN: RedisDsn = RedisDsn('redis://127.0.0.1:6379/0?protocol=3&decode_responses=True')

    TASK_QUEUE_BROKER: AmqpDsn = AmqpDsn('amqp://127.0.0.1:5672')
    TASK_QUEUE_BACKEND: AnyUrl = RedisDsn('redis://127.0.0.1:6379/0?protocol=3')
    TASK_DEFAULT_QUEUE: str = 'ind267'
    TASK_CHUNK_QUEUE: str = 'ind267-chunk'

    AZURE_OPENAI_MODEL: str = ''
    AZURE_OPENAI_API_KEY: str = ''
    AZURE_OPENAI_API_ENDPOINT: str = ''

    VOLCENGINE_DEEPSEEK_R1_MODEL: str = ''
    VOLCENGINE_DEEPSEEK_V3_MODEL: str = ''
    VOLCENGINE_DEEPSEEK_API_KEY: str = ''

    SILICONFLOW_BCE_RERANK_API_KEY: str = ''

    BCE_RERANK_API_BASE_URL: str = 'http://bce-rerank:4000'
    BCE_RERANK_API_PATH: str = '/rerank'

    TOKEN_SERVER: str = ''

    USAGE_LOGGING_PATH: str = './logs/usage.log'

    TEST_USER_ID: int = 349


config = Settings()
