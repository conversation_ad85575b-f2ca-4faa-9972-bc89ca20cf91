from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine

from app.config import config

from .scope import scoped_session

# 创建异步数据库引擎
async_engine = create_async_engine(
    str(config.MYSQL_DSN),
    pool_size=config.MYSQL_POOL_SIZE,
    max_overflow=config.MYSQL_MAX_OVERFLOW,
    pool_timeout=config.MYSQL_POOL_TIMEOUT,
    pool_recycle=config.MYSQL_POOL_RECYCLE,
)

# 创建异步会话工厂
async_session: async_sessionmaker[AsyncSession] = async_sessionmaker(async_engine, expire_on_commit=False)

# 创建scoped session
get_session = scoped_session(async_session)
