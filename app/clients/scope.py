import typing
from contextlib import asynccontextmanager

from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker


def scoped_session(async_session: async_sessionmaker[AsyncSession]) -> typing.Callable[..., typing.AsyncContextManager]:
    @asynccontextmanager
    async def wrapper() -> typing.AsyncGenerator[AsyncSession, typing.Any]:
        session: AsyncSession = async_session()
        try:
            yield session
        finally:
            await session.close()
            await (await session.connection()).close()  # 让 session 不可再用

    return wrapper
