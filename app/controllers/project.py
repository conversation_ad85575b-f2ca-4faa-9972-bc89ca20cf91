import json
import logging
from asyncio import create_task, wait
from datetime import datetime
from io import BytesIO
from uuid import uuid4

from fastapi import Depends, Form, Path, Query, UploadFile
from fastapi.responses import JSONResponse
from sqlmodel import col

from app.clients.mysql import get_session
from app.config import config
from app.models.doc import Doc
from app.models.document import Document, ProductDoucumetStep
from app.models.document_produce import DocumentProduce
from app.models.generated_field import (
    FIELD_COUNT_OF_DOC,
    FIELD_COUNT_OF_PROJECT,
    GeneratedField,
)
from app.models.project import Project
from app.models.user import User
from app.router import router
from app.schemas.doc_base_field import (
    ADMINISTRATION_UNIT_DATA,
    TrialMainType,
    TrialSubType,
)
from app.schemas.project import (
    ProductDocumentProduceMode,
    ProductDoucumetProduceStatus,
    ProductDoucumetProduceType,
    ProductDoucumetType,
    ProjectCreateRequest,
    ProjectFileResponse,
    ProjectListRequest,
    ProjectListResponse,
    ProjectProgressResponse,
    ProjectResponse,
    ProjectSaveRequest,
    ProjectStatus,
    ProjectStopGenerateRequest,
)
from app.tasks.chunk import doc_chunk_task, doc_start_generating_task
from app.utils.api import ResponseBuilder
from app.utils.exception import bad_request_error, forbidden_error
from app.utils.project_upload_file import (
    MAX_FILE_SIZE_DESC,
    allowed_file,
    check_file_size,
    save_file,
)
from app.utils.token import get_current_user
from app.utils.uuid import generate_uuid

SIMPLIFIED_TYPE_MAPPING = {
    TrialSubType.SINGLE_DOSE.value: TrialMainType.SINGLE_DOSE.value,
    TrialSubType.REPEATED_DOSE_SPECIFICITY.value: TrialMainType.REPEATED_DOSE.value,
    TrialSubType.REPEATED_DOSE.value: TrialMainType.REPEATED_DOSE.value,
    TrialSubType.AMES_TEST.value: TrialMainType.GENOTOXICITY.value,
    TrialSubType.CHROMOSOME_ABERRATION_TEST.value: TrialMainType.GENOTOXICITY.value,
    TrialSubType.MICRONUCLEUS_TEST.value: TrialMainType.GENOTOXICITY.value,
}


@router.get('/projects')
async def get_project_list_api(request: ProjectListRequest = Depends(), user=Depends(get_current_user)) -> JSONResponse:
    """获取项目列表接口"""

    async with get_session() as session:
        (total, projects) = await Project.get_list_by_condition(
            session, user.id, request.page, request.page_size, request.keyword
        )

    project_list = ProjectListResponse(total=total, projects=projects)

    return ResponseBuilder.success(data=project_list)


@router.get('/project')
async def get_project_detail_api(project_id: int = Query(ge=1), user=Depends(get_current_user)) -> JSONResponse:
    """获取项目详情接口"""
    async with get_session() as session:
        project: Project = await Project.get_by_id(session, project_id)

    if not project or project.is_delete:
        logging.error('项目不存在')
        raise bad_request_error('项目不存在')

    # 过滤测试用户
    if project.creator_id != user.id and user.id != config.TEST_USER_ID:
        raise forbidden_error

    if project.files:
        project.files = json.loads(project.files)
    else:
        project.files = None

    return ResponseBuilder.success(data=project)


@router.post('/project/upload')
async def upload_project_file_api(
    file: UploadFile,
    trial_type: int = Form(),  # 1: 单次给药； 2: 重复给药毒性试验关键试验； 3: 重复给药毒性试验非关键试验； 4: 回复突变试验； 5: 染色体试验； 6: 微核试验；
    project_id: int = Form(ge=1),
    user=Depends(get_current_user),
) -> JSONResponse:
    """文件上传接口"""
    if not file.filename:
        logging.error('文件名不能为空')
        raise bad_request_error('文件名不能为空')

    if not file.size:
        logging.error('文件大小不能为空')
        raise bad_request_error('文件大小不能为空')

    # 校验文件类型
    if not allowed_file(file.filename):
        logging.error('文件类型不支持 .docx 以外文件')
        raise bad_request_error('文件类型不支持 .docx 以外文件')

    # 校验文件大小
    if not check_file_size(file.size):
        logging.error('文件过大')
        raise bad_request_error(f'文件不能大于{MAX_FILE_SIZE_DESC}MB')

    # 重置文件指针
    await file.seek(0)

    # 获取简化的试验类型映射
    input_type = SIMPLIFIED_TYPE_MAPPING.get(trial_type)

    # 校验试验类型
    if input_type is None:
        logging.error('非法试试验类型')
        raise bad_request_error('非法试试验类型')

    async with get_session() as session:
        project: ProjectResponse = await Project.get_by_id(
            session,
            project_id,
            columns=(Project.status, Project.is_delete, Project.creator_id),
        )

    if not project or project.is_delete:
        logging.error('项目不存在')
        raise bad_request_error('项目不存在')

    # 校验项目状态
    if project.status > ProjectStatus.READY.value:
        logging.error('项目已生成，无法再次上传文件')
        raise bad_request_error('项目已生成，无法再次上传文件')

    # 判断用户是否为项目创建者
    # 过滤测试用户
    if project.creator_id != user.id and user.id != config.TEST_USER_ID:
        raise forbidden_error

    # 保存文件
    file_content_as_bytes = await file.read()
    file_io = BytesIO(file_content_as_bytes)
    file_path = await save_file(file_io, filename=f'{generate_uuid()}_{file.filename}', sub_dir='temp')

    async with get_session() as session:
        # doc 表插入数据
        new_doc = Doc(
            trial_main_type=input_type,
            trial_subtype=trial_type,
            glp_compliance=trial_type == TrialSubType.REPEATED_DOSE_SPECIFICITY.value,
            administration_unit=ADMINISTRATION_UNIT_DATA[TrialSubType(trial_type)],
            project_id=project_id,
            doc_file_path=file_path,
        )
        session.add(new_doc)
        await session.flush()
        doc_id = new_doc.id

        await session.commit()

    # 文档切片任务异步处理
    await doc_chunk_task.kicker().with_task_id(f'chunk_doc:doc_{doc_id}_{generate_uuid()}').kiq(doc_id)
    return ResponseBuilder.success(data=ProjectFileResponse(doc_id=doc_id))


@router.post('/project/save')
async def save_project_api(request: ProjectSaveRequest, user: User = Depends(get_current_user)) -> JSONResponse:
    """保存项目"""
    project_id = request.project_id
    project_name = request.project_name or ''
    template = request.template
    version = request.version
    files = request.files
    is_temp = request.is_temp

    async with get_session() as session:

        # 根据是否有 project_id 判断是新增还是更新
        if project_id:
            # 更新项目信息
            project: ProjectResponse = await Project.get_by_id(session, project_id, for_update=True)
            if not project or project.is_delete:
                logging.error('项目不存在')
                raise bad_request_error('项目不存在')

            # 项目状态校验
            if project.status > ProjectStatus.READY.value:
                logging.error('项目已生成，无法再次保存')
                raise bad_request_error('项目已生成，无法再次保存')

            # 用户权限校验
            # 过滤测试用户
            if project.creator_id != user.id and user.id != config.TEST_USER_ID:
                raise forbidden_error

            project.project_name = project_name
            project.template = template
            project.version = version

            # 项目信息
            document_result = await Document.get_by_ref_id(session, project_id)
            if not document_result:
                # 新建文档信息
                new_document = Document(
                    type=ProductDoucumetType.IND267.value,
                    ref_id=project_id,
                    user_id=user.id,
                    create_time=datetime.now(),
                    update_time=datetime.now(),
                )
                session.add(new_document)
                await session.flush()

            # 更新项目文档信息，由于是 JSON 类型，将 files 直接推入数据库
            files_dict = files.model_dump()
            project.files = json.dumps(files_dict, ensure_ascii=False)

            # 如果项目状态为 0，更新为 1，表示为正式创建项目
            if project.status == ProjectStatus.NOT_READY.value:
                project.status = ProjectStatus.READY.value

            await session.flush()

            # 标记所有存储的 doc 为 启用状态, 不在 files 中的 doc标记为 false
            # 1. 获取所有 doc
            # 2. 标记所有 doc 为 false
            # 3. 标记 files 中的 doc 为 true
            # 获取所有 files里的 doc_id
            doc_ids = []

            for doc in (
                files.singleDose
                + files.repeatedDose
                + files.repeatedDoseNonKey
                + files.mutagenicity
                + files.chromosome
                + files.micronucleus
            ):
                doc_ids.append(doc.doc_id)

            await Doc.set_active(session, project_id, doc_ids)

        else:
            # 新建项目，区分是否backlog项目
            new_project = Project(
                project_name=project_name,
                template=template,
                version=version,
                files=json.dumps(files.model_dump(), ensure_ascii=False),
                status=(
                    ProjectStatus.READY.value if not is_temp else ProjectStatus.NOT_READY.value
                ),  # 如果是backlog项目，状态为 0
                creator_name=user.name,
                creator_id=user.id,
            )
            session.add(new_project)
            await session.flush()
            project_id = new_project.id

            if not is_temp:
                # 新建文档信息
                new_document = Document(
                    type=ProductDoucumetType.IND267.value,
                    ref_id=project_id,
                    user_id=user.id,
                    create_time=datetime.now(),
                    update_time=datetime.now(),
                )
                session.add(new_document)
                await session.flush()

            # 当不是backlog时，标记所有存储的 doc 为 启用状态, 不在 files 中的 doc标记为 0
            if not is_temp:
                # 1. 获取所有 doc
                # 2. 标记所有 doc 为 False
                # 3. 标记 files 中的 doc 为 True
                doc_ids = []
                for doc in (
                    files.singleDose
                    + files.repeatedDose
                    + files.repeatedDoseNonKey
                    + files.mutagenicity
                    + files.chromosome
                    + files.micronucleus
                ):
                    doc_ids.append(doc.doc_id)

                await Doc.set_active(session, project_id, doc_ids)

        await session.commit()

    return ResponseBuilder.success(data={'id': project_id})


@router.post('/project/generate')
async def generate_project_api(request: ProjectCreateRequest, user: User = Depends(get_current_user)) -> JSONResponse:
    """生成接口"""

    project_id = request.project_id
    project_name = request.project_name
    template = request.template
    version = request.version
    files = request.files

    async with get_session() as session:
        # 判断是否有 project_id，如果有，则为更新项目，否则为创建项目

        if project_id:
            # 存在 project_id，更新项目
            project = await Project.get_by_id(session, project_id)
            # 判断项目是否存在
            if not project:
                logging.error('项目不存在')
                raise bad_request_error('项目不存在')

            # 判断项目状态是否 > 1 (生成中/已生成/失败)
            if project.status > ProjectStatus.READY.value:
                logging.error('项目已生成，无法再次生成')
                raise bad_request_error('项目已生成，无法再次生成')

            # 判断用户是否为项目创建者
            # 过滤测试用户
            if project.creator_id != user.id and user.id != config.TEST_USER_ID:
                raise forbidden_error

            # 更新项目信息
            project.project_name = project_name
            project.template = template
            project.version = version

            # 更新项目文档信息，由于是 JSON 类型，将 files 直接推入数据库
            project.files = json.dumps(files.model_dump(), ensure_ascii=False)

            # 如果项目状态小与1，更新为2，表示为生成中
            project.status = max(project.status, ProjectStatus.GENERATING.value)

            # 更新文档步骤状态
            await Document.update_by_ref_id(session, project_id, ProductDoucumetStep.FIRST_STEP)

            await session.flush()
        else:
            # 不存在 project_id, 新建项目
            new_project = Project(
                project_name=project_name,
                template=template,
                version=version,
                files=json.dumps(files.model_dump(), ensure_ascii=False),
                status=ProjectStatus.GENERATING.value,  # 生成中
                creator_name=user.name,
                creator_id=user.id,
            )
            session.add(new_project)
            await session.flush()
            project_id = new_project.id

            # 新建文档信息
            new_document = Document(
                type=ProductDoucumetType.IND267.value,
                ref_id=project_id,
                user_id=user.id,
                create_time=datetime.now(),
                update_time=datetime.now(),
            )
            session.add(new_document)
            await session.flush()

        # 校验 docs
        # 判断 docs 至少有一份文件
        if not files or (
            not files.singleDose
            and not files.repeatedDose
            and not files.repeatedDoseNonKey
            and not files.mutagenicity
            and not files.chromosome
            and not files.micronucleus
        ):
            logging.error('docs不能为空')
            raise bad_request_error('docs不能为空')

        # 将 docs 中关联 doc 表的数据 is_active 设置为 true, 其他设置为 false
        # 1. 获取所有 doc
        # 2. 标记所有 doc 为 false
        # 3. 标记 files 中的 doc 为 true
        doc_ids = []

        for doc in (
            files.singleDose
            + files.repeatedDose
            + files.repeatedDoseNonKey
            + files.mutagenicity
            + files.chromosome
            + files.micronucleus
        ):
            doc_ids.append(doc.doc_id)

        await Doc.set_active(session, project_id, doc_ids)

        # 更新项目文档信息
        new_document_produce = DocumentProduce(
            id=uuid4().hex,
            document_produce_status=ProductDoucumetProduceStatus.GENERATING.value,  # 生成中
            document_produce_type=ProductDoucumetProduceType.IND267.value,
            document_produce_mode=ProductDocumentProduceMode.MULTI.value,
            document_type=ProductDoucumetType.IND267.value,
            target_id=project_id,
            user_id=user.id,
            create_time=datetime.now(),
            update_time=datetime.now(),
        )
        session.add(new_document_produce)
        await session.flush()

        await session.commit()

    # 清理缓存
    await GeneratedField.delete(project_id, doc_ids)

    # 启动生成任务
    await doc_start_generating_task.kicker().with_task_id(f'chunk_doc:project_{project_id}_{generate_uuid()}').kiq(
        project_id
    )

    return ResponseBuilder.success(data={'id': project_id})


@router.delete('/project/{project_id}')
async def delete_project_api(project_id: int = Path(ge=1), user=Depends(get_current_user)) -> JSONResponse:
    """删除项目接口"""

    async with get_session() as session:
        # 获取项目信息
        creator_id = await Project.get_by_id(session, project_id, columns=col(Project.creator_id))

        if not creator_id:
            logging.error('项目不存在')
            raise bad_request_error('项目不存在')

        # 过滤测试用户
        if creator_id != user.id and user.id != config.TEST_USER_ID:
            raise forbidden_error

        # 删除项目（软删除）
        project_deleted = await Project.soft_delete_by_id(session, project_id)

        if project_deleted:
            # 删除项目文档信息（软删除）
            await Doc.soft_delete_by_project_id(session, project_id)

        await session.commit()

    return ResponseBuilder.success()


@router.post('/project/generate/stop')
async def stop_generate_project_api(
    request: ProjectStopGenerateRequest,
    user=Depends(get_current_user),
) -> JSONResponse:
    """停止生成项目接口"""
    async with get_session() as session:
        # 获取项目信息
        project = await Project.get_by_id(session, request.project_id, columns=(Project.creator_id, Project.status))

        if not project:
            logging.error('项目不存在')
            raise bad_request_error('项目不存在')

        # 过滤测试用户
        if project.creator_id != user.id and user.id != config.TEST_USER_ID:
            raise forbidden_error

        if project.status != ProjectStatus.GENERATING.value:
            logging.error('项目不在生成中状态')
            raise bad_request_error('项目不在生成中状态')

        if await Project.stop_generate(session, request.project_id):
            # 更新文档信息
            await DocumentProduce.update_by_target_id(session, request.project_id, ProductDoucumetProduceStatus.CANCEL)
            # 更新文档步骤状态
            await Document.update_by_ref_id(session, request.project_id, ProductDoucumetStep.FIRST_STEP)

            await session.commit()

    return ResponseBuilder.success()


@router.get('/project/generate/status')
async def get_project_gen_status_api(project_id: int, user=Depends(get_current_user)) -> JSONResponse:
    """获取项目生成状态接口"""
    async with get_session() as session:
        project = await Project.get_by_id(session, project_id, columns=(Project.creator_id, Project.status))
        if not project:
            logging.error('项目不存在')
            raise bad_request_error('项目不存在')

        # 过滤测试用户
        if project.creator_id != user.id and user.id != config.TEST_USER_ID:
            raise forbidden_error

        if project.status > ProjectStatus.GENERATING.value:
            # 更新文档信息
            await DocumentProduce.update_by_target_id(session, project_id, ProductDoucumetProduceStatus.GENERATED)
            # 更新文档步骤状态
            await Document.update_by_ref_id(session, project_id, ProductDoucumetStep.SECOND_STEP)

            await session.commit()

            final_result = ProjectProgressResponse(progress=100, status=project.status)
            return ResponseBuilder.success(data=final_result)

        docs = await Doc.get_by_project(session, project_id, columns=(Doc.id, Doc.trial_subtype))

    if not docs:
        logging.error('项目没有文档切片')
        raise bad_request_error('项目没有文档切片')

    count = 0
    total = FIELD_COUNT_OF_PROJECT
    doc_ids: list[int] = []
    for doc in docs:
        doc_ids.append(doc.id)
        if doc.trial_subtype:
            total += int(FIELD_COUNT_OF_DOC.get(TrialSubType(doc.trial_subtype), 0))

    tasks = [create_task(GeneratedField.count_by_project_id(project_id))]
    for doc_id in doc_ids:
        tasks.append(create_task(GeneratedField.count_by_doc_id(doc_id)))
    await wait(tasks)

    for task in tasks:
        count += task.result()

    progress = count * 100 // total
    logging.info('Task project: %d, count: %d, total: %d, progress: %d', project_id, count, total, progress)
    if progress >= 100:
        progress = 99

    return ResponseBuilder.success(data=ProjectProgressResponse(progress=progress, status=project.status))
