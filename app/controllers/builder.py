import io
import json
import urllib.parse

from fastapi import Depends, Query
from fastapi.responses import JSONResponse, StreamingResponse
from sqlalchemy.engine.row import Row
from sqlmodel import col

from app.clients.mysql import get_session
from app.config import config
from app.models.project import Project
from app.models.user import User
from app.router import router
from app.schemas.builder import (
    BuilderChapter,
    BuilderDetail,
    BuilderExportDetail,
    BuilderSaveDetail,
    BuilderTable,
    TableKey,
)
from app.schemas.doc_generate import GenerateBaseData
from app.schemas.project import ProjectStatus
from app.utils.api import ResponseBuilder
from app.utils.doc.doc_generate import DocGenerate
from app.utils.exception import bad_request_error, forbidden_error
from app.utils.token import get_current_user


def format_save_detail_to_dict(save_detail: BuilderSaveDetail) -> dict:
    """
    将 BuilderSaveDetail 对象转换为字典，并将其中的枚举从对象形式转换为字符串形式。
    参数:
    :param save_detail (BuilderSaveDetail): 包含构建详情的 BuilderSaveDetail 对象。
    返回:
    :return dict: 转换后的字典，其中某些属性已从对象形式转换为字符串形式。
    """

    # 获取 save_detail 转换后的字典数据
    data_dict = save_detail.model_dump()

    # 确保 'table_list' 存在，避免 KeyError
    table_list = data_dict.get('table_list', [])

    for table in table_list:
        if 'table_key' in table and hasattr(table['table_key'], 'value'):
            table['table_key'] = table['table_key'].value

        for table_row in table.get('rows', []):
            for table_col in table_row.get('cols', []):
                if 'align_vertical' in table_col and hasattr(table_col['align_vertical'], 'value'):
                    table_col['align_vertical'] = table_col['align_vertical'].value
                if 'align_horizontally' in table_col and hasattr(table_col['align_horizontally'], 'value'):
                    table_col['align_horizontally'] = table_col['align_horizontally'].value

    return data_dict


@router.get('/builder')
async def get_project_api(project_id: int = Query(ge=1), user: User = Depends(get_current_user)) -> JSONResponse:
    """获取Builder详情接口"""
    chapter_list: list[BuilderChapter] = [
        BuilderChapter(table_key=TableKey.TABLE_2671, name='2.6.7.1 毒理学概述'),
        BuilderChapter(table_key=TableKey.TABLE_2672, name='2.6.7.2 毒代动力学: 毒代动力学试验概述'),
        BuilderChapter(table_key=TableKey.TABLE_2673, name='2.6.7.3 毒代动力学: 毒代动力学数据概述'),
        BuilderChapter(table_key=TableKey.TABLE_2675, name='2.6.7.5 单次给药毒性'),
        BuilderChapter(table_key=TableKey.TABLE_2676, name='2.6.7.6 重复给药毒性: 非关键试验'),
        BuilderChapter(table_key=TableKey.TABLE_2677, name='2.6.7.7 重复给药毒性: 关键试验'),
        BuilderChapter(table_key=TableKey.TABLE_2678, name='2.6.7.8 遗传毒性: 体外'),
        BuilderChapter(table_key=TableKey.TABLE_2679, name='2.6.7.9 遗传毒性: 体内'),
    ]

    # 查询数据
    async with get_session() as session:
        # 项目信息
        project: Row = await Project.get_by_id(
            session,
            project_id,
            columns=[
                Project.project_name,
                Project.builder_data,
                Project.creator_id,
                Project.status,
                Project.is_delete,
            ],
        )

    # 项目不存在
    if not project:
        raise bad_request_error('项目不存在')

    # 不是创建者
    # 过滤测试用户
    if user.id != project.creator_id and user.id != config.TEST_USER_ID:
        raise forbidden_error

    # 项目状态不正常
    if project.status != ProjectStatus.GENERATED.value:
        raise bad_request_error('项目未生成完成')

    # 项目已删除
    if project.is_delete:
        raise bad_request_error('项目已删除')

    # builder数据为空
    if project.builder_data:
        json_data: dict = json.loads(project.builder_data)
        json_data['project_name'] = project.project_name
        json_data['chapter_list'] = chapter_list
        return ResponseBuilder.success(data=json_data)

    # 基础信息
    generate_data: GenerateBaseData = await DocGenerate.base_field_data_format(project_id)

    applicant_list: list[str] = []
    test_product_list: list[str] = []

    for data in (
        generate_data.single_data,
        generate_data.repetition_data,
    ):
        for item in data:
            if item:
                if item.applicant:
                    applicant_list.append(item.applicant)
                if item.test_product:
                    test_product_list.append(item.test_product)
    for item in (
        generate_data.back_mutation_data,
        generate_data.chromosome_data,
        generate_data.micronucleus_data,
    ):
        if item:
            if item.applicant:
                applicant_list.append(item.applicant)
            if item.test_product:
                test_product_list.append(item.test_product)

    # 申请人 去重
    applicant_list = list(set(applicant_list))
    # 受试物/供试品 去重
    test_product_list = list(set(test_product_list))

    # 表格数据
    table_list: list[BuilderTable] = await DocGenerate.generate_to_builder(project_id, generate_data)

    # 保存第一版(V1版本)的数据
    async with get_session() as session:
        await Project.update_by_id(
            session,
            project_id,
            {
                Project.builder_data.name: json.dumps(
                    format_save_detail_to_dict(
                        BuilderSaveDetail(
                            id=project_id,
                            test_product_list=test_product_list,
                            applicant_list=applicant_list,
                            table_list=table_list,
                        )
                    ),
                    ensure_ascii=False,
                )
            },
        )
        await session.commit()

    return ResponseBuilder.success(
        data=BuilderDetail(
            id=project_id,
            project_name=project.project_name,
            test_product_list=test_product_list,
            applicant_list=applicant_list,
            table_list=table_list,
            chapter_list=chapter_list,
        )
    )


@router.post('/builder/save')
async def save_builder_api(save_data: BuilderSaveDetail, user: User = Depends(get_current_user)) -> JSONResponse:
    """保存Builder数据接口"""

    # 转字符串
    data_dict: dict = format_save_detail_to_dict(save_data)

    async with get_session() as session:
        creator_id = await Project.get_by_id(session, save_data.id, columns=col(Project.creator_id))

        # 过滤测试用户
        if user.id != creator_id and user.id != config.TEST_USER_ID:
            raise forbidden_error

        await Project.update_by_id(
            session, save_data.id, {Project.builder_data.name: json.dumps(data_dict, ensure_ascii=False)}
        )
        await session.commit()
    return ResponseBuilder.success(msg=f'项目保存成功, 项目ID为 {save_data.id}')


@router.post('/builder/export')
async def output_api(export_data: BuilderExportDetail, user: User = Depends(get_current_user)) -> StreamingResponse:
    """导出文档接口"""
    async with get_session() as session:
        # 项目信息
        project = await Project.get_by_id(
            session,
            export_data.id,
            columns=[
                Project.builder_data,
                Project.creator_id,
                Project.status,
                Project.is_delete,
            ],
        )

    # 过滤测试用户
    if user.id != project.creator_id and user.id != config.TEST_USER_ID:
        raise forbidden_error

    if not project or project.status == ProjectStatus.NOT_READY:
        raise bad_request_error('项目不存在')

    if project.is_delete:
        raise bad_request_error('项目已删除')

    # 使用传入数据进行导出
    file_io: io.BytesIO
    if export_data.table_list:
        file_io = await DocGenerate.builder_export_to_word(export_data.id, export_data)
    elif project.builder_data:
        builder_data_dict: dict = json.loads(project.builder_data)
        builder_data: BuilderSaveDetail = BuilderSaveDetail(**builder_data_dict)
        file_io = await DocGenerate.builder_export_to_word(export_data.id, builder_data)
    else:
        raise bad_request_error('请先保存文档')

    filename = f'{export_data.id}-267-毒理学列表总结.docx'
    encoded_filename = urllib.parse.quote(filename)

    return StreamingResponse(
        file_io,
        headers={
            'Content-Disposition': f'attachment; filename={encoded_filename}',
            'Content-Type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        },
    )
