import logging

from taskiq import (
    AsyncBroker,
    InMemoryBroker,
    SimpleRetryMiddleware,
    TaskiqEvents,
    TaskiqState,
)
from taskiq_aio_pika import AioPikaBroker
from taskiq_redis import RedisAsyncResultBackend

from app.config import config
from app.utils.json_logger import setup_logger, teardown_logger
from app.utils.taskiq_serializer import JSONSerializer
from app.utils.test import IS_TESTING

logging.basicConfig(
    level=logging.INFO,
    format='[%(levelname)1.1s %(asctime)s %(pathname)s:%(lineno)d] %(message)s',
    datefmt='%y%m%d %H:%M:%S',
)

broker: AsyncBroker
broker_chunk: AsyncBroker
if IS_TESTING:
    broker = InMemoryBroker()
    broker_chunk = InMemoryBroker()
else:
    broker = (
        AioPikaBroker(
            str(config.TASK_QUEUE_BROKER),
            queue_name=config.TASK_DEFAULT_QUEUE,
            exchange_name=config.TASK_DEFAULT_QUEUE,
        )
        .with_result_backend(RedisAsyncResultBackend(str(config.TASK_QUEUE_BACKEND), result_ex_time=3600))
        .with_middlewares(SimpleRetryMiddleware(default_retry_count=3))
    )
    broker_chunk = (
        AioPikaBroker(
            str(config.TASK_QUEUE_BROKER),
            queue_name=config.TASK_CHUNK_QUEUE,
            exchange_name=config.TASK_CHUNK_QUEUE,
        )
        .with_result_backend(
            RedisAsyncResultBackend(str(config.TASK_QUEUE_BACKEND), result_ex_time=3600, serializer=JSONSerializer())
        )
        .with_middlewares(SimpleRetryMiddleware(default_retry_count=3))
    )


@broker.on_event(TaskiqEvents.WORKER_STARTUP)
async def startup_broker(state: TaskiqState) -> None:
    await setup_logger()


@broker.on_event(TaskiqEvents.WORKER_SHUTDOWN)
async def shutdown_broker(state: TaskiqState) -> None:
    await teardown_logger()


@broker_chunk.on_event(TaskiqEvents.WORKER_STARTUP)
async def startup_broker_chunk(state: TaskiqState) -> None:
    await broker.startup()


@broker_chunk.on_event(TaskiqEvents.WORKER_SHUTDOWN)
async def shutdown_broker_chunk(state: TaskiqState) -> None:
    await broker.shutdown()
