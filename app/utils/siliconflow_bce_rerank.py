import logging

from aiohttp import ClientSession

from app.config import config
from app.utils.exception import ModelError

from .singleton import Singleton


class SiliconflowBCERerank(metaclass=Singleton):
    API_PATH = '/v1/rerank'

    def __init__(self) -> None:
        self.client = ClientSession(
            base_url='https://api.siliconflow.cn',
            headers={'Authorization': f'Bearer {config.SILICONFLOW_BCE_RERANK_API_KEY}'},
        )

    async def shutdown(self) -> None:
        await self.client.close()

    async def rerank(
        self,
        query: str,
        documents: list[str],
        top_n: int = 3,
    ) -> list[tuple[str, float]]:  # [(文档, 分数)]
        async with self.client.post(
            url=self.API_PATH,
            json={
                'model': 'netease-youdao/bce-reranker-base_v1',
                'query': query,
                'documents': documents,
                'top_n': top_n,
            },
        ) as response:
            if not response.ok:
                raise ModelError(f'status: {response.status}, text: {response.text}')

            try:
                result = await response.json()
            except Exception as e:
                logging.exception('Failed to parse BCE Rerank response: %s', response.text)
                raise ModelError(f'invalid response: {response.text}') from e

        error = result.get('error')
        if error:
            logging.error('BCE Rerank error: %s', error)
            raise ModelError(error)

        output = []
        for result in result['results']:
            output.append((documents[result['index']], result['relevance_score']))

        output.sort(key=lambda x: x[1], reverse=True)
        return output
