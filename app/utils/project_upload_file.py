import os
from io import BytesIO
from tempfile import SpooledTemporaryFile

from app.schemas.doc_base_field import TrialSubType

# 配置文件
UPLOAD_DIR = 'uploads'  # 文件保存的目录
ALLOWED_EXTENSIONS = {'docx'}  # 允许的文件扩展名
MAX_FILE_SIZE = 50 * 1024 * 1024  # 最大文件大小，50MB
MAX_FILE_SIZE_DESC = 50


def allowed_file(filename: str) -> bool:
    """检查文件扩展名是否允许上传"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS


def check_file_size(file_size: int) -> bool:
    """检查文件大小是否超过限制"""
    return file_size <= MAX_FILE_SIZE


# 2 5 6
Dosing_Regimen_Valid = [
    TrialSubType.REPEATED_DOSE_SPECIFICITY.value,
    TrialSubType.CHROMOSOME_ABERRATION_TEST.value,
    TrialSubType.MICRONUCLEUS_TEST.value,
]


async def save_file(file: BytesIO | SpooledTemporaryFile, filename: str, sub_dir: str | None = None) -> str:
    """保存文件到本地并返回文件路径"""

    # 确定目标目录
    target_dir = os.path.join(UPLOAD_DIR, sub_dir) if sub_dir else UPLOAD_DIR

    # 如果目录不存在，创建目录
    os.makedirs(target_dir, exist_ok=True)

    # 构建文件路径
    file_path = os.path.join(target_dir, filename)  # 可添加动态文件名

    # 将内容写入文件
    with open(file_path, 'wb') as f:
        content = file.read()  # 同步读取
        f.write(content)
        f.flush()
        os.fsync(f.fileno())

    return file_path
