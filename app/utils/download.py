import io
from urllib.parse import quote

import aiohttp
import logging

class Download:
    # 定义一个类变量，用于存储用户代理字符串，模拟浏览器行为
    UA: str = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3"

    @classmethod
    def needs_encoding(cls, url: str) -> bool:
        """
        判断URL是否需要编码
        参数:
        url: str - 待检查的URL字符串
        返回值:
        bool - 如果URL包含非ASCII字符则返回True，否则返回False
        """
        # 检查URL中是否包含非ASCII字符，以决定是否需要进行URL编码
        return not all(ord(char) < 128 for char in url)

    @classmethod
    async def download_file_bytes(cls, url: str) -> io.BytesIO | None:
        """
        异步下载文件
        参数:
        url: str - 文件的URL地址
        返回值:
        Optional[io.BytesIO] - 下载成功返回BytesIO对象，否则返回None
        """
        # 检查URL是否为空，如果为空则直接返回None
        if not url:
            logging.error("下载URL为空。")
            return None

        # 根据URL是否需要编码，来决定使用原始URL还是编码后的URL
        if cls.needs_encoding(url):
            encoded_url = quote(url, safe=':/')
        else:
            encoded_url = url

        # 设置请求头，包含用户代理信息
        headers = { "User-Agent": cls.UA }

        # 创建一个aiohttp的ClientSession对象，用于发起HTTP请求
        async with aiohttp.ClientSession(headers=headers) as session:
            try:
                # 发起GET请求，尝试下载文件
                async with session.get(encoded_url, ssl=False) as response:
                    # 检查HTTP响应状态码，如果为200则表示下载成功
                    if response.status == 200:
                        # 读取内容，这是一个异步操作
                        content = await response.read()
                        # 将读取的内容转换为BytesIO对象
                        return io.BytesIO(content)
                    else:
                        # 如果下载失败，记录错误日志，并返回None
                        logging.error(f"无法下载文件，HTTP状态码：{response.status}")
                        return None
            except aiohttp.ClientError as e:
                # 如果在下载过程中发生异常，记录错误日志，并返回None
                logging.error(f"下载文件时出错：{str(e)}")
                return None