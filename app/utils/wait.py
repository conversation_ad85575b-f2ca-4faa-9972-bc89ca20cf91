import asyncio
from typing import Any, Coroutine

async def wait(*coros: Coroutine) -> set[asyncio.Task]:
    """
    启动所有提供的协程并等待它们完成，直到其中一个抛出异常。

    参数:
    *coros (Coroutine): 一个或多个协程对象。

    返回:
    set[asyncio.Task]: 一个包含已完成任务的集合。

    当某个任务完成时，其他任务将被取消。
    """
    # 创建每个协程对应的任务
    tasks = (asyncio.create_task(coro) for coro in coros)
    # 等待任务完成，直到其中一个任务抛出异常
    done, _ = await asyncio.wait(
        tasks,
        return_when=asyncio.FIRST_EXCEPTION,
    )
    # 遍历已完成的任务，获取结果（如果有异常，则直接抛出）
    for task in done:
        task.result()
    # 返回已完成的任务集合
    return done


async def wait_with_names(*coros_with_names: tuple[Coroutine, str]) -> dict[str, Any]:
    """
    类似于wait函数，但额外接受任务名称，并返回一个映射，将任务名称映射到其结果。

    参数:
    *coros_with_names (tuple[Coroutine, str]): 一个或多个包含协程和名称的元组。

    返回:
    dict[str, Any]: 一个字典，将任务名称映射到其结果。

    当某个任务完成时，其他任务将被取消。
    """
    # 创建每个协程对应的任务，并为其指定名称
    tasks = (asyncio.create_task(coro, name=name) for coro, name in coros_with_names)
    # 等待任务完成，直到其中一个任务抛出异常
    done, _ = await asyncio.wait(
        tasks,
        return_when=asyncio.FIRST_EXCEPTION,
    )
    # 返回一个字典，将每个完成任务的名称映射到其结果（如果有异常，则直接抛出）
    return {task.get_name(): task.result() for task in done}
