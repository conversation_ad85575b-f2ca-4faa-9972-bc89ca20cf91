
from datetime import datetime
from typing import Any, Sequence

from sqlalchemy import Row
from sqlmodel import SQLModel
from sqlmodel.main import IncEx

# 用于处理和转换SQLAlchemy和SQLModel中的数据

def row_dump(row: Row, convert_datetime=True) -> dict[str, Any]:
    """
    将SQLAlchemy的Row对象转换为字典。

    参数:
    - row: Row对象，数据库查询结果的一行。
    - convert_datetime: 是否将datetime对象转换为Unix时间戳，默认为True。

    返回:
    - 包含Row数据的字典，如果convert_datetime为True，则datetime字段被转换为Unix时间戳。
    """
    if convert_datetime:
        return {k: int(v.timestamp()) if isinstance(v, datetime) else v for k, v in row._asdict().items()}
    return row._asdict()


def rows_dump(rows: Sequence[Row], convert_datetime=True) -> list[dict[str, Any]]:
    """
    将多个SQLAlchemy的Row对象转换为字典列表。

    参数:
    - rows: Row对象序列，数据库查询结果的多行。
    - convert_datetime: 是否将datetime对象转换为Unix时间戳，默认为True。

    返回:
    - 包含多个字典的列表，每个字典代表一行数据。如果convert_datetime为True，则datetime字段被转换为Unix时间戳。
    """
    if convert_datetime:
        return [
            {k: int(v.timestamp()) if isinstance(v, datetime) else v for k, v in row._asdict().items()} for row in rows
        ]
    return [row._asdict() for row in rows]


def model_dump(
    model: SQLModel,
    include: IncEx = None,
    exclude: IncEx = None,
    exclude_unset: bool = False,
    exclude_defaults: bool = False,
    exclude_none: bool = False,
    convert_datetime=True,
) -> dict[str, Any]:
    """
    将SQLModel模型实例转换为字典。

    参数:
    - model: SQLModel实例。
    - include: 包含的字段，None表示全部字段。
    - exclude: 排除的字段，None表示不排除任何字段。
    - exclude_unset: 是否排除未设置值的字段，默认为False。
    - exclude_defaults: 是否排除等于默认值的字段，默认为False。
    - exclude_none: 是否排除值为None的字段，默认为False。
    - convert_datetime: 是否将datetime对象转换为Unix时间戳，默认为True。

    返回:
    - 包含模型数据的字典。如果convert_datetime为True，则datetime字段被转换为Unix时间戳。
    """
    dumped = model.model_dump(
        include=include,
        exclude=exclude,
        exclude_unset=exclude_unset,
        exclude_defaults=exclude_defaults,
        exclude_none=exclude_none,
    )
    if convert_datetime:
        return {k: int(v.timestamp()) if isinstance(v, datetime) else v for k, v in dumped.items()}
    return dumped


def models_dump(
    models: Sequence[SQLModel],
    include: IncEx = None,
    exclude: IncEx = None,
    exclude_unset: bool = False,
    exclude_defaults: bool = False,
    exclude_none: bool = False,
    convert_datetime=True,
) -> list[dict[str, Any]]:
    """
    将多个SQLModel模型实例转换为字典列表。

    参数:
    - models: SQLModel实例序列。
    - include: 包含的字段，None表示全部字段。
    - exclude: 排除的字段，None表示不排除任何字段。
    - exclude_unset: 是否排除未设置值的字段，默认为False。
    - exclude_defaults: 是否排除等于默认值的字段，默认为False。
    - exclude_none: 是否排除值为None的字段，默认为False。
    - convert_datetime: 是否将datetime对象转换为Unix时间戳，默认为True。

    返回:
    - 包含多个字典的列表，每个字典代表一个模型实例的数据。如果convert_datetime为True，则datetime字段被转换为Unix时间戳。
    """
    result = [
        model.model_dump(
            include=include,
            exclude=exclude,
            exclude_unset=exclude_unset,
            exclude_defaults=exclude_defaults,
            exclude_none=exclude_none,
        )
        for model in models
    ]
    if convert_datetime:
        return [{k: int(v.timestamp()) if isinstance(v, datetime) else v for k, v in model.items()} for model in result]
    return result