from aiohttp import Client<PERSON>ession
from starlette.requests import Request

from app.config import config
from app.models.token import Token
from app.models.user import User

from .exception import not_authenticated_error, unauthorized_error

token_client: ClientSession


def init_token_client():
    if config.TOKEN_SERVER:
        global token_client
        token_client = ClientSession(
            base_url=config.TOKEN_SERVER,
        )


async def shutdown_token_client():
    if config.TOKEN_SERVER:
        await token_client.close()


async def verify_token(token: str) -> User:
    if not config.TOKEN_SERVER:
        return User(1, 'test')

    user = await Token.get(token)
    if user:
        return user
    else:
        async with token_client.post('/cra-server/auth/validate_token', json={'token': token}) as response:
            response.raise_for_status()
            result = await response.json()
        if result['code'] == 10000:
            data = result['data']
            user = User(data['userId'], data['userName'])
            await Token.set(token, user)
            return user
    raise unauthorized_error(result['message'])


async def get_current_user(request: Request) -> User:
    token = request.headers.get('Token')
    if not token:
        raise not_authenticated_error
    return await verify_token(token)
