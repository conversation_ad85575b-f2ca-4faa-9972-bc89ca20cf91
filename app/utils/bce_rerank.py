import json
import logging
from typing import Protocol, Sequence

from aiohttp import ClientSession

from app.config import config
from app.utils.exception import ModelError

from .singleton import Singleton


class DocChunk(Protocol):
    paragraph: str


class TableChunk(Protocol):
    content_md: str


class BCERerank(metaclass=Singleton):
    def __init__(self) -> None:
        self.client = ClientSession(base_url=config.BCE_RERANK_API_BASE_URL)

    async def shutdown(self) -> None:
        await self.client.close()

    async def rerank(self, query: str, documents: Sequence[str], top_n: int = 1) -> list[str]:
        async with self.client.post(
            url=config.BCE_RERANK_API_PATH,
            json={
                'query': query,
                'documents': documents,
                'top_n': top_n,
            },
        ) as response:
            if not response.ok:
                raise ModelError(f'status: {response.status}, text: {response.text}')

            try:
                result = await response.json()
            except Exception as e:
                logging.exception('Failed to parse BCE Rerank response: %s', response.text)
                raise ModelError(f'invalid response: {response.text}') from e

        return result

    async def most_relatived_content(self, query: str, contents: Sequence[str]) -> str:
        if len(contents) > 1:
            contents = list(set(contents))
            if len(contents) > 1:
                reranked_contents = await self.rerank(query, contents)
                return reranked_contents[0]
        return contents[0]

    async def most_relatived_paragraph(self, query: str, paragraphs: Sequence[str]) -> str:
        contents = ['\n'.join(json.loads(paragraph)) for paragraph in paragraphs]
        if len(contents) > 1:
            contents = list(set(contents))
            if len(contents) > 1:
                reranked_contents = await self.rerank(query, contents)
                return reranked_contents[0]
        return contents[0]

    async def most_relatived_doc_chunk(self, query: str, doc_chunks: Sequence[DocChunk]) -> str:
        if len(doc_chunks) > 1:
            contents = ['\n'.join(json.loads(doc_chunk.paragraph)) for doc_chunk in doc_chunks]
            contents = list(set(contents))
            if len(contents) > 1:
                reranked_contents = await self.rerank(query, contents)
                return reranked_contents[0]
            return contents[0]
        return doc_chunks[0].paragraph

    async def most_relatived_table_chunk(self, query: str, table_chunks: Sequence[TableChunk]) -> str:
        if len(table_chunks) > 1:
            contents = list(set(table_chunk.content_md for table_chunk in table_chunks))
            if len(contents) > 1:
                reranked_contents = await self.rerank(query, contents)
                return reranked_contents[0]
            return contents[0]
        return table_chunks[0].content_md
