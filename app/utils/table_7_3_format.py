import re

PERCENTAGE_PATTERN = re.compile(r'([+-]?)(\d+\.\d+|\d+)([^\d%]*)')


def replace_percentage(match):
    sign, value = match.group(1), match.group(2)
    if sign == '+':
        return f'{value}%↑'  # %↑
    elif sign == '-':
        return f'{value}%↓'  # %↓
    else:
        return f'{value}%'


def format_percentage(text: str) -> str:
    # 使用函数形式处理正则表达式替换逻辑
    text = PERCENTAGE_PATTERN.sub(replace_percentage, text)  # 统一正则表达式
    return text
