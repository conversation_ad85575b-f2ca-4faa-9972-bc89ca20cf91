from enum import Enum


class ErrorCode(Enum):
    SUCCESS = 0  # 成功
    FAILED = 1  # 失败
    BAD_REQUEST = 2  # 错误的请求
    UNAUTHORIZED = 3  # 未授权
    FORBIDDEN = 4  # 禁止访问
    NOT_FOUND = 5  # 找不到资源
    INTERNAL_SERVER_ERROR = 6  # 内部服务器错误
    DATABASE_ERROR = 7  # 数据库错误
    INTERNAL_API_ERROR = 8  # 内部API错误
    EXTERNAL_API_ERROR = 9  # 外部API错误
    MODEL_ERROR = 10  # 模型错误
    DOC_TYPE_ERROR = 11  # 文档不符合规范
