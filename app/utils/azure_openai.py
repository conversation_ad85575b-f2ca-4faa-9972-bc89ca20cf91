import logging
from time import time

from aiohttp import ClientSession
from openai.types.chat.chat_completion_message_param import ChatCompletionMessageParam
from openai.types.chat.chat_completion_system_message_param import (
    ChatCompletionSystemMessageParam,
)
from openai.types.chat.chat_completion_user_message_param import (
    ChatCompletionUserMessageParam,
)

from app.config import config
from app.schemas.completion_result import CompletionResult, Usage
from app.utils.exception import ModelError
from app.utils.json_logger import log_json

from .singleton import Singleton


class AzureOpenAI(metaclass=Singleton):
    # 接口文档：https://learn.microsoft.com/en-us/azure/ai-services/openai/reference

    API_PATH = f'/openai/deployments/{config.AZURE_OPENAI_MODEL}/chat/completions?api-version=2024-02-15-preview'

    def __init__(self) -> None:
        self.client = ClientSession(
            base_url=config.AZURE_OPENAI_API_ENDPOINT,
            headers={'api-key': config.AZURE_OPENAI_API_KEY},
        )

    async def shutdown(self) -> None:
        await self.client.close()

    async def chat(
        self,
        system_message: str = '',
        user_message: str = '',
        **kwargs,
    ) -> CompletionResult:
        messages: list[ChatCompletionMessageParam] = []
        if system_message:
            messages.append(ChatCompletionSystemMessageParam(content=system_message, role='system'))
        if user_message:
            messages.append(ChatCompletionUserMessageParam(content=user_message, role='user'))
        assert messages, 'neither system_message nor user_message is provided'

        async with self.client.post(
            url=self.API_PATH,
            json={
                'messages': messages,
                'temperature': 0,
                'seed': 520,  # 固定一个值，减少随机性
                **kwargs,
            },
        ) as response:
            if not response.ok:
                if response.status == 429:
                    logging.error('OpenAI rate limit exceeded')
                raise ModelError(f'status: {response.status}, text: {response.text}')

            try:
                result = await response.json()
            except Exception as e:
                logging.exception('Failed to parse OpenAI response: %s', response.text)
                raise ModelError(f'invalid response: {response.text}') from e

        error = result.get('error')
        if error:
            logging.error('OpenAI error: %s', error)
            raise ModelError(error)

        content = result['choices'][0]['message']['content']
        usage_data = result['usage']
        usage = Usage(
            input_tokens=usage_data['prompt_tokens'],
            output_tokens=usage_data['completion_tokens'],
        )
        if system_message:
            system_messages = system_message.split('\n')
            system_messages_str = '\n'.join(system_messages[:2])  # 只记录 system_message 的前 2 行
            message = f'{system_messages_str}\n{user_message}'
        else:
            message = user_message
        log_json(
            {
                'model': 'gpt-4o',
                'message': message,
                'result': content,
                'input_tokens': usage.input_tokens,
                'output_tokens': usage.output_tokens,
                'timestamp': int(time()),
            }
        )
        return CompletionResult(
            content=content,
            usage=usage,
        )
