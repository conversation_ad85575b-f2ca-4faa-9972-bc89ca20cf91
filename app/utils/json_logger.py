import logging
from asyncio import create_task, sleep
from json import dumps
from os import makedirs
from os.path import dirname, exists

import aiofiles

from app.config import config


def log_json(data: dict):
    _log_json(data)


def _log_json(data: dict):
    pass


async def teardown_logger():
    await _teardown()


async def _teardown():
    pass


async def setup_logger():
    global _log_json, _teardown

    if config.USAGE_LOGGING_PATH:
        try:
            dir_name = dirname(config.USAGE_LOGGING_PATH)
            if not exists(dir_name):
                makedirs(dir_name, exist_ok=True)

            log_file = await aiofiles.open(config.USAGE_LOGGING_PATH, 'a', encoding='utf-8')

            logs = []
            is_running = True

            async def teardown():
                nonlocal is_running
                is_running = False
                await sleep(1)  # 等待 save()

            _teardown = teardown

            def log_json(data: dict):
                logs.append(dumps(data, ensure_ascii=False))

            _log_json = log_json

            async def save():
                async def _save():
                    logs.append('')  # 让最后一行以 \n 结尾
                    content = '\n'.join(logs)
                    logs.clear()
                    await log_file.write(content)
                    await log_file.flush()

                while True:
                    if logs:
                        await _save()
                    if is_running:
                        await sleep(1)
                    else:
                        if logs:  # 退出前再检查一次，避免有遗漏
                            await _save()
                        break

            create_task(save())

        except Exception:
            logging.exception('Failed to initialize JSON logger')
