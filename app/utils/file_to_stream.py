import io
import os


def file_to_stream(file_path):
    """
    将指定路径的文件转换为字节流。

    参数:
    file_path (str): 文件的路径。

    返回:
    io.BytesIO: 包含文件内容的字节流对象。

    异常:
    FileNotFoundError: 如果指定路径的文件不存在。
    """
    if not os.path.isfile(file_path):
        raise FileNotFoundError(f"文件 {file_path} 不存在")

    with open(file_path, 'rb') as file:
        # 读取文件内容
        file_content = file.read()

    stream = io.BytesIO(file_content)
    return stream