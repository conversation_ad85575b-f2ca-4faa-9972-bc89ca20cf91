import asyncio
import copy
import io
import json
import logging
import re
import zipfile
from collections import defaultdict
from typing import Literal, Sequence, Tuple

from docx import Document
from docx.opc.oxml import BaseOxmlElement
from lxml import etree
from sqlalchemy import Row
from sqlmodel import col as Col

from app.clients.mysql import get_session
from app.models.doc import Doc
from app.models.generated_field import GeneratedField
from app.models.project import Project
from app.schemas.builder import (
    BuilderCol,
    BuilderSaveDetail,
    BuilderTable,
    BuilderTableRow,
    TableKey,
)
from app.schemas.doc_base_field import TrialBaseFieldInfo, TrialSubType
from app.schemas.doc_generate import (
    CellHorizontallyAlign,
    CellVerticalAlign,
    GenerateBaseData,
    GenerateCol,
    GenerateContent,
    GenerateRow,
    GenerateTable,
)
from app.schemas.table import Table
from app.utils.doc.doc_chunk import get_text_from_element
from app.utils.doc.doc_config import (
    table2671,
    table2672,
    table2675,
    table2676,
    table26771,
    table26781,
    table26782,
    table26791,
)
from app.utils.doc.doc_type import NAME_SPACES, ElementType, WordType
from app.utils.file_to_stream import file_to_stream


class DocGenerate:
    OUTPUT_TEMPLATE_URL: str = 'static/output-template.docx'
    OUTPUT_URL: str = 'static/{}-267-毒理学列表总结.docx'
    # 替换上/下标字符集合 - origin: (sub, remainder_before)
    SUB_SIGN: list[dict[str, Tuple[str, str]]] = [{'AUClast': ('last', 'AUC')}, {'Cmax': ('max', 'C')}]
    SUP_SIGN: list[dict[str, Tuple[str, str]]] = [{'阳性对照b': ('b', '阳性对照')}, {'阳性对照c': ('c', '阳性对照')}]
    # 由数字和零个或多个 * 组成的字符串，可能有负号和小数点（如：*5、-3.14、***2.5）。
    # 包含 ± 符号的数值区间（如：5.5±0.1）。
    # 任何包含 ↑ 或 ↓ 的字符串（如：value↑）。
    # 仅为一个单独的负号 -。
    SKIP_VALUES_PATTERN = re.compile(
        r'^(?:\**-?\d+(\.\d*)?|\*+-?\d+(\.\d*)?|\d+(\.\d*)?\*+|\*+\d+(\.\d*)*\*+|-|\d+\.\d+±\d+\.\d+|.*[↑↓].*)$'
    )
    # 数值/数值区间正则表达式（正负数小数）
    SKIP_VALUES_NUMBER_PATTERN = re.compile(r'^-?\d+(\.\d+)?/-?\d+(\.\d+)?$')
    HEADER_TEST_PRODUCT = 'test_product'
    HEADER_APPLICANT = 'applicant'
    ERROR_KEYWORD = 'error'

    @classmethod
    async def generate_to_builder(cls, project_id: int, generate_data: GenerateBaseData) -> list[BuilderTable]:
        """
        异步生成Builder表格列表。
        该方法接收项目ID和生成基础数据作为参数，返回一个Builder表格列表。
        它并发地从不同的数据源生成数据，并将这些数据转换为Builder表格对象，
        最后返回这些对象的列表。
        参数:
        :param project_id 项目ID。
        :param generate_data 生成基础数据，包含各种试验数据。
        返回:
        :return 表格列表。
        """
        builder_table_list: list[BuilderTable] = []

        # 并发执行
        (
            data2671,
            data2672,
            data2673,
            data2675,
            data2676,
            data26781_above,
            data26781_below,
            data26782_above,
            data26782_below,
            data2679_above,
            data2679_below,
        ) = await asyncio.gather(
            cls.generate_overview_of_toxicology_data(generate_data),
            cls.generate_overview_of_tox_data(generate_data.repetition_data, project_id),
            cls.generate_data_overview_of_tox_data(project_id),
            cls.generate_single_dose_toxicity_data(generate_data.single_data, project_id),
            cls.generate_repeat_dose_tox_test_data(generate_data.repetition_data, project_id),
            cls.generate_genotoxicity_vivo_above_data(generate_data.back_mutation_data, project_id),
            cls.generate_genotoxicity_vivo_below_data(project_id),
            cls.generate_genotoxicity_vivo_above_data2(generate_data.chromosome_data, project_id),
            cls.generate_genotoxicity_vivo_below_data2(project_id),
            cls.generate_micronucleus_test_above_data(project_id, generate_data.micronucleus_data),
            cls.generate_micronucleus_test_below_data(project_id),
            return_exceptions=True,
        )

        # ******* 毒理学概述
        builder_table_list.append(
            cls.generate_table_to_builder_table(
                TableKey.TABLE_2671,
                data2671,
                left_fixed_header_title='******* 毒理学概述',
                test_product=generate_data.single_data[0].test_product if generate_data.single_data else '',
            )
        )

        # ******* 毒代动力学：毒代动力学试验概述
        if generate_data.repetition_data:
            builder_table_list.append(
                cls.generate_table_to_builder_table(
                    TableKey.TABLE_2672,
                    data2672,
                    left_fixed_header_title='******* 毒代动力学: 毒代动力学试验概述',
                    test_product=generate_data.repetition_data[0].test_product,
                )
            )
        else:
            builder_table_list.append(
                BuilderTable(
                    left_fixed_header_title='******* 毒代动力学: 毒代动力学试验概述',
                    table_key=TableKey.TABLE_2672,
                    is_empty=True,
                )
            )

        # 2.6.7.3 毒代动力学：毒代动力学数据概述
        if generate_data.repetition_data:
            builder_table_list.append(
                cls.generate_table_to_builder_table(
                    TableKey.TABLE_2673,
                    data2673,
                    left_fixed_header_title='2.6.7.3 毒代动力学: 毒代动力学数据概述',
                    test_product=generate_data.repetition_data[0].test_product,
                )
            )
        else:
            builder_table_list.append(
                BuilderTable(
                    left_fixed_header_title='2.6.7.3 毒代动力学: 毒代动力学数据概',
                    table_key=TableKey.TABLE_2673,
                    is_empty=True,
                )
            )

        # 2.6.7.5 单次给药毒性
        if generate_data.single_data:
            builder_table_list.append(
                cls.generate_table_to_builder_table(
                    TableKey.TABLE_2675,
                    data2675,
                    left_fixed_header_title='2.6.7.5 单次给药毒性',
                    test_product=generate_data.single_data[0].test_product,
                )
            )
        else:
            builder_table_list.append(
                BuilderTable(
                    left_fixed_header_title='2.6.7.5 单次给药毒性',
                    table_key=TableKey.TABLE_2675,
                    is_empty=True,
                )
            )

        # 2.6.7.6 重复给药毒性：非关键试验
        if generate_data.repetition_data:
            builder_table_list.append(
                cls.generate_table_to_builder_table(
                    TableKey.TABLE_2676,
                    data2676,
                    left_fixed_header_title='2.6.7.6 重复给药毒性: 非关键试验',
                    test_product=generate_data.repetition_data[0].test_product,
                )
            )
        else:
            builder_table_list.append(
                BuilderTable(
                    left_fixed_header_title='2.6.7.6 重复给药毒性: 非关键试验',
                    table_key=TableKey.TABLE_2676,
                    is_empty=True,
                )
            )

        # 独立处理多文档类型2.6.7.7
        if not generate_data.repetition_data:
            builder_table_list.append(
                BuilderTable(
                    left_fixed_header_title='2.6.7.7 重复给药毒性',
                    table_key=TableKey.TABLE_2677,
                    is_empty=True,
                )
            )

        task_2677_list = []
        trial_2677_title_list: list[str] = []
        trial_number_2677_list: list[str] = []
        test_product_2677_list: list[str] = []
        for repetition_data in generate_data.repetition_data:
            if repetition_data.trial_subtype == TrialSubType.REPEATED_DOSE_SPECIFICITY.value:
                task_2677_list.append(cls.generate_repeat_dose_critical_test_data(repetition_data, repetition_data.id))
                trial_number_2677_list.append(repetition_data.trial_number)
                test_product_2677_list.append(repetition_data.test_product)
                trial_2677_title_list.append(repetition_data.trial_title)
        # 并发执行
        data2677_futures = await asyncio.gather(*task_2677_list)

        # 2.6.7.7 重复给药毒性：关键试验
        for result_index, result_task in enumerate(data2677_futures):
            builder_table_list.append(
                cls.generate_table_to_builder_table(
                    TableKey.TABLE_2677,
                    result_task[0],
                    left_fixed_header_title='2.6.7.7 重复给药毒性：关键试验' if result_index == 0 else '',
                    left_fixed_subheader_title=f'2.6.7.7.{result_index + 1} 报告标题: 重复给药毒性: ',
                    subheader_title=trial_2677_title_list[result_index],
                    trial_number=trial_number_2677_list[result_index],
                    test_product=test_product_2677_list[result_index],
                )
            )
            builder_table_list.append(
                cls.generate_table_to_builder_table(
                    TableKey.TABLE_2677,
                    result_task[1],
                )
            )
            builder_table_list.append(
                cls.generate_table_to_builder_table(
                    TableKey.TABLE_2677,
                    result_task[2],
                    left_fixed_subheader_title=f'2.6.7.7.{result_index + 1} 报告标题: 重复给药毒性: ',
                    subheader_title=f'{trial_2677_title_list[result_index]}（续）',
                    trial_number=trial_number_2677_list[result_index],
                    test_product=test_product_2677_list[result_index],
                )
            )

        # 2.6.7.8.1 遗传毒性：体外 - 回复
        if generate_data.back_mutation_data:
            builder_table_list.append(
                cls.generate_table_to_builder_table(
                    TableKey.TABLE_2678,
                    data26781_above,
                    left_fixed_header_title='2.6.7.8 遗传毒性: 体外',
                    left_fixed_subheader_title='2.6.7.8.1',
                    subheader_title=generate_data.back_mutation_data.trial_title,
                    trial_number=generate_data.back_mutation_data.trial_number,
                    test_product=generate_data.back_mutation_data.test_product,
                )
            )
            builder_table_list.append(
                cls.generate_table_to_builder_table(
                    TableKey.TABLE_2678,
                    data26781_below,
                    left_fixed_header_title='',
                )
            )
        else:
            builder_table_list.append(
                BuilderTable(
                    left_fixed_header_title='2.6.7.8 遗传毒性: 体外',
                    left_fixed_subheader_title='2.6.7.8.1',
                    table_key=TableKey.TABLE_2678,
                    is_empty=True,
                )
            )
        # 2.6.7.8.2 遗传毒性：体外 - 染色体
        if generate_data.chromosome_data:
            builder_table_list.append(
                cls.generate_table_to_builder_table(
                    TableKey.TABLE_2678,
                    data26782_above,
                    left_fixed_subheader_title='2.6.7.8.2 报告标题: ',
                    subheader_title=generate_data.chromosome_data.trial_title,
                    trial_number=generate_data.chromosome_data.trial_number,
                    test_product=generate_data.chromosome_data.test_product,
                )
            )
            builder_table_list.append(
                cls.generate_table_to_builder_table(
                    TableKey.TABLE_2678,
                    data26782_below,
                )
            )
        else:
            builder_table_list.append(
                BuilderTable(
                    left_fixed_subheader_title='2.6.7.8.2',
                    table_key=TableKey.TABLE_2678,
                    is_empty=True,
                )
            )
        # 2.6.7.9 遗传毒性：体外 - 微核
        if generate_data.micronucleus_data:
            builder_table_list.append(
                cls.generate_table_to_builder_table(
                    TableKey.TABLE_2679,
                    data2679_above,
                    left_fixed_header_title='2.6.7.9 遗传毒性: 体内',
                    left_fixed_subheader_title='2.6.7.9.1 报告标题: ',
                    subheader_title=generate_data.micronucleus_data.trial_title,
                    trial_number=generate_data.micronucleus_data.trial_number,
                    test_product=generate_data.micronucleus_data.test_product,
                )
            )
            builder_table_list.append(
                cls.generate_table_to_builder_table(
                    TableKey.TABLE_2679,
                    data2679_below,
                )
            )
        else:
            builder_table_list.append(
                BuilderTable(
                    left_fixed_header_title='2.6.7.9 遗传毒性: 体内',
                    table_key=TableKey.TABLE_2679,
                    is_empty=True,
                )
            )

        return builder_table_list

    @classmethod
    async def builder_export_to_word(cls, project_id: int, builder_data: BuilderSaveDetail) -> io.BytesIO:
        """
        该方法通过将 `builder_data` 中的数据填充到一个现有的 Word 模板中，生成一个新的 Word 文档 (.docx)，
        并将最终的文档保存到文件中，同时返回一个 BytesIO 流以便后续使用。
        方法步骤：
        1. 从指定 URL 加载 Word 模板。
        2. 解析模板中的 `document.xml` 文件，并用提供的 `builder_data` 中的相关数据更新其内容。
        3. 根据 `builder_data` 中的表格键（table keys），遍历不同的表格并应用特定的处理逻辑，
           例如生成文本和表格以填充各种毒理学数据。
        4. 更新 `document.xml` 中的内容，并将修改后的内容保存到一个新的 BytesIO 流中。
        5. 将修改后的文档保存到磁盘文件中，并将最终的 BytesIO 流返回，以便进一步使用（如通过 HTTP 发送或本地保存）。
        参数:
        :param project_id: int - 项目的唯一标识符，用于确定生成文档时保存文件的路径。
        :param builder_data: BuilderSaveDetail - 包含所有必要数据的对象，包括不同表格的数据，用于填充 Word 文档中的具体内容。
        返回:
        :return: io.BytesIO - 包含生成的 Word 文档内容的 BytesIO 流，返回的 BytesIO 对象可用于进一步处理或直接下载。
        异常:
        :raises FileNotFoundError: 如果模板文件流不存在，将引发该异常，表示无法生成文档。
        """
        file_bytes_io: io.BytesIO = file_to_stream(cls.OUTPUT_TEMPLATE_URL)

        if file_bytes_io is None:
            raise FileNotFoundError('缺失文件流数据，无法生成 (io.BytesIO)')

        with zipfile.ZipFile(file_bytes_io, 'r') as zip_ref:
            # 读取word/document.xml文件的内容
            document_xml = zip_ref.read('word/document.xml')

            # 解析XML内容
            tree = etree.XML(document_xml)

            # 获取body元素
            body: BaseOxmlElement = tree.find('.//w:body', namespaces=NAME_SPACES)

            # 构建一个字典，以 TableKey 为键，BuilderTable 列表为值
            table_map = defaultdict(list)
            for table in builder_data.table_list:
                table_map[table.table_key].append(table)

            # ******* 毒理学概述
            chapter2671 = table_map.get(TableKey.TABLE_2671, [])
            if chapter2671:
                await cls.generate_overview_of_toxicology(
                    body,
                    cls.builder_table_to_generate_table(chapter2671[0]),
                    chapter2671[0].test_product,
                    chapter2671[0].footer,
                )

            # ******* 毒代动力学：毒代动力学试验概述
            chapter2672 = table_map.get(TableKey.TABLE_2672, [])
            if chapter2672:
                await cls.generate_overview_of_tox(
                    body,
                    cls.builder_table_to_generate_table(chapter2672[0]),
                    chapter2672[0].test_product,
                    chapter2672[0].footer,
                )

            # 2.6.7.3 毒代动力学：毒代动力学数据概述
            chapter2673 = table_map.get(TableKey.TABLE_2673, [])
            if chapter2673:
                await cls.generate_data_overview_of_tox(
                    body,
                    cls.builder_table_to_generate_table(chapter2673[0]),
                    chapter2673[0].test_product,
                    chapter2673[0].footer,
                )

            # 2.6.7.5 毒代动力学：毒代动力学数据概述
            chapter2675 = table_map.get(TableKey.TABLE_2675, [])
            if chapter2675:
                await cls.generate_single_dose_toxicity(
                    body,
                    cls.builder_table_to_generate_table(chapter2675[0]),
                    chapter2675[0].test_product,
                    chapter2675[0].footer,
                )

            # 2.6.7.6 重复给药毒性：非关键试验
            chapter2676 = table_map.get(TableKey.TABLE_2676, [])
            if chapter2676:
                await cls.generate_repeat_dose_tox_test(
                    body,
                    cls.builder_table_to_generate_table(chapter2676[0]),
                    chapter2676[0].test_product,
                    chapter2676[0].footer,
                )

            # 2.6.7.7 重复给药毒性：关键试验
            chapter2677 = table_map.get(TableKey.TABLE_2677, [])
            if chapter2677:
                await cls.generate_repeat_dose_critical_test(chapter2677, body)

            # 2.6.7.8 遗传毒性：体外
            chapter2678 = table_map.get(TableKey.TABLE_2678, [])
            if len(chapter2678) == 4:
                # 2.6.7.8.1
                data26781_above1: GenerateTable = cls.builder_table_to_generate_table(chapter2678[0])
                data26781_below1: GenerateTable = cls.builder_table_to_generate_table(chapter2678[1])
                subheader_title1: str = chapter2678[0].subheader_title
                trial_number1: str = chapter2678[0].trial_number
                test_product1: str = chapter2678[0].test_product
                footer_above1: str = chapter2678[0].footer
                footer_below1: str = chapter2678[1].footer
                await cls.generate_genotoxicity_vitro(
                    body,
                    data26781_above1,
                    data26781_below1,
                    subheader_title1,
                    test_product1,
                    trial_number1,
                    footer_above1,
                    footer_below1,
                )

                # 2.6.7.8.2
                data26781_above2: GenerateTable = cls.builder_table_to_generate_table(chapter2678[2])
                data26781_below2: GenerateTable = cls.builder_table_to_generate_table(chapter2678[3])
                subheader_title2: str = chapter2678[2].subheader_title
                trial_number2: str = chapter2678[2].trial_number
                test_product2: str = chapter2678[2].test_product
                footer_above2: str = chapter2678[2].footer
                footer_below2: str = chapter2678[3].footer
                await cls.generate_genotoxicity_vitro2(
                    body,
                    data26781_above2,
                    data26781_below2,
                    subheader_title2,
                    test_product2,
                    trial_number2,
                    footer_above2,
                    footer_below2,
                )

            # 2.6.7.9 遗传毒性：体外
            chapter2679 = table_map.get(TableKey.TABLE_2679, [])
            if len(chapter2679) == 2:
                data2679_above: GenerateTable = cls.builder_table_to_generate_table(chapter2679[0])
                data2679_below: GenerateTable = cls.builder_table_to_generate_table(chapter2679[1])
                subheader_title: str = chapter2679[0].subheader_title
                trial_number: str = chapter2679[0].trial_number
                test_product: str = chapter2679[0].test_product
                footer_above: str = chapter2679[0].footer
                footer_below: str = chapter2679[1].footer
                await cls.generate_micronucleus_test(
                    body,
                    data2679_above,
                    data2679_below,
                    subheader_title,
                    test_product,
                    trial_number,
                    footer_above,
                    footer_below,
                )

            # 重新保存整个文档到 BytesIO 对象
            output_bytes_io = io.BytesIO()
            with zipfile.ZipFile(output_bytes_io, 'w', zipfile.ZIP_DEFLATED) as new_zip:
                # 保留原有内容并更新 document.xml
                for file_info in zip_ref.infolist():
                    if file_info.filename != 'word/document.xml':
                        new_zip.writestr(file_info.filename, zip_ref.read(file_info.filename))

                # 更新 document.xml 文件
                updated_document_xml = etree.tostring(tree, xml_declaration=True, encoding='UTF-8')
                new_zip.writestr('word/document.xml', updated_document_xml)

            # 游标重置到开始位置
            output_bytes_io.seek(0)

        output_bytes_io = cls.set_header_text(
            output_bytes_io, builder_data.applicant_list, builder_data.test_product_list
        )

        # 保存修改后的 docx 文件
        with open(cls.OUTPUT_URL.format(project_id), 'wb') as f:
            f.write(output_bytes_io.read())

        # 二次游标重置到开始位置
        output_bytes_io.seek(0)

        return output_bytes_io

    @classmethod
    async def base_field_data_format(cls, project_id: int) -> GenerateBaseData:
        """
        根据项目ID格式化基础字段数据。
        此函数根据项目ID获取相关文档数据，并根据文档的试验子类型将数据格式化为GenerateBaseData对象。
        GenerateBaseData对象根据试验子类型包含不同的数据列表或单个数据对象。
        参数:
        :param project_id: int - 项目ID，用于获取相关文档数据。
        返回:
        :return GenerateBaseData - 包含格式化后基础字段数据的对象。
        """
        generate_data: GenerateBaseData = GenerateBaseData(
            single_data=[], repetition_data=[], back_mutation_data=None, chromosome_data=None, micronucleus_data=None
        )

        def add_data(data: Doc) -> TrialBaseFieldInfo:
            """
            辅助函数，用于将Doc对象转换为TrialBaseFieldInfo对象。
            参数:
            :param data: Doc - 需要转换的Doc对象。
            返回:
            :return TrialBaseFieldInfo - 转换后的对象，包含试验的基础信息。
            """
            return TrialBaseFieldInfo(
                id=data.id,
                trial_title=data.trial_title,
                trial_institution=data.trial_institution,
                trial_number=data.trial_number,
                species=data.species,
                solvent_and_dosage_form=data.solvent_and_dosage_form,
                glp_compliance=data.glp_compliance,
                trial_main_type=data.trial_main_type,
                trial_subtype=data.trial_subtype,
                administration_method=data.administration_method,
                administration_unit=data.administration_unit,
                dosing_regimen=data.dosing_regimen,
                administration_dosage=data.administration_dosage,
                test_product=data.test_product,
                applicant=data.applicant,
            )

        async with get_session() as session:
            docs = await Doc.get_by_project(session, project_id)

        for doc in docs:
            assert isinstance(doc, Doc)
            if doc.trial_subtype == 1:
                generate_data.single_data.append(add_data(doc))
            if doc.trial_subtype in (2, 3):
                generate_data.repetition_data.append(add_data(doc))
            if doc.trial_subtype == 4:
                generate_data.back_mutation_data = add_data(doc)
            if doc.trial_subtype == 5:
                generate_data.chromosome_data = add_data(doc)
            if doc.trial_subtype == 6:
                generate_data.micronucleus_data = add_data(doc)

        return generate_data

    # 2671 data
    @classmethod
    async def generate_overview_of_toxicology_data(cls, generate_data: GenerateBaseData) -> GenerateTable:
        """
        生成 ******* 毒理学概述的表格数据。
        该方法是一个类方法，用于在给定的文档主体中生成关于毒理学概述的表格。
        它通过查找标题下的表格元素，并根据提供的数据填充表格。
        参数:
        :param generate_data: GenerateBaseData类型，包含生成表格所需的数据。
        返回:
        :return GenerateTable - 转换后的对象，包含试验的基础信息。
        """
        generate_table: GenerateTable = GenerateTable(pass_before_row=0, rows=[])
        generate_table.rows.append(table2671.rows[0])

        def generate_data_fun(aside_title: str, data: list[TrialBaseFieldInfo]) -> None:
            """
            根据给定的数据生成表格行。

            该函数根据数据列表的长度决定是否合并行，并逐项遍历数据列表，
            根据每个项目的信息生成对应的表格行。

            参数:
            :param aside_title: str类型，表示侧标题。
            :param data: list[TrialBaseFieldInfo]类型，包含试验信息的数据列表。
            """
            if not data:
                return

            data_not_empty: list[TrialBaseFieldInfo] = [item for item in data if item is not None]
            row_merge: bool = len(data_not_empty) > 1
            for index, item in enumerate(data):
                if not item:
                    continue
                # 默认不合并
                row_span_count: int = 1
                # 多行数据下发生合并
                if row_merge:
                    row_span_count = 0 if index != 0 else len(data_not_empty)

                generate_table.rows.append(
                    GenerateRow(
                        cols=[
                            GenerateCol(
                                align_vertical=CellVerticalAlign.CENTER,
                                align_horizontally=CellHorizontallyAlign.CENTER,
                                row_span=row_merge,
                                row_span_count=row_span_count,
                                row_span_first_row=(index == 0 and row_merge),
                                content_line=[GenerateContent(value=aside_title)],
                            ),
                            GenerateCol(
                                align_vertical=CellVerticalAlign.CENTER,
                                align_horizontally=CellHorizontallyAlign.CENTER,
                                content_line=[GenerateContent(value=item.species)],
                            ),
                            GenerateCol(
                                align_vertical=CellVerticalAlign.CENTER,
                                align_horizontally=CellHorizontallyAlign.CENTER,
                                content_line=[GenerateContent(value=item.administration_method)],
                            ),
                            GenerateCol(
                                align_vertical=CellVerticalAlign.CENTER,
                                align_horizontally=CellHorizontallyAlign.CENTER,
                                content_line=[GenerateContent(value=item.dosing_regimen)],
                            ),
                            GenerateCol(
                                align_vertical=CellVerticalAlign.CENTER,
                                align_horizontally=CellHorizontallyAlign.CENTER,
                                content_line=[GenerateContent(value=item.administration_dosage.replace(' | ', '、'))],
                            ),
                            GenerateCol(
                                align_vertical=CellVerticalAlign.CENTER,
                                align_horizontally=CellHorizontallyAlign.CENTER,
                                content_line=[GenerateContent(value='是' if item.glp_compliance else '否')],
                            ),
                            GenerateCol(
                                align_vertical=CellVerticalAlign.CENTER,
                                align_horizontally=CellHorizontallyAlign.CENTER,
                                content_line=[GenerateContent(value=item.trial_institution)],
                            ),
                            GenerateCol(
                                align_vertical=CellVerticalAlign.CENTER,
                                align_horizontally=CellHorizontallyAlign.CENTER,
                                content_line=[GenerateContent(value=item.trial_number, font_color='0000FF')],
                            ),
                            GenerateCol(
                                align_vertical=CellVerticalAlign.CENTER,
                                align_horizontally=CellHorizontallyAlign.CENTER,
                                content_line=[GenerateContent(value='4.2.3')],
                            ),
                        ]
                    )
                )

        # 单次给药毒性
        generate_data_fun('单次给药毒性', generate_data.single_data)
        # 重复给药毒性
        generate_data_fun('重复给药毒性', generate_data.repetition_data)
        # 遗传毒性
        generate_data_fun(
            '遗传毒性',
            [generate_data.back_mutation_data, generate_data.chromosome_data, generate_data.micronucleus_data],
        )

        return generate_table

    # 2671 generate
    @classmethod
    async def generate_overview_of_toxicology(
        cls, body: Document, generate_data: BuilderTable, test_product: str, footer: str
    ) -> None:
        """
        生成 ******* 毒理学概述的表格。
        该方法是一个类方法，用于在给定的文档主体中生成关于毒理学概述的表格。
        它通过查找标题下的表格元素，并根据提供的数据填充表格。
        参数:
        :param body: Document | None类型，代表文档的主体部分。
        :param generate_data: GenerateBaseData类型，包含生成表格所需的数据。
        :param test_product: str类型，供试品。
        :param footer: str类型，表尾。
        """
        if not generate_data or not generate_data.rows:
            return
        title_toxicology = '毒理学概述'

        if generate_data.is_error:
            return

        table_element = cls.find_title_next_table(body, title_toxicology)
        assert table_element is not None
        p_element = cls.find_title(body, title_toxicology, is_include_title=True)
        assert p_element is not None
        cls.add_run_to_element(p_element, test_product)

        del generate_data.rows[0]
        await cls.generate_table(table_element, generate_data)

        # 添加表尾数据
        cls.add_footer_text(table_element, footer)

    # 2672 data
    @classmethod
    async def generate_overview_of_tox_data(
        cls, generate_data: list[TrialBaseFieldInfo], project_id: int
    ) -> GenerateTable:
        """
        生成 ******* 毒代动力学：毒代动力学试验概述的数据。
        该方法是一个类方法，用于在给定的文档主体中生成关于毒理学概述的表格。
        它通过查找标题下的表格元素，并根据提供的数据填充表格。
        参数:
        :param generate_data: GenerateBaseData类型，包含生成表格所需的数据。
        :param project_id: Int类型，表示项目ID。
        """
        generate_table: GenerateTable = GenerateTable(pass_before_row=0)
        generate_table.rows.append(table2672.rows[0])

        experiment_type_data: dict[int, str] = {}

        async with get_session() as session:
            docs: Sequence[Row] = await Doc.get_by_project(session, project_id, columns=(Doc.id, Doc.data))

        try:
            for doc in docs:
                experiment_type_data[doc.id] = json.loads(doc.data)['experiment_type']
        except Exception:
            logging.exception('毒代动力学试验概述文档数据错误')
            return GenerateTable(is_error=True)

        for item in generate_data:
            generate_table.rows.append(
                GenerateRow(
                    cols=[
                        GenerateCol(
                            align_vertical=CellVerticalAlign.CENTER,
                            align_horizontally=CellHorizontallyAlign.CENTER,
                            content_line=[GenerateContent(value=experiment_type_data[item.id])],
                        ),
                        GenerateCol(
                            align_vertical=CellVerticalAlign.CENTER,
                            align_horizontally=CellHorizontallyAlign.CENTER,
                            content_line=[GenerateContent(value=item.species)],
                        ),
                        GenerateCol(
                            align_vertical=CellVerticalAlign.CENTER,
                            align_horizontally=CellHorizontallyAlign.CENTER,
                            content_line=[GenerateContent(value=item.administration_method)],
                        ),
                        GenerateCol(
                            align_vertical=CellVerticalAlign.CENTER,
                            align_horizontally=CellHorizontallyAlign.CENTER,
                            content_line=[GenerateContent(value=item.administration_dosage)],
                        ),
                        GenerateCol(
                            align_vertical=CellVerticalAlign.CENTER,
                            align_horizontally=CellHorizontallyAlign.CENTER,
                            content_line=[GenerateContent(value='是' if item.glp_compliance else '否')],
                        ),
                        GenerateCol(
                            align_vertical=CellVerticalAlign.CENTER,
                            align_horizontally=CellHorizontallyAlign.CENTER,
                            content_line=[GenerateContent(value=item.trial_number, font_color='0000FF')],
                        ),
                        GenerateCol(
                            align_vertical=CellVerticalAlign.CENTER,
                            align_horizontally=CellHorizontallyAlign.CENTER,
                            content_line=[GenerateContent(value='4.2.3')],
                        ),
                    ]
                )
            )

        return generate_table

    # 2672 generate
    @classmethod
    async def generate_overview_of_tox(
        cls, body: Document, generate_data: BuilderTable, test_product: str, footer: str
    ) -> None:
        """
        生成 ******* 毒代动力学：毒代动力学试验概述的表格。
        该方法是一个类方法，用于在给定的文档主体中生成关于毒理学概述的表格。
        它通过查找标题下的表格元素，并根据提供的数据填充表格。
        参数:
        :param body: Document类型，代表文档的主体部分。
        :param generate_data: GenerateBaseData类型，包含生成表格所需的数据。
        :param test_product: str类型，供试品。
        :param footer: str类型，表尾。
        """
        if not generate_data or not generate_data.rows:
            return
        title_toxicology = '毒代动力学试验概述'

        table_element = cls.find_title_next_table(body, title_toxicology)
        assert table_element is not None
        p_element = cls.find_title(body, title_toxicology, is_include_title=True)
        assert p_element is not None
        cls.add_run_to_element(p_element, test_product)

        del generate_data.rows[0]
        await cls.generate_table(table_element, generate_data)

        # 添加表尾数据
        cls.add_footer_text(table_element, footer)

    # 2673 data
    @classmethod
    async def generate_data_overview_of_tox_data(cls, project_id: int) -> GenerateTable:
        """
        生成毒代动力学数据概述数据。
        该方法是异步的类方法，用于根据项目ID生成毒代动力学数据概述。
        它从项目数据中提取表格数据，格式化并生成一个表格对象。
        参数:
        :param project_id: 项目ID，用于获取项目数据。
        返回:
        :return GenerateTable: 生成的表格对象，包含格式化后的毒代动力学数据概述。
        """
        table_data: Table

        async with get_session() as session:
            project_data_str = await Project.get_by_id(session, project_id, columns=Col(Project.data))

        project_data: dict[str, str] = json.loads(project_data_str)
        table_data_str: str = project_data[GeneratedField.PK_TABLE]
        if cls.ERROR_KEYWORD in table_data_str:
            return GenerateTable(is_error=True)
        table_data = json.loads(table_data_str)

        generate_table: GenerateTable = GenerateTable()
        cls.fill_table_data_with_span(table_data, generate_table)

        # 设置(第1列 & 前5行)加粗和居中对齐
        for row_index, row in enumerate(generate_table.rows):
            for col_index, col in enumerate(row.cols):
                col.align_vertical = CellVerticalAlign.CENTER
                col.align_horizontally = CellHorizontallyAlign.CENTER
                for content in col.content_line:
                    if col_index == 0 or row_index < 5:
                        content.is_value_bold = True

        return generate_table

    # 2673 generate
    @classmethod
    async def generate_data_overview_of_tox(
        cls, body: Document, generate_data: BuilderTable, test_product: str, footer: str
    ) -> None:
        """
        生成毒代动力学数据概述表。
        该方法是异步的类方法，用于根据项目ID生成毒代动力学数据概述。
        它从项目数据中提取表格数据，格式化并生成一个表格对象。
        参数:
        :param body: 文档对象，如果提供，将在其中包含生成的表格。
        :param generate_data: 项目数据。
        :param test_product: str类型，供试品。
        :param footer: str类型，表尾。
        返回:
        :return  GenerateTable: 生成的表格对象，包含格式化后的毒代动力学数据概述。
        """
        if not generate_data or not generate_data.rows:
            return
        title_toxicology = '毒代动力学数据概述'

        p_element: BaseOxmlElement = cls.find_title(body, title_toxicology, is_include_title=True)
        cls.add_run_to_element(p_element, test_product)

        if generate_data.is_error:
            return

        # 删除合并列
        cls.delete_merge_column(generate_data)

        # 设置(第1列 & 前5行)加粗和居中对齐
        for row_index, row in enumerate(generate_data.rows):
            for col_index, col in enumerate(row.cols):
                col.align_vertical = CellVerticalAlign.CENTER
                col.align_horizontally = CellHorizontallyAlign.CENTER
                for content in col.content_line:
                    if col_index == 0 or row_index < 5:
                        content.is_value_bold = True

        p_element: BaseOxmlElement = cls.include_title(body, title_toxicology)
        table_element: BaseOxmlElement = etree.Element('{%s}tbl' % NAME_SPACES['w'])

        # 规整下标字体
        cls.format_vert_align_text(generate_data)

        await cls.generate_table(table_element, generate_data)
        p_element.addnext(table_element)

        # 添加表尾数据
        cls.add_footer_text(table_element, footer)

    # 2675 data
    @classmethod
    async def generate_single_dose_toxicity_data(
        cls, generate_data: list[TrialBaseFieldInfo], project_id: int
    ) -> GenerateTable:
        """
        生成 2.6.7.5 生成单次给药毒性试验的表格。
        :param generate_data: 一个包含试验基本信息的列表，用于生成毒性试验数据。
        :param project_id: 项目ID，用于数据库查询或其他需要项目信息的操作。
        """
        generate_table: GenerateTable = GenerateTable(pass_before_row=0)
        generate_table.rows.append(table2675.rows[0])

        fill_datas: dict[int, dict] = {}

        async with get_session() as session:
            docs = await Doc.get_by_project(
                session, project_id, trial_subtype=TrialSubType.SINGLE_DOSE.value, columns=(Doc.id, Doc.data)
            )

        try:
            for doc in docs:
                fill_datas[doc.id] = json.loads(doc.data)
        except Exception:
            logging.exception('单次给药试验文档数据错误')
            return GenerateTable(is_error=True)

        if not fill_datas:
            return GenerateTable(is_error=True)

        for item in generate_data:
            fill_data = fill_datas[item.id]
            gender: str = fill_data['gender_and_number_per_group'] if 'gender_and_number_per_group' in fill_data else ''
            notable_result: str = fill_data['notable_result'] if 'notable_result' in fill_data else ''
            dosage_max: str = fill_data['dosage_max'] if 'dosage_max' in fill_data else ''
            method_and_dosage_form = (
                fill_data['administration_method_solvent_and_dosage_form']
                if 'administration_method_solvent_and_dosage_form' in fill_data
                else ''
            )
            parts = method_and_dosage_form.split('\n')
            parts_1: str = parts[0] if parts else ''
            parts_2: str = parts[1] if parts and len(parts) > 1 else ''

            generate_table.rows.append(
                GenerateRow(
                    cols=[
                        GenerateCol(
                            align_vertical=CellVerticalAlign.CENTER,
                            align_horizontally=CellHorizontallyAlign.CENTER,
                            content_line=[GenerateContent(value=item.species)],
                        ),
                        GenerateCol(
                            align_vertical=CellVerticalAlign.CENTER,
                            align_horizontally=CellHorizontallyAlign.CENTER,
                            content_line=[GenerateContent(value=parts_1), GenerateContent(value=parts_2)],
                            is_error=cls.ERROR_KEYWORD in method_and_dosage_form,
                        ),
                        GenerateCol(
                            align_vertical=CellVerticalAlign.CENTER,
                            align_horizontally=CellHorizontallyAlign.CENTER,
                            content_line=[GenerateContent(value=item.administration_dosage)],
                        ),
                        GenerateCol(
                            align_vertical=CellVerticalAlign.CENTER,
                            align_horizontally=CellHorizontallyAlign.CENTER,
                            content_line=[GenerateContent(value=gender)],
                            is_error=cls.ERROR_KEYWORD in gender,
                        ),
                        GenerateCol(
                            align_vertical=CellVerticalAlign.CENTER,
                            align_horizontally=CellHorizontallyAlign.CENTER,
                            content_line=[GenerateContent(value=dosage_max)],
                            is_error=cls.ERROR_KEYWORD in dosage_max,
                        ),
                        GenerateCol(
                            align_vertical=CellVerticalAlign.CENTER,
                            align_horizontally=CellHorizontallyAlign.CENTER,
                            content_line=[GenerateContent(value=notable_result)],
                            is_error=cls.ERROR_KEYWORD in notable_result,
                        ),
                        GenerateCol(
                            align_vertical=CellVerticalAlign.CENTER,
                            align_horizontally=CellHorizontallyAlign.CENTER,
                            content_line=[GenerateContent(value=item.trial_number, font_color='0000FF')],
                        ),
                    ]
                )
            )

        return generate_table

    # 2675 generate
    @classmethod
    async def generate_single_dose_toxicity(
        cls, body: Document, generate_data: BuilderTable, test_product: str, footer: str
    ) -> None:
        """
        生成 2.6.7.5 生成单次给药毒性试验的表格。
        :param body: Word文档的主体部分，用于查找和替换数据。
        :param generate_data: 一个包含试验基本信息的列表，用于生成毒性试验数据。
        :param test_product: str类型，供试品。
        :param footer: str类型，表格页脚文本。
        """
        if not generate_data or not generate_data.rows:
            return
        title_toxicology = '单次给药毒性'
        table_element = cls.find_title_next_table(body, title_toxicology)
        assert table_element is not None

        p_element = cls.find_title(body, title_toxicology, is_include_title=True)
        assert p_element is not None
        cls.add_run_to_element(p_element, test_product)

        if generate_data.is_error:
            return

        del generate_data.rows[0]
        await cls.generate_table(table_element, generate_data)

        # 添加表尾数据
        cls.add_footer_text(table_element, footer)

    # 2676 data
    @classmethod
    async def generate_repeat_dose_tox_test_data(
        cls, generate_data: list[TrialBaseFieldInfo], project_id: int
    ) -> GenerateTable:
        """
        生成 2.6.7.6 生成重复给药毒性：非关键试验的表格数据。
        :param generate_data: 一个包含试验基本信息的列表，用于生成给药毒性试验数据。
        :param project_id: 项目ID，用于数据库查询或其他需要项目信息的操作。
        """
        generate_table: GenerateTable = GenerateTable(pass_before_row=0)
        generate_table.rows.append(table2676.rows[0])

        fill_datas: dict[int, dict] = {}

        async with get_session() as session:
            docs = await Doc.get_by_project(
                session, project_id, trial_subtype=TrialSubType.REPEATED_DOSE.value, columns=(Doc.id, Doc.data)
            )

        try:
            for doc in docs:
                fill_datas[doc.id] = json.loads(doc.data)
        except Exception:
            logging.exception('重复给药试验文档数据错误')
            return GenerateTable(is_error=True)

        if not fill_datas:
            return GenerateTable(is_error=True)

        for item in [x for x in generate_data if x.trial_subtype == TrialSubType.REPEATED_DOSE.value]:
            fill_data = fill_datas[item.id]
            gender: str = fill_data['gender_and_number_per_group'] if 'gender_and_number_per_group' in fill_data else ''
            notable_result: str = fill_data['notable_result'] if 'notable_result' in fill_data else ''
            dosage_max: str = fill_data['dosage_max'] if 'dosage_max' in fill_data else ''
            method_and_dosage_form = (
                fill_data['administration_method_solvent_and_dosage_form']
                if 'administration_method_solvent_and_dosage_form' in fill_data
                else ''
            )
            parts = method_and_dosage_form.split('\n')
            parts_1: str = parts[0] if parts else ''
            parts_2: str = parts[1] if parts and len(parts) > 1 else ''

            generate_table.rows.append(
                GenerateRow(
                    cols=[
                        GenerateCol(
                            align_vertical=CellVerticalAlign.CENTER,
                            align_horizontally=CellHorizontallyAlign.CENTER,
                            content_line=[GenerateContent(value=item.species)],
                        ),
                        GenerateCol(
                            align_vertical=CellVerticalAlign.CENTER,
                            align_horizontally=CellHorizontallyAlign.CENTER,
                            content_line=[GenerateContent(value=parts_1), GenerateContent(value=parts_2)],
                            is_error=cls.ERROR_KEYWORD in method_and_dosage_form,
                        ),
                        GenerateCol(
                            align_vertical=CellVerticalAlign.CENTER,
                            align_horizontally=CellHorizontallyAlign.CENTER,
                            content_line=[GenerateContent(value=item.dosing_regimen)],
                        ),
                        GenerateCol(
                            align_vertical=CellVerticalAlign.CENTER,
                            align_horizontally=CellHorizontallyAlign.CENTER,
                            content_line=[GenerateContent(value=item.administration_dosage)],
                        ),
                        GenerateCol(
                            align_vertical=CellVerticalAlign.CENTER,
                            align_horizontally=CellHorizontallyAlign.CENTER,
                            content_line=[GenerateContent(value=gender)],
                            is_error=cls.ERROR_KEYWORD in gender,
                        ),
                        GenerateCol(
                            align_vertical=CellVerticalAlign.CENTER,
                            align_horizontally=CellHorizontallyAlign.CENTER,
                            content_line=[GenerateContent(value=dosage_max)],
                            is_error=cls.ERROR_KEYWORD in dosage_max,
                        ),
                        GenerateCol(
                            align_vertical=CellVerticalAlign.CENTER,
                            align_horizontally=CellHorizontallyAlign.CENTER,
                            content_line=[GenerateContent(value=notable_result)],
                            is_error=cls.ERROR_KEYWORD in notable_result,
                        ),
                        GenerateCol(
                            align_vertical=CellVerticalAlign.CENTER,
                            align_horizontally=CellHorizontallyAlign.CENTER,
                            content_line=[GenerateContent(value=item.trial_number, font_color='0000FF')],
                        ),
                    ]
                )
            )

        return generate_table

    # 2676 generate
    @classmethod
    async def generate_repeat_dose_tox_test(
        cls, body: Document, generate_data: BuilderTable, test_product: str, footer: str
    ) -> None:
        """
        生成 2.6.7.6 生成重复给药毒性：非关键试验的表格。
        :param body: Word文档的主体部分，用于查找和替换数据。
        :param generate_data: 一个包含试验基本信息的列表，用于生成给药毒性试验数据。
        :param test_product: str类型，供试品。
        :param footer: str类型，表格页脚。
        """
        if not generate_data or not generate_data.rows:
            return
        title_toxicology = '重复给药毒性：非关键试验'
        table_element = cls.find_title_next_table(body, title_toxicology)
        assert table_element is not None

        p_element = cls.find_title(body, title_toxicology, is_include_title=True)
        assert p_element is not None
        cls.add_run_to_element(p_element, test_product)

        if generate_data.is_error:
            return

        del generate_data.rows[0]
        await cls.generate_table(table_element, generate_data)

        # 页脚
        cls.add_footer_text(table_element, footer)

    # 2677 data
    @classmethod
    async def generate_repeat_dose_critical_test_data(
        cls,
        generate_data: TrialBaseFieldInfo,
        doc_id: int,
    ) -> list[GenerateTable]:
        """
        生成 2.6.7.7 生成重复给药毒性：关键试验的表格。
        :param generate_data: 一个包含试验基本信息的列表，用于生成给药毒性试验数据。
        :param doc_id: 文档ID，用于数据库查询或其他需要文档信息的操作。
        """
        async with get_session() as session:
            doc_data = await Doc.get_by_id(session, doc_id, columns=Col(Doc.data))

        if not doc_data:
            logging.error('重复给药关键试验文档数据错误')
            return [GenerateTable(is_error=True), GenerateTable(is_error=True), GenerateTable(is_error=True)]
        fill_datas: dict = json.loads(doc_data)

        if not fill_datas:
            return [GenerateTable(is_error=True), GenerateTable(is_error=True), GenerateTable(is_error=True)]

        table_data: GenerateTable = copy.deepcopy(table26771)

        table_data = cls.fill_table_data(
            table_data,
            {
                'species': generate_data.species,
                'dosing_regimen': generate_data.dosing_regimen,
                'glp_compliance': '是' if generate_data.glp_compliance else '否',
                'administration_method': generate_data.administration_method,
                'solvent_and_dosage_form': generate_data.solvent_and_dosage_form,
                'initial_age': fill_datas.get(GeneratedField.INITIAL_AGE, ''),
                'recovery_period': fill_datas.get(GeneratedField.RECOVERY_PERIOD, ''),
                'date_of_first_dosage': fill_datas.get(GeneratedField.DATE_OF_FIRST_DOSAGE, ''),
                'no_adverse_reaction_dosage': fill_datas.get(GeneratedField.NO_ADVERSE_REACTION_DOSAGE, ''),
            },
        )

        # 错误单元格标识
        cls.identifies_error_cell(table_data)

        if GeneratedField.TABLE_7_2 not in fill_datas or cls.ERROR_KEYWORD in fill_datas[GeneratedField.TABLE_7_2]:
            table72_generate_table: GenerateTable = GenerateTable(is_error=True)
        else:
            table72_str: str = fill_datas[GeneratedField.TABLE_7_2]
            table72_data: Table = json.loads(table72_str)
            table72_generate_table: GenerateTable = GenerateTable()
            fill_result2: bool = cls.fill_table_data_with_span(table72_data, table72_generate_table)
            if not fill_result2:
                table72_generate_table.is_error = True

            # 非第一列居中
            for row in table72_generate_table.rows:
                for col_index, col in enumerate(row.cols):
                    if col_index > 0:
                        col.align_vertical = CellVerticalAlign.CENTER
                        col.align_horizontally = CellHorizontallyAlign.CENTER

        if GeneratedField.TABLE_7_3 not in fill_datas or cls.ERROR_KEYWORD in fill_datas[GeneratedField.TABLE_7_3]:
            table73_generate_table: GenerateTable = GenerateTable(is_error=True)
        else:
            table73_str: str = fill_datas[GeneratedField.TABLE_7_3]
            table73_data: Table = json.loads(table73_str)
            table73_generate_table: GenerateTable = GenerateTable()
            fill_result3: bool = cls.fill_table_data_with_span(table73_data, table73_generate_table)
            if not fill_result3:
                table73_generate_table.is_error = True

            # 设置非第一列居中
            for row in table73_generate_table.rows:
                for col_index, col in enumerate(row.cols):
                    if col_index > 0:
                        col.align_vertical = CellVerticalAlign.CENTER
                        col.align_horizontally = CellHorizontallyAlign.CENTER

        return [table_data, table72_generate_table, table73_generate_table]

    # 2677 generate
    @classmethod
    async def generate_repeat_dose_critical_test(cls, builder_data: list[BuilderTable], body: BaseOxmlElement) -> None:
        """
        生成重复给药毒性关键试验的文档内容。
        根据提供的builder_data列表和body元素，异步生成文档中的表格和相关元素。
        参数:
        param builder_data: 包含BuilderTable对象的列表，用于生成表格。
        param body: 文档的主体元素，用于添加生成的内容。
        """
        if not builder_data:
            return

        # 只有一份数据且标记为空的情况
        if len(builder_data) == 1 and builder_data[0].is_empty:
            return

        pattern_day = re.compile(r'^\s*第[0-9]+天\s*$')
        title_toxicology = '重复给药毒性：关键试验'
        title_element: BaseOxmlElement = cls.find_title(body, title_toxicology, is_include_title=True).getnext()
        title_sec_element: BaseOxmlElement = title_element.getnext()

        async def generate_table(target_element: BaseOxmlElement, tables: list[BuilderTable]) -> BaseOxmlElement:
            # 空行
            p_empty: BaseOxmlElement = etree.Element('{%s}p' % NAME_SPACES['w'])
            # 子标题
            cls.add_run_to_element(target_element, tables[0].subheader_title, is_bold=True)

            # 上表
            table_element_above: BaseOxmlElement = etree.Element('{%s}tbl' % NAME_SPACES['w'])
            await cls.generate_table(table_element_above, cls.builder_table_to_generate_table(tables[0]))
            target_element.addnext(table_element_above)

            # 编号 & 供试品
            p_sec_element: BaseOxmlElement = copy.copy(title_sec_element)
            table_element_above.addprevious(p_sec_element)

            number_run: BaseOxmlElement = etree.Element('{%s}run' % NAME_SPACES['w'])
            cls.add_run_to_element(number_run, tables[0].trial_number, is_bold=True, color='0000FF')

            cls.add_run_to_element(p_sec_element, tables[0].test_product)

            # 找到制表符的run
            tab_list: list[BaseOxmlElement] = p_sec_element.findall('.//w:tab', namespaces=NAME_SPACES)
            if len(tab_list) > 1:
                tab_r: BaseOxmlElement = tab_list[1].getparent()
                tab_r.addprevious(number_run)

            # 添加表尾数据
            last_element: BaseOxmlElement = cls.add_footer_text(table_element_above, tables[0].footer)

            # 空行
            last_element.addnext(copy.copy(p_empty))
            last_element = last_element.getnext()

            # 下表
            if not tables[1].is_error:
                table_element_below: BaseOxmlElement = etree.Element('{%s}tbl' % NAME_SPACES['w'])
                below_data: GenerateTable = cls.builder_table_to_generate_table(tables[1])

                # 删除合并列
                cls.delete_merge_column(below_data)

                # 设置首列加粗（第X天不加粗）和非第一列居中
                for row in below_data.rows:
                    for col_index, col in enumerate(row.cols):
                        for content in col.content_line:
                            if col_index == 0 and not pattern_day.match(content.value):
                                content.is_value_bold = True
                            if col_index > 0:
                                col.align_vertical = CellVerticalAlign.CENTER
                                col.align_horizontally = CellHorizontallyAlign.CENTER

                await cls.generate_table(table_element_below, below_data)

                last_element.addnext(table_element_below)

                # 添加表尾数据
                last_element = cls.add_footer_text(table_element_below, tables[1].footer)

                # 空行
                last_element.addnext(copy.deepcopy(p_empty))
                last_element = last_element.getnext()

                # 分页
                cls.add_page_break(last_element)
                last_element = last_element.getnext()

            # 续表
            if not tables[2].is_error:
                title_text: str = tables[2].left_fixed_subheader_title + tables[2].subheader_title
                p_third_element: BaseOxmlElement = etree.Element('{%s}p' % NAME_SPACES['w'])
                cls.add_run_to_element(p_third_element, title_text, is_bold=True)

                last_element.addnext(p_third_element)

                # 表
                table_element_last: BaseOxmlElement = etree.Element('{%s}tbl' % NAME_SPACES['w'])
                last_data: GenerateTable = cls.builder_table_to_generate_table(tables[2])

                # 删除合并列
                cls.delete_merge_column(last_data)

                await cls.generate_table(table_element_last, last_data)

                p_third_element.addnext(table_element_last)

                # 编号 & 供试品
                p_sec_element: BaseOxmlElement = copy.copy(title_sec_element)
                table_element_last.addprevious(p_sec_element)

                number_run: BaseOxmlElement = etree.Element('{%s}run' % NAME_SPACES['w'])
                cls.add_run_to_element(number_run, tables[2].trial_number, is_bold=True, color='0000FF')

                cls.add_run_to_element(p_sec_element, tables[2].test_product)

                # 找到制表符的run
                tab_list: list[BaseOxmlElement] = p_sec_element.findall('.//w:tab', namespaces=NAME_SPACES)
                if len(tab_list) > 1:
                    tab_r: BaseOxmlElement = tab_list[1].getparent()
                    tab_r.addprevious(number_run)

                # 添加表尾数据
                footer_element: BaseOxmlElement = cls.add_footer_text(table_element_last, tables[2].footer)

                # 空行
                footer_element.addnext(copy.copy(p_empty))
                last_element = footer_element.getnext()

            # 分页
            cls.add_page_break(last_element)

            return last_element.getnext()

        # 构造新标题
        current_element: BaseOxmlElement = copy.deepcopy(title_element)
        title_element.addnext(current_element)
        for i in range(0, len(builder_data), 3):
            next_element = await generate_table(current_element, builder_data[i : i + 3])
            # 最后一次跳出循环
            if len(builder_data) - 3 == i:
                break
            current_new_element = copy.deepcopy(title_element)
            next_element.addnext(current_new_element)
            current_element = next_element.getnext()
        # 移除原始标题
        title_element.getparent().remove(title_element)
        title_sec_element.getparent().remove(title_sec_element)

    # 26781 above data
    @classmethod
    async def generate_genotoxicity_vivo_above_data(
        cls, generate_data: TrialBaseFieldInfo, project_id: int
    ) -> GenerateTable:
        """
        生成 2.6.8.1 遗传毒性：体外 上表数据。
        该方法是一个类方法，用于在给定的文档主体中生成关于毒理学概述的表格。
        它通过查找标题下的表格元素，并根据提供的数据填充表格。
        参数:
        :param generate_data: GenerateBaseData类型，包含生成表格所需的数据。
        :param project_id: 项目ID，用于数据库查询或其他需要项目信息的操作。
        """
        async with get_session() as session:
            docs = await Doc.get_by_project(
                session, project_id, trial_subtype=TrialSubType.AMES_TEST.value, columns=Col(Doc.data)
            )

        if len(docs) != 1:
            logging.error('回复突变文档数量错误，实际为: %s', len(docs))
            return GenerateTable(is_error=True)

        try:
            fill_datas: dict = json.loads(str(docs[0]))
        except Exception:
            logging.exception('回复突变文档数据错误')
            return GenerateTable(is_error=True)

        if not fill_datas:
            return GenerateTable(is_error=True)

        table_data: GenerateTable = table26781
        table_data = cls.fill_table_data(
            table_data,
            {
                'number_of_parallel_cultures': fill_datas.get(GeneratedField.NUMBER_OF_PARALLEL_CULTURES, ''),
                'glp_compliance': '是' if generate_data.glp_compliance else '否',
                'solvent_and_dosage_form': generate_data.solvent_and_dosage_form,
                'species': generate_data.species,
                'positive_control_sample': fill_datas.get(GeneratedField.POSITIVE_CONTROL_SAMPLE, ''),
                'metabolic_system': fill_datas.get(GeneratedField.METABOLIC_SYSTEM, ''),
                'handle_8': fill_datas.get(GeneratedField.HANDLE_8, ''),
                'cytotoxic_effects_8': fill_datas.get(GeneratedField.CYTOTOXIC_EFFECTS_8, ''),
                'genotoxic_effects_8': fill_datas.get(GeneratedField.GENOTOXIC_EFFECTS_8, ''),
            },
        )

        # 错误单元格标识
        cls.identifies_error_cell(table_data)

        return table_data

    # 26781 below data
    @classmethod
    async def generate_genotoxicity_vivo_below_data(cls, project_id: int) -> GenerateTable:
        """
        生成 2.6.8.1 遗传毒性：体外 下表数据。
        该方法是一个类方法，用于在给定的文档主体中生成关于毒理学概述的表格。
        它通过查找标题下的表格元素，并根据提供的数据填充表格。
        参数:
        :param project_id: 项目ID，用于数据库查询或其他需要项目信息的操作。
        """
        async with get_session() as session:
            docs = await Doc.get_by_project(
                session, project_id, trial_subtype=TrialSubType.AMES_TEST.value, columns=Col(Doc.data)
            )

        if len(docs) != 1:
            logging.error('回复突变文档数量错误，实际为: %s', len(docs))
            return GenerateTable(is_error=True)

        try:
            fill_datas: dict = json.loads(str(docs[0]))
        except Exception:
            logging.exception('回复突变文档数据错误')
            return GenerateTable(is_error=True)

        if not fill_datas:
            return GenerateTable(is_error=True)

        mc_generate_table: GenerateTable = GenerateTable()

        if cls.ERROR_KEYWORD in fill_datas[GeneratedField.TABLE_8_1]:
            mc_generate_table.is_error = True
            return mc_generate_table

        mc_table_data: Table = json.loads(fill_datas[GeneratedField.TABLE_8_1])

        cls.fill_table_data_with_span(mc_table_data, mc_generate_table)

        # 设置首行加粗和居中对齐
        for row_index, row in enumerate(mc_generate_table.rows):
            for col in row.cols:
                col.align_vertical = CellVerticalAlign.CENTER
                col.align_horizontally = CellHorizontallyAlign.CENTER
                for content in col.content_line:
                    if row_index == 0:
                        content.is_value_bold = True

        return mc_generate_table

    # 26781 generate
    @classmethod
    async def generate_genotoxicity_vitro(
        cls,
        body: Document,
        above_data: BuilderTable,
        below_data: BuilderTable,
        subheader_title: str,
        test_product: str,
        trial_number: str,
        footer_above: str,
        footer_below: str,
    ) -> None:
        """
        生成 2.6.8.1 遗传毒性：体外 表格。
        该方法是一个类方法，用于在给定的文档主体中生成关于毒理学概述的表格。
        它通过查找标题下的表格元素，并根据提供的数据填充表格。
        参数:
        :param body: Document类型，代表文档的主体部分。
        :param above_data: GenerateTable类型，包含生成表格所需的数据。
        :param below_data: GenerateTable类型，包含生成表格所需的数据。
        :param subheader_title: Str类型，标题数据。
        :param test_product: Str类型，供试品数据。
        :param trial_number: Str类型，编号数据。
        :param footer_above: Str类型，上表表尾数据。
        :param footer_below: Str类型，下表表尾数据。
        """
        if not above_data or not above_data.rows:
            return

        title_toxicology = '遗传毒性：体外'

        p_element: BaseOxmlElement = cls.find_title(body, title_toxicology)

        # 文档标题
        p_title: BaseOxmlElement = p_element.getnext()
        cls.add_run_to_element(p_title, subheader_title, is_bold=True)

        p_title_sec: BaseOxmlElement = p_title.getnext()
        number_run: BaseOxmlElement = etree.Element('{%s}run' % NAME_SPACES['w'])
        cls.add_run_to_element(number_run, trial_number, is_bold=True, color='0000FF')

        # 找到制表符的run
        tab_list: list[BaseOxmlElement] = p_title_sec.findall('.//w:tab', namespaces=NAME_SPACES)
        if len(tab_list) > 1:
            tab_r: BaseOxmlElement = tab_list[1].getparent()
            tab_r.addprevious(number_run)

        # 供试品
        p_element: BaseOxmlElement = cls.find_title(body, title_toxicology, is_include_title=True)
        cls.add_run_to_element(p_element.getnext().getnext(), test_product)

        table_element: BaseOxmlElement = etree.Element('{%s}tbl' % NAME_SPACES['w'])
        await cls.generate_table(table_element, above_data)
        p_title_sec.addnext(table_element)

        p_empty: BaseOxmlElement = etree.Element('{%s}p' % NAME_SPACES['w'])
        table_element.addnext(p_empty)

        # 添加表尾数据
        cls.add_footer_text(table_element, footer_above)

        # 下表
        if not below_data or below_data.is_error:
            return

        # 删除合并列
        cls.delete_merge_column(below_data)

        # 设置首行加粗和居中对齐
        for row_index, row in enumerate(below_data.rows):
            for col in row.cols:
                col.align_vertical = CellVerticalAlign.CENTER
                col.align_horizontally = CellHorizontallyAlign.CENTER
                for content in col.content_line:
                    if row_index == 0:
                        content.is_value_bold = True

        mc_table_element: BaseOxmlElement = etree.Element('{%s}tbl' % NAME_SPACES['w'])
        await cls.generate_table(mc_table_element, below_data)
        p_empty.addnext(mc_table_element)

        # 添加表尾数据
        cls.add_footer_text(mc_table_element, footer_below)

    # 26782 above data
    @classmethod
    async def generate_genotoxicity_vivo_above_data2(
        cls, generate_data: TrialBaseFieldInfo, project_id: int
    ) -> GenerateTable:
        """
        生成 2.6.8.2 遗传毒性：体外 上表数据。
        该方法是一个类方法，用于在给定的文档主体中生成关于毒理学概述的表格。
        它通过查找标题下的表格元素，并根据提供的数据填充表格。
        参数:
        :param project_id: 项目ID，用于数据库查询或其他需要项目信息的操作。
        :param generate_data: GenerateBaseData类型，包含生成表格所需的数据。
        """
        async with get_session() as session:
            docs = await Doc.get_by_project(
                session, project_id, trial_subtype=TrialSubType.CHROMOSOME_ABERRATION_TEST.value, columns=Col(Doc.data)
            )

        if len(docs) != 1:
            logging.error('染色体文档数量错误，实际为: %s', len(docs))
            return GenerateTable(is_error=True)

        try:
            fill_datas: dict = json.loads(str(docs[0]))
        except Exception:
            logging.exception('染色体文档数据错误')
            return GenerateTable(is_error=True)

        if not fill_datas:
            return GenerateTable(is_error=True)

        table_data: GenerateTable = table26782
        table_data = cls.fill_table_data(
            table_data,
            {
                'species': generate_data.species,
                'number_of_parallel_cultures_chromosome': (
                    fill_datas['number_of_parallel_cultures_chromosome']
                    if 'number_of_parallel_cultures_chromosome' in fill_datas
                    else ''
                ),
                'glp_compliance': '是' if generate_data.glp_compliance else '否',
                'metabolic_system_chromosome': fill_datas.get(GeneratedField.METABOLIC_SYSTEM_CHROMOSOME, ''),
                'analyze_cell_number_chromosome': fill_datas.get(GeneratedField.ANALYZE_CELL_NUMBER_CHROMOSOME, ''),
                'administration_date_chromosome': fill_datas.get(GeneratedField.ADMINISTRATION_DATE_CHROMOSOME, ''),
                'solvent_and_dosage_form': generate_data.solvent_and_dosage_form,
                'positive_control_sample_chromosome': fill_datas.get(
                    GeneratedField.POSITIVE_CONTROL_SAMPLE_CHROMOSOME, ''
                ),
                'handle_chromosome': fill_datas.get(GeneratedField.HANDLE_CHROMOSOME, ''),
                'toxic_effects_chromosome': fill_datas.get(GeneratedField.TOXIC_EFFECTS_CHROMOSOME, ''),
                'genotoxic_effects_chromosome': fill_datas.get(GeneratedField.GENOTOXIC_EFFECTS_CHROMOSOME, ''),
            },
        )

        # 错误单元格标识
        cls.identifies_error_cell(table_data)

        return table_data

    # 26782 below data
    @classmethod
    async def generate_genotoxicity_vivo_below_data2(cls, project_id: int) -> GenerateTable:
        """
        生成 2.6.8.2 遗传毒性：体外 下表数据。
        该方法是一个类方法，用于在给定的文档主体中生成关于毒理学概述的表格。
        它通过查找标题下的表格元素，并根据提供的数据填充表格。
        参数:
        :param param project_id: 项目ID，用于数据库查询或其他需要项目信息的操作。
        """
        async with get_session() as session:
            docs = await Doc.get_by_project(
                session, project_id, trial_subtype=TrialSubType.CHROMOSOME_ABERRATION_TEST.value, columns=Col(Doc.data)
            )

        if len(docs) != 1:
            logging.error('染色体文档数量错误，实际为: %s', len(docs))
            return GenerateTable(is_error=True)

        try:
            fill_datas: dict = json.loads(str(docs[0]))
        except Exception:
            logging.exception('染色体文档数据错误')
            return GenerateTable(is_error=True)

        if not fill_datas:
            return GenerateTable(is_error=True)

        mc_generate_table: GenerateTable = GenerateTable()

        if cls.ERROR_KEYWORD in fill_datas[GeneratedField.CH_TABLE]:
            mc_generate_table.is_error = True
            return mc_generate_table

        mc_table_data: Table = json.loads(fill_datas[GeneratedField.CH_TABLE])

        cls.fill_table_data_with_span(mc_table_data, mc_generate_table)

        # 设置首行加粗和居中对齐
        for row_index, row in enumerate(mc_generate_table.rows):
            for col in row.cols:
                col.align_vertical = CellVerticalAlign.CENTER
                col.align_horizontally = CellHorizontallyAlign.CENTER
                for content in col.content_line:
                    if row_index == 0:
                        content.is_value_bold = True

        return mc_generate_table

    # 26782 generate
    @classmethod
    async def generate_genotoxicity_vitro2(
        cls,
        body: Document,
        above_data: BuilderTable,
        below_data: BuilderTable,
        subheader_title: str,
        test_product: str,
        trial_number: str,
        footer_above: str,
        footer_below: str,
    ) -> None:
        """
        生成 2.6.8.2 遗传毒性：体外 表格。
        该方法是一个类方法，用于在给定的文档主体中生成关于毒理学概述的表格。
        它通过查找标题下的表格元素，并根据提供的数据填充表格。
        参数:
        :param body: Document类型，代表文档的主体部分。
        :param above_data: GenerateTable类型，包含生成表格所需的数据。
        :param below_data: GenerateTable类型，包含生成表格所需的数据。
        :param subheader_title: Str类型，标题数据。
        :param test_product: Str类型，供试品数据。
        :param trial_number: Str类型，编号数据。
        :param footer_above: Str类型，上表表尾数据。
        :param footer_below: Str类型，下表表尾数据。
        """
        title_toxicology = '报告标题：2.6.7.8.2'

        p_element: BaseOxmlElement = cls.find_title(body, title_toxicology)

        # 供试品
        cls.add_run_to_element(p_element.getnext(), test_product)

        # 文档标题
        p_title: BaseOxmlElement = p_element
        cls.add_run_to_element(p_title, f'报告标题：{subheader_title}', is_bold=True, is_delete=True)

        if not above_data:
            return

        p_title_sec: BaseOxmlElement = p_element.getnext()
        number_run: BaseOxmlElement = etree.Element('{%s}run' % NAME_SPACES['w'])
        cls.add_run_to_element(number_run, trial_number, is_bold=True, color='0000FF')

        # 找到制表符的run
        tab_list: list[BaseOxmlElement] = p_title_sec.findall('.//w:tab', namespaces=NAME_SPACES)
        if len(tab_list) > 1:
            tab_r: BaseOxmlElement = tab_list[1].getparent()
            tab_r.addprevious(number_run)

        table_element: BaseOxmlElement = etree.Element('{%s}tbl' % NAME_SPACES['w'])
        await cls.generate_table(table_element, above_data)
        p_title_sec.addnext(table_element)

        p_empty: BaseOxmlElement = etree.Element('{%s}p' % NAME_SPACES['w'])
        table_element.addnext(p_empty)

        # 添加表尾数据
        cls.add_footer_text(table_element, footer_above)

        # 下表
        if not below_data or below_data.is_error:
            return

        # 删除合并列
        cls.delete_merge_column(below_data)

        # 设置首行加粗和居中对齐
        for row_index, row in enumerate(below_data.rows):
            for col in row.cols:
                for content in col.content_line:
                    if row_index == 0:
                        content.is_value_bold = True

        mc_table_element: BaseOxmlElement = etree.Element('{%s}tbl' % NAME_SPACES['w'])
        await cls.generate_table(mc_table_element, below_data)
        p_empty.addnext(mc_table_element)

        # 添加表尾数据
        cls.add_footer_text(mc_table_element, footer_below)

    # 2679 above data
    @classmethod
    async def generate_micronucleus_test_above_data(
        cls, project_id: int, generate_table: TrialBaseFieldInfo
    ) -> GenerateTable:
        """
        2.6.7.9 遗传毒性：体内 - 微核 - 上表
        该方法是一个类方法，用于在给定的文档主体中生成关于新药001大鼠体内微核试验的表格。
        它通过查找标题下的表格元素，并根据提供的数据填充表格。
        参数:
        :param project_id: 项目ID，用于数据库查询或其他需要项目信息的操作。
        :param generate_table: GenerateBaseData类型，包含生成表格所需的数据。
        """
        if generate_table is None:
            return GenerateTable(is_error=True)

        async with get_session() as session:
            docs = await Doc.get_by_project(
                session, project_id, trial_subtype=TrialSubType.MICRONUCLEUS_TEST.value, columns=Col(Doc.data)
            )

        if len(docs) != 1:
            logging.error('微核文档数量错误，实际为: %s', len(docs))
            return GenerateTable(is_error=True)

        try:
            fill_datas: dict = json.loads(str(docs[0]))
        except Exception:
            logging.exception('微核文档数据错误')
            return GenerateTable(is_error=True)

        if not fill_datas:
            return GenerateTable(is_error=True)

        table_data: GenerateTable = table26791
        table_data = cls.fill_table_data(
            table_data,
            {
                'species': generate_table.species,
                'glp_compliance': '是' if generate_table.glp_compliance else '否',
                'solvent_and_dosage_form': generate_table.solvent_and_dosage_form,
                'sampling_time': fill_datas.get(GeneratedField.SAMPLING_TIME, ''),
                'age': fill_datas.get(GeneratedField.AGE, ''),
                'administration_method_9': fill_datas.get(GeneratedField.ADMINISTRATION_METHOD_9, ''),
                'administration_date': fill_datas.get(GeneratedField.ADMINISTRATION_DATE, ''),
                'evaluate_cells': fill_datas.get(GeneratedField.EVALUATE_CELLS, ''),
                'cell_number': fill_datas.get(GeneratedField.CELL_NUMBER, ''),
                'toxic_effects': fill_datas.get(GeneratedField.TOXIC_EFFECTS, ''),
                'genotoxic_effects_9': fill_datas.get(GeneratedField.GENOTOXIC_EFFECTS_9, ''),
            },
        )

        # 错误单元格标识
        cls.identifies_error_cell(table_data)

        return table_data

    # 2679 below data
    @classmethod
    async def generate_micronucleus_test_below_data(cls, project_id: int) -> GenerateTable:
        """
        2.6.7.9 遗传毒性：体内 - 微核 - 下表
        该方法是一个类方法，用于在给定的文档主体中生成关于新药001大鼠体内微核试验的表格。
        它通过查找标题下的表格元素，并根据提供的数据填充表格。
        参数:
        :param project_id: 项目ID，用于数据库查询或其他需要项目信息的操作。
        """
        async with get_session() as session:
            docs = await Doc.get_by_project(session, project_id, trial_subtype=6, columns=Col(Doc.data))

        if len(docs) != 1:
            logging.error('微核文档数量错误，实际为: %s', len(docs))
            return GenerateTable(is_error=True)

        try:
            fill_datas: dict = json.loads(str(docs[0]))
        except Exception:
            logging.exception('微核文档数据错误')
            return GenerateTable(is_error=True)

        if not fill_datas:
            return GenerateTable(is_error=True)

        mc_generate_table: GenerateTable = GenerateTable()

        if cls.ERROR_KEYWORD in mc_generate_table:
            mc_generate_table.is_error = True
            return mc_generate_table

        mc_table_data: Table = json.loads(fill_datas[GeneratedField.MC_TABLE])
        cls.fill_table_data_with_span(mc_table_data, mc_generate_table)

        # 设置首行加粗和居中对齐
        for row_index, row in enumerate(mc_generate_table.rows):
            for col_index, col in enumerate(row.cols):
                col.align_vertical = CellVerticalAlign.CENTER
                col.align_horizontally = CellHorizontallyAlign.CENTER
                # 移除非表头合并
                if row_index != 0 and col_index > 1:
                    col.col_span_count = 1
                    col.row_span_count = 1
                    col.row_span_first_row = False
                    col.row_span = False
                for content in col.content_line:
                    if row_index == 0:
                        content.is_value_bold = True

        return mc_generate_table

    # 2679 generate
    @classmethod
    async def generate_micronucleus_test(
        cls,
        body: Document,
        above_data: BuilderTable,
        below_data: BuilderTable,
        subheader_title: str,
        test_product: str,
        trial_number: str,
        footer_above: str = '',
        footer_below: str = '',
    ) -> None:
        """
        2.6.7.9 遗传毒性：体内 - 微核 表格
        该方法是一个类方法，用于在给定的文档主体中生成关于新药001大鼠体内微核试验的表格。
        它通过查找标题下的表格元素，并根据提供的数据填充表格。
        参数:
        :param body 代表文档的主体部分。
        :param above_data 包含生成表格所需的数据。
        :param below_data 包含生成表格所需的数据。
        :param subheader_title 标题数据。
        :param test_product 供试品数据。
        :param trial_number 编号数据。
        :param footer_above 上表表尾数据。
        :param footer_below 下表表尾数据。
        """
        # 上表
        title_toxicology = '遗传毒性：体内'

        p_element: BaseOxmlElement = cls.find_title(body, title_toxicology)
        p_title: BaseOxmlElement = p_element.getnext()
        cls.add_run_to_element(p_title, subheader_title, is_bold=True)

        p_title_sec: BaseOxmlElement = p_title.getnext()
        number_run: BaseOxmlElement = etree.Element('{%s}run' % NAME_SPACES['w'])
        cls.add_run_to_element(number_run, trial_number, is_bold=True, color='0000FF')

        # 供试品
        cls.add_run_to_element(p_title_sec, test_product)

        # 找到制表符的run
        tab_list: list[BaseOxmlElement] = p_title_sec.findall('.//w:tab', namespaces=NAME_SPACES)
        if len(tab_list) > 1:
            tab_r: BaseOxmlElement = tab_list[1].getparent()
            tab_r.addprevious(number_run)

        table_element: BaseOxmlElement = etree.Element('{%s}tbl' % NAME_SPACES['w'])
        await cls.generate_table(table_element, above_data)
        p_title_sec.addnext(table_element)

        p_empty: BaseOxmlElement = etree.Element('{%s}p' % NAME_SPACES['w'])
        table_element.addnext(p_empty)

        # 添加表尾数据
        cls.add_footer_text(table_element, footer_above)

        # 下表
        if not below_data or below_data.is_error:
            return

        # 删除合并列
        cls.delete_merge_column(below_data)

        # 设置首行加粗和居中对齐
        for row_index, row in enumerate(below_data.rows):
            for col_index, col in enumerate(row.cols):
                col.align_vertical = CellVerticalAlign.CENTER
                col.align_horizontally = CellHorizontallyAlign.CENTER
                # 移除非表头合并
                if row_index != 0 and col_index > 1:
                    col.col_span_count = 1
                    col.row_span_count = 1
                    col.row_span_first_row = False
                    col.row_span = False
                for content in col.content_line:
                    if row_index == 0:
                        content.is_value_bold = True

        mc_table_element: BaseOxmlElement = etree.Element('{%s}tbl' % NAME_SPACES['w'])
        await cls.generate_table(mc_table_element, below_data)
        p_empty.addnext(mc_table_element)

        # 添加表尾数据
        cls.add_footer_text(mc_table_element, footer_below)

    @classmethod
    async def generate_table(cls, table_element: BaseOxmlElement, generate_table: GenerateTable) -> None:
        """
        生成表格。

        此方法用于根据GenerateTable实例异步生成表格。它主要负责检查输入的有效性，
        以及通过调用自身的方法来组装表格的单元格。

        参数:
        :param table_element，表格对象XML实例。
        :param generate_table，包含表格的结构信息，如行和列。
        """
        table_width: str = '5000'

        if generate_table is None or generate_table.rows is None or len(generate_table.rows) == 0:
            return

        tbl_pr: BaseOxmlElement = table_element.find('.//w:tblPr', namespaces=NAME_SPACES)
        if tbl_pr is None:
            tbl_pr = etree.SubElement(table_element, '{%s}tblPr' % NAME_SPACES['w'])
        tbl_w: BaseOxmlElement = tbl_pr.find('.//w:tblW', namespaces=NAME_SPACES)
        if tbl_w is None:
            tbl_w = etree.SubElement(tbl_pr, '{%s}tblW' % NAME_SPACES['w'])
        tbl_w.set(f'{{{NAME_SPACES["w"]}}}w', table_width)
        tbl_w.set(f'{{{NAME_SPACES["w"]}}}type', 'pct')

        cls.set_table_border(tbl_pr)

        for index, row in enumerate(generate_table.rows):
            tr: BaseOxmlElement = etree.Element('{%s}tr' % NAME_SPACES['w'])
            for col in row.cols:
                cls.generate_tc(tr, col)

            tr_list: list[BaseOxmlElement] = table_element.findall('.//w:tr', namespaces=NAME_SPACES)
            if generate_table.pass_before_row != -1 and len(tr_list) > 0:
                tr_current_list: list[BaseOxmlElement] = table_element.findall('.//w:tr', namespaces=NAME_SPACES)
                if index == 0:
                    tr_last: BaseOxmlElement = tr_current_list[generate_table.pass_before_row]
                else:
                    tr_last: BaseOxmlElement = tr_current_list[-1]
                tr_last.addnext(tr)
            else:
                table_element.append(tr)

    @classmethod
    def generate_tc(cls, tr: BaseOxmlElement, col: GenerateCol) -> BaseOxmlElement:
        """
        向给定的表格行中添加一个单元格。
        :param tr: 表格行的OXML元素，类型为BaseOxmlElement。
        :param col: 单元格数据。
        :return: 创建的表格单元格（tc）的OXML元素。
        """
        lang: str = 'zh-CN'
        asc_ii: str = 'Times New Roman'
        east_asia: str = '宋体'
        tc: BaseOxmlElement = etree.SubElement(tr, '{%s}tc' % NAME_SPACES['w'])
        tc_pr: BaseOxmlElement = etree.SubElement(tc, '{%s}tcPr' % NAME_SPACES['w'])
        v_align: BaseOxmlElement = etree.SubElement(tc_pr, '{%s}vAlign' % NAME_SPACES['w'])
        v_align.set(f'{{{NAME_SPACES["w"]}}}val', col.align_vertical.value)

        def set_r_style(
            content_data: GenerateContent,
            p_element: BaseOxmlElement,
            p_pr_element: BaseOxmlElement,
            content_type: Literal['title', 'value'],
        ) -> BaseOxmlElement:
            """
            设置样式。
            参数:
            :param content_data 包含要生成的内容的属性，如字体大小和颜色。
            :param p_element P元素XML实例对象。
            :param p_pr_element P_PR元素XML实例对象。
            :param content_type 内容类型（标题区域/单元格正文区域）。
            返回:
            :return BaseOxmlElement 类型的对象，表示新创建的段落样式元素。
            """
            r: BaseOxmlElement = etree.SubElement(p_element, '{%s}r' % NAME_SPACES['w'])
            r_pr: BaseOxmlElement = etree.SubElement(r, '{%s}rPr' % NAME_SPACES['w'])
            r_sz: BaseOxmlElement = etree.SubElement(r_pr, '{%s}sz' % NAME_SPACES['w'])
            r_sz_cs: BaseOxmlElement = etree.SubElement(r_pr, '{%s}szCs' % NAME_SPACES['w'])
            r_lang: BaseOxmlElement = etree.SubElement(r_pr, '{%s}lang' % NAME_SPACES['w'])
            r_fonts: BaseOxmlElement = etree.SubElement(r_pr, '{%s}rFonts' % NAME_SPACES['w'])
            r_sz.set(f'{{{NAME_SPACES["w"]}}}val', str(content_data.font_size))
            r_sz_cs.set(f'{{{NAME_SPACES["w"]}}}val', str(content_data.font_size))
            r_lang.set(f'{{{NAME_SPACES["w"]}}}eastAsia', lang)
            r_fonts.set(f'{{{NAME_SPACES["w"]}}}ascii', asc_ii)
            r_fonts.set(f'{{{NAME_SPACES["w"]}}}hAnsi', asc_ii)
            r_fonts.set(f'{{{NAME_SPACES["w"]}}}cs', asc_ii)
            r_fonts.set(f'{{{NAME_SPACES["w"]}}}eastAsia', east_asia)

            # 颜色应用于整个段落
            if content_data.font_color is not None:
                # p
                p_color_r: BaseOxmlElement = etree.SubElement(p_pr_element, '{%s}color' % NAME_SPACES['w'])
                p_color_r.set(f'{{{NAME_SPACES["w"]}}}val', content_data.font_color)
                # r
                color_r: BaseOxmlElement = etree.SubElement(r_pr, '{%s}color' % NAME_SPACES['w'])
                color_r.set(f'{{{NAME_SPACES["w"]}}}val', content_data.font_color)

            if (content_type == 'title' and content_data.is_title_bold) or (
                content_type == 'value' and content_data.is_value_bold
            ):
                b_element = etree.SubElement(r_pr, '{%s}b' % NAME_SPACES['w'])
                b_element.set(f'{{{NAME_SPACES["w"]}}}val', '1')

            return r

        def set_text(r_element: BaseOxmlElement, text: str, is_title: bool = False) -> None:
            """
            设置文本内容并居中显示。
            创建一个新的 `<w:t>` 元素，将其添加到提供的父元素 `r` 中，并设置文本内容为 `content_data` 中的标题和值。
            同时，设置段落的水平和垂直对齐方式均为居中。
            参数:
            :param r_element RUN元素XML实例对象。
            :param text 包含要设置的文本内容（标题和值）。
            :param is_title 是否为标题。
            """
            t: BaseOxmlElement = etree.SubElement(r_element, '{%s}t' % NAME_SPACES['w'])
            # 遇到错误值未纠正时，默认为空
            if not is_title and col.is_error:
                t.text = ''
            else:
                t.text = text if text is not None else ''

        def set_tiny_run(
            content_data: GenerateContent, p_element: BaseOxmlElement, content_type: Literal['sup', 'sub']
        ) -> None:
            # 前置字符
            before_r: BaseOxmlElement = etree.SubElement(p_element, '{%s}r' % NAME_SPACES['w'])
            before_r_pr: BaseOxmlElement = etree.SubElement(before_r, '{%s}rPr' % NAME_SPACES['w'])
            before_r_sz: BaseOxmlElement = etree.SubElement(before_r_pr, '{%s}sz' % NAME_SPACES['w'])
            before_r_sz_cs: BaseOxmlElement = etree.SubElement(before_r_pr, '{%s}szCs' % NAME_SPACES['w'])
            before_r_lang: BaseOxmlElement = etree.SubElement(before_r_pr, '{%s}lang' % NAME_SPACES['w'])
            before_r_fonts: BaseOxmlElement = etree.SubElement(before_r_pr, '{%s}rFonts' % NAME_SPACES['w'])
            before_r_sz.set(f'{{{NAME_SPACES["w"]}}}val', str(content_data.font_size))
            before_r_sz_cs.set(f'{{{NAME_SPACES["w"]}}}val', str(content_data.font_size))
            before_r_lang.set(f'{{{NAME_SPACES["w"]}}}eastAsia', lang)
            before_r_fonts.set(f'{{{NAME_SPACES["w"]}}}ascii', asc_ii)
            before_r_fonts.set(f'{{{NAME_SPACES["w"]}}}hAnsi', asc_ii)
            before_r_fonts.set(f'{{{NAME_SPACES["w"]}}}cs', asc_ii)
            before_r_fonts.set(f'{{{NAME_SPACES["w"]}}}eastAsia', east_asia)

            # 小号字符
            r: BaseOxmlElement = etree.SubElement(p_element, '{%s}r' % NAME_SPACES['w'])
            r_pr: BaseOxmlElement = etree.SubElement(r, '{%s}rPr' % NAME_SPACES['w'])
            r_sz: BaseOxmlElement = etree.SubElement(r_pr, '{%s}sz' % NAME_SPACES['w'])
            r_sz_cs: BaseOxmlElement = etree.SubElement(r_pr, '{%s}szCs' % NAME_SPACES['w'])
            r_lang: BaseOxmlElement = etree.SubElement(r_pr, '{%s}lang' % NAME_SPACES['w'])
            r_fonts: BaseOxmlElement = etree.SubElement(r_pr, '{%s}rFonts' % NAME_SPACES['w'])
            vert_align: BaseOxmlElement = etree.SubElement(r_pr, '{%s}vertAlign' % NAME_SPACES['w'])
            r_sz.set(f'{{{NAME_SPACES["w"]}}}val', str(content_data.font_size))
            r_sz_cs.set(f'{{{NAME_SPACES["w"]}}}val', str(content_data.font_size))
            r_lang.set(f'{{{NAME_SPACES["w"]}}}eastAsia', 'zh-CN')
            r_fonts.set(f'{{{NAME_SPACES["w"]}}}eastAsiaTheme', 'minorEastAsia')
            vert_align.set(f'{{{NAME_SPACES["w"]}}}val', 'subscript' if content_type == 'sub' else 'superscript')

            if content_data.font_color is not None:
                color_r: BaseOxmlElement = etree.SubElement(r_pr, '{%s}color' % NAME_SPACES['w'])
                before_color_r: BaseOxmlElement = etree.SubElement(before_r_pr, '{%s}color' % NAME_SPACES['w'])
                color_r.set(f'{{{NAME_SPACES["w"]}}}val', content_data.font_color)
                before_color_r.set(f'{{{NAME_SPACES["w"]}}}val', content_data.font_color)

            if content_data.is_value_bold:
                b_element = etree.SubElement(r_pr, '{%s}b' % NAME_SPACES['w'])
                before_b_element = etree.SubElement(before_r_pr, '{%s}b' % NAME_SPACES['w'])
                b_element.set(f'{{{NAME_SPACES["w"]}}}val', '1')
                before_b_element.set(f'{{{NAME_SPACES["w"]}}}val', '1')

            t: BaseOxmlElement = etree.SubElement(r, '{%s}t' % NAME_SPACES['w'])
            before_t: BaseOxmlElement = etree.SubElement(before_r, '{%s}t' % NAME_SPACES['w'])

            sub_key: str = content_data.sub_key

            for item in cls.SUB_SIGN if content_type == 'sub' else cls.SUP_SIGN:
                for key in list(item.keys()):
                    if key == sub_key:
                        t.text = item[key][0]
                        before_t.text = item[key][1]

        for content in col.content_line:
            p: BaseOxmlElement = etree.SubElement(tc, '{%s}p' % NAME_SPACES['w'])
            p_pr: BaseOxmlElement = etree.SubElement(p, '{%s}pPr' % NAME_SPACES['w'])
            jc: BaseOxmlElement = etree.SubElement(p_pr, '{%s}jc' % NAME_SPACES['w'])
            jc.set(f'{{{NAME_SPACES["w"]}}}val', col.align_horizontally.value)

            # font - p
            p_r_pr: BaseOxmlElement = etree.SubElement(p_pr, '{%s}rPr' % NAME_SPACES['w'])
            p_sz: BaseOxmlElement = etree.SubElement(p_r_pr, '{%s}sz' % NAME_SPACES['w'])
            p_sz_cs: BaseOxmlElement = etree.SubElement(p_r_pr, '{%s}szCs' % NAME_SPACES['w'])
            p_lang: BaseOxmlElement = etree.SubElement(p_r_pr, '{%s}lang' % NAME_SPACES['w'])
            p_fonts: BaseOxmlElement = etree.SubElement(p_r_pr, '{%s}rFonts' % NAME_SPACES['w'])
            p_sz.set(f'{{{NAME_SPACES["w"]}}}val', str(content.font_size))
            p_sz_cs.set(f'{{{NAME_SPACES["w"]}}}val', str(content.font_size))
            p_lang.set(f'{{{NAME_SPACES["w"]}}}eastAsia', lang)
            p_fonts.set(f'{{{NAME_SPACES["w"]}}}ascii', asc_ii)
            p_fonts.set(f'{{{NAME_SPACES["w"]}}}hAnsi', asc_ii)
            p_fonts.set(f'{{{NAME_SPACES["w"]}}}cs', asc_ii)
            p_fonts.set(f'{{{NAME_SPACES["w"]}}}eastAsia', east_asia)

            # title
            title_r: BaseOxmlElement = set_r_style(content, p, p_pr, 'title')
            set_text(title_r, content.title, is_title=True)

            # tiny value sup
            if content.sub_key is not None:
                set_tiny_run(content, p, 'sup')

            # tiny value sub
            if content.sub_key is not None:
                set_tiny_run(content, p, 'sub')

            # value
            value_r: BaseOxmlElement = set_r_style(content, p, p_pr, 'value')
            set_text(value_r, content.value, is_title=False)

        if col.row_span:
            v_merger: BaseOxmlElement = etree.SubElement(tc_pr, '{%s}vMerge' % NAME_SPACES['w'])
            if col.row_span_first_row:
                v_merger.set(f'{{{NAME_SPACES["w"]}}}val', 'restart')

        if col.col_span_count > 1:
            grid_span: BaseOxmlElement = etree.SubElement(tc_pr, '{%s}gridSpan' % NAME_SPACES['w'])
            grid_span.set(f'{{{NAME_SPACES["w"]}}}val', str(col.col_span_count))

        return tc

    @classmethod
    def set_table_border(cls, tbl_pr: BaseOxmlElement) -> None:
        """
        设置表格的边框样式。
        参数:
        :param tbl_pr: 表格的属性元素，用于添加边框样式。
        """
        tbl_borders: BaseOxmlElement = etree.SubElement(tbl_pr, '{%s}tblBorders' % NAME_SPACES['w'])
        top: BaseOxmlElement = etree.SubElement(tbl_borders, '{%s}top' % NAME_SPACES['w'])
        bottom: BaseOxmlElement = etree.SubElement(tbl_borders, '{%s}bottom' % NAME_SPACES['w'])
        left: BaseOxmlElement = etree.SubElement(tbl_borders, '{%s}left' % NAME_SPACES['w'])
        right: BaseOxmlElement = etree.SubElement(tbl_borders, '{%s}right' % NAME_SPACES['w'])
        inside_h: BaseOxmlElement = etree.SubElement(tbl_borders, '{%s}insideH' % NAME_SPACES['w'])
        inside_v: BaseOxmlElement = etree.SubElement(tbl_borders, '{%s}insideV' % NAME_SPACES['w'])

        w_val: str = f'{{{NAME_SPACES["w"]}}}val'
        w_space: str = f'{{{NAME_SPACES["w"]}}}space'
        w_color: str = f'{{{NAME_SPACES["w"]}}}color'

        top.set(w_val, 'single')
        bottom.set(w_val, 'single')
        left.set(w_val, 'single')
        right.set(w_val, 'single')
        inside_h.set(w_val, 'single')
        inside_v.set(w_val, 'single')

        top.set(w_space, '0')
        bottom.set(w_space, '0')
        left.set(w_space, '0')
        right.set(w_space, '0')
        inside_h.set(w_space, '0')
        inside_v.set(w_space, '0')

        top.set(w_color, 'auto')
        bottom.set(w_color, 'auto')
        left.set(w_color, 'auto')
        right.set(w_color, 'auto')
        inside_h.set(w_color, 'auto')
        inside_v.set(w_color, 'auto')

    @classmethod
    def find_title_next_table(cls, body: BaseOxmlElement, title: str) -> BaseOxmlElement | None:
        """
        在给定的文档主体中查找指定标题后的第一个表格。
        :param body: 文档的主体部分，类型为BaseOxmlElement
        :param title: 要查找的标题文本
        :return: 如果找到标题后的表格，则返回表格元素；否则返回None
        """
        title_p: BaseOxmlElement | None = None

        for element in body:
            if WordType.get_type(element) == ElementType.PARAGRAPH:
                p_text: str = get_text_from_element(element)
                if title in p_text.strip():
                    title_p = element

        if title_p is None:
            return None

        next_element: BaseOxmlElement = title_p.getnext()
        if next_element is not None and WordType.get_type(next_element) == ElementType.TABLE:
            return next_element
        return None

    @classmethod
    def find_title(
        cls, body: BaseOxmlElement, title: str, is_strip: bool = True, is_include_title: bool = False
    ) -> BaseOxmlElement | None:
        """
        在给定的文档主体中查找指定标题的元素。

        遍历文档主体的每个元素，寻找类型为段落的元素，并检查其文本内容是否与指定的标题相符。
        如果找到匹配的元素，则返回该元素；如果未找到，则返回None。

        :param body: BaseOxmlElement类型，代表文档的主体部分
        :param title: 字符串类型，代表要查找的标题文本
        :param is_strip: 是否去掉首尾空格
        :param is_include_title: 是否查询包含标题的情况
        :return: 如果找到匹配的标题元素，则返回BaseOxmlElement类型元素；否则返回None
        """
        # 遍历文档主体中的每个元素
        for element in body:
            if WordType.get_type(element) != ElementType.PARAGRAPH:
                continue

            # 获取段落文本
            text = get_text_from_element(element)

            # 根据是否去掉空白处理文本
            if is_strip:
                text = text.strip()

            # 判断是否是包含标题的情况
            if is_include_title:
                if title in text:
                    return element
            else:
                if title == text:
                    return element

        return None

    @classmethod
    def include_title(cls, body: BaseOxmlElement, title: str) -> BaseOxmlElement | None:
        """
        在给定的文档主体中查找指定标题的元素。

        遍历文档主体的每个元素，寻找类型为段落的元素，并检查其文本内容是否与指定的标题包含。
        如果找到匹配的元素，则返回该元素；如果未找到，则返回None。

        :param body: BaseOxmlElement类型，代表文档的主体部分
        :param title: 字符串类型，代表要查找的标题文本
        :return: 如果找到匹配的标题元素，则返回BaseOxmlElement类型元素；否则返回None
        """
        for element in body:
            if WordType.get_type(element) == ElementType.PARAGRAPH:
                if title in get_text_from_element(element):
                    return element
        return None

    @classmethod
    def fill_table_data(cls, table: GenerateTable, data: dict) -> GenerateTable:
        """
        根据传入的 data，将值填充到 table 的对应位置。

        :param table: GenerateTable 配置对象
        :param data: 数据对象 {key: value}
        :return: 填充完成的 GenerateTable
        """
        for row in table.rows:
            for col in row.cols:
                for content in col.content_line:
                    if content.key in data:
                        content.value = data[content.key]
        return table

    @classmethod
    def fill_table_data_with_span(cls, table_data: Table, generate_table: GenerateTable) -> bool:
        """
        根据给定的表格数据和生成表格的对象，填充表格数据并处理跨行跨列。
        参数:
        :param table_data: 二维列表，包含表格数据。
        :param generate_table: GenerateTable对象，用于生成表格。
        返回: 无
        """

        # 移除非法的空数组数据
        table_data = [sublist for sublist in table_data if sublist]

        # 行数为空的数组则不处理
        if not len(table_data):
            return False

        # 每行的列数不一致的表格则不处理
        expected_column_first = len(table_data[0])
        # 遍历每一行，检查是否所有行的列数一致
        for row in table_data:
            if len(row) != expected_column_first:
                # 如果有任何一行的列数不一致，返回False
                return False

        for row_index, row_data in enumerate(table_data):
            generate_row = GenerateRow(cols=[])
            for col_index, cell_data in enumerate(row_data):
                generate_col = GenerateCol(content_line=[])
                text_list = str(cell_data).split('\n') if cell_data else []
                for text in text_list:
                    generate_col.content_line.append(GenerateContent(value=text))
                generate_row.cols.append(generate_col)
            generate_table.rows.append(generate_row)

        # 合并行
        for current_col_index, col in enumerate(generate_table.rows[0].cols):
            current_value: str = ''
            start_merge_row_index: int = -1
            row_merge_count: int = 0
            for row_index, row in enumerate(generate_table.rows):
                col = row.cols[current_col_index]
                content_value: str = ''
                for content in col.content_line:
                    content_value += content.value
                # 不做合并处理
                if (
                    not content_value
                    or bool(cls.SKIP_VALUES_PATTERN.match(content_value))
                    or bool(cls.SKIP_VALUES_NUMBER_PATTERN.match(content_value))
                ):
                    # 前置的合并数据
                    if row_merge_count > 1:
                        col_data = generate_table.rows[start_merge_row_index].cols[current_col_index]
                        col_data.row_span_count = row_merge_count
                        col_data.row_span_first_row = True
                        col_data.row_span = True
                    # 清空当前数据
                    current_value = ''
                    start_merge_row_index = -1
                    row_merge_count = 0
                    continue
                # 第一个非空数值项
                if not current_value:
                    current_value = content_value
                    start_merge_row_index = row_index
                    row_merge_count = 1
                    continue
                # 遇到不同的值，结束合并
                if current_value != content_value:
                    # 只有一个单元格，直接忽略
                    if row_merge_count == 1:
                        current_value = content_value
                        start_merge_row_index = row_index
                    else:
                        col_data = generate_table.rows[start_merge_row_index].cols[current_col_index]
                        col_data.row_span_count = row_merge_count
                        col_data.row_span_first_row = True
                        col_data.row_span = True
                        # 清空当前数据
                        current_value = content_value
                        start_merge_row_index = row_index
                        row_merge_count = 1
                    continue
                # 合并
                if current_value == content_value:
                    row_merge_count += 1
                    col.row_span = True
                    col.row_span_count = 0
            if row_merge_count > 1:
                col_data = generate_table.rows[start_merge_row_index].cols[current_col_index]
                col_data.row_span_count = row_merge_count
                col_data.row_span_first_row = True
                col_data.row_span = True

        # 合并列
        for row_index, row in enumerate(generate_table.rows):
            current_value: str = ''
            start_merge_col_index: int = -1
            col_merge_count: int = 0
            for col_index, col in enumerate(row.cols):
                content_value: str = ''
                for content in col.content_line:
                    content_value += content.value
                # 不做合并处理
                if (
                    not content_value
                    or bool(cls.SKIP_VALUES_PATTERN.match(content_value))
                    or bool(cls.SKIP_VALUES_NUMBER_PATTERN.match(content_value))
                ):
                    # 前置的合并数据
                    if col_merge_count > 1:
                        col_data = row.cols[start_merge_col_index]
                        col_data.col_span_count = col_merge_count
                        col_data.col_span = True
                    # 清空当前数据
                    current_value = ''
                    start_merge_col_index = -1
                    col_merge_count = 0
                    continue
                # 第一个非空数值项
                if not current_value:
                    current_value = content_value
                    start_merge_col_index = col_index
                    col_merge_count = 1
                    continue
                # 遇到不同的值，结束合并
                if current_value != content_value:
                    # 只有一个单元格，直接忽略
                    if col_merge_count == 1:
                        current_value = content_value
                        start_merge_col_index = col_index
                    else:
                        col_data = row.cols[start_merge_col_index]
                        col_data.col_span_count = col_merge_count
                        col_data.col_span = True
                        # 清空当前数据
                        current_value = content_value
                        start_merge_col_index = col_index
                        col_merge_count = 1
                    continue
                # 合并
                if current_value == content_value:
                    col_merge_count += 1
                    col.col_span = True
                    col.col_span_count = 0
            if col_merge_count > 1:
                col_data = row.cols[start_merge_col_index]
                col_data.col_span_count = col_merge_count
                col_data.col_span = True

        # 删除生成表中的合并列移动至生成过程中执行，以保证发给前端的合并逻辑不会被影响，该逻辑后置。

        return True

    @classmethod
    def delete_merge_column(cls, generate_table: GenerateTable) -> None:
        """
        删除生成表中的合并列。
        遍历生成表的每一行，然后逆序遍历每一行的列。如果某列的合并列数为0，
        则将该列从当前行中删除。逆序遍历是为了确保在删除列时不会跳过后续的列。
        :param generate_table: 需要删除合并列的生成表对象。
        :type generate_table: GenerateTable
        :return: 无返回值。
        :rtype: None
        """
        for row in generate_table.rows:
            for col in reversed(row.cols):
                if not col.col_span_count:
                    row.cols.remove(col)

    @classmethod
    def add_run_to_element(
        cls,
        element: BaseOxmlElement,
        text: str,
        is_bold: bool = False,
        color: str | None = None,
        is_delete: bool = False,
    ) -> None:
        """
        向给定的元素中添加一个新的文本运行，并设置文本内容和是否加粗。

        参数:
        :param  element: BaseOxmlElement - 段落元素，新的文本运行将添加到该元素中。
        :param  text: str - 要添加到新运行中的文本内容。
        :param  color: str - 颜色。
        :param  is_bold: bool (默认为False) - 指定文本是否应设置为加粗。
        :param  is_delete: bool (默认为False) - 是否需要清空当前元素的文本。

        返回: None
        """
        if is_delete:
            for run in element.findall('.//w:r', namespaces=NAME_SPACES):
                run.getparent().remove(run)

        lang: str = 'zh-CN'
        asc_ii: str = 'Times New Roman'
        east_asia: str = '宋体'

        new_run = etree.SubElement(element, '{%s}r' % NAME_SPACES['w'])
        r_pr = etree.SubElement(new_run, '{%s}rPr' % NAME_SPACES['w'])
        r_lang: BaseOxmlElement = etree.SubElement(r_pr, '{%s}lang' % NAME_SPACES['w'])
        r_fonts: BaseOxmlElement = etree.SubElement(r_pr, '{%s}rFonts' % NAME_SPACES['w'])
        r_lang.set(f'{{{NAME_SPACES["w"]}}}eastAsia', lang)
        r_fonts.set(f'{{{NAME_SPACES["w"]}}}ascii', asc_ii)
        r_fonts.set(f'{{{NAME_SPACES["w"]}}}hAnsi', asc_ii)
        r_fonts.set(f'{{{NAME_SPACES["w"]}}}cs', asc_ii)
        r_fonts.set(f'{{{NAME_SPACES["w"]}}}eastAsia', east_asia)

        if is_bold:
            b_element = etree.SubElement(r_pr, '{%s}b' % NAME_SPACES['w'])
            b_element.set(f'{{{NAME_SPACES["w"]}}}val', '1')

        if color is not None:
            color_r: BaseOxmlElement = etree.SubElement(r_pr, '{%s}color' % NAME_SPACES['w'])
            color_r.set(f'{{{NAME_SPACES["w"]}}}val', color)

        t_element = etree.SubElement(new_run, '{%s}t' % NAME_SPACES['w'])
        t_element.text = text

    @classmethod
    def insert_table_after_element(cls, element: BaseOxmlElement, target_element: BaseOxmlElement):
        """
        将表格插入到指定元素的下一个位置。

        :param element: 原始元素 (BaseOxmlElement)
        :param target_element: 被插入元素 (BaseOxmlElement)
        """
        parent = element.getparent()

        if parent is None:
            raise RuntimeError('文档解析错误，无法找到指定位置元素的父元素')

        index = parent.index(element)
        parent.insert(index + 2, target_element)

    @classmethod
    def format_vert_align_text(cls, data: GenerateTable):
        """
        格式化垂直对齐文本。
        本方法用于处理表格中可能需要垂直对齐的文本内容。通过检查每个单元格的内容，
        并根据特定的替换规则（SUB_SIGN），将内容分割为下标和剩余部分，以实现垂直对齐的视觉效果。
        参数:
        :param  data: GenerateTable 类的实例，包含了表格的相关数据，如行、列等信息。
        """
        for row in data.rows:
            for col in row.cols:
                for content in col.content_line:
                    for item in cls.SUB_SIGN:
                        for key in list(item.keys()):
                            if key in content.value:
                                start_index: int | None = content.value.find(key)
                                if start_index != -1:
                                    content.value = content.value[start_index + len(key) :]
                                    content.sub_key = key

    @classmethod
    def add_footer_text(cls, table_element: BaseOxmlElement, footer_text: str) -> BaseOxmlElement:
        """
        为给定的表格元素添加页脚文本。
        :param table_element: 要添加页脚文本的表格元素。
        :param footer_text: 要添加的页脚文本，可以包含换行符以表示多行文本。
        :return: 无
        """
        current_element: BaseOxmlElement = table_element
        font_size: str = '18'

        if not footer_text:
            return current_element

        lang: str = 'zh-CN'
        asc_ii: str = 'Times New Roman'
        east_asia: str = '宋体'

        for text in footer_text.split('\n'):
            # P
            p: BaseOxmlElement = etree.Element('{%s}p' % NAME_SPACES['w'])
            # PR
            p_pr: BaseOxmlElement = etree.SubElement(p, '{%s}pPr' % NAME_SPACES['w'])
            p_r_pr: BaseOxmlElement = etree.SubElement(p_pr, '{%s}rPr' % NAME_SPACES['w'])
            p_sz: BaseOxmlElement = etree.SubElement(p_r_pr, '{%s}sz' % NAME_SPACES['w'])
            p_sz_cs: BaseOxmlElement = etree.SubElement(p_r_pr, '{%s}szCs' % NAME_SPACES['w'])
            p_sz.set(f'{{{NAME_SPACES["w"]}}}val', font_size)
            p_sz_cs.set(f'{{{NAME_SPACES["w"]}}}val', font_size)
            # RUN
            r: BaseOxmlElement = etree.SubElement(p, '{%s}r' % NAME_SPACES['w'])
            # RUN RP
            r_pr: BaseOxmlElement = etree.SubElement(r, '{%s}rPr' % NAME_SPACES['w'])
            sz: BaseOxmlElement = etree.SubElement(r_pr, '{%s}sz' % NAME_SPACES['w'])
            sz_cs: BaseOxmlElement = etree.SubElement(r_pr, '{%s}szCs' % NAME_SPACES['w'])
            sz.set(f'{{{NAME_SPACES["w"]}}}val', font_size)
            sz_cs.set(f'{{{NAME_SPACES["w"]}}}val', font_size)
            r_lang: BaseOxmlElement = etree.SubElement(r_pr, '{%s}lang' % NAME_SPACES['w'])
            r_fonts: BaseOxmlElement = etree.SubElement(r_pr, '{%s}rFonts' % NAME_SPACES['w'])
            r_lang.set(f'{{{NAME_SPACES["w"]}}}eastAsia', lang)
            r_fonts.set(f'{{{NAME_SPACES["w"]}}}ascii', asc_ii)
            r_fonts.set(f'{{{NAME_SPACES["w"]}}}hAnsi', asc_ii)
            r_fonts.set(f'{{{NAME_SPACES["w"]}}}cs', asc_ii)
            r_fonts.set(f'{{{NAME_SPACES["w"]}}}eastAsia', east_asia)
            t: BaseOxmlElement = etree.SubElement(r, '{%s}t' % NAME_SPACES['w'])
            t.text = text
            # 追加
            current_element.addnext(p)
            # 移动到下一个元素
            current_element = p

        return current_element

    @classmethod
    def generate_table_to_builder_table(
        cls,
        table_key: TableKey,
        table: GenerateTable,
        header_title: str = '',
        left_fixed_header_title: str = '',
        subheader_title: str = '',
        left_fixed_subheader_title: str = '',
        test_product: str = '',
        trial_number: str = '',
    ) -> BuilderTable:
        """
        将生成的表格数据GenerateTable对象转换为BuilderTable对象。
        此方法用于将包含表格结构和数据的GenerateTable对象，以及相关的标题和试验信息，
        转换为BuilderTable对象，以便于后续处理和展示。
        参数:
        :param table_key: 表格的唯一键，用于标识表格。
        :param table: 包含表格结构和数据的GenerateTable对象。
        :param header_title: 表格头部标题。
        :param left_fixed_header_title: 固定在左侧的头部标题。
        :param subheader_title: 表格副标题。
        :param left_fixed_subheader_title: 固定在左侧的副标题。
        :param test_product: 测试产品名称。
        :param trial_number: 试验编号。
        返回:
        :return BuilderTable对象，包含转换后的表格数据和结构。
        """
        generate_table = table if isinstance(table, GenerateTable) else GenerateTable()

        builder_table = BuilderTable(
            table_key=table_key,
            pass_before_row=generate_table.pass_before_row,
            header_title=header_title,
            left_fixed_header_title=left_fixed_header_title,
            subheader_title=subheader_title,
            left_fixed_subheader_title=left_fixed_subheader_title,
            test_product=test_product,
            trial_number=trial_number,
            footer='',
            is_error=generate_table.is_error,
        )

        for generate_row in generate_table.rows:
            builder_row = BuilderTableRow(cols=[], is_bold=generate_row.is_bold)
            for generate_col in generate_row.cols:
                titles: list[str] = []
                values: list[str] = []
                for content in generate_col.content_line:
                    title = content.title
                    if title:
                        titles.append(title)
                    value = content.value
                    if value:
                        values.append(value)
                merged_title = '\n'.join(titles)
                merged_value = '\n'.join(values)

                builder_col = BuilderCol(
                    title=merged_title,
                    value=merged_value,
                    font_color=str(generate_col.content_line[0].font_color) if generate_col.content_line else '',
                    align_vertical=generate_col.align_vertical,
                    align_horizontally=generate_col.align_horizontally,
                    row_span=generate_col.row_span,
                    row_span_first_row=generate_col.row_span_first_row,
                    row_span_count=generate_col.row_span_count,
                    col_span=generate_col.col_span,
                    col_span_count=generate_col.col_span_count,
                    is_error=generate_col.is_error,
                )
                builder_row.cols.append(builder_col)
            builder_table.rows.append(builder_row)
        return builder_table

    @classmethod
    def builder_table_to_generate_table(cls, table: BuilderTable | None = None) -> GenerateTable:
        """
        将 BuilderTable 对象转换为 GenerateTable 对象。
        如果输入的 table 不是 BuilderTable 实例，则创建一个新的 BuilderTable 实例。
        基于 BuilderTable 的配置生成 GenerateTable 实例，包括行和单元格的内容和样式。
        参数:
        table (BuilderTable): 输入的 BuilderTable 实例或用于创建 BuilderTable 实例的数据。
        返回:
        GenerateTable: 生成的 GenerateTable 实例。
        """
        builder_table = table if table else BuilderTable()
        generate_table = GenerateTable(
            rows=[],
            pass_before_row=builder_table.pass_before_row,
            footer=builder_table.footer,
            is_error=builder_table.is_error,
        )

        def string_to_generate_content_array(title: str, value: str, font_color: str) -> list[GenerateContent]:
            # 将输入字符串按换行符拆分成行
            lines_title: list[str] = title.split('\n')
            lines_value: list[str] = value.split('\n')

            # 计算需要填充的长度，保证两者行数一致
            max_length = max(len(lines_title), len(lines_value))

            # 填充较短的列表，确保两者长度一致
            while len(lines_title) < max_length:
                lines_title.append('')  # 填充空字符串到 title
            while len(lines_value) < max_length:
                lines_value.append('')  # 填充空字符串到 value

            # 使用列表推导式创建 GenerateContent 对象，填充 title 和 value
            content_list = [
                GenerateContent(
                    title=lines_title[i],
                    value=lines_value[i],
                    font_color=font_color,
                    is_title_bold=len(lines_title) > 0,
                )
                for i in range(max_length)
            ]

            return content_list

        for builder_row in builder_table.rows:
            generate_row = GenerateRow(cols=[], is_bold=builder_row.is_bold)

            for builder_col in builder_row.cols:
                generate_row.cols.append(
                    GenerateCol(
                        content_line=string_to_generate_content_array(
                            builder_col.title, builder_col.value, builder_col.font_color
                        ),
                        align_vertical=builder_col.align_vertical,
                        align_horizontally=builder_col.align_horizontally,
                        row_span=builder_col.row_span,
                        row_span_first_row=builder_col.row_span_first_row,
                        row_span_count=builder_col.row_span_count,
                        col_span=builder_col.col_span,
                        col_span_count=builder_col.col_span_count,
                        is_error=builder_col.is_error,
                    )
                )
            generate_table.rows.append(generate_row)

        return generate_table

    @classmethod
    def set_header_text(
        cls, file_bytes_io: io.BytesIO, applicant_list: list[str], test_product_list: list[str]
    ) -> io.BytesIO:
        """
        修改页眉内容的函数。
        :param file_bytes_io: 包含文档的字节流（Zip 文件流）
        :param applicant_list: 申请人列表
        :param test_product_list: 测试产品列表
        :return: 修改后的 header1.xml 的 BytesIO 对象
        """
        header_file_path = 'word/header1.xml'

        with zipfile.ZipFile(file_bytes_io, 'r') as zip_ref:
            # 检查 header1.xml 是否在压缩包中
            if header_file_path in zip_ref.namelist():
                header_part = zip_ref.read(header_file_path)
                header_file = io.BytesIO(header_part)

                # 解析 header1.xml 内容
                header_tree = etree.XML(header_file.read())

                # 获取页眉中的所有段落
                paragraphs = header_tree.findall('.//w:p', namespaces=NAME_SPACES)
                for p in paragraphs:
                    # 查找所有的文本元素
                    r_list = p.findall('.//w:r', namespaces=NAME_SPACES)
                    for r in r_list:
                        t = r.find('.//w:t', namespaces=NAME_SPACES)
                        if t is not None:
                            text = t.text
                            # 如果匹配占位符，替换为相应的文本
                            if text == cls.HEADER_APPLICANT:
                                t.text = '、'.join(applicant_list)
                            elif text == cls.HEADER_TEST_PRODUCT:
                                t.text = '、'.join(test_product_list)

                # 重新保存整个文档到 BytesIO 对象
                output_bytes_io = io.BytesIO()
                with zipfile.ZipFile(output_bytes_io, 'w', zipfile.ZIP_DEFLATED) as new_zip:
                    # 保留原有内容并更新 document.xml
                    for file_info in zip_ref.infolist():
                        if file_info.filename != 'word/header1.xml':
                            new_zip.writestr(file_info.filename, zip_ref.read(file_info.filename))

                    # 更新 document.xml 文件
                    updated_document_xml = etree.tostring(header_tree, xml_declaration=True, encoding='UTF-8')
                    new_zip.writestr('word/header1.xml', updated_document_xml)

                # 游标重置到开始位置
                output_bytes_io.seek(0)
                return output_bytes_io

            raise FileNotFoundError(f"'{header_file_path}' not found in the document archive.")

    # 错误单元格标识
    @classmethod
    def identifies_error_cell(cls, generate_table: GenerateTable) -> None:
        """
        根据特定的错误关键词，识别并标记表格中的错误单元格。
        :param generate_table: GenerateTable 实例，表示一个待处理的表格。
        :return: 无返回值。此方法直接修改表格中每个单元格的错误状态。
        """
        for row in generate_table.rows:
            for col in row.cols:
                is_error: bool = False
                for content_line in col.content_line:
                    if cls.ERROR_KEYWORD in content_line.value:
                        is_error = True
                    break
                col.is_error = is_error

    @classmethod
    def add_page_break(cls, element: BaseOxmlElement) -> None:
        """
        在给定的BaseOxmlElement对象后添加一个分页符。
        参数:
        element (BaseOxmlElement): 需要在其后添加分页符的元素。
        返回:
        None
        """
        p: BaseOxmlElement = etree.Element('{%s}p' % NAME_SPACES['w'])

        p_pr: BaseOxmlElement = etree.SubElement(p, '{%s}pPr' % NAME_SPACES['w'])
        spacing: BaseOxmlElement = etree.SubElement(p_pr, '{%s}spacing' % NAME_SPACES['w'])
        spacing.set(f'{{{NAME_SPACES["w"]}}}before', '0')

        r: BaseOxmlElement = etree.SubElement(p, '{%s}r' % NAME_SPACES['w'])
        br: BaseOxmlElement = etree.SubElement(r, '{%s}br' % NAME_SPACES['w'])
        br.set(f'{{{NAME_SPACES["w"]}}}type', 'page')

        element.addnext(p)
