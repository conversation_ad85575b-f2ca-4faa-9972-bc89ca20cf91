from app.schemas.doc_generate import (
    GenerateTable,
    GenerateRow,
    GenerateCol,
    GenerateContent,
    CellHorizontallyAlign,
    CellVerticalAlign,
)

table2671 = GenerateTable(
    rows=[
        GenerateRow(
            is_bold=True,
            cols=[
                GenerateCol(
                    align_horizontally=CellHorizontallyAlign.CENTER,
                    align_vertical=CellVerticalAlign.CENTER,
                    content_line=[
                        GenerateContent(title="试验类型", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    align_horizontally=CellHorizontallyAlign.CENTER,
                    align_vertical=CellVerticalAlign.CENTER,
                    content_line=[
                        GenerateContent(title="种属与品系", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    align_horizontally=CellHorizontallyAlign.CENTER,
                    align_vertical=CellVerticalAlign.CENTER,
                    content_line=[
                        GenerateContent(title="给药方法", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    align_horizontally=CellHorizontallyAlign.CENTER,
                    align_vertical=CellVerticalAlign.CENTER,
                    content_line=[
                        GenerateContent(title="给药周期", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    align_horizontally=CellHorizontallyAlign.CENTER,
                    align_vertical=CellVerticalAlign.CENTER,
                    content_line=[
                        GenerateContent(title="剂量", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    align_horizontally=CellHorizontallyAlign.CENTER,
                    align_vertical=CellVerticalAlign.CENTER,
                    content_line=[
                        GenerateContent(title="GLP依从性", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    align_horizontally=CellHorizontallyAlign.CENTER,
                    align_vertical=CellVerticalAlign.CENTER,
                    content_line=[
                        GenerateContent(title="试验机构", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    align_horizontally=CellHorizontallyAlign.CENTER,
                    align_vertical=CellVerticalAlign.CENTER,
                    content_line=[
                        GenerateContent(title="试验编号", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    align_horizontally=CellHorizontallyAlign.CENTER,
                    align_vertical=CellVerticalAlign.CENTER,
                    content_line=[
                        GenerateContent(title="位置", is_title_bold=True),
                    ],
                ),
            ],
        )
    ]
)

table2672 = GenerateTable(
    rows=[
        GenerateRow(
            is_bold=True,
            cols=[
                GenerateCol(
                    align_horizontally=CellHorizontallyAlign.CENTER,
                    align_vertical=CellVerticalAlign.CENTER,
                    content_line=[
                        GenerateContent(title="试验类型", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    align_horizontally=CellHorizontallyAlign.CENTER,
                    align_vertical=CellVerticalAlign.CENTER,
                    content_line=[
                        GenerateContent(title="试验系统", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    align_horizontally=CellHorizontallyAlign.CENTER,
                    align_vertical=CellVerticalAlign.CENTER,
                    content_line=[
                        GenerateContent(title="给药方法", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    align_horizontally=CellHorizontallyAlign.CENTER,
                    align_vertical=CellVerticalAlign.CENTER,
                    content_line=[
                        GenerateContent(title="剂量（mg/kg/day）", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    align_horizontally=CellHorizontallyAlign.CENTER,
                    align_vertical=CellVerticalAlign.CENTER,
                    content_line=[
                        GenerateContent(title="GLP依从性", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    align_horizontally=CellHorizontallyAlign.CENTER,
                    align_vertical=CellVerticalAlign.CENTER,
                    content_line=[
                        GenerateContent(title="试验编号", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    align_horizontally=CellHorizontallyAlign.CENTER,
                    align_vertical=CellVerticalAlign.CENTER,
                    content_line=[
                        GenerateContent(title="位置", is_title_bold=True),
                    ],
                ),
            ],
        )
    ]
)

table2675 = GenerateTable(
    rows=[
        GenerateRow(
            is_bold=True,
            cols=[
                GenerateCol(
                    align_horizontally=CellHorizontallyAlign.CENTER,
                    align_vertical=CellVerticalAlign.CENTER,
                    content_line=[
                        GenerateContent(title="种属/品系", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    align_horizontally=CellHorizontallyAlign.CENTER,
                    align_vertical=CellVerticalAlign.CENTER,
                    content_line=[
                        GenerateContent(title="给药方法\n（溶媒/剂型）", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    align_horizontally=CellHorizontallyAlign.CENTER,
                    align_vertical=CellVerticalAlign.CENTER,
                    content_line=[
                        GenerateContent(title="剂量\n（mg/kg）", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    align_horizontally=CellHorizontallyAlign.CENTER,
                    align_vertical=CellVerticalAlign.CENTER,
                    content_line=[
                        GenerateContent(title="性别和\n数量组", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    align_horizontally=CellHorizontallyAlign.CENTER,
                    align_vertical=CellVerticalAlign.CENTER,
                    content_line=[
                        GenerateContent(title="观察到的最大耐受\n剂量（mg/kg）", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    align_horizontally=CellHorizontallyAlign.CENTER,
                    align_vertical=CellVerticalAlign.CENTER,
                    content_line=[
                        GenerateContent(title="值得注意的结果", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    align_horizontally=CellHorizontallyAlign.CENTER,
                    align_vertical=CellVerticalAlign.CENTER,
                    content_line=[
                        GenerateContent(title="试验编号", is_title_bold=True),
                    ],
                ),
            ],
        )
    ]
)

table2676 = GenerateTable(
    rows=[
        GenerateRow(
            is_bold=True,
            cols=[
                GenerateCol(
                    align_horizontally=CellHorizontallyAlign.CENTER,
                    align_vertical=CellVerticalAlign.CENTER,
                    content_line=[
                        GenerateContent(title="种属/品系", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    align_horizontally=CellHorizontallyAlign.CENTER,
                    align_vertical=CellVerticalAlign.CENTER,
                    content_line=[
                        GenerateContent(title="给药方法\n（溶媒/剂型）", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    align_horizontally=CellHorizontallyAlign.CENTER,
                    align_vertical=CellVerticalAlign.CENTER,
                    content_line=[
                        GenerateContent(title="给药期限", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    align_horizontally=CellHorizontallyAlign.CENTER,
                    align_vertical=CellVerticalAlign.CENTER,
                    content_line=[
                        GenerateContent(title="剂量\n（mg/kg）", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    align_horizontally=CellHorizontallyAlign.CENTER,
                    align_vertical=CellVerticalAlign.CENTER,
                    content_line=[
                        GenerateContent(title="性别和\n数量组", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    align_horizontally=CellHorizontallyAlign.CENTER,
                    align_vertical=CellVerticalAlign.CENTER,
                    content_line=[
                        GenerateContent(title="最大耐受剂量（mg/kg）", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    align_horizontally=CellHorizontallyAlign.CENTER,
                    align_vertical=CellVerticalAlign.CENTER,
                    content_line=[
                        GenerateContent(title="值得注意的结果", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    align_horizontally=CellHorizontallyAlign.CENTER,
                    align_vertical=CellVerticalAlign.CENTER,
                    content_line=[
                        GenerateContent(title="试验编号", is_title_bold=True),
                    ],
                ),
            ],
        )
    ]
)

table26771 = GenerateTable(
    rows=[
        GenerateRow(
            cols=[
                GenerateCol(
                    content_line=[
                        GenerateContent(title="种属/品系： ", key="species", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    content_line=[
                        GenerateContent(title="给药期限： ", key="dosing_regimen", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    content_line=[
                        GenerateContent(title="CTD中的位置： ", value="4.2.3.2", is_title_bold=True),
                    ],
                ),
            ]
        ),
        GenerateRow(
            cols=[
                GenerateCol(
                    content_line=[
                        GenerateContent(title="初始年龄： ", key="initial_age", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    content_line=[
                        GenerateContent(title="恢复期： ", key="recovery_period", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    content_line=[
                        GenerateContent(title="GLP依从性： ", key="glp_compliance", is_title_bold=True),
                    ],
                ),
            ]
        ),
        GenerateRow(
            cols=[
                GenerateCol(
                    content_line=[
                        GenerateContent(title="首次给药日期： ", key="date_of_first_dosage", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    col_span_count=2,
                    content_line=[
                        GenerateContent(title="给药方法： ", key="administration_method", is_title_bold=True),
                    ],
                ),
            ]
        ),
        GenerateRow(
            cols=[
                GenerateCol(
                    col_span_count=3,
                    content_line=[
                        GenerateContent(title="溶媒/剂型： ", key="solvent_and_dosage_form", is_title_bold=True),
                    ],
                ),
            ]
        ),
        GenerateRow(
            cols=[
                GenerateCol(
                    col_span_count=3,
                    content_line=[
                        GenerateContent(title="特殊情况： ", value="N/A", is_title_bold=True),
                    ],
                ),
            ]
        ),
        GenerateRow(
            cols=[
                GenerateCol(
                    col_span_count=3,
                    content_line=[
                        GenerateContent(
                            title="未见不良反应剂量（NOAEL）： ", key="no_adverse_reaction_dosage", is_title_bold=True
                        ),
                    ],
                ),
            ]
        ),
    ]
)

table26781 = GenerateTable(
    rows=[
        GenerateRow(
            cols=[
                GenerateCol(
                    content_line=[
                        GenerateContent(title="所检测的诱导作用： ", value="细菌回复突变", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    content_line=[
                        GenerateContent(title="独立试验次数： ", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    content_line=[
                        GenerateContent(
                            title="平行培养物数量： ", key="number_of_parallel_cultures", is_title_bold=True
                        ),
                    ],
                ),
                GenerateCol(
                    content_line=[
                        GenerateContent(title="GLP 依从性： ", key="glp_compliance", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    content_line=[
                        GenerateContent(title="溶媒： ", key="solvent_and_dosage_form", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    content_line=[
                        GenerateContent(title="位置： ", value="4.2.3.3", is_title_bold=True),
                    ],
                ),
            ]
        ),
        GenerateRow(
            cols=[
                GenerateCol(
                    content_line=[GenerateContent(title="品系： ", key="species", is_title_bold=True)], col_span_count=6
                )
            ]
        ),
        GenerateRow(
            cols=[
                GenerateCol(
                    content_line=[
                        GenerateContent(title="阳性对照品： ", key="positive_control_sample", is_title_bold=True)
                    ],
                    col_span_count=6,
                )
            ]
        ),
        GenerateRow(
            cols=[
                GenerateCol(
                    content_line=[GenerateContent(title="代谢系统： ", key="metabolic_system", is_title_bold=True)],
                    col_span_count=3,
                ),
                GenerateCol(
                    content_line=[GenerateContent(title="处理： ", key="handle_8", is_title_bold=True)],
                    col_span_count=3,
                ),
            ]
        ),
        GenerateRow(
            cols=[
                GenerateCol(
                    content_line=[
                        GenerateContent(title="细胞毒性作用： ", key="cytotoxic_effects_8", is_title_bold=True)
                    ],
                    col_span_count=6,
                )
            ]
        ),
        GenerateRow(
            cols=[
                GenerateCol(
                    content_line=[
                        GenerateContent(title="遗传毒性作用： ", key="genotoxic_effects_8", is_title_bold=True)
                    ],
                    col_span_count=6,
                )
            ]
        ),
    ]
)

table26782 = GenerateTable(
    rows=[
        GenerateRow(
            cols=[
                GenerateCol(
                    content_line=[
                        GenerateContent(title="检测的诱导作用： ", value="染色体畸变", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    content_line=[
                        GenerateContent(title="独立试验数量： ", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    content_line=[
                        GenerateContent(title="位置： ", value="4.2.3.3", is_title_bold=True),
                    ],
                ),
            ]
        ),
        GenerateRow(
            cols=[
                GenerateCol(
                    content_line=[GenerateContent(title="品系： ", key="species", is_title_bold=True)],
                ),
                GenerateCol(
                    content_line=[
                        GenerateContent(
                            title="平行培养物数量： ", key="number_of_parallel_cultures_chromosome", is_title_bold=True
                        )
                    ],
                ),
                GenerateCol(
                    content_line=[
                        GenerateContent(title="GLP 依从性： ", key="glp_compliance", is_title_bold=True),
                    ],
                ),
            ]
        ),
        GenerateRow(
            cols=[
                GenerateCol(
                    content_line=[
                        GenerateContent(title="代谢系统： ", key="metabolic_system_chromosome", is_title_bold=True)
                    ],
                ),
                GenerateCol(
                    content_line=[
                        GenerateContent(title="分析细胞数： ", key="analyze_cell_number_chromosome", is_title_bold=True)
                    ],
                ),
                GenerateCol(
                    content_line=[
                        GenerateContent(title="给药日期： ", key="administration_date_chromosome", is_title_bold=True),
                    ],
                ),
            ]
        ),
        GenerateRow(
            cols=[
                GenerateCol(
                    content_line=[GenerateContent(title="溶媒： ", key="solvent_and_dosage_form", is_title_bold=True)],
                ),
                GenerateCol(
                    content_line=[
                        GenerateContent(
                            title="阳性对照： ", key="positive_control_sample_chromosome", is_title_bold=True
                        )
                    ],
                    col_span_count=2,
                ),
            ]
        ),
        GenerateRow(
            cols=[
                GenerateCol(
                    content_line=[GenerateContent(title="处理： ", is_title_bold=True)],
                ),
                GenerateCol(
                    content_line=[GenerateContent(key="handle_chromosome", is_title_bold=True)], col_span_count=2
                ),
            ]
        ),
        GenerateRow(
            cols=[
                GenerateCol(
                    content_line=[GenerateContent(title="细胞毒性作用： ", is_title_bold=True)],
                ),
                GenerateCol(
                    content_line=[GenerateContent(key="toxic_effects_chromosome", is_title_bold=True)], col_span_count=2
                ),
            ]
        ),
        GenerateRow(
            cols=[
                GenerateCol(
                    content_line=[GenerateContent(title="遗传毒性作用： ", is_title_bold=True)],
                ),
                GenerateCol(
                    content_line=[GenerateContent(key="genotoxic_effects_chromosome", is_title_bold=True)],
                    col_span_count=2,
                ),
            ]
        ),
    ]
)

table26791 = GenerateTable(
    rows=[
        GenerateRow(
            cols=[
                GenerateCol(
                    content_line=[
                        GenerateContent(title="检测的诱导作用： ", value="骨髓微核", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    content_line=[
                        GenerateContent(title="给药方案： ", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    content_line=[
                        GenerateContent(title="位置： ", value="4.2.3.3", is_title_bold=True),
                    ],
                ),
            ]
        ),
        GenerateRow(
            cols=[
                GenerateCol(
                    content_line=[
                        GenerateContent(title="种属/品系： ", key="species", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    content_line=[
                        GenerateContent(title="采样时间： ", key="sampling_time", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    content_line=[
                        GenerateContent(title="GLP依从性： ", key="glp_compliance", is_title_bold=True),
                    ],
                ),
            ]
        ),
        GenerateRow(
            cols=[
                GenerateCol(
                    content_line=[
                        GenerateContent(title="年龄： ", key="age", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    content_line=[
                        GenerateContent(title="给药方法： ", key="administration_method_9", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    content_line=[
                        GenerateContent(title="给药日期： ", key="administration_date", is_title_bold=True),
                    ],
                ),
            ]
        ),
        GenerateRow(
            cols=[
                GenerateCol(
                    content_line=[
                        GenerateContent(title="评价的细胞： ", key="evaluate_cells", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    content_line=[
                        GenerateContent(title="溶媒/剂型： ", key="solvent_and_dosage_form", is_title_bold=True),
                    ],
                ),
                GenerateCol(
                    content_line=[
                        GenerateContent(title="每只动物分析的细胞数量： ", key="cell_number", is_title_bold=True),
                    ],
                ),
            ]
        ),
        GenerateRow(
            cols=[
                GenerateCol(
                    content_line=[
                        GenerateContent(title="毒性/细胞毒性作用： ", key="toxic_effects", is_title_bold=True),
                    ],
                    col_span_count=3,
                )
            ]
        ),
        GenerateRow(
            cols=[
                GenerateCol(
                    content_line=[
                        GenerateContent(title="遗传毒性作用： ", key="genotoxic_effects_9", is_title_bold=True),
                    ],
                    col_span_count=3,
                )
            ]
        ),
        GenerateRow(
            cols=[
                GenerateCol(
                    content_line=[
                        GenerateContent(title="暴露的证据： ", is_title_bold=True),
                    ],
                    col_span_count=3,
                )
            ]
        ),
    ]
)
