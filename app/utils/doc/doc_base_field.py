import io
import re
import xml.etree.ElementTree as ET
import zipfile
from re import compile

from docx.opc.oxml import BaseOxmlElement

from app.extractors.base_field import (
    AdministrationMethod,
    AmesSpecies,
    ChromosomeAberrationRegimen,
    ChromosomeAberrationSpecies,
    DosageAmes,
    DosageChromosome,
    DosageDoseSpecies,
    DosageMicronucleus,
    DosageSingle,
    MicronucleusRegimen,
    MicronucleusSolvent,
    MicronucleusSpecies,
    RepeatedDoseRegimen,
    RepeatedDoseSpecies,
    SingleAndRepeatedDoseSolvent,
    SingleDoseSpecies,
)
from app.schemas.doc_base_field import TrialMainType, TrialResultTask, TrialSubType
from app.schemas.doc_chunk import ChunkHeadlineData
from app.utils.doc.doc_chunk import (
    chunk_find_previous_tr_merged_text,
    chunk_word_process,
    get_text_from_element,
)
from app.utils.doc.doc_type import NAME_SPACES, ElementType, WordType
from app.utils.exception import ExtractError


# 1 - 3
# 基础字段 - 试验标题
def get_trial_title(cover_p_text_list: list[str]) -> str:
    # 预处理：去除空格后的字符串长度为 0 的段落
    cover_p_text_list = [p_text for p_text in cover_p_text_list if p_text.replace(' ', '')]

    # 清理所有段落的文本，将空格去掉并生成清理后的列表
    cleaned_text_list = [p_text.replace(' ', '') for p_text in cover_p_text_list]

    # 判断是否有'受试物'关键词
    if '受试物' in ''.join(cleaned_text_list):
        # 查找'受试物'出现的段落索引
        for i, cleaned_text in enumerate(cleaned_text_list):
            if '受试物' in cleaned_text:
                # 拼接'受试物'前面所有段落的文本
                trial_title = ''.join(cover_p_text_list[:i])
                break
    else:
        # 如果没有'受试物'，直接拼接前2个段落的文本
        trial_title = ''.join(cover_p_text_list[:2])

    # 移除'试验报告'和'总结报告'并返回结果
    return trial_title.replace('试验报告', '').replace('总结报告', '').strip()


TIME_PATTERN = compile(r'[\d一二三四五六七八九十百千][年月周日天时分秒]')
GENOTOXICITY_PATTERN = compile(r'回复突变|染色体|微核')
GLP_PATTERN = compile(r'遵从性声明|符合性声明|依从性声明|GLP声明')


def get_trial_type(trial_title: str) -> tuple[TrialMainType, TrialSubType]:
    """
    根据试验标题判断试验的主要类型。
    通过检查试验标题中是否包含特定关键词，来决定试验是属于遗传毒性试验、重复给药试验还是单次给药试验。
    参数:
    - trial_title: 试验的标题，用于分析试验类型。
    返回:
    - TrialMainType: 试验的主要类型，包括遗传毒性（GENOTOXICITY）、重复给药（REPEATED_DOSE）和单次给药（SINGLE_DOSE）。
    - TrialSubType: 试验的子类型，暂不判断重复给药的关键性
    """
    match = GENOTOXICITY_PATTERN.search(trial_title)
    if match:
        matched_group = match.group()
        if matched_group == '回复突变':
            sub_type = TrialSubType.AMES_TEST
        elif matched_group == '染色体':
            sub_type = TrialSubType.CHROMOSOME_ABERRATION_TEST
        elif matched_group == '微核':
            sub_type = TrialSubType.MICRONUCLEUS_TEST
        else:  # 不可能到这里
            raise ExtractError()
        return (TrialMainType.GENOTOXICITY, sub_type)

    if TIME_PATTERN.search(trial_title):
        return (TrialMainType.REPEATED_DOSE, TrialSubType.REPEATED_DOSE)

    return (TrialMainType.SINGLE_DOSE, TrialSubType.SINGLE_DOSE)


def is_glp(body, trial_main_type: TrialMainType) -> bool:
    """
    收集GLP（Good Laboratory Practice）信息。

    根据试验的主要类型和文档内容，判断是否有关于GLP的信息。

    参数:
    - body: 包含文档元素的列表，用于检查GLP信息。
    - trial_main_type: 试验的主要类型，用于决定如何处理GLP信息。

    返回:
    - bool: 表示是否找到了GLP信息。False表示没有找到或试验类型为单剂量。
    """

    if trial_main_type != TrialMainType.SINGLE_DOSE:
        for element in body:
            if WordType.get_type(element) == ElementType.PARAGRAPH:
                p_element: BaseOxmlElement = element
                p_text = get_text_from_element(p_element)
                if GLP_PATTERN.search(p_text):
                    return True
    return False


# 1 - 5
# 基础字段 - 试验编号
def get_trial_number(cover_p_text_list: list[str]) -> str:
    # 定义需要匹配的关键字列表
    keywords = [
        '专题号',
        '专题代号',
        '专题编号',
        '专题代码',
        '专题编码',
        '专题码号',
        '专题标号',
        '专题序号',
        '专题记号',
        '专题符号',
        '专题识别号',
        '专题登记号',
        '专题注册号',
        '专题统一编号',
        '专题特定号',
        '专题主题号',
        '专题专题编号',
        '专题项目代号',
        '专题任务编号',
        '专题研究代码',
        '专题测试编号',
        '专题样品代码',
        '专题批次号',
        '专题系列号',
        '专题分类号',
        '专题归类号',
        '专题组号',
        '专题类别号',
        '专题版本号',
        '专题修订号',
        '专题序列号',
        '专题流水号',
        '专题档案号',
        '专题ID号',
        '专题凭证号',
        '专题授权号',
        '专题许可号',
        '专题标识码',
        '专题跟踪号',
        '专题参考号',
        '专题索引号',
        '专题记录号',
    ]

    # 构建正则表达式
    pattern = re.compile('|'.join(re.escape(keyword) for keyword in keywords))

    for p_text in cover_p_text_list:
        cleaned_text = p_text.replace(' ', '')  # 去除空格
        match = pattern.search(cleaned_text)
        if match:
            start_index = match.start()
            trial_number = cleaned_text[start_index + len(match.group()) :]
            trial_number = trial_number.replace('：', '').replace(':', '').strip()
            if trial_number:
                return trial_number
    return ''


async def collect_species(body, trial_title: str, trial_subtype: TrialSubType) -> TrialResultTask:
    """
    收集并提取实验中的种属信息。
    根据实验类型（如单次给药、重复给药、微核测试等）从提供的文档正文中提取种属信息，支持表格与非表格两种形式的提取，并根据不同的试验子类型（trial_subtype）调用相应的提取方法。
    :param body: Word文档的内容元素，类型为BaseOxmlElement的列表，包含所有段落和表格等。
    :param trial_title: 实验标题，用于微核测试和染色体畸变测试时提取种属信息，类型为str。
    :param trial_subtype: 当前试验的子类型，类型为TrialSubType，决定提取种属信息的方式。
    :return: TrialResultTask对象，其中包含提取到的种属信息（species）。
    """
    # 种属
    species: str = ''
    animal_p_element: BaseOxmlElement | None = None
    anes_p_element: BaseOxmlElement | None = None
    micronucleus_p_element: BaseOxmlElement | None = None

    for element in body:
        if WordType.get_type(element) == ElementType.PARAGRAPH:
            p_element: BaseOxmlElement = element
            p_text: str = ''
            r_element_list: list[BaseOxmlElement] = p_element.findall('.//w:r', namespaces=NAME_SPACES)
            for r_element in r_element_list:
                t_element: BaseOxmlElement = r_element.find('.//w:t', namespaces=NAME_SPACES)
                if t_element is not None and t_element.text.strip() != '':
                    p_text += t_element.text
            # 种属 - 单次给药
            # 种属 - 重复给药 - 关键
            # 种属 - 重复给药 - 非关键
            if (
                trial_subtype.value < TrialSubType.AMES_TEST.value
                and '正文表' in p_text
                and '实验动物' in p_text
                and animal_p_element is None
            ):
                # 规避其余干扰段落
                if p_text.index('正文表') < p_text.index('实验动物'):
                    animal_p_element = p_element
            # 另一种描述
            if (
                trial_subtype.value < TrialSubType.AMES_TEST.value
                and '文本表' in p_text
                and '实验动物' in p_text
                and animal_p_element is None
            ):
                # 规避其余干扰段落
                if p_text.index('文本表') < p_text.index('实验动物'):
                    animal_p_element = p_element
            # 种属 - 回复
            if trial_subtype == TrialSubType.AMES_TEST and '实验系统' in p_text.strip():
                # 规避目录干扰段落
                if p_element.find('.//w:hyperlink', namespaces=NAME_SPACES) is None:
                    anes_p_element = p_element.getnext().getnext()
            # 种属 - 微核
            if trial_subtype == TrialSubType.MICRONUCLEUS_TEST and '实验动物种系和等级' in p_text.strip():
                # 规避目录干扰段落
                if p_element.find('.//w:hyperlink', namespaces=NAME_SPACES) is None:
                    micronucleus_p_element = p_element.getnext()

    # 种属 - 单次给药
    # 种属 - 重复给药 - 关键
    # 种属 - 重复给药 - 非关键
    if animal_p_element is not None:
        format_text: str = ''
        table_element: BaseOxmlElement = animal_p_element.getnext()
        if WordType.get_type(table_element) == ElementType.TABLE:
            tr_element_list: list[BaseOxmlElement] = table_element.findall('.//w:tr', namespaces=NAME_SPACES)
            for tr_element in tr_element_list:
                tc_element_list: list[BaseOxmlElement] = tr_element.findall('.//w:tc', namespaces=NAME_SPACES)
                if len(tc_element_list) > 1:
                    tc_text: str = ''
                    # 寻找第一列的单元格，并匹配关键词'种属和品种'
                    tc_element_first = tc_element_list[0]
                    r_element_list: list[BaseOxmlElement] = tc_element_first.findall('.//w:r', namespaces=NAME_SPACES)
                    for r_element in r_element_list:
                        t_element: BaseOxmlElement = r_element.find('.//w:t', namespaces=NAME_SPACES)
                        if t_element is not None:
                            tc_text += t_element.text
                    if '种属' in tc_text:
                        tc_element_second = tc_element_list[1]
                        r_element_list: list[BaseOxmlElement] = tc_element_second.findall(
                            './/w:r', namespaces=NAME_SPACES
                        )
                        for r_element in r_element_list:
                            t_element: BaseOxmlElement = r_element.find('.//w:t', namespaces=NAME_SPACES)
                            if t_element is not None:
                                format_text += t_element.text
        if trial_subtype == TrialSubType.SINGLE_DOSE:
            species: str = await SingleDoseSpecies.extract(format_text)
        if trial_subtype in (TrialSubType.REPEATED_DOSE_SPECIFICITY, TrialSubType.REPEATED_DOSE):
            species: str = await RepeatedDoseSpecies.extract(format_text)

    # 直接提取不需要提示词
    # 种属 - 重复给药 - 单次 - 非表格特殊情况
    # 种属 - 重复给药 - 关键 - 非表格特殊情况
    # 种属 - 重复给药 - 非关键 - 非表格特殊情况
    if trial_subtype.value < TrialSubType.AMES_TEST.value and animal_p_element is None:
        element: BaseOxmlElement
        for element in body:
            if WordType.get_type(element) == ElementType.PARAGRAPH:
                p_text: str = get_text_from_element(element)
                if '种系及品系' in p_text or '种系和品系' in p_text:
                    species: str = get_text_from_element(element.getnext())

    # 种属 - 回复
    if anes_p_element is not None:
        p_text: str = ''
        r_element_list: list[BaseOxmlElement] = anes_p_element.findall('.//w:r', namespaces=NAME_SPACES)
        for r_element in r_element_list:
            t_element: BaseOxmlElement = r_element.find('.//w:t', namespaces=NAME_SPACES)
            if t_element is not None:
                p_text += t_element.text
        species: str = await AmesSpecies.extract(p_text)

    # 种属 - 染色体
    if trial_subtype == TrialSubType.CHROMOSOME_ABERRATION_TEST and len(trial_title) > 0:
        species = await ChromosomeAberrationSpecies.extract(trial_title)

    # 种属 - 微核
    if micronucleus_p_element is not None:
        p_text: str = ''
        r_element_list: list[BaseOxmlElement] = micronucleus_p_element.findall('.//w:r', namespaces=NAME_SPACES)
        for r_element in r_element_list:
            t_element: BaseOxmlElement = r_element.find('.//w:t', namespaces=NAME_SPACES)
            if t_element is not None:
                p_text += t_element.text
        species = await MicronucleusSpecies.extract(p_text)

    return TrialResultTask(task='species', value=species)


NEXT_TEXT_PATTERN = compile(r'[:：]\s*(.*)')


async def collect_solvent(body, trial_subtype: TrialSubType, file_bytes_io: io.BytesIO) -> TrialResultTask:
    """
    收集并提取溶媒与剂型信息。
    根据实验类型（如单次给药、重复给药、微核测试等）从提供的文档正文中提取溶媒与剂型信息，支持表格和非表格的提取方式，
    并根据不同的试验子类型（trial_subtype）调用相应的提取方法。同时，处理特殊的段落和文件（如`styles.xml`）中的溶媒信息。
    :param body: Word文档的内容元素，类型为BaseOxmlElement的列表，包含所有段落和表格等。
    :param trial_subtype: 当前试验的子类型，类型为TrialSubType，决定提取溶媒信息的方式。
    :param file_bytes_io: 包含Word文档的字节流，用于处理特殊段落和`styles.xml`中的溶媒信息，类型为`io.BytesIO`。
    :return: TrialResultTask对象，其中包含提取到的溶媒与剂型信息（solvent）。
    """
    # 溶媒与剂型
    solvent: str = ''
    animal_p_element: BaseOxmlElement | None = None
    anes_p_element: BaseOxmlElement | None = None
    micronucleus_p_element: BaseOxmlElement | None = None

    for element in body:
        if WordType.get_type(element) == ElementType.PARAGRAPH:
            p_element: BaseOxmlElement = element
            p_text: str = ''
            r_element_list: list[BaseOxmlElement] = p_element.findall('.//w:r', namespaces=NAME_SPACES)
            for r_element in r_element_list:
                t_element: BaseOxmlElement = r_element.find('.//w:t', namespaces=NAME_SPACES)
                if t_element is not None and t_element.text.strip() != '':
                    p_text += t_element.text
            # 溶媒与剂型 - 单次给药
            # 溶媒与剂型 - 重复给药 - 关键
            # 溶媒与剂型 - 重复给药 - 非关键
            if (
                trial_subtype.value < TrialSubType.AMES_TEST.value
                and '正文表' in p_text
                and '试验设计' in p_text
                and animal_p_element is None
            ):
                # 规避其余干扰段落
                if p_text.index('正文表') < p_text.index('试验设计'):
                    animal_p_element = p_element
            # 另一种描述
            if (
                trial_subtype.value < TrialSubType.AMES_TEST.value
                and '文本表' in p_text
                and '试验设计' in p_text
                and animal_p_element is None
            ):
                # 规避其余干扰段落
                if p_text.index('文本表') < p_text.index('试验设计'):
                    animal_p_element = p_element
            # 溶媒与剂型 - 回复
            # 溶媒与剂型 - 染色体
            if (
                trial_subtype in (TrialSubType.AMES_TEST, TrialSubType.CHROMOSOME_ABERRATION_TEST)
                and '溶媒对照品信息' in p_text
            ):
                # 规避目录干扰段落
                if p_element.find('.//w:hyperlink', namespaces=NAME_SPACES) is None:
                    anes_p_element = p_element
            # 溶媒与剂型 - 微核
            if '溶媒对照品信息' in p_text:
                # 规避目录干扰段落
                if p_element.find('.//w:hyperlink', namespaces=NAME_SPACES) is None:
                    micronucleus_p_element = p_element

    # 溶媒与剂型 - 单次给药
    # 溶媒与剂型 - 重复给药 - 关键
    # 溶媒与剂型 - 重复给药 - 非关键
    if animal_p_element is not None and WordType.get_type(animal_p_element.getnext()) == ElementType.TABLE:
        table_element: BaseOxmlElement = animal_p_element.getnext()
        current_p: BaseOxmlElement = table_element.getnext()
        p_text: str = ''
        while current_p is not None and WordType.get_type(table_element.getnext()) == ElementType.PARAGRAPH:
            if current_p.find('.//w:b', namespaces=NAME_SPACES) is not None:
                break
            p_text += f'{get_text_from_element(current_p)} \n'
            current_p = current_p.getnext()
        if trial_subtype == TrialSubType.SINGLE_DOSE:
            solvent = await SingleAndRepeatedDoseSolvent.extract(p_text)
        else:
            solvent = await SingleAndRepeatedDoseSolvent.extract(p_text)

    # 溶媒与剂型 - 重复给药 - 特殊段落提取情况
    if (
        trial_subtype.value in (TrialSubType.REPEATED_DOSE.value, TrialSubType.REPEATED_DOSE_SPECIFICITY.value)
        and solvent == ''
    ):
        with zipfile.ZipFile(file_bytes_io) as docx_zip:
            if 'word/styles.xml' in docx_zip.namelist():
                with docx_zip.open('word/styles.xml') as styles_file:
                    styles_tree: ET.ElementTree = ET.parse(styles_file)
                    styles_root: ET.Element = styles_tree.getroot()
                    for p in body:
                        if WordType.get_type(p) == ElementType.PARAGRAPH:
                            if '溶媒对照' in get_text_from_element(p):
                                p_pr = p.find('.//w:pPr', namespaces=NAME_SPACES)
                                outline_lvl = p_pr.find('.//w:outlineLvl', namespaces=NAME_SPACES)
                                p_style: BaseOxmlElement = p_pr.find('.//w:pStyle', namespaces=NAME_SPACES)
                                if outline_lvl is None and p_style is not None:
                                    style_id: str = p_style.get(f'{{{NAME_SPACES["w"]}}}val', default=None)
                                    if style_id is not None:
                                        style_element: ET.Element | None = styles_root.find(
                                            f".//w:style[@w:styleId='{style_id}']", NAME_SPACES
                                        )
                                        if style_element is not None:
                                            outline_lvl = style_element.find('.//w:outlineLvl', namespaces=NAME_SPACES)
                                if outline_lvl is not None:
                                    p_next: BaseOxmlElement = p.getnext()
                                    p_next_text: str = get_text_from_element(p_next)
                                    match = NEXT_TEXT_PATTERN.search(p_next_text)
                                    solvent = match.group(1) if match else ''

    # 溶媒与剂型 - 回复
    # 溶媒与剂型 - 染色体
    if anes_p_element is not None:
        format_text: str = ''
        table_element: BaseOxmlElement = anes_p_element.getnext()
        if WordType.get_type(table_element) == ElementType.TABLE:
            tr_element_list: list[BaseOxmlElement] = table_element.findall('.//w:tr', namespaces=NAME_SPACES)
            for tr_element in tr_element_list:
                tc_element_list: list[BaseOxmlElement] = tr_element.findall('.//w:tc', namespaces=NAME_SPACES)
                if len(tc_element_list) > 1:
                    tc_text: str = ''
                    # 寻找第一列的单元格，并匹配关键词'名称'
                    tc_element_first = tc_element_list[0]
                    r_element_list: list[BaseOxmlElement] = tc_element_first.findall('.//w:r', namespaces=NAME_SPACES)
                    for r_element in r_element_list:
                        t_element: BaseOxmlElement = r_element.find('.//w:t', namespaces=NAME_SPACES)
                        if t_element is not None:
                            tc_text += t_element.text
                    if '名称' in tc_text:
                        tc_element_second = tc_element_list[1]
                        r_element_list: list[BaseOxmlElement] = tc_element_second.findall(
                            './/w:r', namespaces=NAME_SPACES
                        )
                        for r_element in r_element_list:
                            t_element: BaseOxmlElement = r_element.find('.//w:t', namespaces=NAME_SPACES)
                            if t_element is not None:
                                format_text += t_element.text
        solvent = format_text

    # 溶媒与剂型 - 微核
    if micronucleus_p_element is not None:
        p_text: str = ''
        next_p_element: BaseOxmlElement = micronucleus_p_element.getnext()
        r_element_list: list[BaseOxmlElement] = next_p_element.findall('.//w:r', namespaces=NAME_SPACES)
        for r_element in r_element_list:
            t_element: BaseOxmlElement = r_element.find('.//w:t', namespaces=NAME_SPACES)
            if t_element is not None:
                p_text += t_element.text
        solvent = await MicronucleusSolvent.extract(p_text)

    return TrialResultTask(task='solvent_and_dosage_form', value=solvent)


async def collect_method(body, trial_subtype: TrialSubType, trial_title: str) -> TrialResultTask:
    """
    收集并提取给药方法信息。
    根据实验的子类型（如AMETest、染色体畸变测试、微核测试等），从提供的文档正文中提取给药方法信息。
    支持从表格和段落中提取给药方法，并针对不同的试验子类型调用相应的提取方法。
    对于特定试验子类型，直接返回已知的给药方法（如体外、体内），而对于其他试验，尝试从文档正文或试验标题中提取详细信息。
    :param body: Word文档的内容元素，类型为BaseOxmlElement的列表，包含所有段落和表格等。
    :param trial_subtype: 当前试验的子类型，类型为TrialSubType，决定给药方法的提取方式。
    :param trial_title: 实验标题，用于提取重复给药类型试验中的给药方法信息，类型为str。
    :return: TrialResultTask对象，其中包含提取到的给药方法（administration_method）。
    """
    if trial_subtype in (TrialSubType.AMES_TEST, TrialSubType.CHROMOSOME_ABERRATION_TEST):
        return TrialResultTask(task='administration_method', value='体外')

    if trial_subtype == TrialSubType.MICRONUCLEUS_TEST:
        return TrialResultTask(task='administration_method', value='体内')

    method: str = ''
    animal_p_element: BaseOxmlElement | None = None

    for element in body:
        if WordType.get_type(element) == ElementType.PARAGRAPH:
            p_element: BaseOxmlElement = element
            p_text: str = ''
            r_element_list: list[BaseOxmlElement] = p_element.findall('.//w:r', namespaces=NAME_SPACES)
            for r_element in r_element_list:
                t_element: BaseOxmlElement = r_element.find('.//w:t', namespaces=NAME_SPACES)
                if t_element is not None and t_element.text.strip() != '':
                    p_text += t_element.text
            # 给药方法 - 单次给药
            if (
                trial_subtype == TrialSubType.SINGLE_DOSE
                and '正文表' in p_text
                and '给药信息' in p_text
                and animal_p_element is None
            ):
                # 规避其余干扰段落
                if p_text.index('正文表') < p_text.index('给药信息'):
                    animal_p_element = p_element
            # 另一种描述
            if (
                trial_subtype == TrialSubType.SINGLE_DOSE
                and '文本表' in p_text
                and '给药信息' in p_text
                and animal_p_element is None
            ):
                # 规避其余干扰段落
                if p_text.index('文本表') < p_text.index('给药信息'):
                    animal_p_element = p_element

    # 给药方法 - 单次给药
    if animal_p_element is not None:
        format_text: str = ''
        table_element: BaseOxmlElement = animal_p_element.getnext()
        if WordType.get_type(table_element) == ElementType.TABLE:
            tr_element_list: list[BaseOxmlElement] = table_element.findall('.//w:tr', namespaces=NAME_SPACES)
            for tr_element in tr_element_list:
                tc_element_list: list[BaseOxmlElement] = tr_element.findall('.//w:tc', namespaces=NAME_SPACES)
                if len(tc_element_list) > 1:
                    tc_text: str = ''
                    # 寻找第一列的单元格，并匹配关键词'给药途径'
                    tc_element_first = tc_element_list[0]
                    r_element_list: list[BaseOxmlElement] = tc_element_first.findall('.//w:r', namespaces=NAME_SPACES)
                    for r_element in r_element_list:
                        t_element: BaseOxmlElement = r_element.find('.//w:t', namespaces=NAME_SPACES)
                        if t_element is not None:
                            tc_text += t_element.text
                    if '给药途径' in tc_text:
                        tc_element_second = tc_element_list[1]
                        r_element_list: list[BaseOxmlElement] = tc_element_second.findall(
                            './/w:r', namespaces=NAME_SPACES
                        )
                        for r_element in r_element_list:
                            t_element: BaseOxmlElement = r_element.find('.//w:t', namespaces=NAME_SPACES)
                            if t_element is not None:
                                format_text += t_element.text
                        return TrialResultTask(task='administration_method', value=format_text)

    if trial_subtype in (TrialSubType.REPEATED_DOSE_SPECIFICITY, TrialSubType.REPEATED_DOSE):
        method = await AdministrationMethod.extract(trial_title)

    return TrialResultTask(task='administration_method', value=method)


async def collect_regimen(body, trial_subtype: TrialSubType, trial_title: str) -> TrialResultTask:
    """
    收集并提取给药剂量信息。
    根据实验的子类型（如单次给药、染色体畸变测试、微核测试等），从提供的文档正文中提取给药剂量信息。
    支持从表格和段落中提取给药剂量，并根据不同的试验子类型调用相应的提取方法。
    对于已知的试验子类型（如单次给药、AMES测试等），直接返回默认值。对于其他类型的试验，提取表格或段落中的给药信息，并处理特殊情况。
    :param body: Word文档的内容元素，类型为BaseOxmlElement的列表，包含所有段落和表格等。
    :param trial_subtype: 当前试验的子类型，类型为TrialSubType，决定给药剂量的提取方式。
    :param trial_title: 实验标题，用于提取重复给药类型试验中的给药剂量信息，类型为str。
    :return: TrialResultTask对象，其中包含提取到的给药剂量信息（dosing_regimen）。
    """
    regimen: str = ''
    anes_p_element: BaseOxmlElement | None = None
    micronucleus_p_element: BaseOxmlElement | None = None

    if trial_subtype in (TrialSubType.SINGLE_DOSE, TrialSubType.AMES_TEST):
        return TrialResultTask(task='dosing_regimen', value='-')

    if trial_subtype in (TrialSubType.REPEATED_DOSE_SPECIFICITY, TrialSubType.REPEATED_DOSE):
        regimen = await RepeatedDoseRegimen.extract(trial_title)

    for element in body:
        if WordType.get_type(element) == ElementType.PARAGRAPH:
            p_element: BaseOxmlElement = element
            p_text: str = get_text_from_element(p_element)
            # 给药周期 - 染色体
            if (
                trial_subtype == TrialSubType.CHROMOSOME_ABERRATION_TEST
                and '正文表' in p_text
                and '分析染色体畸变浓度' in p_text
                and anes_p_element is None
            ):
                # 规避其余干扰段落
                if p_text.index('正文表') < p_text.index('分析染色体畸变浓度'):
                    anes_p_element = p_element
            # 另一种描述词
            if (
                trial_subtype == TrialSubType.CHROMOSOME_ABERRATION_TEST
                and '文本表' in p_text
                and '分析染色体畸变浓度' in p_text
                and anes_p_element is None
            ):
                if p_text.index('文本表') < p_text.index('分析染色体畸变浓度'):
                    anes_p_element = p_element
            # 给药周期 - 微核
            if (
                trial_subtype == TrialSubType.MICRONUCLEUS_TEST
                and '给药方法和持续时间' in p_text
                and micronucleus_p_element is None
            ):
                # 规避目录干扰段落
                if p_element.find('.//w:hyperlink', namespaces=NAME_SPACES) is None:
                    micronucleus_p_element = p_element

    # 给药周期 - 染色体
    if anes_p_element is not None:
        table_text: str = ''
        table_element: BaseOxmlElement = anes_p_element.getnext()
        if WordType.get_type(table_element) == ElementType.TABLE:
            tr_element_list: list[BaseOxmlElement] = table_element.findall('.//w:tr', namespaces=NAME_SPACES)
            for tr_element in tr_element_list:
                tr_text: str = '| '
                tc_element_list: list[BaseOxmlElement] = tr_element.findall('.//w:tc', namespaces=NAME_SPACES)
                for tc_element in tc_element_list:
                    tc_text: str = ''
                    r_element_list: list[BaseOxmlElement] = tc_element.findall('.//w:r', namespaces=NAME_SPACES)
                    for r_element in r_element_list:
                        t_element: BaseOxmlElement = r_element.find('.//w:t', namespaces=NAME_SPACES)
                        if t_element is not None:
                            tc_text += t_element.text
                    # 单元格文本追加至行文本
                    if len(table_text) == 0:
                        tr_text += f'{tc_text} | '
                    else:
                        # 加上单元格结束符 |
                        tc_text += ' | '
                        tr_text += tc_text
                # 行元素追加至表格元素，加上换行符 \n
                tr_text += '\n'
                table_text += tr_text
        # 处理表格数据
        if len(table_text) != 0:
            regimen = await ChromosomeAberrationRegimen.extract(table_text)

    if micronucleus_p_element is not None:
        next_p: BaseOxmlElement = micronucleus_p_element.getnext()
        p_text: str = ''
        r_element_list: list[BaseOxmlElement] = next_p.findall('.//w:r', namespaces=NAME_SPACES)
        for r_element in r_element_list:
            t_element: BaseOxmlElement = r_element.find('.//w:t', namespaces=NAME_SPACES)
            if t_element is not None:
                p_text += t_element.text
        if len(p_text) > 0:
            regimen = await MicronucleusRegimen.extract(p_text)

    return TrialResultTask(task='dosing_regimen', value=regimen)


async def collect_administration_dosage(body, trial_subtype: TrialSubType, file_path: str) -> TrialResultTask:
    """
    收集并提取给药剂量信息。
    根据实验的子类型（如单次给药、染色体畸变测试、微核测试等），从提供的文档正文中提取给药剂量信息。
    该方法首先根据试验类型（如AMES测试、微核测试等）进行初步分类，然后通过分析表格内容和段落内容，提取给药剂量信息。
    方法包括：
    1. 根据试验设计中的关键词（如“试验设计”、“剂量水平”等）识别相关段落。
    2. 对于复杂的表格结构，进行详细的解析，处理列和行的合并等特殊情况。
    3. 对于微核测试，进行专门的文本提取和处理。
    4. 调用不同的提取函数（如 `DosageSingle.extract`、`DosageDoseSpecies.extract` 等）来处理不同类型的试验数据。
    参数：
    :param body: 文档的主体内容，包含多个段落和表格元素（BaseOxmlElement类型）。
    :param trial_subtype: 当前试验的子类型，类型为TrialSubType，决定给药剂量提取的逻辑。
    :param file_path: 文档的文件路径，用于处理微核测试时的文件内容。
    返回：
    :return: TrialResultTask对象，包含提取的给药剂量信息（`administration_dosage`）。
    """
    administration_dosage: str = ''
    animal_p_element: BaseOxmlElement | None = None
    ames_p_element: BaseOxmlElement | None = None
    ames_table_element: BaseOxmlElement | None = None
    chromosome_p_element: BaseOxmlElement | None = None

    for element in body:
        if WordType.get_type(element) == ElementType.PARAGRAPH:
            p_element: BaseOxmlElement = element
            p_text: str = get_text_from_element(p_element)
            # 给药剂量 - 单次给药
            # 给药剂量 - 多次给药 - 关键
            # 给药剂量 - 多次给药 - 非关键
            if '正文表' in p_text and '试验设计' in p_text and animal_p_element is None:
                # 规避其余干扰段落
                if p_text.index('正文表') < p_text.index('试验设计'):
                    animal_p_element = p_element
            # 另一种描述词
            if '文本表' in p_text and '试验设计' in p_text and animal_p_element is None:
                if p_text.index('文本表') < p_text.index('试验设计'):
                    animal_p_element = p_element
            # 给药剂量 - 回复
            if '表' in p_text and '细菌回复突变主试验的剂量水平' in p_text and ames_p_element is None:
                if p_text.index('表') < p_text.index('细菌回复突变主试验的剂量水平'):
                    ames_p_element = p_element
            # 给药周期 - 染色体
            if '表' in p_text and '分析染色体畸变浓度' in p_text and ames_p_element is None:
                if p_text.index('表') < p_text.index('分析染色体畸变浓度'):
                    chromosome_p_element = p_element

    # 另一种描述词
    if animal_p_element is None:
        for element in body:
            if WordType.get_type(element) == ElementType.PARAGRAPH:
                p_element: BaseOxmlElement = element
                p_text: str = get_text_from_element(p_element)
                if '表' in p_text and '剂量设计' in p_text and animal_p_element is None:
                    if p_text.index('表') < p_text.index('剂量设计'):
                        animal_p_element = p_element

    # 防御原作者把表头单独放在表格内，导致无法识别，这里做特殊处理
    if ames_table_element is None and trial_subtype == TrialSubType.AMES_TEST:
        tables: list[BaseOxmlElement] = body.findall('.//w:tbl', namespaces=NAME_SPACES)
        for table in tables:
            tc_element_list: list[BaseOxmlElement] = table.findall('.//w:tc', namespaces=NAME_SPACES)
            for tc_element in tc_element_list:
                tc_text: str = get_text_from_element(tc_element)
                if '细菌回复突变主试验的剂量水平' in tc_text:
                    ames_table_element = table

    # 给药周期 - 微核
    if trial_subtype == TrialSubType.MICRONUCLEUS_TEST:
        gpt_text: str = '以下是正文内容：\n\n'
        fulltext_list: list[ChunkHeadlineData] = await chunk_word_process(
            file_path, TrialSubType.MICRONUCLEUS_TEST.value
        )
        for fulltext in fulltext_list:
            if fulltext.name == '概要' and fulltext.article:
                gpt_text += ' \n '.join(fulltext.article)
                gpt_text += '以下是表格内容：\n\n'
                for table in fulltext.tables:
                    gpt_text += '表头：\n'
                    gpt_text += ' \n '.join(table.header)
                    gpt_text += '表格：\n'
                    gpt_text += ' \n '.join(table.content)
                    gpt_text += ' \n '.join(table.footer)
                administration_dosage = await DosageMicronucleus.extract(gpt_text)
                return TrialResultTask(task='administration_dosage', value=administration_dosage)

    # 给药剂量 - 单次给药
    # 给药剂量 - 多次给药 - 关键
    # 给药剂量 - 多次给药 - 非关键
    # 给药剂量 - 回复
    # 给药周期 - 染色体
    table_text_list: list[str] = []
    table_element: BaseOxmlElement | None = None
    if trial_subtype.value < 4 and animal_p_element is not None:
        table_element = animal_p_element.getnext()
    if trial_subtype == TrialSubType.AMES_TEST and ames_p_element is not None:
        table_element = ames_p_element.getnext()
    if trial_subtype == TrialSubType.AMES_TEST and ames_table_element is not None:
        table_element = ames_table_element
    if trial_subtype == TrialSubType.CHROMOSOME_ABERRATION_TEST and chromosome_p_element is not None:
        table_element = chromosome_p_element.getnext()
    if table_element is not None:
        if WordType.get_type(table_element) == ElementType.TABLE:
            # markdown格式
            tr_element_list: list[BaseOxmlElement] = table_element.findall('.//w:tr', namespaces=NAME_SPACES)
            for tr_element in tr_element_list:
                tr_text: str = '| '
                tc_element_list: list[BaseOxmlElement] = tr_element.findall('.//w:tc', namespaces=NAME_SPACES)
                for tc_index, tc_element in enumerate(tc_element_list):
                    # 发现存在列合并标识符，则直接往右扩展
                    # 列被合并tc不会被保留
                    tc_grid_span: BaseOxmlElement = tc_element.find('.//w:gridSpan', namespaces=NAME_SPACES)
                    grid_span_len: int = 1
                    if tc_grid_span is not None:
                        grid_span_len = int(tc_grid_span.get(f'{{{NAME_SPACES["w"]}}}val', 1))

                    # 发现存在行合并标识符<w:vMerge />，往上检索查找最近的行合并标识符<w:vMerge w:val="restart" />
                    # 行被合并tc会保留
                    tc_v_merge: BaseOxmlElement = tc_element.find('.//w:vMerge', namespaces=NAME_SPACES)

                    # 段落文本
                    tc_text: str = ''

                    # 发现行合并标识符
                    if tc_v_merge is not None:
                        tc_v_merge_value: str = tc_v_merge.get(f'{{{NAME_SPACES["w"]}}}val', default=None)
                        # 这是初始的第一个合并标识符
                        if tc_v_merge_value == 'restart':
                            tc_text = get_text_from_element(tc_element)
                        # 这是初始的被合并标识符
                        if tc_v_merge_value != 'restart':
                            tc_text = chunk_find_previous_tr_merged_text(tr_element, tc_index)
                    # 未发现行合并现象，按照正常处理
                    if tc_v_merge is None:
                        tc_text: str = get_text_from_element(tc_element)

                    # 单元格文本追加至行文本
                    row_cells: list[str] = [f' {tc_text} ']
                    # 合并列处理 追加至行文本
                    row_cells.extend([f' {tc_text} '] * (grid_span_len - 1))
                    # 拼接所有列
                    tr_text += '|'.join(row_cells) + ' |'
                table_text_list.append(f'{tr_text}\n')
        # 处理表格数据
        if len(table_text_list) != 0:
            table_text: str = ''.join(table_text_list)
            if trial_subtype == TrialSubType.SINGLE_DOSE:
                # 给药剂量 - 单次给药
                administration_dosage = await DosageSingle.extract(table_text)
            elif trial_subtype in (TrialSubType.REPEATED_DOSE_SPECIFICITY, TrialSubType.REPEATED_DOSE):
                # 给药剂量 - 多次给药 - 关键/非关键
                administration_dosage = await DosageDoseSpecies.extract(table_text)
            elif trial_subtype == TrialSubType.AMES_TEST:
                # 给药剂量 - 回复突变
                administration_dosage = await DosageAmes.extract(table_text)
            elif trial_subtype == TrialSubType.CHROMOSOME_ABERRATION_TEST:
                # 给药剂量 - 染色体
                administration_dosage = await DosageChromosome.extract(table_text)

    return TrialResultTask(task='administration_dosage', value=administration_dosage)


TEST_PRODUCT_PATTERN = compile(r'受试物[:：]\s*([^\s]+)')


def get_test_product(body) -> str:
    """
    从给定的文档片段中提取受试物的信息。
    参数:
    :param body: 文档片段列表，包含多个元素，每个元素代表文档的一部分。
    返回:
    :return str: 提取的受试物信息作为字符串返回。如果没有找到受试物信息，则返回空字符串。
    """
    for element in body:
        if WordType.get_type(element) == ElementType.PARAGRAPH:
            p_text: str = get_text_from_element(element)
            match = TEST_PRODUCT_PATTERN.search(p_text)
            if match:
                return match.group(1)
    return '-'


def get_applicant() -> str:
    """
    获取委托方的标识。
    当前第一第二批的单次给药文档都叫委托方 & 提取规则复杂无规律，则直接使用委托方，后续有变更再改此处逻辑
    返回:
    :return str: 委托方的标识，固定返回'委托方'。
    """
    return '委托方'
