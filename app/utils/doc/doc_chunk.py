import asyncio
import io
import xml.etree.ElementTree as ET
import zipfile
from concurrent.futures import ProcessPoolExecutor

from docx.opc.oxml import BaseOxmlElement
from lxml import etree

from app.schemas.doc_base_field import TrialSubType
from app.schemas.doc_chunk import (
    HEADLINE_TEXT_SIGN,
    ChunkCoverData,
    ChunkHeadlineData,
    ChunkTableData,
    Table,
)
from app.schemas.table import TableRows
from app.utils.doc.doc_type import NAME_SPACES, ElementType, WordType
from app.utils.file_to_stream import file_to_stream
from app.utils.uuid import generate_uuid

MAX_TABLE_HEADER_LENGTH = 100


async def chunk_word_process(
    file_path: str, trial_subtype: int, online_file_path=''
) -> tuple[list[ChunkHeadlineData], ChunkCoverData, str]:
    """
    根据文件路径处理文档分块。
    参数:
    file_path (str): 本地文件路径。
    trial_subtype (int): 文档子类型
    online_file_path (str): 在线文件路径。
    返回:
    tuple[list[ChunkHeadlineData],str]: 包含分块标题数据的列表和目录信息。
    异常:
    如果文件路径为None，则抛出BadRequestError异常。
    """
    if file_path is None:
        raise FileNotFoundError('文件未找到')

    path: str = online_file_path if online_file_path else file_path

    # 获取当前事件循环
    loop = asyncio.get_running_loop()

    # 使用 ProcessPoolExecutor 运行子进程
    with ProcessPoolExecutor() as executor:
        result = await loop.run_in_executor(executor, chunk_process_document, path, trial_subtype)

    return result


def chunk_process_document(file_path: str, trial_subtype: int) -> tuple[list[ChunkHeadlineData], ChunkCoverData, str]:
    """
    定义一个异步函数 chunk_process_document，用于处理文档并返回分块的数据
    参数:
    data: str - 文件的路径
    trial_subtype: int - 文档子类型
    返回:
    tuple[list[ChunkHeadlineData], ChunkCoverData, str] - 树形结构关系的文档数据列表。
    """
    file_bytes_io: io.BytesIO = file_to_stream(file_path)
    # 读取 ZIP 文件中的 document.xml
    with zipfile.ZipFile(file_bytes_io, 'r') as zip_ref:
        document_xml = zip_ref.read('word/document.xml')
    tree = etree.XML(document_xml)
    body = tree.find('.//w:body', namespaces=NAME_SPACES)
    # 获取标题信息 - 即为携带了自动编号的段落
    title_data: list[ChunkHeadlineData] = chunk_get_auto_number_info_main(file_bytes_io, body)
    # 封面数据
    cover_data: ChunkCoverData = chunk_cover(body, title_data[0].name)
    # 切片数据
    result = chunk_paragraph(file_bytes_io, body, title_data, trial_subtype)
    # 设置唯一ID
    result = chunk_find_parent_node(result)
    # 目录数据
    table_of_contents: str = chunk_table_of_contents(body)
    # 返回数据
    return (result, cover_data, table_of_contents)


def chunk_table_of_contents(body: BaseOxmlElement) -> str:
    """
    从文档的主体中提取目录段落。
    参数:
    body (BaseOxmlElement): 文档的主体元素。
    返回:
    str: 包含目录段落的列表。
    """
    table_of_contents: str = ''

    for element in body:
        p_text: str = ''
        if WordType.get_type(element) == ElementType.PARAGRAPH:
            r_list: list[BaseOxmlElement] = element.findall('.//w:r', namespaces=NAME_SPACES)
            for r in r_list:
                br: BaseOxmlElement = r.find('.//w:br', namespaces=NAME_SPACES)
                if br is not None:
                    p_text += '\v'
                    continue
                t_list: list[BaseOxmlElement] = r.findall('.//w:t', namespaces=NAME_SPACES)
                for t in t_list:
                    if t.text.strip():  # 仅在文本不为空时添加
                        p_text += t.text

        if '目录' in p_text:
            break
        elif p_text.strip():  # 仅在p_text不为空时添加
            table_of_contents += f'{p_text}\n'

    return table_of_contents


def chunk_get_auto_number_info_main(file_bytes_io: io.BytesIO, body: BaseOxmlElement) -> list[ChunkHeadlineData]:
    """
    从给定的Word文档（通过BytesIO传入）中提取章节标题及其编号信息。
    该函数通过解析Word文档中的样式文件和编号文件，收集所有符合条件的标题段落信息。特别地，它会解析包含编号信息的段落，提取这些段落的文本和层级（`outlineLvl`），并返回包含标题及其层级信息的列表。
    参数：
    :param file_bytes_io: Word文档的字节流（io.BytesIO对象），用于从中提取内容。
    :param body: Word文档中的主内容部分（BaseOxmlElement），包含段落、表格等元素。
    返回：
    :return: 包含所有标题段落数据的列表，每个元素是 `ChunkHeadlineData` 对象，包含标题文本及其层级（`outlineLvl`）。
    """
    if body is None or file_bytes_io is None:
        return []

    # 标题数据
    headline_data_list: list[ChunkHeadlineData] = []

    # 打开编号与样式信息文件
    # with打开文件，自动资源管理，保证文件对象会在块的末尾自动关闭
    with zipfile.ZipFile(file_bytes_io) as docx_zip:
        if 'word/numbering.xml' not in docx_zip.namelist():
            return []
        if 'word/styles.xml' not in docx_zip.namelist():
            return []

        with docx_zip.open('word/styles.xml') as styles_file:
            styles_tree: ET.ElementTree = ET.parse(styles_file)
            styles_root: BaseOxmlElement = styles_tree.getroot()

            # 查找段落数据
            for element in body:
                if WordType.get_type(element) == ElementType.PARAGRAPH:
                    p: BaseOxmlElement = element
                    p_text: str = get_text_from_element(p)
                    # 去除附录和附表
                    if '附录' in p_text or '附表' in p_text:
                        continue
                    # 获取目录相关的段落的目录属性
                    hyperlink: BaseOxmlElement = p.find('.//w:hyperlink', namespaces=NAME_SPACES)
                    hyperlink_anchor: str | None = (
                        hyperlink.get(f'{{{NAME_SPACES["w"]}}}anchor') if hyperlink is not None else None
                    )
                    is_catalogue: bool = hyperlink_anchor.lower().find('toc') != -1 if hyperlink is not None else False
                    # 排除目录结构的段落
                    if is_catalogue:
                        continue
                    # 获取段落中的样式描述标签
                    p_pr = p.find('.//w:pPr', namespaces=NAME_SPACES)
                    if p_pr is not None:
                        # 获取段落中的样式标签
                        p_style: BaseOxmlElement = p_pr.find('.//w:pStyle', namespaces=NAME_SPACES)
                        # 返回结果
                        chunk_headline_data: ChunkHeadlineData | None = None
                        # 查找视窗元素
                        outline_lvl = p_pr.find('.//w:outlineLvl', namespaces=NAME_SPACES)
                        if outline_lvl is not None:
                            level: str = outline_lvl.get(f'{{{NAME_SPACES["w"]}}}val')
                            chunk_headline_data = ChunkHeadlineData(name=p_text, outline_lvl=level)
                        # document未携带视窗信息
                        if outline_lvl is None and p_style is not None:
                            style_id: str = p_style.get(f'{{{NAME_SPACES["w"]}}}val')
                            # 排除空异常
                            if style_id is not None:
                                style_element: BaseOxmlElement = styles_root.find(
                                    f".//w:style[@w:styleId='{style_id}']", NAME_SPACES
                                )
                                # 查找视窗元素
                                outline_lvl = style_element.find('.//w:outlineLvl', namespaces=NAME_SPACES)
                                if outline_lvl is not None:
                                    level: str = outline_lvl.get(f'{{{NAME_SPACES["w"]}}}val')
                                    chunk_headline_data = ChunkHeadlineData(name=p_text, outline_lvl=level)
                        # 收集数据
                        if chunk_headline_data is not None:
                            headline_data_list.append(chunk_headline_data)

    # 过滤空数据
    headline_data_list = [item for item in headline_data_list if item.name]
    return headline_data_list


def chunk_paragraph(
    file_bytes_io: io.BytesIO, body: BaseOxmlElement, title_data: list[ChunkHeadlineData], trial_subtype: int
) -> list[ChunkHeadlineData]:
    """
    切分段落函数，主要用于处理给定文档中的段落切分逻辑。

    参数:
    - file_bytes_io: 文件的字节流对象，用于读取文档文件。
    - body: 文档的主体部分，作为Open XML元素处理。
    - title_data: 包含段落标题数据的列表，用于存储和处理段落切分的相关信息。
    - trial_subtype: 文档子类型。

    返回:
    - 切分后的段落标题数据列表。
    """

    # 打开样式信息文件
    # with打开文件，自动资源管理，保证文件对象会在块的末尾自动关闭
    with zipfile.ZipFile(file_bytes_io) as docx_zip:
        if 'word/styles.xml' not in docx_zip.namelist():
            return []

        with docx_zip.open('word/styles.xml') as styles_file:
            styles_tree: ET.ElementTree = ET.parse(styles_file)
            styles_root: BaseOxmlElement = styles_tree.getroot()

            chunk_paragraph_headline_main(body, title_data, styles_root, trial_subtype)

    # 移除全空的数组
    distance_list: list[ChunkHeadlineData] = []
    for item in title_data:
        if item.article is not None or item.tables:
            distance_list.append(item)

    return distance_list


def chunk_cover(body: BaseOxmlElement, title_first: str) -> ChunkCoverData:
    """
    定义一个函数 chunk_cover，用于处理文档并返回封面的数据(收集第一个章节前的文本/表格为目录，第一个章节之后的数据存储于chunk中)
    参数:
    body: BaseOxmlElement - 文档
    title_first: str - 第一个章节标题数据
    返回:
    ChunkCoverData - 封面数据
    """
    data: ChunkCoverData = ChunkCoverData()

    for element in body:
        if WordType.get_type(element) == ElementType.PARAGRAPH:
            p_text: str = get_text_from_element(element)
            if p_text == title_first:
                break
            if not p_text.strip():
                continue
            data.paragraph_list.append(p_text)
        if WordType.get_type(element) == ElementType.TABLE:
            table_data: Table = []
            table_data_md: list[str] = []
            tr_element_list: list[BaseOxmlElement] = element.findall('.//w:tr', namespaces=NAME_SPACES)
            for tr_element in tr_element_list:
                row_data: list[str] = []
                row_data_md: str = '| '
                tc_element_list: list[BaseOxmlElement] = tr_element.findall('w:tc', namespaces=NAME_SPACES)
                for tc_element in tc_element_list:
                    tc_text: str = chunk_get_text_form_tc(tc_element)
                    if not tc_text.strip():
                        continue
                    row_data.append(tc_text)
                    row_data_md += f'{tc_text} | '
                table_data.append(row_data)
                table_data_md.append(row_data_md)
            data.tables.append(table_data)
            data.tables_md.append(table_data_md)

    return data


def chunk_paragraph_headline_main(
    body: BaseOxmlElement, title_data: list[ChunkHeadlineData], styles_root: BaseOxmlElement, trial_subtype
) -> None:
    """
    切分文档段落、标题和表格内容。

    该方法用于从一个Word文档中提取特定标题下的段落和表格内容。它通过查找标题的起始和结束标记，
    然后遍历这些标记之间的所有元素，将它们分类为文章内容或表格数据。

    :param body: 文档的主体部分，包含所有段落和表格，类型为BaseOxmlElement。
    :param title_data: 包含标题信息的列表，每个元素为ChunkHeadlineData类型，包含标题的链接ID等。
    :param styles_root: 文档的样式部分，包含所有样式定义，类型为BaseOxmlElement。
    :param trial_subtype: 文档子类型。
    :return: None，直接修改传入的title_data列表中的table和article属性。
    """

    # 处理主数据
    for index, item in enumerate(title_data):
        matching_p: BaseOxmlElement | None = None
        next_p_name: str = title_data[index + 1].name if index < len(title_data) - 1 else ''
        article: list[str] = []
        tables: list[ChunkTableData] = []
        temp_table: ChunkTableData = ChunkTableData()

        matching_p_elements: list[BaseOxmlElement] = []

        # 去除附录和附表(类型非遗传毒性的不获取附录)
        if trial_subtype <= TrialSubType.REPEATED_DOSE.value:
            if '附录' in item.name or '附表' in item.name or '参考文献' in item.name:
                continue

        for element in body:
            if WordType.get_type(element) == ElementType.PARAGRAPH:
                element_text: str = get_text_from_element(element)
                if element_text == item.name:
                    matching_p_elements.append(element)

        current_name_index = -1
        for temp_i, temp in enumerate(title_data):
            if temp.name == item.name:
                current_name_index += 1
            if temp_i == index:
                break

        if current_name_index >= len(matching_p_elements):
            continue

        matching_p = matching_p_elements[current_name_index]

        if matching_p is None:
            continue

        current_element: BaseOxmlElement | None = matching_p
        article_style_id: str = ''
        while True:
            current_element = current_element.getnext()
            # 如果到达文档尾部，则跳出循环
            if current_element is None:
                break

            word_type: ElementType = WordType.get_type(current_element)
            # 段落类型
            if word_type == ElementType.PARAGRAPH:
                # 如果是结束符的段落，则跳出循环
                p_text: str = get_text_from_element(current_element)
                if next_p_name == p_text and next_p_name != '':
                    break

                # 如果空白段落
                if len(p_text.strip()) == 0:
                    # 如果当前有表格信息，则先推送表格信息，清空当前临时的表格信息
                    if temp_table.content != '':
                        tables.append(temp_table)
                        temp_table = ChunkTableData()
                    # 如果空白段落，跳出当前循环
                    continue

                # P样式中判断
                jc: bool = current_element.find(".//w:jc[@w:val='center']", NAME_SPACES) is not None
                b: bool = current_element.find('.//w:b', NAME_SPACES) is not None
                # 内部样式判断
                jc_inner: bool = False
                b_inner: bool = False
                # P映射的内层样式文件中判断
                p_style: BaseOxmlElement | None = current_element.find('.//w:pStyle', NAME_SPACES)
                if p_style is not None:
                    p_style_id: str = p_style.get(f'{{{NAME_SPACES["w"]}}}val', '')
                    if p_style_id:
                        # 打开样式文件进行深层级的搜索
                        w_style: BaseOxmlElement = styles_root.find(
                            f".//w:style[@w:styleId='{p_style_id}']", NAME_SPACES
                        )
                        if w_style is not None:
                            jc_inner: bool = w_style.find(".//w:jc[@w:val='center']", NAME_SPACES) is not None
                            b_inner: bool = w_style.find('.//w:b', NAME_SPACES) is not None
                else:
                    p_style_id = ''

                is_headline: bool = len(p_text) < MAX_TABLE_HEADER_LENGTH  # 表头的长度不应该超过100
                if is_headline:
                    # 外部样式文件中判断
                    is_bold: bool = b or b_inner
                    is_align_center: bool = jc or jc_inner
                    # 如果段落存在文本
                    is_text_with_keyword: bool = HEADLINE_TEXT_SIGN in p_text
                    # 是否包含句号
                    not_contains_period: bool = '。' not in p_text

                    # 当前段落是否为标准的表头段落，至少 3 个条件为True
                    is_headline = (is_bold, is_align_center, is_text_with_keyword, not_contains_period).count(True) >= 3

                    # 是标准表头文本(加粗 + 居中 + 关键字'表')
                    if is_headline:
                        # 当前已经存在了表格，追加到表格信息中，并清空当前临时的表格信息
                        if temp_table.content:
                            tables.append(temp_table)
                            temp_table = ChunkTableData()
                        temp_table.header.append(p_text)
                        continue

                # 不是表头文本
                # 当前如果已经有表格信息信息，可能是判断为表尾文本
                if temp_table.content:
                    # 有样式，且样式不是正文样式，或设置了不同的字体，则判断为表尾文本
                    if (p_style_id and p_style_id != article_style_id) or current_element.findall(
                        './/w:sz', NAME_SPACES
                    ):
                        temp_table.footer.append(p_text)
                    else:  # 否则判断为正文
                        article.append(p_text)
                        # 已添加正文，说明当前表格已结束
                        tables.append(temp_table)
                        temp_table = ChunkTableData()
                # 当前如果没有表格信息，但是已经存在了表头信息，则当前段落为处于表头文本与表格之间的段落
                elif temp_table.header:
                    temp_table.header.append(p_text)
                # 当前未收集到表头信息与表格信息，则判断为普通段落
                else:
                    article.append(p_text)
                    article_style_id = p_style_id
                continue

            # 表格类型
            elif word_type == ElementType.TABLE:
                tr_element_list: list[BaseOxmlElement] = current_element.findall('.//w:tr', namespaces=NAME_SPACES)

                if temp_table.content:  # 上一个表格需要结束
                    tables.append(temp_table)
                    temp_table = ChunkTableData()

                if temp_table.header:
                    header = '\n'.join(temp_table.header)
                    if len(header) > MAX_TABLE_HEADER_LENGTH:  # 表头长度不应该超过100
                        temp_table.header = [header[:MAX_TABLE_HEADER_LENGTH]]
                # 如果没有表格标题，尝试获取表格上面的第一个段落的文本当做表头
                else:
                    header = get_text_from_element(current_element.getprevious())
                    if len(header) < MAX_TABLE_HEADER_LENGTH:  # 表头长度不应该超过100
                        temp_table.header = [header]

                # 预处理列合并
                for tr_element in tr_element_list:
                    tc_element_list: list[BaseOxmlElement] = tr_element.findall('w:tc', namespaces=NAME_SPACES)
                    tc_list_new: list[BaseOxmlElement] = []
                    for tc_index, tc_element in enumerate(tc_element_list):
                        # 发现存在列合并标识符，则直接往右扩展
                        # 列被合并tc不会被保留
                        tc_grid_span: BaseOxmlElement = tc_element.find('.//w:gridSpan', namespaces=NAME_SPACES)
                        grid_span_len: int = (
                            int(tc_grid_span.get(f'{{{NAME_SPACES["w"]}}}val')) if tc_grid_span is not None else 1
                        )
                        if grid_span_len > 1:
                            # 复制内容到后续合并的单元格
                            for _ in range(grid_span_len):
                                tc: BaseOxmlElement = etree.Element('{%s}tc' % NAME_SPACES['w'])
                                p = etree.SubElement(tc, '{%s}p' % NAME_SPACES['w'])
                                r = etree.SubElement(p, '{%s}r' % NAME_SPACES['w'])
                                t = etree.SubElement(r, '{%s}t' % NAME_SPACES['w'])
                                t.text = get_text_from_element(tc_element)
                                tc_list_new.append(tc)
                        else:
                            tc_list_new.append(tc_element)

                    for tc in tc_element_list:
                        tr_element.remove(tc)
                    for new_tc in tc_list_new:
                        tr_element.append(new_tc)

                for tr_element in tr_element_list:
                    tr_text: str = '| '
                    tc_element_list: list[BaseOxmlElement] = tr_element.findall('.//w:tc', namespaces=NAME_SPACES)
                    for tc_index, tc_element in enumerate(tc_element_list):
                        # 发现存在行合并标识符<w:vMerge />，往上检索查找最近的行合并标识符<w:vMerge w:val="restart" />
                        # 行被合并tc会保留
                        tc_v_merge: BaseOxmlElement = tc_element.find('.//w:vMerge', namespaces=NAME_SPACES)

                        # 段落文本
                        tc_text: str = ''

                        # 发现行合并标识符
                        if tc_v_merge is not None:
                            tc_v_merge_value: str = tc_v_merge.get(f'{{{NAME_SPACES["w"]}}}val')
                            # 这是初始的第一个合并标识符
                            if tc_v_merge_value == 'restart':
                                tc_text = chunk_get_text_form_tc(tc_element)
                            # 这是初始的被合并标识符
                            if tc_v_merge_value != 'restart':
                                tc_text = chunk_find_previous_tr_merged_text(tr_element, tc_index)
                        # 未发现行合并现象，按照正常处理
                        if tc_v_merge is None:
                            tc_text = chunk_get_text_form_tc(tc_element)

                        # 单元格文本追加至行文本
                        tr_text += f'{tc_text} | '
                    temp_table.content += f'{tr_text}\n'

                # 数组格式
                content_array: TableRows = []
                for tr_element in tr_element_list:
                    tr_list: list[str] = []
                    tc_element_list: list[BaseOxmlElement] = tr_element.findall('.//w:tc', namespaces=NAME_SPACES)
                    for tc_element in tc_element_list:
                        tr_list.append(get_text_from_element(tc_element))
                    content_array.append(tr_list)
                temp_table.content_array = content_array

        # 最后结尾的表格信息推送至结果列表
        if temp_table.content != '':
            tables.append(temp_table)

        # 更新原始数据
        item.tables = tables
        item.article = article

        # 去除附录和附表(类型非遗传毒性的不获取附录)
        if trial_subtype <= TrialSubType.REPEATED_DOSE.value:
            # 预处理数据，清除为空的Table和Article
            references_index: int | None = None
            for index, item in enumerate(title_data):
                if item is None:
                    continue
                if item.name in ('参考文献', '附表'):
                    references_index = index

            if references_index is not None:
                del title_data[references_index + 1 :]


def get_text_from_element(element: BaseOxmlElement) -> str:
    """
    从给定的元素对象中提取文本内容。
    参数:
    p: BaseOxmlElement - 代表的Open XML元素。
    返回:
    str - 提取的文本内容，如果没有有效的文本则返回空字符串。
    """
    texts: list[str] = []
    r_list: list[BaseOxmlElement] = element.findall('.//w:r', namespaces=NAME_SPACES)
    for r in r_list:
        t_text = r.find('.//w:t', namespaces=NAME_SPACES)
        if t_text is not None:
            texts.append(t_text.text)
    return ''.join(texts)


def get_text_from_run(element: BaseOxmlElement) -> str:
    """
    从给定的元素对象中提取文本内容。
    参数:
    p: BaseOxmlElement - 代表的Open XML元素。
    返回:
    str - 提取的文本内容，如果没有有效的文本则返回空字符串。
    """
    texts: list[str] = []
    t_list: list[BaseOxmlElement] = element.findall('.//w:t', namespaces=NAME_SPACES)
    for t in t_list:
        texts.append(t.text)
    return ''.join(texts)


def chunk_find_previous_tr_merged_text(current_tr: BaseOxmlElement, tc_index_origin: int) -> str:
    """
    查找与当前表格行（tr）合并的前一个表格行中的文本。

    当前函数旨在处理表格中单元格合并的情况，特别是当一个合并单元格跨越多行时，
    需要找到与当前行合并的前一个单元格中的文本内容。

    :param current_tr: 当前的表格行元素，类型为BaseOxmlElement。
    :param tc_index_origin: 当前需要查找文本的单元格索引。
    :return: 返回找到的文本字符串，如果没有找到则返回空字符串。
    """
    tr: BaseOxmlElement = current_tr
    while tr is not None:
        tr = tr.getprevious()
        # 如果循环到了空元素，则直接跳出
        if tr is None:
            return ''
        # 跳过插在中间的其他元素
        if WordType.get_type(tr) != ElementType.TABLE_ROW:
            continue
        tc_element_list: list[BaseOxmlElement] = tr.findall('.//w:tc', namespaces=NAME_SPACES)

        # 收集原始merge的行的被合并的列的数量，给后续的表格进行减
        new_tc_index_origin = tc_index_origin
        # 本逻辑只在当前单元格左侧有列合并的时候执行
        left_has_merge: bool = False
        tc_current_list: list[BaseOxmlElement] = current_tr.findall('.//w:tc', namespaces=NAME_SPACES)
        for i in range(tc_index_origin):
            tc_grid_span: BaseOxmlElement = tc_current_list[i].find('.//w:gridSpan', namespaces=NAME_SPACES)
            grid_span_len: int = (
                (int(tc_grid_span.get(f'{{{NAME_SPACES["w"]}}}val')) - 1) if tc_grid_span is not None else 0
            )
            if grid_span_len > 0:
                left_has_merge = True

        if left_has_merge:
            for tc_index, tc_element in enumerate(tc_element_list):
                tc_grid_span: BaseOxmlElement = tc_element.find('.//w:gridSpan', namespaces=NAME_SPACES)
                grid_span_len: int = (
                    (int(tc_grid_span.get(f'{{{NAME_SPACES["w"]}}}val')) - 1) if tc_grid_span is not None else 0
                )
                new_tc_index_origin -= grid_span_len

        for tc_index, tc_element in enumerate(tc_element_list):
            # 收集本行的被合并的列的数量
            if new_tc_index_origin == tc_index:
                tc_v_merge: BaseOxmlElement = tc_element.find('.//w:vMerge', namespaces=NAME_SPACES)
                tc_v_merge_value: str = tc_v_merge.get(f'{{{NAME_SPACES["w"]}}}val') if tc_v_merge is not None else ''
                # 本行是第一个合并的初始行的单元格
                if tc_v_merge_value == 'restart':
                    tc_text: str = chunk_get_text_form_tc(tc_element)
                    return tc_text
                # else pass 本行还是被合并的行的单元格
    return ''


def chunk_get_text_form_tc(tc_element: BaseOxmlElement) -> str:
    """
    从表格单元格（tc）元素中提取文本内容。
    参数:
    tc_element (BaseOxmlElement): 表格单元格的XML元素，用于提取其中的文本内容。
    返回:
    str: 表格单元格中的所有文本内容，以字符串形式返回。
    """
    tc_text: list[str] = []
    p_list: list[BaseOxmlElement] = tc_element.findall('.//w:p', namespaces=NAME_SPACES)
    for p in p_list:
        p_text: str = get_text_from_element(p)
        if p_text.strip():
            tc_text.append(p_text)

    return '<br>'.join(tc_text)


def chunk_find_parent_node(data: list[ChunkHeadlineData]) -> list[ChunkHeadlineData]:
    """
    查找父亲元素的数据。
    为每个文档标题数据生成唯一的UUID，并根据其大纲级别（outline_lvl）确定其父级关系，
    从而构建出一个树形结构的数据组织形式。
    参数:
    data: list[ChunkHeadlineData] - 一个包含多个文档标题数据的列表。
    返回:
    list[ChunkHeadlineData] - 经过处理后，具有树形结构关系的文档标题数据列表。
    """
    for item in data:
        item.uuid = generate_uuid()

    for index, item in enumerate(data):
        for j in range(index - 1, -1, -1):
            if int(str(data[j].outline_lvl)) < int(str(item.outline_lvl)):
                item.parent_uuid = data[j].uuid
                break
        if item.parent_uuid == '':
            item.parent_uuid = item.uuid

    return data
