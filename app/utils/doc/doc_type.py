import zipfile
from enum import Enum
from io import BytesIO

from docx.opc.oxml import BaseOxmlElement
from docx.oxml.ns import qn
from lxml import etree

CUSTOM_NAMESPACE = 'http://pureglobal.com/'
MC_NS = 'http://schemas.openxmlformats.org/markup-compatibility/2006'
CUST_PREFIX = 'pure'
NAME_SPACES = {
    'wpc': 'http://schemas.microsoft.com/office/word/2010/wordprocessingCanvas',
    'cx': 'http://schemas.microsoft.com/office/drawing/2014/chartex',
    'cx1': 'http://schemas.microsoft.com/office/drawing/2015/9/8/chartex',
    'cx2': 'http://schemas.microsoft.com/office/drawing/2015/10/21/chartex',
    'cx3': 'http://schemas.microsoft.com/office/drawing/2016/5/9/chartex',
    'cx4': 'http://schemas.microsoft.com/office/drawing/2016/5/10/chartex',
    'cx5': 'http://schemas.microsoft.com/office/drawing/2016/5/11/chartex',
    'cx6': 'http://schemas.microsoft.com/office/drawing/2016/5/12/chartex',
    'cx7': 'http://schemas.microsoft.com/office/drawing/2016/5/13/chartex',
    'cx8': 'http://schemas.microsoft.com/office/drawing/2016/5/14/chartex',
    'mc': 'http://schemas.openxmlformats.org/markup-compatibility/2006',
    'aink': 'http://schemas.microsoft.com/office/drawing/2016/ink',
    'am3d': 'http://schemas.microsoft.com/office/drawing/2017/model3d',
    'o': 'urn:schemas-microsoft-com:office:office',
    'oel': 'http://schemas.microsoft.com/office/2019/extlst',
    'r': 'http://schemas.openxmlformats.org/officeDocument/2006/relationships',
    'm': 'http://schemas.openxmlformats.org/officeDocument/2006/math',
    'v': 'urn:schemas-microsoft-com:vml',
    'wp14': 'http://schemas.microsoft.com/office/word/2010/wordprocessingDrawing',
    'wp': 'http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing',
    'w10': 'urn:schemas-microsoft-com:office:word',
    'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main',
    'w14': 'http://schemas.microsoft.com/office/word/2010/wordml',
    'w15': 'http://schemas.microsoft.com/office/word/2012/wordml',
    'w16cex': 'http://schemas.microsoft.com/office/word/2018/wordml/cex',
    'w16cid': 'http://schemas.microsoft.com/office/word/2016/wordml/cid',
    'w16': 'http://schemas.microsoft.com/office/word/2018/wordml',
    'w16du': 'http://schemas.microsoft.com/office/word/2023/wordml/word16du',
    'w16sdtdh': 'http://schemas.microsoft.com/office/word/2020/wordml/sdtdatahash',
    'w16se': 'http://schemas.microsoft.com/office/word/2015/wordml/symex',
    'wpg': 'http://schemas.microsoft.com/office/word/2010/wordprocessingGroup',
    'wpi': 'http://schemas.microsoft.com/office/word/2010/wordprocessingInk',
    'wne': 'http://schemas.microsoft.com/office/word/2006/wordml',
    'wps': 'http://schemas.microsoft.com/office/word/2010/wordprocessingShape',
    'a': 'http://schemas.openxmlformats.org/drawingml/2006/main',
    'rels': 'http://schemas.openxmlformats.org/package/2006/relationships',
    'a14': 'http://schemas.microsoft.com/office/drawing/2010/main',
    CUST_PREFIX: CUSTOM_NAMESPACE,
}


class ElementType(str, Enum):
    ID = "id"
    PARAGRAPH = "Paragraph"
    TABLE = "Table"
    TABLE_BORDER = "Table Border (tblBorders)"
    TABLE_BORDER_TOP = "Table Border (top)"
    TABLE_BORDER_LEFT = "Table Border (left)"
    TABLE_BORDER_RIGHT = "Table Border (right)"
    TABLE_BORDER_BOTTOM = "Table Border (bottom)"
    TABLE_GRID_SPAN = "Table Grid Span (gridSpan)"
    TABLE_V_MERGE = "Table Row Merge (vMerge)"
    TABLE_PR = "Table Properties (tblPr)"
    TABLE_GRID = "Table Grid (tblGrid)"
    TABLE_GRID_COL = "Grid Column (gridCol)"
    TABLE_STYLE = "Table Style (tblStyle)"
    TABLE_WIDTH = "Table Width (tblW)"
    TABLE_LOOK = "Table Look (tblLook)"
    TABLE_ROW = "Row (tr)"
    TABLE_ROW_PR = "Row (trPr)"
    TABLE_ROW_BORDER = "Table Row Merge (trBorders)"
    TABLE_CELL = "Cell (tc)"
    TABLE_CELL_PR = "Cell (tcPr)"
    TABLE_CELL_SHD = "Cell (shd)"
    TABLE_CELL_V_ALIGN = "Cell (vAlign)"
    TABLE_CELL_BORDER = "Table Cell Border (tcBorders)"
    CELL_WIDTH = "Table Cell Width (tcW)"
    PARAGRAPH_PROPERTIES = "Paragraph Properties (pPr)"
    PARAGRAPH_INT = "Paragraph Ind (ind)"
    PARAGRAPH_P_STYLE = "Paragraph Style (pStyle)"
    RUN = "Run (r)"
    RUN_STYLE = "Run (rStyle)"
    RUN_TEXT = "Text (t)"
    RUN_PROPERTIES = "Run (rPr)"
    RUN_FONTS = "Run Fonts (rFonts)"
    RUN_FONT_SIZE = "Run Font Size (sz)"
    RUN_ERROR = "Proofing Error (proofErr)"
    RUN_NO_ERROR = "Proofing Error (noProof)"
    RUN_B = "Run (b)"
    RUN_B_CS = "Run (bCs)"
    RUN_I = "Run (i)"
    RUN_I_CS = "Run (iCs)"
    RUN_VERT_ALIGN = "Run sup / sub (vertAlign)"
    TABS = "Tabs (tabs)"
    TAB = "Tab (tab)"
    WIDOW_CONTROL = "widowControl"
    PICTURE = "Picture"
    PICTURE_INLINE = "Picture (inline)"
    PICTURE_EXTENT = "Picture (extent)"
    PICTURE_EFFECT_EXTENT = "Picture (effectExtent)"
    PICTURE_DOC_PR = "Picture (docPr)"
    PICTURE_C_NV_GRAPHIC_FRAME_PR = "Picture (cNvGraphicFramePr)"
    PICTURE_GRAPHIC_FRAME_LOCKS = "Picture (graphicFrameLocks)"
    PICTURE_GRAPHIC = "Picture (graphic)"
    PICTURE_GRAPHIC_DATA = "Picture (graphicData)"
    PICTURE_PIC = "Picture (pic)"
    PICTURE_NV_PIC_PR = "Picture (nvPicPr)"
    PICTURE_C_NV_PR = "Picture (cNvPr)"
    PICTURE_C_NV_PIC_PR = "Picture (cNvPicPr)"
    PICTURE_PIC_LOCKS = "Picture (picLocks)"
    PICTURE_BLIP_FILL = "Picture (blipFill)"
    PICTURE_BLIP = "Picture (blip)"
    PICTURE_EXT_LST = "Picture (extLst)"
    PICTURE_EXT = "Picture (ext)"
    PICTURE_USE_LOCAL_DPI = "Picture (useLocalDpi)"
    PICTURE_SRC_RECT = "Picture (srcRect)"
    PICTURE_STRETCH = "Picture (stretch)"
    PICTURE_FILL_RECT = "Picture (fillRect)"
    PICTURE_SP_PR = "Picture (spPr)"
    PICTURE_X_FRM = "Picture (xfrm)"
    PICTURE_OFF = "Picture (off)"
    PICTURE_PRST_GEOM = "Picture (prstGeom)"
    PICTURE_AV_LST = "Picture (avLst)"
    PICTURE_NO_FILL = "Picture (noFill)"
    PICTURE_LN = "Picture (ln)"
    SHAPE = "Shape"
    RICH_TEXT_CONTENT_CONTROL = "Rich Text Content Control"
    PLAIN_TEXT_CONTENT_CONTROL = "Plain Text Content Control"
    PICTURE_CONTENT_CONTROL = "Picture Content Control"
    CHECKBOX_CONTENT_CONTROL = "Checkbox Content Control"
    CHECKBOX_CHECKED = "Checkbox Checked (checked)"
    CHECKBOX_UN_CHECKED_STATE = "Checkbox Un Checked State (uncheckedState)"
    CHECKBOX_CHECKED_STATE = "Checkbox Checked State (checkedState)"
    SDT_GROUP_CONTROL = "SDT Group Control"
    SDT_PROPERTIES = "Structured Document Tag Properties (sdtPr)"
    SDT_CONTENT = "Structured Document Tag Content (sdtContent)"
    SDT_PLACEHOLDER = "Structured Document placeholder (placeholder)"
    TAG = "Tag"
    FORM = "Form (ffData)"
    FORM_FLD_CHAR = "Form (fldChar)"
    FORM_NAME = "Form (name)"
    FORM_ENABLED = "Form (enabled)"
    FORM_CALCON_EXIT = "Form (calcOnExit)"
    FORM_INSTR_TEXT = "Form (instrText)"
    FORM_TEXT_INPUT = "Form TextInput Control (textInput)"
    FORM_CHECKBOX = "Form Checkbox Control (checkBox)"
    FORM_SIZE_AUTO = "Form Checkbox Control (sizeAuto)"
    FORM_DEFAULT = "Form Checkbox Control (default)"
    BOOK_MARK_START = "bookmarkStart (bookmarkStart)"
    BOOK_MARK_END = "bookmarkStart (bookmarkEnd)"
    SECTPR = "Section Properties (sectPr)"
    NUM_PR = "Numbering Properties (numPr)"
    ILVL = "ilvl"
    NUM_ID = "numId"
    PAGE_SIZE = "Page Size (pgSz)"
    PAGE_MARGIN = "Page Margin (pgMar)"
    PAGE_COLS = "Page Columns (cols)"
    PAGE_DOC_GRID = "Page doc grid (docGrid)"
    PAGE_DOC_PART = "Page doc part (docPart)"
    PAGE_SHOWING_PLC_HDR = "Page showing plc hdr "
    COMMENT_RANGE_START = "comment range start"
    COMMENT_RANGE_END = "comment range end"
    HYPER_LINK = "hyper link"
    UNKNOWN = "Unknown"


class WordType:
    # Define a class-level dictionary mapping tags to ElementType
    TAG_TO_ELEMENT_TYPE = {
        # ID
        qn('w:id'): ElementType.ID,
        # Paragraphs
        qn('w:p'): ElementType.PARAGRAPH,
        qn('w:pPr'): ElementType.PARAGRAPH_PROPERTIES,
        qn('w:rFonts'): ElementType.RUN_FONTS,
        qn('w:sc'): ElementType.RUN_FONT_SIZE,
        qn('w:ind'): ElementType.PARAGRAPH_INT,
        qn('w:pStyle'): ElementType.PARAGRAPH_P_STYLE,
        qn('w:r'): ElementType.RUN,
        qn('w:rStyle'): ElementType.RUN_STYLE,
        qn('w:t'): ElementType.RUN_TEXT,
        qn('w:rPr'): ElementType.RUN_PROPERTIES,
        qn('w:proofErr'): ElementType.RUN_ERROR,
        qn('w:noProof'): ElementType.RUN_NO_ERROR,
        qn('w:b'): ElementType.RUN_B,
        qn('w:bCs'): ElementType.RUN_B_CS,
        qn('w:i'): ElementType.RUN_I,
        qn('w:iCs'): ElementType.RUN_I_CS,
        qn('w:vertAlign'): ElementType.RUN_VERT_ALIGN,
        qn('w:tabs'): ElementType.TABS,
        qn('w:tab'): ElementType.TAB,
        qn('w:widowControl'): ElementType.WIDOW_CONTROL,
        # Tables
        qn('w:tbl'): ElementType.TABLE,
        qn('w:tblBorders'): ElementType.TABLE_BORDER,
        qn('w:top'): ElementType.TABLE_BORDER_TOP,
        qn('w:left'): ElementType.TABLE_BORDER_LEFT,
        qn('w:right'): ElementType.TABLE_BORDER_RIGHT,
        qn('w:bottom'): ElementType.TABLE_BORDER_BOTTOM,
        qn('w:gridSpan'): ElementType.TABLE_GRID_SPAN,
        qn('w:vMerge'): ElementType.TABLE_V_MERGE,
        qn('w:tblPr'): ElementType.TABLE_PR,
        qn('w:tblStyle'): ElementType.TABLE_STYLE,
        qn('w:tblW'): ElementType.TABLE_WIDTH,
        qn('w:tblLook'): ElementType.TABLE_LOOK,
        qn('w:tblGrid'): ElementType.TABLE_GRID,
        qn('w:gridCol'): ElementType.TABLE_GRID_COL,
        qn('w:tr'): ElementType.TABLE_ROW,
        qn('w:trPr'): ElementType.TABLE_ROW_PR,
        qn('w:tcBorders'): ElementType.TABLE_ROW_BORDER,  # Note: 'w:tcBorders' is used twice in original code
        qn('w:tc'): ElementType.TABLE_CELL,
        qn('w:tcPr'): ElementType.TABLE_CELL_PR,
        qn('w:shd'): ElementType.TABLE_CELL_SHD,
        qn('w:vAlign'): ElementType.TABLE_CELL_V_ALIGN,
        qn('w:tcBorders'): ElementType.TABLE_CELL_BORDER,  # Overwrites previous 'w:tcBorders'
        qn('w:tcW'): ElementType.CELL_WIDTH,
        # Pictures and Shapes
        qn('w:drawing'): ElementType.PICTURE,
        qn('w:pict'): ElementType.PICTURE,
        qn('w:picture'): ElementType.PICTURE_CONTENT_CONTROL,
        qn('wp:inline'): ElementType.PICTURE_INLINE,
        qn('wp:extent'): ElementType.PICTURE_EXTENT,
        qn('wp:effectExtent'): ElementType.PICTURE_EFFECT_EXTENT,
        qn('wp:docPr'): ElementType.PICTURE_DOC_PR,
        qn('wp:cNvGraphicFramePr'): ElementType.PICTURE_C_NV_GRAPHIC_FRAME_PR,
        qn('a:graphicFrameLocks'): ElementType.PICTURE_GRAPHIC_FRAME_LOCKS,
        qn('a:graphic'): ElementType.PICTURE_GRAPHIC,
        qn('a:graphicData'): ElementType.PICTURE_GRAPHIC_DATA,
        qn('pic:pic'): ElementType.PICTURE_PIC,
        qn('pic:nvPicPr'): ElementType.PICTURE_NV_PIC_PR,
        qn('pic:cNvPr'): ElementType.PICTURE_C_NV_PR,
        qn('pic:cNvPicPr'): ElementType.PICTURE_C_NV_PIC_PR,
        qn('a:picLocks'): ElementType.PICTURE_PIC_LOCKS,
        qn('pic:blipFill'): ElementType.PICTURE_BLIP_FILL,
        qn('a:blip'): ElementType.PICTURE_BLIP,
        qn('a:extLst'): ElementType.PICTURE_EXT_LST,
        qn('a:ext'): ElementType.PICTURE_EXT,
        f'{{{NAME_SPACES["a14"]}}}useLocalDpi': ElementType.PICTURE_USE_LOCAL_DPI,
        qn('a:srcRect'): ElementType.PICTURE_SRC_RECT,
        qn('a:stretch'): ElementType.PICTURE_STRETCH,
        qn('a:fillRect'): ElementType.PICTURE_FILL_RECT,
        qn('pic:spPr'): ElementType.PICTURE_SP_PR,
        qn('a:xfrm'): ElementType.PICTURE_X_FRM,  # Corrected to PICTURE_X_FRM
        qn('a:off'): ElementType.PICTURE_OFF,
        qn('a:prstGeom'): ElementType.PICTURE_PRST_GEOM,
        qn('a:avLst'): ElementType.PICTURE_AV_LST,
        qn('a:noFill'): ElementType.PICTURE_NO_FILL,
        qn('a:ln'): ElementType.PICTURE_LN,
        # Structured Document Tags (SDT)
        qn('w:sdt'): ElementType.SDT_GROUP_CONTROL,
        qn('w:placeholder'): ElementType.SDT_PLACEHOLDER,
        qn('w:tag'): ElementType.TAG,
        qn('w:sdtPr'): ElementType.SDT_PROPERTIES,
        qn('w:sdtContent'): ElementType.SDT_CONTENT,
        # Content Controls
        qn('w:richText'): ElementType.RICH_TEXT_CONTENT_CONTROL,
        qn('w:text'): ElementType.PLAIN_TEXT_CONTENT_CONTROL,
        qn('w14:checkbox'): ElementType.CHECKBOX_CONTENT_CONTROL,
        qn('w14:checked'): ElementType.CHECKBOX_CHECKED,
        qn('w14:uncheckedState'): ElementType.CHECKBOX_UN_CHECKED_STATE,
        qn('w14:checkedState'): ElementType.CHECKBOX_CHECKED_STATE,
        # Legacy Forms
        qn('w:ffData'): ElementType.FORM,
        qn('w:fldChar'): ElementType.FORM_FLD_CHAR,
        qn('w:name'): ElementType.FORM_NAME,
        qn('w:enabled'): ElementType.FORM_ENABLED,
        qn('w:calcOnExit'): ElementType.FORM_CALCON_EXIT,
        qn('w:instrText'): ElementType.FORM_INSTR_TEXT,
        qn('w:textInput'): ElementType.FORM_TEXT_INPUT,
        qn('w:checkBox'): ElementType.FORM_CHECKBOX,
        qn('w:sizeAuto'): ElementType.FORM_SIZE_AUTO,
        qn('w:default'): ElementType.FORM_DEFAULT,
        # Bookmarks
        qn('w:bookmarkStart'): ElementType.BOOK_MARK_START,
        qn('w:bookmarkEnd'): ElementType.BOOK_MARK_END,
        # Section Properties
        qn('w:sectPr'): ElementType.SECTPR,
        # Numbering
        qn('w:numPr'): ElementType.NUM_PR,
        qn('w:ilvl'): ElementType.ILVL,
        qn('w:numId'): ElementType.NUM_ID,
        # Page Settings
        qn('w:pgSz'): ElementType.PAGE_SIZE,
        qn('w:pgMar'): ElementType.PAGE_MARGIN,
        qn('w:cols'): ElementType.PAGE_COLS,
        qn('w:docGrid'): ElementType.PAGE_DOC_GRID,
        qn('w:docPart'): ElementType.PAGE_DOC_PART,
        qn('w:showingPlcHdr'): ElementType.PAGE_SHOWING_PLC_HDR,
        # Comments
        qn('w:commentRangeStart'): ElementType.COMMENT_RANGE_START,
        qn('w:commentRangeEnd'): ElementType.COMMENT_RANGE_END,
        # Hyperlinks
        qn('w:hyperlink'): ElementType.HYPER_LINK,
    }

    @staticmethod
    def get_type(element: BaseOxmlElement) -> ElementType:
        """
        Determine the ElementType based on the tag of the XML element.
        """
        element_type = WordType.TAG_TO_ELEMENT_TYPE.get(element.tag, ElementType.UNKNOWN)
        if element_type == ElementType.UNKNOWN:
            print(f"UNKNOWN TYPE {element.tag}")
        return element_type

    @staticmethod
    def modify_docx_with_custom_namespace(file_bytes_io: BytesIO) -> BytesIO:
        """
        修改 docx 文件中的 XML 内容，在根元素添加自定义命名空间，并设置 Ignorable 属性。
        该方法接受一个包含 docx 文件内容的 BytesIO 对象，解压文件并解析其中的 XML 文件，
        然后在 XML 的根元素上添加一个自定义的命名空间，并设置 `mc:Ignorable` 属性。最后，
        将修改后的 XML 内容重新打包到一个新的 docx 文件中，并返回新的 BytesIO 对象。
        参数:
            :param file_bytes_io: 包含原始 docx 文件内容的字节流。
        返回:
            :return BytesIO: 包含修改后 docx 文件内容的字节流。
        """
        # 解压缩 docx 文件
        with zipfile.ZipFile(file_bytes_io) as docx_zip:
            # 提取 word/document.xml 文件
            xml_content = docx_zip.read('word/document.xml')

        # 解析 XML 文件内容
        doc_root = etree.fromstring(xml_content)

        # 定义完整的 nsmap（包括自定义命名空间）
        nsmap = doc_root.nsmap.copy()
        nsmap[CUST_PREFIX] = CUSTOM_NAMESPACE

        # 将根元素重新创建为包含自定义命名空间
        new_root = etree.Element(doc_root.tag, nsmap=nsmap)

        # 将原始内容附加到新的根元素
        for child in doc_root:
            new_root.append(child)

        # 设置 mc:Ignorable 属性
        new_root.set(f'{{{MC_NS}}}Ignorable', CUST_PREFIX)

        # 修改后的 XML（可选）调试使用
        # etree.tostring(new_root, pretty_print=True).decode()

        # 将修改后的 XML 转回字节流
        modified_xml = etree.tostring(new_root, pretty_print=True, xml_declaration=True, encoding='UTF-8')

        # 创建新的 BytesIO 对象
        new_docx_bytes_io = BytesIO()

        # 重新打包为 docx 文件
        with zipfile.ZipFile(new_docx_bytes_io, 'w') as new_docx_zip:
            # 复制原始内容到新的 ZIP 文件
            with zipfile.ZipFile(file_bytes_io) as docx_zip:
                for item in docx_zip.infolist():
                    if item.filename == 'word/document.xml':
                        # 替换为修改后的 document.xml
                        new_docx_zip.writestr(item.filename, modified_xml)
                    else:
                        # 复制其他文件
                        new_docx_zip.writestr(item.filename, docx_zip.read(item.filename))

        # 确保将 BytesIO 文件指针重置为开头
        new_docx_bytes_io.seek(0)

        return new_docx_bytes_io
