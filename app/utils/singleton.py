from abc import ABCMeta
from typing import Any, TypeVar

T = TypeVar('T', bound='Singleton')


singleton_classes: list[type] = []


class Singleton(ABCMeta):
    """
    用来实现单例模式的元类。
    会占用使用该元类的 _instance 类属性作为唯一实例。
    因为 FastAPI 是单线程模型，所以这里不需要加锁。
    """

    def __call__(cls: type[T], *args: Any, **kwargs: Any) -> T:
        instance = getattr(cls, '_instance', None)
        if instance is None:
            instance = super().__call__(*args, **kwargs)
            setattr(cls, '_instance', instance)
            singleton_classes.append(cls)
        return instance
