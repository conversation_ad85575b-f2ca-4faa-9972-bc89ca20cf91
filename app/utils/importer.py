import importlib
import pkgutil


def auto_import(path: str):
    """
    自动导入指定路径下的所有模块。

    本函数通过遍历给定路径下的所有模块，并使用importlib.import_module方法将它们导入。
    这对于动态加载和管理模块非常有用，尤其是在需要在运行时自动发现和加载模块时。

    参数:
    path (str): 模块所在的文件路径。应该是目录路径，函数将自动查找此目录下的所有模块。

    返回:
    无
    """
    for _, name, _ in pkgutil.iter_modules([path]):
        importlib.import_module(f'{path.replace("/", ".")}.{name}')


def get_import_modules(path: str) -> list[str]:
    """
    获取指定路径下所有模块的导入路径。

    与auto_import函数类似，本函数遍历指定路径下的所有模块，但不导入这些模块。
    相反，它返回一个包含所有模块导入路径的列表。这允许调用者在需要时手动导入这些模块。

    参数:
    path (str): 模块所在的文件路径。应该是目录路径，函数将自动查找此目录下的所有模块。

    返回:
    list[str]: 包含所有模块导入路径的列表。
    """
    modules = []
    for _, name, _ in pkgutil.iter_modules([path]):
        modules.append(f'{path.replace("/", ".")}.{name}')
    return modules
