import logging
from abc import ABC
from time import time
from typing import ClassVar

from aiohttp import ClientSession
from openai.types.chat.chat_completion_message_param import ChatCompletionMessageParam
from openai.types.chat.chat_completion_system_message_param import (
    ChatCompletionSystemMessageParam,
)
from openai.types.chat.chat_completion_user_message_param import (
    ChatCompletionUserMessageParam,
)

from app.config import config
from app.schemas.completion_result import CompletionResult, Usage
from app.utils.exception import ModelError
from app.utils.json_logger import log_json

from .singleton import Singleton


class VolcEngineDeepSeek(ABC, metaclass=Singleton):
    API_PATH = 'api/v3/chat/completions'
    MODEL_NAME: ClassVar[str]

    def __init__(self) -> None:
        self.client = ClientSession(
            base_url='https://ark.cn-beijing.volces.com/',
            headers={'Authorization': f'Bearer {config.VOLCENGINE_DEEPSEEK_API_KEY}'},
        )

    async def shutdown(self) -> None:
        await self.client.close()

    async def chat(
        self,
        system_message: str = '',
        user_message: str = '',
        **kwargs,
    ) -> CompletionResult:
        messages: list[ChatCompletionMessageParam] = []
        if system_message:
            messages.append(ChatCompletionSystemMessageParam(content=system_message, role='system'))
        if user_message:
            messages.append(ChatCompletionUserMessageParam(content=user_message, role='user'))
        assert messages, 'neither system_message nor user_message is provided'

        async with self.client.post(
            url=self.API_PATH,
            json={
                'model': self.MODEL_NAME,
                'messages': messages,
                **kwargs,
            },
        ) as response:
            if not response.ok:
                raise ModelError(f'status: {response.status}, text: {response.text}')

            try:
                result = await response.json()
            except Exception as e:
                logging.exception('Failed to parse DeepSeek response: %s', response.text)
                raise ModelError(f'invalid response: {response.text}') from e

        error = result.get('error')
        if error:
            logging.error('DeepSeek error: %s', error)
            raise ModelError(error)

        message = result['choices'][0]['message']
        content = message['content']
        reasoning_content = message.get('reasoning_content', '')
        usage_data = result['usage']
        usage = Usage(
            input_tokens=usage_data['prompt_tokens'],
            output_tokens=usage_data['completion_tokens'],
        )
        if system_message:
            system_messages = system_message.split('\n')
            system_messages_str = '\n'.join(system_messages[:2])  # 只记录 system_message 的前 2 行
            message = f'{system_messages_str}\n{user_message}'
        else:
            message = user_message
        log_json(
            {
                'model': self.MODEL_NAME,
                'message': message,
                'result': content,
                'input_tokens': usage.input_tokens,
                'output_tokens': usage.output_tokens,
                'timestamp': int(time()),
            }
        )
        return CompletionResult(
            content=content,
            reasoning_content=reasoning_content,
            usage=usage,
        )


class VolcEngineDeepSeekV3(VolcEngineDeepSeek, metaclass=Singleton):
    MODEL_NAME: ClassVar[str] = config.VOLCENGINE_DEEPSEEK_V3_MODEL


class VolcEngineDeepSeekR1(VolcEngineDeepSeek, metaclass=Singleton):
    MODEL_NAME: ClassVar[str] = config.VOLCENGINE_DEEPSEEK_R1_MODEL
