from typing import ClassVar

from fastapi import Request
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse

from .error_code import ErrorCode


class HTTPError(Exception):
    """
    自定义HTTP错误类，用于处理HTTP错误。

    参数:
    - status_code: HTTP状态码
    - code: 错误代码，默认为1
    - msg: 错误消息，默认为空字符串
    - headers: HTTP响应头，默认为None
    """

    def __init__(self, status_code: int, code: int = 1, msg: str = '', headers=None):
        self.status_code = status_code
        self.code = code
        self.msg = msg
        self.headers = headers


async def http_error_handler(request: Request, exc: HTTPError):
    """
    HTTP错误处理器，用于处理HTTPError异常。

    参数:
    - request: FastAPI请求对象
    - exc: HTTPError异常对象

    返回:
    - JSONResponse对象，包含错误信息和HTTP状态码
    """
    return JSONResponse(
        status_code=exc.status_code,
        content={'code': exc.code, 'msg': exc.msg},
        headers=exc.headers,
    )


async def validation_error_handler(request: Request, exc: RequestValidationError):
    """
    请求验证错误处理器，用于处理请求验证错误。

    参数:
    - request: FastAPI请求对象
    - exc: RequestValidationError异常对象

    返回:
    - JSONResponse对象，包含错误信息和HTTP状态码400
    """
    try:
        arg = exc.args[0][0]
        msg = f'{arg["msg"]}: {"->".join(arg["loc"])}'
    except Exception:
        msg = 'Bad Request'
    return JSONResponse(status_code=400, content={'code': ErrorCode.BAD_REQUEST.value, 'msg': msg})


async def exception_handler(request: Request, exc: Exception):
    """
    通用异常处理器，用于处理所有未捕获的异常。

    参数:
    - request: FastAPI请求对象
    - exc: Exception异常对象

    返回:
    - JSONResponse对象，包含错误信息和HTTP状态码500
    """
    return JSONResponse(
        status_code=500,
        content={
            'code': ErrorCode.INTERNAL_SERVER_ERROR.value,
            'msg': 'Internal Server Error',
        },
    )


def bad_request_error(msg) -> HTTPError:
    """
    生成一个HTTP错误对象，用于表示一个错误请求。

    参数:
    - msg: 错误消息

    返回:
    - HTTPError对象，HTTP状态码为400
    """
    return HTTPError(status_code=400, code=ErrorCode.BAD_REQUEST.value, msg=msg)


def unauthorized_error(msg) -> HTTPError:
    """
    生成一个HTTP错误对象，用于表示一个未授权错误。

    参数:
    - msg: 错误消息

    返回:
    - HTTPError对象，HTTP状态码为401
    """
    return HTTPError(status_code=401, code=ErrorCode.UNAUTHORIZED.value, msg=msg)


def not_found_error(msg) -> HTTPError:
    """
    生成一个HTTP错误对象，用于表示一个未找到资源的错误。

    参数:
    - msg: 错误消息

    返回:
    - HTTPError对象，HTTP状态码为404
    """
    return HTTPError(status_code=404, code=ErrorCode.NOT_FOUND.value, msg=msg)


# 定义几个常见的HTTP错误实例
expired_token_error = unauthorized_error('Expired token')
invalid_token_error = unauthorized_error('Invalid token')
not_authenticated_error = unauthorized_error('Not authenticated')

forbidden_error = HTTPError(
    status_code=403,
    code=ErrorCode.FORBIDDEN.value,
    msg='Forbidden',
)

internal_server_error = HTTPError(
    status_code=500,
    code=ErrorCode.INTERNAL_SERVER_ERROR.value,
    msg='Internal Server Error',
)


class IND267Error(Exception): ...


class DocFileError(IND267Error): ...


class DocBaseFieldGetFailed(DocFileError): ...


class DocFileOpenFailed(DocFileError): ...


class DocFileTypeError(DocFileError): ...


class GenerateError(IND267Error):
    PREFIX = 'error: '
    KEY: ClassVar[str]

    @classmethod
    def is_error(cls, value: str) -> bool:
        return value.startswith(cls.PREFIX)

    @classmethod
    def message(cls) -> str:
        return cls.PREFIX + cls.KEY


class GenerateFailed(GenerateError):
    KEY = 'failed'


class ExtractError(GenerateFailed):
    KEY = 'extract error'


class ModelError(ExtractError):
    KEY = 'model error'


class GenerateCancelled(GenerateError):
    KEY = 'cancelled'


class MissingProjectError(GenerateError):
    KEY = 'missing project'


class DocError(GenerateError):
    KEY = 'doc error'


class MissingDocError(DocError):
    KEY = 'missing doc'


class MissingDocChunkError(DocError):
    KEY = 'missing doc chunk'


class MissingTableChunkError(DocError):
    KEY = 'missing table chunk'


class MissingFieldError(DocError):
    KEY = 'missing field'


async def ind_error_handler(request: Request, exc: IND267Error):
    """
    文档基础字段提取错误处理器，用于处理提取过程中发生的Error异常。

    参数:
    - request: FastAPI请求对象
    - exc: Error异常对象

    返回:
    - JSONResponse对象，包含错误信息和HTTP状态码
    """
    if isinstance(exc, ModelError):
        code = ErrorCode.MODEL_ERROR.value
    elif isinstance(exc, DocFileError):
        code = ErrorCode.DOC_TYPE_ERROR.value
    else:
        code = ErrorCode.FAILED.value

    msg = exc.args[0] if exc.args else ''

    return JSONResponse(
        status_code=400,
        content={'code': code, 'msg': msg},
    )
