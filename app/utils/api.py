from typing import Any

from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder

from app.schemas.restful import ResponseSchema


class ResponseBuilder:
    """通用返回"""

    @staticmethod
    def success(data: Any = None, msg: str = 'Success') -> JSONResponse:
        # 使用 jsonable_encoder 转换数据为可序列化格式
        serialized_data = jsonable_encoder(data)
        response = ResponseSchema(code=0, data=serialized_data, msg=msg)
        return JSONResponse(content=response.model_dump(), status_code=200)

    @staticmethod
    def error(code: int, msg: str, data: Any = None) -> JSONResponse:
        response = ResponseSchema(code=code, data=data, msg=msg)
        return JSONResponse(content=response.model_dump(), status_code=code)
