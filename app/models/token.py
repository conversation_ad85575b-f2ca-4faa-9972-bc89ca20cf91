from app.clients.redis import redis_client

from .user import User


class Token:
    KEY = 'token:{}'

    @classmethod
    async def get(cls, token: str) -> User | None:
        data = await redis_client.get(cls.KEY.format(token))
        if data:
            user_id, user_name = data.split(',', 1)
            return User(int(user_id), user_name)
        return None

    @classmethod
    async def set(cls, token: str, user: User) -> None:
        value = f'{user.id},{user.name}'
        return await redis_client.set(cls.KEY.format(token), value, ex=3600)
