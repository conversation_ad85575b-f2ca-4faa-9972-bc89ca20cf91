from re import compile, escape
from typing import Any, ClassVar, Optional, Sequence

from sqlalchemy.engine.row import Row
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Mapped
from sqlalchemy.orm.attributes import InstrumentedAttribute
from sqlalchemy.sql import ColumnElement
from sqlmodel import Field, and_, col, or_, select

from . import BaseModel


class DocChunk(BaseModel, table=True):
    """
    文档分块 Model
    """

    __tablename__: ClassVar[str] = 't_ind267_doc_chunk'

    parent_id: int = Field(..., description='父亲ID')
    project_id: int = Field(..., description='项目ID')
    doc_id: int = Field(..., description='文档ID')
    title: str = Field(..., max_length=255, description='模板数据')
    numbering: Optional[str] = Field(default=None, max_length=50, description='自动编号的序号')
    paragraph: Optional[str] = Field(default=None, description='段落数组JSON字符串')
    is_delete: int = Field(default=0)

    @classmethod
    async def query(
        cls,
        session: AsyncSession,
        doc_id: int,
        parent_id: int = 0,
        title: str | tuple[str, ...] | list[str] = '',
        columns: list | tuple | InstrumentedAttribute | Mapped | None = None,
    ) -> Sequence:
        if columns is None:
            query = select(cls)
            scalar = True
        else:
            if isinstance(columns, (list, tuple)):
                query = select(*columns)
                scalar = False
            else:
                query = select(columns)
                scalar = True

        query = query.where(cls.doc_id == doc_id, cls.is_delete == 0)
        if parent_id:
            query = query.where(cls.parent_id == parent_id)
        if title:
            if isinstance(title, str):
                if '%' in title:
                    query = query.where(col(cls.title).like(title))
                else:
                    query = query.where(cls.title == title)
            else:
                filters = []
                for title in title:
                    if '%' in title:
                        filter = col(cls.title).like(title)
                    else:
                        filter = cls.title == title
                    filters.append(filter)

                query = query.where(or_(*filters))

        if scalar:
            return (await session.scalars(query)).all()
        else:
            return (await session.execute(query)).all()

    @classmethod
    async def query_first(
        cls,
        session: AsyncSession,
        doc_id: int,
        parent_id: int = 0,
        title: str | tuple[str, ...] | list[str] = '',
        columns: list | tuple | InstrumentedAttribute | Mapped | None = None,
    ) -> Any:
        if columns is None:
            query = select(cls)
            scalar = True
        else:
            if isinstance(columns, (list, tuple)):
                query = select(*columns)
                scalar = False
            else:
                query = select(columns)
                scalar = True

        query = query.where(cls.doc_id == doc_id, cls.is_delete == 0)
        if parent_id:
            query = query.where(cls.parent_id == parent_id)
        if title:
            if isinstance(title, str):
                if '%' in title:
                    query = query.where(col(cls.title).like(title))
                else:
                    query = query.where(cls.title == title)
            else:
                filters = []
                for title in title:
                    if '%' in title:
                        filter = col(cls.title).like(title)
                    else:
                        filter = cls.title == title
                    filters.append(filter)

                query = query.where(or_(*filters))

        if scalar:
            return await session.scalar(query)
        else:
            return (await session.execute(query)).first()

    @classmethod
    async def is_sub(cls, session: AsyncSession, parent_id: int, chunk: Row) -> bool:
        for _ in range(3):  # 最多查 3 层
            if chunk.parent_id == parent_id:
                return True
            if chunk.parent_id < parent_id:
                return False
            else:
                chunk = await cls.get_by_id(session, chunk.parent_id, columns=(cls.id, cls.parent_id))
        return False

    @classmethod
    async def query_sub(
        cls,
        session: AsyncSession,
        doc_id: int,
        parent_id: int,
        title: str | tuple[str, ...] | list[str],
        columns: list | tuple | Mapped | None = None,  # columns 里需要包含 id 和 parent_id
    ) -> Any:
        chunks = await cls.query(session, doc_id, title=title, columns=columns)
        for chunk in chunks:
            if await cls.is_sub(session, parent_id, chunk):
                return chunk

    @classmethod
    async def get_sub_ids(
        cls,
        session: AsyncSession,
        doc_id: int,
        parent_ids: int | Sequence[int],
    ) -> Sequence[int]:
        query = select(cls.id).where(cls.doc_id == doc_id, cls.is_delete == 0)
        if isinstance(parent_ids, int):
            query = query.where(col(cls.parent_id) == parent_ids)
        else:
            query = query.where(col(cls.parent_id).in_(parent_ids), col(cls.id).not_in(parent_ids))
        return (await session.scalars(query)).all()

    @classmethod
    async def get_subs(
        cls,
        session: AsyncSession,
        doc_id: int,
        parent_ids: int | Sequence[int],
        columns: tuple | InstrumentedAttribute | Mapped | None = None,
    ) -> Sequence:
        if columns is None:
            query = select(cls)
            execute_func = session.scalars
        else:
            if isinstance(columns, (list, tuple)):
                query = select(*columns)
                execute_func = session.execute
            else:
                query = select(columns)
                execute_func = session.scalars
        query = query.where(cls.doc_id == doc_id, cls.is_delete == 0)

        if isinstance(parent_ids, int):
            query = query.where(col(cls.parent_id) == parent_ids)
        else:
            query = query.where(col(cls.parent_id).in_(parent_ids), col(cls.id).not_in(parent_ids))
        return (await execute_func(query)).all()

    @classmethod
    async def extend_with_sub_ids(
        cls,
        session: AsyncSession,
        doc_id: int,
        doc_chunk_ids: Sequence[int],
    ) -> list[int]:
        assert isinstance(doc_chunk_ids, list)
        sub_doc_chunk_ids = await DocChunk.get_sub_ids(session, doc_id, doc_chunk_ids)
        if sub_doc_chunk_ids:
            doc_chunk_ids.extend(sub_doc_chunk_ids)
        return doc_chunk_ids

    @classmethod
    async def query_doc_chunks(
        cls,
        session: AsyncSession,
        doc_id: int,
        parent_id: int = 0,
        parent_titles: str | tuple[str, ...] = '',
        titles: str | tuple[str, ...] = '',
        contents: str | tuple[str, ...] | tuple[tuple[str, ...], ...] = '',
        columns: tuple | InstrumentedAttribute | Mapped | None = None,
    ) -> Sequence:
        parent_title_filters = []
        if parent_titles:
            if isinstance(parent_titles, str):
                parent_title_filters.append(col(cls.title).like(f'%{parent_titles}%'))
            else:
                for parent_title in parent_titles:
                    parent_title_filters.append(col(cls.title).like(f'%{parent_title}%'))

        title_filters = []
        if titles:
            if isinstance(titles, str):
                title_filters.append(col(cls.title).like(f'%{titles}%'))
            else:
                for title in titles:
                    title_filters.append(col(cls.title).like(f'%{title}%'))

        content_filters = []
        if contents:
            if isinstance(contents, str):
                content_filters.append(col(cls.paragraph).like(f'%{contents}%'))
            else:
                for content in contents:
                    if isinstance(content, str):
                        content_filters.append(col(cls.paragraph).like(f'%{content}%'))
                    else:
                        and_filters = []
                        for keyword in content:
                            and_filters.append(col(cls.paragraph).like(f'%{keyword}%'))
                        content_filters.append(and_(*and_filters))

        # 查询父章节
        if parent_id:
            doc_chunk_query_with_parent = select(cls.id).where(cls.doc_id == doc_id, cls.parent_id == parent_id)
            doc_chunk_ids = (await session.scalars(doc_chunk_query_with_parent)).all()
        elif parent_title_filters:
            doc_chunk_query_with_parent = select(cls.id).where(cls.doc_id == doc_id, or_(*parent_title_filters))
            doc_chunk_ids = (await session.scalars(doc_chunk_query_with_parent)).all()
        else:
            doc_chunk_ids = []

        if columns is None:
            doc_chunk_query = select(cls)
            execute_func = session.scalars
        else:
            if isinstance(columns, (list, tuple)):
                doc_chunk_query = select(*columns)
                execute_func = session.execute
            else:
                doc_chunk_query = select(columns)
                execute_func = session.scalars
        doc_chunk_query = doc_chunk_query.where(cls.doc_id == doc_id, cls.is_delete == 0)

        # 判断正文是否包含关键字
        if content_filters:
            doc_chunk_query = doc_chunk_query.where(or_(*content_filters))

        doc_chunk_query_with_title = doc_chunk_query

        # 找到父章节时，限制 id 范围
        if doc_chunk_ids:
            doc_chunk_query_with_title = doc_chunk_query_with_title.where(col(cls.id).in_(doc_chunk_ids))

        if title_filters:
            doc_chunk_query_with_title = doc_chunk_query_with_title.where(or_(*title_filters))

        doc_chunks = (await execute_func(doc_chunk_query_with_title)).all()
        if not doc_chunks and content_filters:
            # 尝试查找包含正文关键字的一级子章节
            if doc_chunk_ids or title_filters:
                sub_query = select(cls.id).where(cls.doc_id == doc_id, cls.is_delete == 0, or_(*content_filters))
                if doc_chunk_ids:
                    sub_query = sub_query.where(col(cls.parent_id).in_(doc_chunk_ids))
                if title_filters:
                    sub_query = sub_query.where(or_(*title_filters))
                doc_chunk_query_sub = doc_chunk_query.where(col(cls.id).in_(sub_query))
                doc_chunks = (await execute_func(doc_chunk_query_sub)).all()

            if not doc_chunks:
                # TODO: 通过模型判断字段可能包含在哪个切片标题中
                # 忽略标题和父章节，直接返回包含正文关键字的切片
                doc_chunks = (await execute_func(doc_chunk_query)).all()
        return doc_chunks

    @classmethod
    async def query_v2(
        cls,
        session: AsyncSession,
        doc_id: int,
        parent_id: int | None = None,
        titles: str | tuple[str, ...] = '',
        columns: tuple | InstrumentedAttribute | Mapped | None = None,
        # 为了后续调用 get_subs() 和 filter_content()，columns 中应包含 id 和 paragraph
    ) -> Sequence:
        # 查询父章节
        if parent_id:
            doc_chunk_ids = (
                await session.scalars(
                    select(cls.id).where(cls.doc_id == doc_id, cls.parent_id == parent_id, cls.is_delete == 0)
                )
            ).all()
            if not doc_chunk_ids:
                # 未找到父章节，强过滤条件不成立，检索失败
                return []

            # 将一级子章节也纳入检索范围
            sub_doc_chunk_ids = await cls.get_sub_ids(session, doc_id, doc_chunk_ids)
            if sub_doc_chunk_ids:
                assert isinstance(doc_chunk_ids, list)
                assert isinstance(sub_doc_chunk_ids, list)
                doc_chunk_ids += sub_doc_chunk_ids
        else:
            doc_chunk_ids = []

        title_filter: ColumnElement | None = None
        if titles:
            if isinstance(titles, str):
                title_filter = col(cls.title).like(f'%{titles}%')
            else:
                title_filters: list[ColumnElement] = []
                for title in titles:
                    title_filters.append(col(cls.title).like(f'%{title}%'))
                title_filter = or_(*title_filters)

        if columns is None:
            doc_chunk_query = select(cls)
            execute_func = session.scalars
        else:
            if isinstance(columns, (list, tuple)):
                doc_chunk_query = select(*columns)
                execute_func = session.execute
            else:
                doc_chunk_query = select(columns)
                execute_func = session.scalars
        doc_chunk_query = doc_chunk_query.where(cls.doc_id == doc_id, cls.is_delete == 0)

        # 过滤父章节时，限制 id 范围
        if doc_chunk_ids:
            doc_chunk_query = doc_chunk_query.where(col(cls.id).in_(doc_chunk_ids))

        # 检索标题符合的切片
        if title_filter is not None:
            doc_chunks = (await execute_func(doc_chunk_query.where(title_filter))).all()
            if doc_chunks:
                return doc_chunks

        if doc_chunk_ids:
            # 回退到只过滤父章节
            doc_chunks = (await execute_func(doc_chunk_query)).all()
            if doc_chunks:
                return doc_chunks
        return []

    @classmethod
    async def filter_content(
        cls,
        session: AsyncSession,
        doc_id: int,
        doc_chunks: Sequence,
        parent_id: int | None = None,
        contents: str | tuple[str | tuple[str, ...], ...] = '',
        columns: tuple | InstrumentedAttribute | Mapped | None = None,
        required: bool = False,  # 如果为 True，则必须匹配到正文关键字，可以忽略 doc_chunks
    ) -> Sequence:
        if doc_chunks:
            # 在已检索到的切片中过滤正文关键字
            if isinstance(contents, str):
                pattern = compile(escape(contents).replace('%', '.*'))
            else:
                or_patterns = []
                for content in contents:
                    if isinstance(content, str):
                        # 如果是单个字符串，添加到模式中
                        or_patterns.append(escape(content).replace('%', '.*'))
                    else:
                        # 如果是元组，表示是一个关键字组，所有关键字需要同时匹配
                        and_pattern = ''.join([f'(?=.*{escape(keyword)})' for keyword in content])
                        or_patterns.append(and_pattern)
                # 合并所有模式为一个正则表达式
                pattern = compile(f"({'|'.join(or_patterns)})")

            doc_chunk = doc_chunks[0]
            if hasattr(doc_chunk, 'paragraph'):
                filtered_doc_chunks = [doc_chunk for doc_chunk in doc_chunks if pattern.search(doc_chunk.paragraph)]
            else:  # 不是切片列表，而是 paragraph 的列表
                filtered_doc_chunks = [doc_chunk for doc_chunk in doc_chunks if pattern.search(doc_chunk)]
            if required:
                if filtered_doc_chunks:
                    return filtered_doc_chunks
            else:
                return filtered_doc_chunks or doc_chunks  # 如果没有匹配的切片，返回原始切片列表

        # 如果没有检索到切片，在所有切片中检索
        if columns is None:
            doc_chunk_query = select(cls)
            execute_func = session.scalars
        else:
            if isinstance(columns, (list, tuple)):
                doc_chunk_query = select(*columns)
                execute_func = session.execute
            else:
                doc_chunk_query = select(columns)
                execute_func = session.scalars
        doc_chunk_query = doc_chunk_query.where(cls.doc_id == doc_id, cls.is_delete == 0)
        if parent_id:
            doc_chunk_query = doc_chunk_query.where(cls.parent_id == parent_id)

        if isinstance(contents, str):
            doc_chunk_query = doc_chunk_query.where(col(cls.paragraph).like(f'%{contents}%'))
        else:
            content_filters: list[ColumnElement] = []
            for content in contents:
                if isinstance(content, str):
                    content_filters.append(col(cls.paragraph).like(f'%{content}%'))
                else:
                    and_filters = []
                    for keyword in content:
                        and_filters.append(col(cls.paragraph).like(f'%{keyword}%'))
                    content_filters.append(and_(*and_filters))
            doc_chunk_query = doc_chunk_query.where(or_(*content_filters))
        return (await execute_func(doc_chunk_query)).all()
