from datetime import datetime
from typing import ClassVar, List, Self, Sequence

from sqlalchemy import Row, text
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Mapped
from sqlalchemy.orm.attributes import InstrumentedAttribute
from sqlmodel import Field, col, select, update

from . import BaseModel


class Doc(BaseModel, table=True):
    """
    文档信息 Model
    """

    __tablename__: ClassVar[str] = 't_ind267_doc'

    project_id: int = Field(..., description='项目ID')
    trial_main_type: int = Field(..., description='试验主类型')
    trial_subtype: int | None = Field(default=None, description='试验子类型')
    trial_title: str | None = Field(default=None, max_length=255, description='试验标题')
    trial_institution: str | None = Field(default=None, max_length=255, description='试验机构')
    trial_number: str | None = Field(default=None, max_length=255, description='试验编号')
    species: str | None = Field(default=None, max_length=255, description='种属')
    solvent_and_dosage_form: str | None = Field(default=None, max_length=255, description='溶媒与剂型')
    glp_compliance: bool | None = Field(default=None, description='GLP依从性')
    administration_method: str | None = Field(default=None, max_length=255, description='给药方法')
    administration_unit: str | None = Field(default=None, max_length=255, description='给药计量单位')
    dosing_regimen: str | None = Field(default=None, max_length=255, description='给药周期')
    administration_dosage: str | None = Field(default=None, max_length=255, description='给药剂量')
    test_product: str | None = Field(default=None, max_length=50, description='受试物')
    applicant: str | None = Field(default=None, max_length=50, description='申请人')
    data: str | None = None
    table_of_contents: str | None = None
    doc_file_path: str | None = Field(default=None, max_length=255, description='文档地址')
    chunk_status: int = Field(default=0, description='切片状态 0: 未开始 1:切片中 2: 切片完成 3: 切片失败')
    cover_paragraphs: str | None = Field(default=None, description='封面段落数据')
    cover_tables: str | None = Field(default=None, description='封面表格数据')
    cover_tables_md: str | None = Field(default=None, description='封面表格MD格式数据')
    is_active: bool = Field(default=False, description='是否启用')
    create_time: datetime = Field(default=None, sa_column_kwargs={'server_default': text('CURRENT_TIMESTAMP')})
    update_time: datetime = Field(
        default=None,
        sa_column_kwargs={
            'server_default': text('CURRENT_TIMESTAMP'),
            'server_onupdate': text('CURRENT_TIMESTAMP'),
        },
    )

    @classmethod
    async def get_by_project(
        cls,
        session: AsyncSession,
        project_id: int,
        trial_main_type: int | None = None,
        trial_subtype: int | None = None,
        chunk_status: int | None = None,
        columns: list | tuple | InstrumentedAttribute | Mapped | None = None,
    ) -> Sequence[Self] | Sequence[Row]:
        """
        通过项目ID查询DOC
        """
        if columns is None:
            query = select(cls)
            scalar = True
        else:
            if isinstance(columns, (list, tuple)):
                query = select(*columns)
                scalar = False
            else:
                query = select(columns)
                scalar = True

        query = query.where(cls.project_id == project_id, cls.is_active == True)
        if trial_main_type is not None:
            query = query.where(cls.trial_main_type == trial_main_type)
        if trial_subtype is not None:
            query = query.where(cls.trial_subtype == trial_subtype)
        if chunk_status is not None:
            query = query.where(cls.chunk_status == chunk_status)

        if scalar:
            return (await session.scalars(query)).all()
        return (await session.execute(query)).all()

    @classmethod
    async def set_active(cls, session: AsyncSession, project_id: int, doc_ids: List[int]):
        """
        标记所有存储的 doc 为 启用状态, 不在 files 中的 doc标记为 false
        """
        await session.execute(update(cls).where(col(cls.project_id) == project_id).values({cls.is_active: False}))

        await session.execute(
            update(cls).where(col(cls.project_id) == project_id, col(cls.id).in_(doc_ids)).values({cls.is_active: True})
        )

        return True

    @classmethod
    async def soft_delete_by_project_id(cls, session: AsyncSession, project_id: int) -> bool:
        """
        软删除项目下的所有文档
        """
        # 执行更新操作，返回一个 Result 对象
        result = await session.execute(
            update(cls).where(col(cls.project_id) == project_id).values({cls.is_active: False})
        )

        # 获取受影响的行数
        return result.rowcount > 1
