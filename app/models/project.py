from datetime import datetime
from typing import ClassVar, Self, Sequence

from sqlalchemy import func, or_, text
from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import Field, col, desc, select, update

from app.config import config
from app.schemas.project import ProjectStatus

from . import BaseModel


class Project(BaseModel, table=True):
    """
    项目信息 Model
    """

    __tablename__: ClassVar[str] = 't_ind267_project'

    project_name: str = Field(..., max_length=255)
    template: str | None = Field(default=None, max_length=255)
    version: str | None = Field(default=None, max_length=255)
    files: str | None = Field(default=None)
    doc_name: str | None = Field(default=None, max_length=255)
    doc_path: str | None = None
    data: str | None = None
    builder_data: str | None = None
    status: int = 0
    creator_name: str = Field(..., max_length=255)
    creator_id: int
    is_delete: int = 0
    create_time: datetime = Field(default=None, sa_column_kwargs={'server_default': text('CURRENT_TIMESTAMP')})
    update_time: datetime = Field(
        default=None,
        sa_column_kwargs={
            'server_default': text('CURRENT_TIMESTAMP'),
            'server_onupdate': text('CURRENT_TIMESTAMP'),
        },
    )

    @classmethod
    async def soft_delete_by_id(cls, session: AsyncSession, project_id: int) -> bool:
        """
        软删除项目
        """
        updated = (
            await session.execute(update(cls).where(col(cls.id) == project_id).values({cls.is_delete: True}))
        ).rowcount > 0

        return updated

    @classmethod
    async def get_list_by_condition(
        cls,
        session: AsyncSession,
        user_id: int,
        page: int,
        page_size: int = 10,
        keyword: str | None = None,
    ) -> tuple[int, Sequence[Self]]:  # count, projects
        """
        获取符合条件的项目列表，并支持分页和关键词过滤
        """
        # 基础查询条件
        base_condition = [
            cls.is_delete == False,  # 未删除
            cls.status > ProjectStatus.NOT_READY.value,  # 状态大于 NOT_READY
            cls.creator_id == user_id,  # 创建者是当前用户
        ]
        
        # 过滤测试用户
        if user_id == config.TEST_USER_ID:
            base_condition.pop()

        # 如果有关键词，添加关键词过滤
        if keyword:
            like = f'%{keyword}%'
            keyword_filter = or_(
                col(cls.project_name).like(like),
                col(cls.template).like(like),
                col(cls.version).like(like),
                col(cls.creator_name).like(like),
            )
            base_condition.append(keyword_filter)

        # 获取总记录数
        total_count_stmt = select(func.count().label('total_count')).where(*base_condition)
        total_count = (await session.execute(total_count_stmt)).scalar()

        if not total_count:
            return 0, []

        # 构建查询
        query = select(
            col(cls.id),
            col(cls.project_name),
            col(cls.template),
            col(cls.version),
            col(cls.creator_name),
            col(cls.status),
            col(cls.create_time),
            col(cls.update_time),
        ).where(*base_condition)

        # 分页逻辑
        query = query.offset((page - 1) * page_size).limit(page_size).order_by(desc(cls.create_time))

        # 执行分页查询
        result = await session.execute(query)
        projects = result.mappings().all()

        return total_count, projects

    @classmethod
    async def stop_generate(cls, session: AsyncSession, project_id: int) -> bool:
        """
        停止生成
        """

        updated = (
            await session.execute(
                update(cls)
                .where(
                    col(cls.id) == project_id,
                    col(cls.status) == ProjectStatus.GENERATING.value,
                )
                .values({cls.status: ProjectStatus.READY.value})
            )
        ).rowcount > 0

        return updated
