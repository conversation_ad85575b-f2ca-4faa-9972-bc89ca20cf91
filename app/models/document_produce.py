from datetime import datetime
from typing import ClassVar

from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import Field, col, update

from app.schemas.project import ProductDoucumetProduceStatus

from . import BaseModel


class DocumentProduce(BaseModel, table=True):
    """
    临研文书 Model
    """

    __tablename__: ClassVar[str] = 't_document_produce'

    id: str = Field(default='', primary_key=True, description='ID')
    document_produce_status: int | None = Field(
        default=None, description='生成状态 1未生成 2生成中 3生成成功 4生成失败  5生成取消'
    )
    document_produce_type: str | None = Field(default=None, max_length=255, description='模块类型')
    document_produce_mode: int | None = Field(default=None, description='生成模式 1单个 2多个')  # 固定2多个
    document_produce_model: int | None = Field(default=None, description='模板类型')  # 空置
    document_type: int | None = Field(default=None, description='文书类型 1知情同意书 2试验方案 3IND 4同类药 5IND267')
    section_name: str | None = Field(default=None, max_length=255, description='标题')  # 空置
    grade: int | None = Field(default=None, description='层级')  # 空置
    pre_data: str | None = Field(default=None, description='历史数据')  # 空置
    target_id: int | None = Field(default=None, description='目标ID')  # project表中的ID
    user_id: int | None = Field(default=None, description='用户ID')
    fail_reason: str | None = Field(default=None, description='失败原因')
    create_time: datetime | None = Field(default=None, description='创建时间')
    update_time: datetime | None = Field(default=None, description='更新时间')
    fail_code: int = Field(default=0, description='算法端生成失败状态码 [400:生成失败/422:文件解析异常/500:系统异常]')

    @classmethod
    async def update_by_target_id(
        cls, session: AsyncSession, target_id: int, status: ProductDoucumetProduceStatus
    ) -> int:
        return (
            await session.execute(
                update(cls)
                .where(col(cls.target_id) == target_id)
                .values({'document_produce_status': status.value, 'update_time': datetime.now()})
            )
        ).rowcount
