from datetime import datetime
from typing import ClassVar, Self

from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import Field, col, select, update

from app.schemas.project import ProductDoucumetStep, ProductDoucumetType

from . import BaseModel


class Document(BaseModel, table=True):
    """
    临研文书 Model
    """

    __tablename__: ClassVar[str] = 't_document'

    type: int | None = Field(default=None, description='文书类型 1知情同意书 2试验方案 3IND 4同类药 5IND267')
    ref_id: int | None = Field(default=None, description='对应的文书表id')
    user_id: int | None = Field(default=None, description='用户id')
    step: int | None = Field(default=1, description='步骤 1, 2, 3, 4')
    is_delete: int | None = Field(default=0, description='是否删除, 0否1是')
    create_time: datetime | None = Field(default=None, description='创建时间')
    update_time: datetime | None = Field(default=None, description='更新时间')

    @classmethod
    async def update_by_ref_id(cls, session: AsyncSession, ref_id: int, step: ProductDoucumetStep) -> int:
        return (
            await session.execute(
                update(cls).where(col(cls.ref_id) == ref_id).values({'step': step.value, 'update_time': datetime.now()})
            )
        ).rowcount

    @classmethod
    async def get_by_ref_id(cls, session: AsyncSession, ref_id: int) -> Self | None:
        query = select(cls).where(cls.ref_id == ref_id, cls.type == ProductDoucumetType.IND267.value).limit(1)
        result = await session.scalars(query)

        return result.first()
