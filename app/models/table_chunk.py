import json
import logging
from re import compile, escape
from typing import Any, ClassVar, Optional, Sequence

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Mapped
from sqlalchemy.orm.attributes import InstrumentedAttribute
from sqlalchemy.sql import ColumnElement
from sqlmodel import Field, and_, col, or_, select

from . import BaseModel


class TableChunk(BaseModel, table=True):
    """
    表格分块 Model
    """

    __tablename__: ClassVar[str] = 't_ind267_table_chunk'

    doc_chunk_id: int = Field(..., description='文档切片ID')
    project_id: int = Field(..., description='项目ID')
    doc_id: int = Field(..., description='文档ID')
    header: Optional[str] = Field(default=None, description='表格的表头段落数组JSON字符串')
    footer: Optional[str] = Field(default=None, description='表格的表尾段落数组JSON字符串')
    content_md: Optional[str] = Field(default=None, description='表格markdown格式字符串')
    content_json: Optional[str] = Field(default=None, description='表格json格式字符串')
    table_title: Optional[str] = Field(default=None, max_length=255, description='表格标题')
    title: Optional[str] = Field(default=None, max_length=255, description='章节标题')
    is_delete: int = Field(default=0)

    @classmethod
    async def query(
        cls,
        session: AsyncSession,
        doc_chunk_id: int,
        table_title: str | tuple[str, ...] | list[str] = '',
        columns: list | tuple | InstrumentedAttribute | Mapped | None = None,
    ) -> Sequence:
        if columns is None:
            query = select(cls)
            scalar = True
        else:
            if isinstance(columns, (list, tuple)):
                query = select(*columns)
                scalar = False
            else:
                query = select(columns)
                scalar = True

        query = query.where(cls.doc_chunk_id == doc_chunk_id, cls.is_delete == 0)
        if table_title:
            if isinstance(table_title, str):
                if '%' in table_title:
                    query = query.where(col(cls.table_title).like(table_title))
                else:
                    query = query.where(cls.table_title == table_title)
            else:
                filters = []
                for title in table_title:
                    if '%' in title:
                        filter = col(cls.table_title).like(title)
                    else:
                        filter = cls.table_title == title
                    filters.append(filter)

                query = query.where(or_(*filters))

        if scalar:
            return (await session.scalars(query)).all()
        else:
            return (await session.execute(query)).all()

    @classmethod
    async def query_first(
        cls,
        session: AsyncSession,
        doc_chunk_id: int,
        table_title: str | tuple[str, ...] | list[str] = '',
        columns: list | tuple | InstrumentedAttribute | Mapped | None = None,
    ) -> Any:
        if columns is None:
            query = select(cls)
            scalar = True
        else:
            if isinstance(columns, (list, tuple)):
                query = select(*columns)
                scalar = False
            else:
                query = select(columns)
                scalar = True

        query = query.where(cls.doc_chunk_id == doc_chunk_id, cls.is_delete == 0)
        if table_title:
            if isinstance(table_title, str):
                if '%' in table_title:
                    query = query.where(col(cls.table_title).like(table_title))
                else:
                    query = query.where(cls.table_title == table_title)
            else:
                filters = []
                for title in table_title:
                    if '%' in title:
                        filter = col(cls.table_title).like(title)
                    else:
                        filter = cls.table_title == title
                    filters.append(filter)

                query = query.where(or_(*filters))

        if scalar:
            return await session.scalar(query)
        else:
            return (await session.execute(query)).first()

    @classmethod
    async def query_field(
        cls,
        session: AsyncSession,
        doc_chunk_id: int,
        table_title: str | tuple[str, ...] | list[str],
        field: str,
    ) -> str | None:
        query = select(col(cls.content_json)).where(cls.doc_chunk_id == doc_chunk_id, cls.is_delete == 0)
        if isinstance(table_title, str):
            if '%' in table_title:
                query = query.where(col(cls.table_title).like(table_title))
            else:
                query = query.where(cls.table_title == table_title)
        else:
            filters = []
            for title in table_title:
                if '%' in title:
                    filter = col(cls.table_title).like(title)
                else:
                    filter = cls.table_title == title
                filters.append(filter)

            query = query.where(or_(*filters))
        content_json = await session.scalar(query.limit(1))
        if content_json:
            try:
                content = json.loads(content_json)
                if isinstance(content, list):
                    for row in content:
                        if isinstance(row, list) and len(row) >= 2:
                            key = row[0]
                            if key == field:
                                return row[1]
            except Exception:
                logging.exception('invalid json: %s', content_json)
        return None

    @classmethod
    async def query_by_doc(
        cls,
        session: AsyncSession,
        doc_id: int,
        table_title: str | tuple[str, ...] | list[str] = '',
        columns: list | tuple | InstrumentedAttribute | Mapped | None = None,
    ) -> Sequence:
        if columns is None:
            query = select(cls)
            scalar = True
        else:
            if isinstance(columns, (list, tuple)):
                query = select(*columns)
                scalar = False
            else:
                query = select(columns)
                scalar = True

        query = query.where(cls.doc_id == doc_id, cls.is_delete == 0)
        if table_title:
            if isinstance(table_title, str):
                if '%' in table_title:
                    query = query.where(col(cls.table_title).like(table_title))
                else:
                    query = query.where(cls.table_title == table_title)
            else:
                filters = []
                for title in table_title:
                    if '%' in title:
                        filter = col(cls.table_title).like(title)
                    else:
                        filter = cls.table_title == title
                    filters.append(filter)

                query = query.where(or_(*filters))

        if scalar:
            return (await session.scalars(query)).all()
        else:
            return (await session.execute(query)).all()

    @classmethod
    async def query_table_chunks(
        cls,
        session: AsyncSession,
        doc_id: int,
        doc_chunk_ids: Sequence[int] = [],
        titles: str | tuple[str, ...] = '',
        contents: str | tuple[str, ...] | tuple[tuple[str, ...], ...] = '',
        columns: tuple | InstrumentedAttribute | Mapped | None = None,
    ) -> Sequence:
        title_filters = []
        if titles:
            if isinstance(titles, str):
                title_filters.append(col(TableChunk.table_title).like(f'%{titles}%'))
            else:
                for title in titles:
                    title_filters.append(col(TableChunk.table_title).like(f'%{title}%'))

        content_filters = []
        if contents:
            if isinstance(contents, str):
                content_filters.append(col(TableChunk.content_md).like(f'%{contents}%'))
            else:
                for content in contents:
                    if isinstance(content, str):
                        content_filters.append(col(TableChunk.content_md).like(f'%{content}%'))
                    else:
                        and_filters = []
                        for keyword in content:
                            and_filters.append(col(TableChunk.content_md).like(f'%{keyword}%'))
                        content_filters.append(and_(*and_filters))

        if columns is None:
            table_chunk_query = select(TableChunk)
            execute_func = session.scalars
        else:
            if isinstance(columns, (list, tuple)):
                table_chunk_query = select(*columns)
                execute_func = session.execute
            else:
                table_chunk_query = select(columns)
                execute_func = session.scalars
        table_chunk_query = table_chunk_query.where(TableChunk.doc_id == doc_id, TableChunk.is_delete == 0)
        table_chunk_query_with_filters = table_chunk_query

        if doc_chunk_ids:
            table_chunk_query_with_filters = table_chunk_query_with_filters.where(
                col(TableChunk.doc_chunk_id).in_(doc_chunk_ids)
            )

        if title_filters:
            table_chunk_query_with_filters = table_chunk_query_with_filters.where(or_(*title_filters))

        if content_filters:
            table_chunk_query_with_filters = table_chunk_query_with_filters.where(or_(*content_filters))

        table_chunks = (await execute_func(table_chunk_query_with_filters)).all()
        if not table_chunks:
            # 找不到时尝试忽略标题
            if title_filters:
                table_chunk_query_with_filters = table_chunk_query
                if doc_chunk_ids:
                    table_chunk_query_with_filters = table_chunk_query_with_filters.where(
                        col(TableChunk.doc_chunk_id).in_(doc_chunk_ids)
                    )
                if content_filters:
                    table_chunk_query_with_filters = table_chunk_query_with_filters.where(or_(*content_filters))
                table_chunks = (await execute_func(table_chunk_query_with_filters)).all()
            if not table_chunks and doc_chunk_ids:
                # 还找不到时尝试忽略文档切片
                table_chunk_query_with_filters = table_chunk_query
                if content_filters:
                    table_chunk_query_with_filters = table_chunk_query_with_filters.where(or_(*content_filters))

                # 先尝试带上标题
                if title_filters:
                    table_chunk_query_with_filters2 = table_chunk_query_with_filters.where(or_(*title_filters))
                    table_chunks = (await execute_func(table_chunk_query_with_filters2)).all()
                    if table_chunks:
                        return table_chunks
                    # else 去掉标题
                # else 去掉标题
                table_chunks = (await execute_func(table_chunk_query_with_filters)).all()
        return table_chunks

    @classmethod
    async def query_v2(
        cls,
        session: AsyncSession,
        doc_id: int,
        doc_chunk_ids: Sequence[int] = [],
        titles: str | tuple[str, ...] = '',
        contents: str | tuple[str, ...] | tuple[tuple[str, ...], ...] = '',
        columns: tuple | InstrumentedAttribute | Mapped | None = None,
        can_ignore_doc_chunk_ids: bool = True,
        # 为了后续调用 _filter_title() 和 _filter_content()，如果传入了 doc_chunk_ids，且需要过滤 titles 或 contents， columns 中应包含 table_title 或 content_md
    ) -> Sequence:
        if columns is None:
            table_chunk_query = select(TableChunk)
            execute_func = session.scalars
        else:
            if isinstance(columns, (list, tuple)):
                table_chunk_query = select(*columns)
                execute_func = session.execute
            else:
                table_chunk_query = select(columns)
                execute_func = session.scalars

        table_chunk_query = table_chunk_query.where(TableChunk.doc_id == doc_id, TableChunk.is_delete == 0)

        if doc_chunk_ids:
            # 优先从传入的文档切片中查找表格切片
            table_chunk_query_with_filters = table_chunk_query.where(col(TableChunk.doc_chunk_id).in_(doc_chunk_ids))
            table_chunks = (await execute_func(table_chunk_query_with_filters)).all()
            if table_chunks:  # 检索到切片，开始缩小范围
                if titles:
                    table_chunks_with_title = cls._filter_title(table_chunks, titles)
                    if table_chunks_with_title:
                        table_chunks = table_chunks_with_title

                if contents:
                    table_chunks_with_content = cls._filter_content(table_chunks, contents)
                    if table_chunks_with_content:
                        table_chunks = table_chunks_with_content

                return table_chunks

            if not can_ignore_doc_chunk_ids:  # 不看忽略文档切片，不再继续查找
                return []

        # 没有传入的文档切片，或这些切片下没有找到表格切片，从所有表格切片中查找

        title_filter: ColumnElement | None = None
        if titles:
            if isinstance(titles, str):
                title_filter = col(cls.table_title).like(f'%{titles}%')
            else:
                title_filters: list[ColumnElement] = []
                for title in titles:
                    title_filters.append(col(cls.table_title).like(f'%{title}%'))
                title_filter = or_(*title_filters)

        content_filter: ColumnElement | None = None
        if contents:
            if isinstance(contents, str):
                content_filter = col(TableChunk.content_md).like(f'%{contents}%')
            else:
                content_filters: list[ColumnElement] = []
                for content in contents:
                    if isinstance(content, str):
                        content_filters.append(col(TableChunk.content_md).like(f'%{content}%'))
                    else:
                        and_filters = []
                        for keyword in content:
                            and_filters.append(col(TableChunk.content_md).like(f'%{keyword}%'))
                        content_filters.append(and_(*and_filters))
                content_filter = or_(*content_filters)

        table_chunks = []
        table_chunk_query_with_filters = table_chunk_query
        if title_filter is not None:
            table_chunk_query_with_filters = table_chunk_query_with_filters.where(title_filter)
            table_chunks = (await execute_func(table_chunk_query_with_filters)).all()

        if content_filter is not None:
            table_chunk_query_with_filters = table_chunk_query_with_filters.where(content_filter)
            table_chunks_with_content = (await execute_func(table_chunk_query_with_filters)).all()
            if table_chunks_with_content:
                return table_chunks_with_content
            elif title_filter is not None:
                # 忽略标题，只检索正文，再重试一次
                table_chunk_query_with_filters = table_chunk_query.where(content_filter)
                table_chunks_with_content = (await execute_func(table_chunk_query_with_filters)).all()
                if table_chunks_with_content:
                    return table_chunks_with_content

        return table_chunks

    @classmethod
    def _filter_title(
        cls,
        table_chunks: Sequence,
        table_titles: str | tuple[str, ...] | list[str],
    ) -> Sequence:
        if not table_chunks:
            return []

        # 在已检索到的切片中过滤标题关键字
        if isinstance(table_titles, str):
            pattern = compile(escape(table_titles).replace('%', '.*'))
        else:
            patterns = []
            for title in table_titles:
                patterns.append(escape(title).replace('%', '.*'))
            # 合并所有模式为一个正则表达式
            pattern = compile(f"({'|'.join(patterns)})")

        table_chunk = table_chunks[0]
        if hasattr(table_chunk, 'table_title'):
            return [table_chunk for table_chunk in table_chunks if pattern.search(table_chunk.table_title)]
        else:  # 不是切片列表，而是 table_title 的列表
            return [table_chunk for table_chunk in table_chunks if pattern.search(table_chunk)]

    @classmethod
    def _filter_content(
        cls,
        table_chunks: Sequence,
        contents: str | tuple[str | tuple[str, ...], ...] = '',
    ) -> Sequence:
        if not table_chunks:
            return []

        # 在已检索到的切片中过滤正文关键字
        if isinstance(contents, str):
            pattern = compile(escape(contents).replace('%', '.*'))
        else:
            or_patterns = []
            for content in contents:
                if isinstance(content, str):
                    # 如果是单个字符串，添加到模式中
                    or_patterns.append(escape(content).replace('%', '.*'))
                else:
                    # 如果是元组，表示是一个关键字组，所有关键字需要同时匹配
                    and_pattern = ''.join([f'(?=.*{escape(keyword)})' for keyword in content])
                    or_patterns.append(and_pattern)
            # 合并所有模式为一个正则表达式
            pattern = compile(f"({'|'.join(or_patterns)})")

        table_chunk = table_chunks[0]
        if hasattr(table_chunk, 'content_md'):
            return [table_chunk for table_chunk in table_chunks if pattern.search(table_chunk.content_md)]
        else:  # 不是切片列表，而是 content_md 的列表
            return [table_chunk for table_chunk in table_chunks if pattern.search(table_chunk)]
