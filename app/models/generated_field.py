from app.clients.redis import redis_client
from app.schemas.doc_base_field import TrialSubType


class GeneratedField:
    """
    用于在 Redis 中保存临时生成的字段。
    全部生成完毕后，应该移入 MySQL 中。

    type: hash
    key: "doc:{doc_id}" or "project:{project_id}"
    field: "{field_name}"
    value: "{field_value}"
    """

    DOC_KEY = 'doc:{}'
    PROJECT_KEY = 'project:{}'

    # 基础字段
    TRIAL_TITLE = 'trial_title'
    TRIAL_INSTITUTION = 'trial_institution'
    TRIAL_NUMBER = 'trial_number'
    SPECIES = 'species'
    SOLVENT_AND_DOSAGE_FORM = 'solvent_and_dosage_form'
    ADMINISTRATION_METHOD = 'administration_method'
    ADMINISTRATION_UNIT = 'administration_unit'
    DOSING_REGIMEN = 'dosing_regimen'
    ADMINISTRATION_DOSAGE = 'administration_dosage'
    TEST_PRODUCT = 'test_product'
    APPLICANT = 'applicant'

    # report2 字段
    EXPERIMENT_TYPE = 'experiment_type'

    # report3 字段
    PK_TABLE = 'pk_table'

    # report5_6 字段
    ADMINISTRATION_METHOD_SOLVENT_AND_DOSAGE_FORM = 'administration_method_solvent_and_dosage_form'
    GENDER_AND_NUMBER_PER_GROUP = 'gender_and_number_per_group'
    DOSAGE_MAX = 'dosage_max'
    NOTABLE_RESULT = 'notable_result'

    # report7 字段
    TABLE_7_2 = 'table_7_2'  # 267.7主表-下半
    TABLE_7_2_HEADER = 'table_7_2_header'  # 267.7主表-下半表头
    TABLE_7_2_DATA = 'table_7_2_data'  # 267.7主表-下半数据
    TABLE_7_3_FOOD_INTAKE = 'table_7_3_food_intake'  # 267.7 续表 - 摄食量
    TABLE_7_3_CLINICAL_OBSERVATION = 'table_7_3_clinical_observation'  # 267.7 续表 - 临床观察
    TABLE_7_3_GROSS_PATHOLOGY = 'table_7_3_gross_pathology'  # 267.7 续表 - 大体病理学
    TABLE_7_3_NEAR_DEATH_OR_DYING = 'table_7_3_near_death_or_dying'  # 267.7 续表 - 濒死/死亡
    TABLE_7_3_URINALYSIS = 'table_7_3_urinalysis'  # 267.7 续表 - 尿液分析
    TABLE_7_3_HEMAGGLUTINATION = 'table_7_3_hemagglutination'  # 267.7 续表 - 血凝

    # report7 字段
    INITIAL_AGE = 'initial_age'  # 初始年龄
    RECOVERY_PERIOD = 'recovery_period'  # 恢复期
    DATE_OF_FIRST_DOSAGE = 'date_of_first_dosage'  # 首次给药日期
    NO_ADVERSE_REACTION_DOSAGE = 'no_adverse_reaction_dosage'  # 未见不良反应剂量

    # report7 续表的动态表格
    TABLE_7_3 = 'table_7_3'  # 2677 续表 大表

    DYING_OR_DEATH_TABLE = 'dying_or_death_table'  # 濒死/死亡
    ABNORMAL_WEIGHT_TABLE = 'abnormal_weight_table'  # 体重异常
    ABNORMAL_BODY_TEMPERATURE_TABLE = 'abnormal_body_temperature_table'  # 体温异常
    ABNORMAL_ELECTROCARDIOGRAM_TABLE = 'abnormal_electrocardiogram_table'  # 心电图异常
    BLOOD_PRESSURE_TABLE = 'blood_pressure_table'  # 血压
    EYE_EXAMINATION_TABLE = 'eye_examination_table'  # 眼科检查
    BLOOD_COGULATION_TABLE = 'blood_coagulation_table'  # 血凝
    SERUM_ELECTROLYTES_TABLE = 'serum_electrolytes_table'  # 血清电解质
    URINALYSIS_TABLE = 'urinalysis_table'  # 尿液分析

    BLOOD_BIOCHEMISTRY_TABLE = 'blood_biochemistry_table'  # 血生化
    HEMATOLOGY_TABLE = 'hematology_table'  # 血液学
    ORGAN_WEIGHT_TABLE = 'organ_weight_table'  # 器官重量
    HISTOPATHOLOGY_TABLE = 'histopathology_table'  # 组织病理学
    CLINICAL_PATHOLOGY_TABLE = 'clinical_pathology_table'  # 临床病理

    # report8 字段
    NUMBER_OF_PARALLEL_CULTURES = 'number_of_parallel_cultures'
    POSITIVE_CONTROL_SAMPLE = 'positive_control_sample'
    METABOLIC_SYSTEM = 'metabolic_system'
    HANDLE_8 = 'handle_8'
    CYTOTOXIC_EFFECTS_8 = 'cytotoxic_effects_8'
    GENOTOXIC_EFFECTS_8 = 'genotoxic_effects_8'
    TABLE_8_1 = 'table_8_1'

    # report8_2 染色体畸变 字段
    NUMBER_OF_PARALLEL_CULTURES_CHROMOSOME = 'number_of_parallel_cultures_chromosome'  # 平行培养物数量
    METABOLIC_SYSTEM_CHROMOSOME = 'metabolic_system_chromosome'  # 代谢系统
    ANALYZE_CELL_NUMBER_CHROMOSOME = 'analyze_cell_number_chromosome'  # 分析细胞数量
    ADMINISTRATION_DATE_CHROMOSOME = 'administration_date_chromosome'  # 给药日期
    POSITIVE_CONTROL_SAMPLE_CHROMOSOME = 'positive_control_sample_chromosome'  # 阳性对照
    HANDLE_CHROMOSOME = 'handle_chromosome'  # 处理
    TOXIC_EFFECTS_CHROMOSOME = 'toxic_effects_chromosome'  # 细胞毒性作用
    GENOTOXIC_EFFECTS_CHROMOSOME = 'genotoxic_effects_chromosome'  # 遗传毒性作用
    SMALL_CH_TABLE_TEXT = 'small_ch_table_text'  # 染色体试验部分下表-小表格的文本
    CH_TABLE = 'ch_table'  # 染色体试验部分下表-动态表格

    # report9 字段
    SAMPLING_TIME = 'sampling_time'
    AGE = 'age'
    ADMINISTRATION_METHOD_9 = 'administration_method_9'
    ADMINISTRATION_DATE = 'administration_date'
    EVALUATE_CELLS = 'evaluate_cells'
    CELL_NUMBER = 'cell_number'
    TOXIC_EFFECTS = 'toxic_effects'
    GENOTOXIC_EFFECTS_9 = 'genotoxic_effects_9'

    # report9 MCTable 动态表格
    SMALL_MC_TABLE = 'small_mc_table'
    MC_TABLE = 'mc_table'
    MALE_MC_TABLE = 'male_mc_table'
    FEMALE_MC_TABLE = 'female_mc_table'
    MC_TABLE_FOOTER = 'mc_table_footer'

    @classmethod
    async def _count(cls, key: str) -> int:
        return await redis_client.hlen(key)

    @classmethod
    async def _get(cls, key: str, field_name: str) -> str | None:
        return await redis_client.hget(key, field_name)

    @classmethod
    async def _multi_get(cls, key: str, *field_names: str) -> list[str] | None:
        return await redis_client.hmget(key, *field_names)

    @classmethod
    async def _get_all(cls, key: str) -> dict[str, str]:
        return await redis_client.hgetall(key)

    @classmethod
    async def _set(cls, key: str, field_name: str, field_value: str) -> None:
        await redis_client.hset(key, field_name, field_value)

    @classmethod
    async def get_by_doc_id(cls, doc_id: int, field_name: str) -> str | None:
        key = cls.DOC_KEY.format(doc_id)
        return await cls._get(key, field_name)

    @classmethod
    async def get_multi_by_doc_id(cls, doc_id: int, *field_names: str) -> list[str] | None:
        key = cls.DOC_KEY.format(doc_id)
        return await cls._multi_get(key, *field_names)

    @classmethod
    async def get_all_by_doc_id(cls, doc_id: int) -> dict[str, str]:
        key = cls.DOC_KEY.format(doc_id)
        return await cls._get_all(key)

    @classmethod
    async def count_by_doc_id(cls, doc_id: int) -> int:
        key = cls.DOC_KEY.format(doc_id)
        return await cls._count(key)

    @classmethod
    async def get_by_project_id(cls, project_id: int, field_name: str) -> str | None:
        key = cls.PROJECT_KEY.format(project_id)
        return await cls._get(key, field_name)

    @classmethod
    async def get_all_by_project_id(cls, project_id: int) -> dict[str, str]:
        key = cls.PROJECT_KEY.format(project_id)
        return await cls._get_all(key)

    @classmethod
    async def count_by_project_id(cls, project_id: int) -> int:
        key = cls.PROJECT_KEY.format(project_id)
        return await cls._count(key)

    @classmethod
    async def set_for_doc_id(cls, doc_id: int, field_name: str, field_value: str) -> None:
        key = cls.DOC_KEY.format(doc_id)
        await cls._set(key, field_name, field_value)

    @classmethod
    async def set_for_project_id(cls, project_id: int, field_name: str, field_value: str) -> None:
        key = cls.PROJECT_KEY.format(project_id)
        await cls._set(key, field_name, field_value)

    @classmethod
    async def delete(cls, project_id: int, doc_ids: list[int]) -> None:
        keys = [cls.DOC_KEY.format(doc_id) for doc_id in doc_ids]
        keys.append(cls.PROJECT_KEY.format(project_id))
        await redis_client.delete(*keys)


# 各报告字段数：
# 2: 1
# 3: 1，仅重复给药
# 5: 4，仅单次给药
# 6: 4，仅重复给药非关键
# 7_1: 4，仅重复给药关键
# 7_2: 2，仅重复给药关键
# 7_3: 18，仅重复给药关键，表头和数据拆成了2个字段
# 8_1: 7，仅回复突变
# 8_2: 10，仅染色体
# 9: 12，仅微核
FIELD_COUNT_OF_DOC: dict[TrialSubType, int] = {
    TrialSubType.SINGLE_DOSE: 5,
    TrialSubType.REPEATED_DOSE_SPECIFICITY: 26,
    TrialSubType.REPEATED_DOSE: 6,
    TrialSubType.AMES_TEST: 8,
    TrialSubType.CHROMOSOME_ABERRATION_TEST: 11,
    TrialSubType.MICRONUCLEUS_TEST: 13,
}

# 各报告字段数：
# 3: 1
FIELD_COUNT_OF_PROJECT = 1
