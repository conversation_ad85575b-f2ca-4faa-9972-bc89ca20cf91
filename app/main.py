import logging
from contextlib import asynccontextmanager

import uvicorn
from fastapi import FastAP<PERSON>
from fastapi.exceptions import RequestValidationError
from starlette.middleware import Middleware
from starlette.middleware.cors import CORSMiddleware

from app.clients.mysql import async_engine
from app.clients.redis import redis_client
from app.config import config
from app.router import router
from app.task import broker, broker_chunk
from app.utils.etag import ETagMiddleware
from app.utils.exception import (
    HTTPError,
    IND267Error,
    exception_handler,
    http_error_handler,
    ind_error_handler,
    validation_error_handler,
)
from app.utils.importer import auto_import
from app.utils.json_logger import setup_logger, teardown_logger
from app.utils.token import init_token_client, shutdown_token_client

logging.basicConfig(
    level=logging.INFO,
    format='[%(levelname)1.1s %(asctime)s %(pathname)s:%(lineno)d] %(message)s',
    datefmt='%y%m%d %H:%M:%S',
)

auto_import('app/controllers')


# Compare this snippet from app/task.py:
@asynccontextmanager
async def lifespan(flask_app: FastAPI):
    await setup_logger()
    init_token_client()
    await broker.startup()
    await broker_chunk.startup()
    yield
    await broker.shutdown()
    await broker_chunk.shutdown()
    await redis_client.aclose()
    await async_engine.dispose()
    await shutdown_token_client()
    await teardown_logger()


app = FastAPI(
    middleware=[
        Middleware(
            CORSMiddleware,
            allow_credentials=True,
            allow_origins=['*'],
            allow_methods=['*'],
            allow_headers=['*'],
        ),
        Middleware(ETagMiddleware),
    ],
    lifespan=lifespan,
)
app.include_router(router)
app.exception_handler(Exception)(exception_handler)
app.exception_handler(HTTPError)(http_error_handler)
app.exception_handler(IND267Error)(ind_error_handler)
app.exception_handler(RequestValidationError)(validation_error_handler)


if __name__ == '__main__':
    uvicorn.run('main:app', host='0.0.0.0', port=config.APP_PORT, reload=config.RELOAD, workers=1)
