from .base import AzureOpenAIExtractor

'''
dosage_8文件中包含以下章节所有需要经由LLM处理的字段：
2.6.7.8.1 遗传毒性：体外-回复突变试验部分
'''


class NumberOfParallelCultures(AzureOpenAIExtractor):
    """
    9.1.2 - 3
    遗传毒性：体外-回复突变试验部分 - 平行培养物数量
    规则：将原文提交给LLM，判断阳性对照组菌落数：溶媒对照组菌落数的倍数
    """

    SYSTEM_PROMPT = '''- 角色: 数据提取专家。
- 目标: 从输入内容中提取“倍数信息”。
- 工作流:
  1. 解析输入的内容。
  2. 判断“阳性对照组菌落数是溶媒对照组菌落数的多少倍”。
  3. 提取的内容要根据原文截取，不做任何修改。
  4. 将提取结果填入<ANSWER>标签中。
- 示例:
  - 输入: 测试菌株阳性对照组的平均回复突变菌落均数至少是平行溶媒对照组回复突变菌落均数的3倍以上。
  - 输出: <ANSWER>3倍以上</ANSWER>'''


class PositiveControlSample(AzureOpenAIExtractor):
    """
    9.1.2 - 8
    遗传毒性：体外-回复突变试验部分 - 阳性对照品
    规则：提取所有的阳性对照品，去重后以“、”分割排列
    """

    SYSTEM_PROMPT = '''- 角色: 数据提取专家。
- 目标: 从输入内容中提取所有阳性对照品的“名称”，并去重。
- 工作流:
  1. 解析输入的内容。
  2. 找到所有阳性对照品的“名称”。
  3. 对所提取的阳性对照品“名称”进行去重，并以“、”分割排列进行返回。
  4. 将提取结果填入<ANSWER>标签中。
- 示例:
  - 输入:
    所有菌株（TA91、TA157和WP2）加代谢活化系统时使用的阳性对照品为：
    名称：	2-氨基蒽（2-AA）
    批号：	STBJ3963

    名称：	2-氨基蒽（2-AA）
    CAS 号:	613-13-8

    菌株TA91不加代谢活化系统时使用的阳性对照品为：
    名称：	2-硝基芴（2-NF）
    失效日期：	2025年06月05日

    菌株TA157不加代谢活化系统时使用的阳性对照品为：
    名称：	ICR-191
    保存条件：	5±3C，避光

    菌株WP2不加代谢活化系统时使用的阳性对照品为：
    名称：	N-甲基-N-硝基-N亚硝基胍（MNNG）
    性状：	固体
    供应商：	上海阿拉丁生化科技股份有限公司
  - 输出: <ANSWER>2-氨基蒽（2-AA）、2-硝基芴（2-NF）、ICR-191、N-甲基-N-硝基-N亚硝基胍（MNNG）</ANSWER>'''


class MetabolicSystem(AzureOpenAIExtractor):
    """
    9.1.2 - 9
    遗传毒性：体外-回复突变试验部分 - 代谢系统
    规则：将原文提交给LLM，判断文中提及的代谢活化系统的具体代指
    """

    SYSTEM_PROMPT = '''- 角色: 数据提取专家。
- 目标: 从输入内容中提取提及的“代谢活化系统”具体是指什么。
- 工作流:
    1. 解析输入的内容。
    2. 找到有关“代谢活化系统”的描述。
    3. 截取原文中“代谢活化系统”的结果，不做任何删改进行返回。
    4. 将提取结果填入<ANSWER>标签中。
- 示例:
    - 输入: 本次试验通过检测新药001在添加或不添加外源性代谢活化系统（β-萘黄酮和苯巴比妥诱导的大鼠肝脏S9）条件下诱导组氨酸营养缺陷型鼠伤寒沙门氏菌（TA98、TA100、TA1535和TA1537）和色氨酸营养缺陷型大肠埃希杆菌（WP2 uvrA）产生回复突变的能力，评价受试物潜在的致突变性。
    - 输出: <ANSWER>代谢活化系统（β-萘黄酮和苯巴比妥诱导的大鼠肝脏S9）</ANSWER>'''


class CytotoxicEffects8(AzureOpenAIExtractor):
    """
    9.1.2 - 11
    遗传毒性：体外-回复突变试验部分 - 细胞毒性作用
    规则：将原文提交给LLM，判断“受试物”的“细菌毒性”为阴性或阳性, 如果是阴性返回“无”；如果是阳性则返回具体的毒性作用。
    """

    SYSTEM_PROMPT = '''- 角色: 数据提取专家。
- 目标: 根据输入内容，判断受试物的“细菌毒性”相关信息。
- 工作流:
    1. 解析输入的内容，找到关于受试物“细菌毒性”的相关描述。
    2. 如果含义为“阴性”或“无细菌毒性”，则返回“无”。
    3. 如果含义为“阳性”或“有细菌毒性”，则截取原文中描述细菌毒性的片段。
    4. 最后将提取结果填入<ANSWER>标签中。
- 示例:
    - 输入: 在所有混合物条件下，所有给药剂量的细菌毒性均为阴性。
    - 输出: <ANSWER>无</ANSWER>
    - 输入：在1000 μg/皿时，细胞的存活率低于30%，表明此浓度下的有较强的细胞毒性。
    - 输出：<ANSWER>在1000 μg/皿时，细胞的存活率低于30%</ANSWER>'''


class GenotoxicEffects8(AzureOpenAIExtractor):
    """
    9.1.2 - 12
    遗传毒性：体外-回复突变试验部分 - 遗传毒性作用
    规则：将原文提交给LLM，判断有无“遗传毒性”，或为阴性或阳性, 如果是阴性返回“无”；如果是阳性则返回具体的毒性作用。
    """

    SYSTEM_PROMPT = '''- 角色: 数据提取专家。
- 目标: 根据输入内容，判断受试物的“遗传毒性”结果，并返回相应的描述。
- 工作流:
    1. 解析输入的内容，找到有关受试物“遗传毒性”测试结果的描述。
    2. 如果结果为“阴性”或“无遗传毒性”，则返回“无”。
    3. 如果结果为“阳性”或“有遗传毒性”，则截取原文中描述遗传毒性结果的片段。
    4. 将最后结果填入<ANSWER>标签中。
- 示例:
    - 输入: 试验有效，新药aaa在本次试验中的测试结果为阴性。
    - 输出: <ANSWER>无</ANSWER>'''
