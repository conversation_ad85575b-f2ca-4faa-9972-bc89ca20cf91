from .base import AzureOpenAIExtractor


class InitialAge(AzureOpenAIExtractor):
    """
    初始年龄
    文档：《4232-31003-231610》
    位置：6.5.1.动物-正文表3-实验动物
    文档：《4232-31003-232003》
    位置：6.5.动物-正文表3-实验动物
    规则：第一次给药时动物年龄同行
    """

    SYSTEM_PROMPT = '''- 角色: 医学研究分析专家
- 目标: 提取表格中的"第一次给药时动物年龄"。
- 工作流程:
  1. 阅读并理解医学表格。
  2. 提取"第一次给药时动物年龄"。
  3. 若提取出来的结果不止一个，只取第一个结果。
  4. 若没有数据，返回空。
  5. 将结果填入<ANSWER>标签中返回。
- 示例一:
  - 输入:
| 参数 | 雄性 | 雌性 |
| 首次给药时动物年龄 | 1-3岁 | 0-1岁 |
| 第五次给药时动物年龄 | 5-8岁 | 7-10岁 |
| 第一次给药时动物体重 | 100 g | 100 g |
  - 输出: <ANSWER>1-3岁</ANSWER>
- 示例二:
  - 输入:
| 参数 | 雄性 | 雌性 |
| 动物接收日期 | 2011年10月06日 | 2011年10月06日 |
  - 输出: <ANSWER></ANSWER>'''


class RecoveryPeriod(AzureOpenAIExtractor):
    """
    恢复期
    文档：《4232-31003-231610》
    位置：试验标题
    文档：《4232-31003-232003》
    位置：试验标题
    规则：将#试验标题#提交给LLM，判断#恢复期#
    """

    SYSTEM_PROMPT = '''- 角色: 数据提取专家。
- 目标: 从输入内容中提取有关"恢复期"的信息。
- 工作流程:
  1. 解析输入的内容。
  2. 从中提取有关"恢复期"的描述。
  3. 若提取出来的结果不止一个，只取第一个结果。
  4. 若没有数据，返回空。
  5. 将结果填入<ANSWER>标签中返回。
- 示例一:
  - 输入: AAA鲨鱼经动脉注射给予新药555连续30小时及恢复100天的毒理学及试验
  - 输出: <ANSWER>100天</ANSWER>
- 示例二:
  - 输入: XXX人体经动手术给予新药20连续5天的毒理学及试验
  - 输出: <ANSWER></ANSWER>'''


class DateOfFirstDosage(AzureOpenAIExtractor):
    """
    首次给药日期
    文档：《重复给药毒性-关键试验报告》
    位置：5.2.试验日程表
    文档：《4232-31003-232003》
    位置：5.2.试验日程表
    文档：《大鼠经口重复给药28天毒性试验》
    位置：1.3.试验日程
    文档：《比格犬经口重复给药4周毒性试验》
    位置：1.3.试验日程
    规则：第一次给药日期同行
    """

    SYSTEM_PROMPT = '''- 角色: 医学研究分析专家
- 目标: 提取表格中的"首次给药日期"。
- 工作流程:
  1. 阅读并理解医学表格。
  2. 提取"首次给药日期"。
  3. 若提取出来的结果不止一个，只取第一个结果。
  4. 若没有数据，返回空。
  5. 将结果填入<ANSWER>标签中返回。
- 示例一:
  - 输入:
| 研究开始日期 | 2000年02月20日 |
| 第一次给药日期 | 2011年10月24日（雄性动物）<br>2011年10月24日（雌性动物） |
| 最后一次给药日期 | 2024年01月17日（雄性动物）<br>2024年01月18日（雌性动物） |
  - 输出: <ANSWER>2011年10月24日（雄性动物）\n2011年10月24日（雌性动物）</ANSWER>
- 示例二:
  - 输入:
| 参数 | 雄性 | 雌性 |
| 动物接收日期 | 2011年10月06日 | 2011年10月06日 |
  - 输出: <ANSWER></ANSWER>'''


class NoAdverseReactionDosage(AzureOpenAIExtractor):
    """
    未见不良反应剂量
    文档：《4232-31003-231610》
    位置：8.结论
    文档：《4232-31003-232003》
    位置：8.结论
    文档：《大鼠经口重复给药28天毒性试验》
    位置：9.讨论和结论
    文档：《比格犬经口重复给药4周毒性试验》
    位置：1.3.试验日程
    规则：10.综合讨论
    """

    SYSTEM_PROMPT = '''- 角色: 数据提取专家。
- 目标: 从输入内容中提取有关"未见不良反应剂量"的信息。
- 工作流程:
  1. 解析输入的内容。
  2. 从中提取有关"未见不良反应剂量"的描述。它可能还有其他表述形式，例如无可见不良反应剂量、无毒性反应剂量、NOAEL等。
  3. 若提取出来的结果不止一个，只取第一个结果。
  4. 若没有数据，返回空。
  5. 将结果填入<ANSWER>标签中返回。
- 示例一:
  - 输入: 旧药XXX的未见不良反应剂量（NOAEL）为20 g/kg/month（雄性）和520 g/kg/month（雌性）。该剂量下连续给药第50天，血浆中新药xxx的Cmax和AUClast平均值，雄性分别为222 ng/mL和666 hr*ng/mL，雌性分别为777 ng/mL和888 hr*ng/mL。
  - 输出: <ANSWER>20 g/kg/month（雄性）和520 g/kg/month（雌性）</ANSWER>
- 示例二:
  - 输入: 首、末次毒代动力学参数分析表明，各剂量组动物体内XXX、XXX、XXX的AUC（0-t）、Cmax存在明显的差异：雄性动物末次给药后，各剂量组XXX的AUC（0-t）、Cmax与首次相近；各剂量组XXX的AUC（0-t）、Cmax、各剂量组XXX的AUC（0-t）及高剂量组XXXCmax均较首次出现不同程度降低，仅低、中剂量组XXXCmax较首次增高。雌性动物末次给药后，中、高剂量组XXX的AUC（0-t）、Cmax与首次相近，而低剂量组较首次增高；高剂量组XXX的AUC（0-t）、Cmax，XXX的Cmax较首次明显增高；中剂量组的XXX和XXX的AUC（0-t）、Cmax较首次出现不同程度降低；低剂量组XXX和XXX仅Cmax较首次略有增高。
  - 输出: <ANSWER></ANSWER>'''
