import json
from typing import Sequence

from sqlalchemy.engine.row import Row

from app.schemas.table import Table, TableRows

'''
表格生成过程：
1. 将sql中的table_chunk数据传入，并将所有表格解析成6个字段信息：
    substance:起始表第一行：“受试物”名称
    solvent:起始表第一行：“溶剂”名称
    strain_of_bacteria:主体表“菌株”名称
    s9:主体表“S9混合物”
    doses:主体表“剂量”列数据
    average_and_stds:主体表“平均菌落数±标准差”列数据

2. 根据s9的正负性，将所有表格json分成两组：positive_s9_data、negative_s9_data；
3. 根据两组数据进行表格生成：
    1. 生成行表头：
        [['代谢活化', '供试品', '剂量水平(μg/皿)'],
        ['代谢活化', '供试品', '剂量水平(μg/皿)']]
    2. 根据供试品名称、剂量列表生成列表头:
        [['代谢活化', '供试品', '剂量水平(μg/皿)'],
        ['代谢活化', '供试品', '剂量水平(μg/皿)'],
        ['有代谢活化', 'xxx', 'xxx'],
        ['有代谢活化', 'xxx', 'xxx'],
        ['有代谢活化', '溶媒', 'xxx'],
        ['有代谢活化', '阳性对照', '阳性对照']]
    3. 遍历所有表的“菌株”和“平均菌落数±标准差”，按列拼接在小表后面：
        [['代谢活化', '供试品', '剂量水平(μg/皿)', '回复突变菌落数（平均菌落数±标准差）'],
        ['代谢活化', '供试品', '剂量水平(μg/皿)', 'TAxx'],
        ['有代谢活化', 'xxx', 'xxx', 'xxx ± xx'],
        ['有代谢活化', 'xxx', 'xxx', 'xxx ± xx'],
        ['有代谢活化', '溶媒', '溶媒', 'xxx ± xx'],
        ['有代谢活化', '阳性对照', '阳性对照', 'xxx ± xx']]
    4. 将无代谢活化的数据拼接到下面：
        [['代谢活化', '供试品', '剂量水平(μg/皿)', '回复突变菌落数（平均菌落数±标准差）', '回复突变菌落数（平均菌落数±标准差）'],
        ['代谢活化', '供试品', '剂量水平(μg/皿)', 'TAxx', 'TAxx'],
        ['有代谢活化', 'xxx', 'xxx', 'xxx ± xx', 'xxx ± xx'],
        ['有代谢活化', 'xxx', 'xxx', 'xxx ± xx', 'xxx ± xx'],
        ['有代谢活化', 'xxx', 'xxx', 'xxx ± xx', 'xxx ± xx'],
        ['有代谢活化', 'xxx', 'xxx', 'xxx ± xx', 'xxx ± xx'],
        ['无代谢活化', 'xxx', 'xxx', 'xxx ± xx', 'xxx ± xx'],
        ['无代谢活化', 'xxx', 'xxx', 'xxx ± xx', 'xxx ± xx'],
        ['无代谢活化', 'xxx', 'xxx', 'xxx ± xx', 'xxx ± xx'],
        ['无代谢活化', 'xxx', 'xxx', 'xxx ± xx', 'xxx ± xx']]
'''

SrcTable = tuple[str, str, list[str], list[str]]  # 菌株, S9混合物, 剂量, 平均菌落数±标准差
MixedSrcTable = tuple[
    str, str, list[str], list[str], str, list[str], list[str]
]  # 菌株, S9混合物1, 剂量, 平均菌落数±标准差, S9混合物2, 剂量_2, 平均菌落数_2±标准差


def get_src_table(title: str, contents_list: TableRows) -> SrcTable | MixedSrcTable | None:
    """
    从源表格中获取下列数据：
    strain_of_bacteria: 主体表“菌株”名称
    s9: 主体表“S9混合物”
    doses: 主体表“剂量”列数据
    average_and_stds: 主体表“平均菌落数±标准差”列数据
    """
    # TODO: 长度检查

    # 只需要“菌株”所在行下面的数据
    if '续' not in title:  # 主表
        for i, row in enumerate(contents_list):
            if '菌株' in row:
                contents_list = contents_list[i:]
                break
    # 续表的第一行就是菌株

    # 定位header的index
    header = contents_list[0]
    # 去掉尾注
    split_index: int = 0
    for i, row in enumerate(contents_list[2:], 1):
        if row[0] != '':
            break
        elif all(cell == '' for cell in row):
            split_index = i

    strain_of_bacteria_index = header.index('菌株')
    s9_index = header.index('S9混合物')
    dose_index = header.index('剂量(μg/皿)')
    average_index = header.index('平均菌落数')
    std_index = header.index('标准差')

    # 提取“菌株”、“剂量”、“S9混合物”、“平均菌落数”、“标准差”的值
    rows = contents_list[1:i]
    first_row = rows[0]
    strain_of_bacteria = first_row[strain_of_bacteria_index]  # 菌株
    if split_index:
        # 同时有 +S9 和 -S9
        s9_1 = first_row[s9_index]  # S9混合物
        rows_1 = rows[:split_index]
        doses = ['阳性对照' if '阳性对照' in row[dose_index] else row[dose_index] for row in rows_1]  # 剂量
        average_and_stds = [f'{row[average_index]}±{row[std_index]}' for row in rows_1]  # 平均菌落数±标准差

        rows_2 = rows[split_index + 1 :]
        s9_2 = rows_2[0][s9_index]  # S9混合物
        doses_2 = ['阳性对照' if '阳性对照' in row[dose_index] else row[dose_index] for row in rows_2]  # 剂量
        average_and_stds_2 = [f'{row[average_index]}±{row[std_index]}' for row in rows_2]  # 平均菌落数±标准差
        return strain_of_bacteria, s9_1, doses, average_and_stds, s9_2, doses_2, average_and_stds_2
    else:
        # 只有 +S9 或 -S9
        s9 = first_row[s9_index]  # S9混合物
        # 若便利了全部元素都没有找到对应的split_index，那么rows全部都要
        rows = contents_list[1:]
        doses = ['阳性对照' if '阳性对照' in row[dose_index] else row[dose_index] for row in rows]  # 剂量
        average_and_stds = [f'{row[average_index]}±{row[std_index]}' for row in rows]  # 平均菌落数±标准差
        return strain_of_bacteria, s9, doses, average_and_stds


def append_table(
    left_table: TableRows, strain_of_bacteria: str, average_and_stds: list[str], append_header: bool = True
) -> TableRows:
    # 添加表头
    if append_header:
        left_table[0].append('回复突变菌落数（平均菌落数±标准差）')
        left_table[1].append(strain_of_bacteria)
        table = left_table[2:]
    else:
        table = left_table
    for row, average_and_std in zip(table, average_and_stds):
        row.append(average_and_std)
    return left_table


def generate_table_8_1(table_chunks: Sequence[Row], substance: str) -> Table:
    """
    主函数：传入所有表格数据，解析获得最后表格进行返回
    table_chunks 的每行包括 table_title 和 content_json 两个 str 字段
    """

    positive_src_tables: list[SrcTable] = []  # 存储有代谢活化数据
    negative_src_tables: list[SrcTable] = []  # 存储无代谢活化数据

    # 解析原表格内容，分别存储为有代谢活化和无代谢活化数据
    for table_title, content_json in table_chunks:
        contents_list = json.loads(content_json)
        src_table = get_src_table(table_title, contents_list)
        if src_table:
            if len(src_table) == 4:  # 单个表
                if src_table[1] == '+S9':
                    positive_src_tables.append(src_table)
                else:
                    negative_src_tables.append(src_table)
            else:  # 2个表
                if src_table[1] == '+S9':
                    positive_src_tables.append(src_table[:4])
                    negative_src_tables.append(src_table[:1] + src_table[4:])
                else:
                    negative_src_tables.append(src_table[:4])
                    positive_src_tables.append(src_table[:1] + src_table[4:])

    # 生成小表列表头
    positive_small_table_header = [['代谢活化', '供试品', '剂量水平(μg/皿)'], ['代谢活化', '供试品', '剂量水平(μg/皿)']]
    negative_small_table_header: TableRows = []  # 无代谢活化的没有前2行表头
    # TODO: 假定 positive_src_tables 和 negative_src_tables 一定有
    src_table = positive_src_tables[0]
    for doses in src_table[2]:
        try:
            float(doses)
        except ValueError:
            # 不是数值时，值可能是"溶媒"、"阳性对照"，需要合并单元格
            positive_small_table_header.append(['有代谢活化', doses, doses])
        else:
            positive_small_table_header.append(['有代谢活化', substance, doses])

    src_table = negative_src_tables[0]
    for doses in src_table[2]:
        try:
            float(doses)
        except ValueError:
            # 不是数值时，值可能是"溶媒"、"阳性对照"，需要合并单元格
            negative_small_table_header.append(['无代谢活化', doses, doses])
        else:
            negative_small_table_header.append(['无代谢活化', substance, doses])

    # 将所有原表格的数据拼接在右边
    positive_medium_table = positive_small_table_header
    for src_table in positive_src_tables:
        positive_medium_table = append_table(positive_medium_table, src_table[0], src_table[3])

    negative_medium_table = negative_small_table_header
    for src_table in negative_src_tables:
        negative_medium_table = append_table(negative_medium_table, src_table[0], src_table[3], append_header=False)

    return positive_medium_table + negative_medium_table
