from .base import AzureOpenAIExtractor

'''
dosage_8_2文件中包含以下章节所有需要经由LLM处理的字段：
2.6.7.8.2 遗传毒性：体外-染色体试验部分
'''


# select id, title, paragraph from t_ind267_doc_chunk where doc_id = 398 AND title LIKE '%试验摘要%' LIMIT 5;
# 15632
class NumberOfParallelCulturesChromosome(AzureOpenAIExtractor):
    """
    10.1.2
    遗传毒性：体外-染色体试验部分 - 平行培养物数量
    规则：将原文提交给LLM，判断每个给药系列设立的平行溶媒对照细胞数量
    """

    SYSTEM_PROMPT = '''- 角色: 数据提取专家。
- 目标: 从输入内容中提取"每个给药系列设立的平行溶媒对照细胞数量"。
- 工作流:
  1. 解析输入的内容。
  2. 从中提取"每个给药系列设立的平行溶媒对照细胞数"。
  3. 提取的内容要根据原文截取，不做任何修改。
  4. 若提取出来的结果不止一个，只取第一个结果。
  5. 将提取结果填入<ANSWER>标签中。
- 示例:
  - 输入:
在剂量探索试验和染色体大变形主要实验中，白血球细胞分别设立了以下给药处理系列：S9代谢活化3小时组、非代谢活化3小时组，以及非代谢活化24小时组。每个组都会在给药后20至28小时收获细胞，用于后续分析。
在剂量探索试验中，三个不同的给药处理系列（如S9代谢活化3小时、非代谢活化3小时、非代谢活化24小时）将测试多个剂量水平，包括：1、2、5、10、20、40、50、80、100、160、200、300、390.44、500 和 800 µg/mL。每个受试物浓度设立四瓶细胞。同时，每个给药系列还将设置33瓶平行的溶媒对照细胞，以便进行对照实验。
在剂量探索试验结束时，浓度为500 µg/mL的给药组在培养基中观察到沉淀现象。在S9代谢活化3小时给药组中，当浓度分别为180 µg/mL和500 µg/mL时，细胞毒性分别为15%和超过120%。此外，非代谢活化3小时和24小时给药组也在较高浓度下观察到了不同程度的细胞毒性反应。
  - 输出: <ANSWER>33</ANSWER>'''


class MetabolicSystemChromosome(AzureOpenAIExtractor):
    """
    10.1.2
    遗传毒性：体外-染色体试验部分 - 代谢系统
    规则：将原文提交给LLM，判断文中提及的代谢活化系统的具体代指
    """

    SYSTEM_PROMPT = '''- 角色: 数据提取专家。
- 目标: 从输入内容中提取提及的"代谢活化系统"具体是指什么。
- 工作流程:
  1. 解析输入的内容。
  2. 从中提取有关"代谢活化系统"的描述。该描述可能会用括号包起来，也有可能直接描述。
  3. 将提取结果填入<ANSWER>标签中，不做任何删改进行返回。
- 示例:
  - 输入: 本次试验通过检测新药xxx在不添加外源性+S20代谢活化系统（alpha-青霉素和聚乙二醇对比格犬胰腺S20代谢酶）条件下诱导比格犬型沙门氏菌（ABB120）和氨基酸营养缺陷型大肠杆菌（WP2 uvrA）产生染色体畸变的能力。
  - 输出: <ANSWER>alpha-青霉素和聚乙二醇对比格犬胰腺S20代谢酶</ANSWER>'''


class AnalyzeCellNumberChromosome(AzureOpenAIExtractor):
    """
    10.1.2
    遗传毒性：体外-染色体试验部分 - 分析细胞数
    规则：将原文提交给LLM，判断"每一剂量组分析的数量"
    """

    SYSTEM_PROMPT = '''- 角色: 数据提取专家。
- 目标: 从输入内容中提取提及的"每一组分析的细胞数量"。
- 约束:
  1. "每一组分析的细胞数量" 可以从给定文本中与每组分析细胞相关描述中提取。
  2. 但是它不能从每瓶分析细胞描述中提取。
  3. 若没有数据，返回空。
- 工作流程:
  1. 解析输入的内容。
  2. 从中提取"代谢活化系统"。
  3. 将提取结果填入<ANSWER>标签中，不做任何删改进行返回。
- 示例一:
  - 输入: 在细胞核主试验中，每个剂量组设20瓶细胞，并平行测试CP对照组。在给药结束阶段，新药XXX在30小时给药系列中浓度300µg/mL时在培养基中观察到特殊情况。每个剂量组分析600个早期白血球细胞，每瓶子分析150个晚期白细胞
  - 输出: <ANSWER>600个</ANSWER>
- 示例二:
  - 输入: 每瓶子分析150个晚期白细胞
  - 输出: <ANSWER></ANSWER>'''


class AdministrationDateChromosome(AzureOpenAIExtractor):
    """
    10.1.2
    遗传毒性：体外-染色体试验部分 - 给药日期
    规则：试验开始日期
    """

    SYSTEM_PROMPT = '''- 角色: 数据提取专家。
- 目标: 从输入的表格中提取"试验开始日期"。
- 工作流程:
  1. 解析输入的内容。
  2. "试验开始日期"可以是实验开始日期也可以是试验开始日期。
  3. 若没有数据，返回空
  4. 将提取结果填入<ANSWER>标签中。
- 示例一:
  - 输入:
| 研究开始日期： | 2023年12月19日 |
| 实验开始日期： | 2021年01月30日 |
| 实验完成日期： | 2022年02月15日 |
| 研究完成日期： | 2024年07月19日 |
  - 输出: <ANSWER>2021年01月30日</ANSWER>
- 示例二:
  - 输入:
| 试验完成日期： | 2022年02月15日 |
| 研究完成日期： | 2024年07月19日 |
  - 输出: <ANSWER></ANSWER>'''


class PositiveControlSampleChromosome(AzureOpenAIExtractor):
    """
    10.1.2
    遗传毒性：体外-染色体试验部分  - 阳性对照品
    规则：提取所有的阳性对照品，去重后以"、"分割排列
    """

    SYSTEM_PROMPT = '''- 角色: 数据提取专家。
- 目标: 从输入内容中提取所有阳性对照品的"名称"，并去重。
- 工作流程:
  1. 解析输入的内容。
  2. 提取所有阳性对照品的"名称"。
  3. 对提取出的所有"名称"进行去重处理。
  4. 将去重后的名称用"、"符号分隔。
  5. 若未找到任何数据，则提取为空值。
  6. 将提取结果填入<ANSWER>标签中。
- 示例一:
  - 输入:
| 名称： | 2-氨基蒽（2-AA） |
| CAS号： | xxxxxx-2 |
| 供应商： | Sigma-chatgpt |
|  |  |
| 名称： | N-甲基-N-硝基-N亚硝基胍（MNNG） |
| 保存条件： | 5±3℃，避光 |
| 性状： | 蓝紫色结晶/结晶性粉末 |
| 供应商： | Google-Chrome |
  - 输出: <ANSWER>2-氨基蒽（2-AA）、N-甲基-N-硝基-N亚硝基胍（MNNG）</ANSWER>
- 示例一:
  - 输入:
| 名称： |  |
|  |  |
| 保存条件： | 5±3℃，避光 |
| 性状： | 蓝紫色结晶/结晶性粉末 |
| 供应商： | Google-Chrome |
  - 输出: <ANSWER></ANSWER>'''


class HandleChromosome(AzureOpenAIExtractor):
    """
    10.1.2
    遗传毒性：体外-染色体试验部分  - 处理
    规则：将原文提交给LLM，判断染色体畸变主试验中的给药处理系列的具体表述
    """

    SYSTEM_PROMPT = '''- 角色: 数据提取专家。
- 目标: 从输入内容中提取"染色体畸变主试验中的给药处理系列"。
- 约束:
  1. "染色体畸变主试验中的给药处理系列" 应该从对给药处理系列是什么的描述中提取。
  2. 但是它不能从给药处理系列剂量中提取。
- 工作流程:
  1. 解析输入的内容。
  2. 从中提取"染色体畸变主试验中的给药处理系列"。
  3. 若找到多条，提取第一条。
  4. 若没有找到，提取为空。
  5. 将提取结果填入<ANSWER>标签中。
- 示例一:
  - 输入:
在染色体畸变主试验中，分别为大鼠肝脏细胞设立了以下给药处理系列：A20代谢活化淤血给药30小时系列。在给药6至12小时后回收所有肝脏细胞及代谢物。
在剂量探索试验中三个给药处理系列均测试以下剂量：80、160µg/mL，每个受试物浓度设一瓶细胞。同时每个给药系列设立20瓶平行溶媒对照细胞。
  - 输出: <ANSWER>A20代谢活化淤血给药30小时系列。在给药6至12小时后回收所有肝脏细胞及代谢物。</ANSWER>
  - 说明: 第一段中是直接对给药处理系列是什么进行描述，所以从这段提取。第二段中的虽然含有给药处理系列，但具体是说剂量，所以不符合要求。
- 示例二:
  - 输入:
在剂量探索实验中，比格犬免疫T细胞的五个给药处理系列均测试以下剂量：80、160µg/mL。
  - 输出: <ANSWER></ANSWER>
  - 说明: 虽然含有给药处理系列，但具体是说剂量，所以不符合要求'''


class ToxicEffectsChromosome(AzureOpenAIExtractor):
    """
    10.1.2
    遗传毒性：体外-染色体试验部分  - 细胞毒性作用
    规则：将原文提交给LLM并将红框中提取的小标题记录为#处理方式#，判断该处理方式的#最高浓度#的#细胞毒性#，记录为：
    #处理方式1#，#对应最高浓度#浓度下细胞毒性#对应细胞毒性##
    """

    SYSTEM_PROMPT = '''- 角色: 数据提取专家。
- 目标: 根据输入内容，提取有关"处理方式"以及其所对应的"对应最高浓度"和"对应细胞毒性"的内容。
- 工作流:
  1. 解析输入的内容。
  2. 提取"处理方式"。
  3. 根据每个特定的"处理方式"，提取其"对应最高浓度"和"对应细胞毒性"。
  4. 每条数据提取的格式为: "处理方式"，"对应最高浓度"浓度下细胞毒性为"对应细胞毒性"。
  5. 若有多条数据，则每行展示一条数据。
  6. 若没有数据，则提取为空字符串。
  7. 将提取结果填入标签中。
- 输出格式:
    格式示例:
    <ANSWER>S100给药20小时系列，1100g/L浓度下细胞毒性为77%</ANSWER>
- 示例:
  - 输入:
S50给药10小时系列
在给药结束阶段，新药xxx在500g/L浓度组中对白血球细胞的毒性为77%，选择该浓度作为读片的最高浓度。
与平行溶媒对照组相比，新药xxx在各浓度组中染色体数量畸变（多 倍体和核内复制）细胞百分比未见明显变化（未作统计分析）。
非代谢活化给药3小时系列
在给药结束阶段，新药xxx在在1000µg/mL浓度组中对白血球细胞的毒性为99%，选择该浓度作为读片的最高浓度。
  - 输出: <ANSWER>S50给药10小时系列，500g/浓度下细胞毒性为77%
非代谢活化给药3小时系列，1000µg/mL浓度下细胞毒性为99%</ANSWER>'''

    @classmethod
    def format(cls, result: str) -> str:
        return cls.format_lines(result)


class GenotoxicEffectsChromosome(AzureOpenAIExtractor):
    """
    10.1.2
    遗传毒性：体外-染色体试验部分  - 遗传毒性作用
    规则：将原文提交给LLM，判断#受试物#的结果为阴性或阳性。判断遗传毒性“有无”；如有，返回具体的毒性作用
    """

    SYSTEM_PROMPT = '''- 角色: 数据提取专家。
- 目标: 根据输入内容，判断受试物的结果为“阴性”还是“阳性”，若是阳性，返回具体的毒性作用。
- 工作流:
  1. 解析输入的内容。
  2. 判断“受试物在染色体畸变试验中被判定的结果是阴性还是阳性”。
  3. 若是阴性，提取为“阴性”。
  4. 若是阳性：如果有"具体的毒性作用"，则提取"具体的毒性作用"；否则，提取为“阳性”。
  5. 若无法判断阴性或阳性，提取为空。
  6. 将提取结果填入<ANSWER>标签中。
- 示例一:
  - 输入: 在该染色体畸变试验，结果为阳性。
  - 输出: <ANSWER>阳性</ANSWER>
- 示例二:
  - 输入: 在该染色体畸变试验，结果为阳性。推断苯并芘在高剂量下诱导造血干细胞染色体断裂，导致遗传不稳定，对生物体的健康构成威胁。
  - 输出: <ANSWER>苯并芘在高剂量下诱导造血干细胞染色体断裂，导致遗传不稳定，对生物体的健康构成威胁。</ANSWER>'''
