from re import IGNORECASE, compile

from app.schemas.table import Table, TableRows
from app.utils.table_7_3_format import format_percentage

from .base import AzureOpenAIExtractor

'''
267.7 重复给药毒性：关键试验（TBC）：续表-体重异常
'''


# 可能的形式：
# 1. day 28
# 2. days 1-28
# 3. days 1-28 (g)
DAYS_PATTERN = compile(r'days? ?\d+(?:-\d+)?(?: ?(?:\([^)]+\)))?', IGNORECASE)


class AbnormalWeightTable(AzureOpenAIExtractor):
    SYSTEM_PROMPT = '''- 角色: 数据转换专家
- 背景: 你是一位精通数据处理和表格转换的专家，能够准确理解和操作复杂的数据结构，将数据从一种格式高效地转换为另一种格式。
- 目标: 根据待生成的表格的表头，从输入的表格中提取雌性和雄性在不同剂量下的数据，生成新的表格，并按照指定格式输出。
- 约束:
  1. 新生成的表格的表头要与提供的待生成的表格的表头一致。
  2. 所有的内容从输入的表格中提取
  3. 待生成的表格的表头中的"动物数量"列的格式为"M:10 | F:10"，其中M代表雄性，F代表雌性，10代表动物数量。

- 输出格式: 新表格将以清晰的列格式展示
- 工作流程:
  1. 识别原始表格中的数据结构和关键信息。
  2. 根据待生成的表格的表头，提取原始表格中的数据。
  3. 对于每项内容，基本提取两行。第一行为原始体重的值，第二行为体重变化百分比的值。如果表格里没有出现这种描述，那自行判断哪些属于体重的值，哪些属于体重变化百分比。
  4. 对于每项内容，首先提取体重变化百分比的值，然后再提取相应的原始体重的值。如果原始体重不只一个值，则提取日期最新的。例如同时存在Day1和Day5的情况下，取Day5的值。如果原始体重有歧义，例如同时存在 Day8 和 Day1-8,则提取Day8。
  5. 对于每一行，若提取不出内容，在此单元格输出"-"。
  6. 按指定格式输出表格，并填入<ANSWER>标签中，不包含其他内容。
- 示例:
  - 输入:
待生成的表格的表头为:
| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 10 | 20 | 30 |
| 动物数量 | M:10 | F:10 | M:10 | F:10 | M:10 | F:10 |
输入的表格为:
| 性别 | 雄性 | 雄性 | 雄性 | 雌性 | 雌性 | 雌性 |
| 剂量（mg/kg/day） | 0 | 5 | 20 | 0 | 10 | 30 |
| Days 1-4 (g) | 1.20 | 3.56 | 4.55 | 2.78 | 6.20 | 8.04 |
| （%差值） | - | +4.80 | -4.08 | - | -6.41 | +32.67 |
| Days 4-8 (g) | 2.12 | 3.11 | 5.22 | 4.33 | 6.85 | 10.01 |
| （%差值） | - | -5.5 | -7.2 | - | -2.2 | -14.4 |
  - 输出:
    <ANSWER>| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 10 | 20 | 30 |
| 动物数量 | M:10 | F:10 | M:10 | F:10 | M:10 | F:10 |
| Days 1-4 (g) | 1.20 | 2.78 | 3.56 | 6.20 | 4.55 | 8.04 |
| （%差值）| - | - | +4.80 | -6.41 | -4.08 | +32.67 |
| Days 4-8 (g) | 2.12 | 4.33 | 3.11 | 6.85 | 5.22 | 10.01 |
| （%差值） | - | - | -5.5 | -2.2 | -7.2 | -14.4 |</ANSWER>'''

    @classmethod
    async def extract(cls, content_md: str, common_header: str) -> str:
        input_text = f'待生成的表格的表头为:\n{common_header}\n输入的表格为:\n{content_md}'
        return await super().extract(input_text)

    @classmethod
    def parse_table(cls, table_text: str, common_header: TableRows) -> Table:
        table: Table = []
        table.extend(common_header)
        table.append(['体重和体重变化'] * len(common_header[0]))
        lines = table_text.split('\n')
        values = lines[2:]  # 前两行是公共表头，第三行开始才是表的内容
        weight_lines = values[::2]  # 体重变化值的行,偶数行
        weight_percent_lines = values[1::2]  # 体重变化百分比的行，奇数行
        for weight_line, weight_percent_line in zip(weight_lines, weight_percent_lines):
            weight_parts = weight_line.strip('|').split('|')
            weight_parts = [part.strip() for part in weight_parts]

            weight_percent_parts = weight_percent_line.strip('|').split('|')
            weight_percent_parts = [part.strip() for part in weight_percent_parts]

            first_part = weight_parts[0]
            match = DAYS_PATTERN.search(first_part)
            head = f'{match.group(0) if match else first_part}\n（%体重变化百分比）'
            row = [head, weight_parts[1], weight_parts[2]]  # 第1、2列是对照组，直接添加
            for weight_part, weight_percent_part in zip(weight_parts[3:], weight_percent_parts[3:]):
                if weight_part == '-' or weight_percent_part == '-':
                    row.append('-')
                else:
                    weight_percent_part = format_percentage(weight_percent_part)
                    row.append(f'{weight_part}\n{weight_percent_part}'.replace('<br>', '\n'))

            table.append(row)

        return table
