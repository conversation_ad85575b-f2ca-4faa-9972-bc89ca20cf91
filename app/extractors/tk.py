from app.schemas.table import Table

from .base import AzureOpenAIExtractor, extract_answer

'''
表格生成分成 3 个阶段：
1. 小表：
    1. 表头：
        | 性别（雄/雌） | 性别（雄/雌） | 雄 | 雌 |
        | 时间 | 剂量(mg/kg) | AUClast (hr*ng/ml) | AUClast (hr*ng/ml) |
    2. 首日剂量：
        | 1 | 10 | | 333 |
        | 1 | 20 | 222 | |
        | 1 | 20 | | 567 |
        | 1 | 40 | 4444 | |
    3. 末日剂量：
        | 14 | 10 | | 333 |
        | 14 | 20 | 222 | |
        | 14 | 20 | | 567 |
        | 14 | 40 | 4344 | |
2. 中表：
    在小表的上方增加表头：
        | 种属 | 种属 | {species} | {species} |
        | 给药途径 | 给药途径 | {route} | {route} |
        | 试验类型 | 试验类型 | {exp_type} | {exp_type} |
3. 大表：
    将所有中表横向合并，其中时间和剂量仅出现一次
'''

TKTableRow = tuple[str, str, str, str]  # 小表或中表的行
TKTableRows = list[TKTableRow]
TKTableValues = dict[str, tuple[str, str]]  # 剂量 -> (雄性AUClast, 雌性AUClast)
SmallTKTable = tuple[TKTableRows, TKTableValues, TKTableValues]  # small_header, first_values, last_values
MediumHeader = tuple[str, str, str]  # species, route, exp_type


class TKTable(AzureOpenAIExtractor):
    """
    毒代动力学
    2.6.7.3
    """

    SYSTEM_PROMPT = '''- Role: 数据转换专家
- Profile: 你是一位精通数据处理和表格转换的专家，能够准确理解和操作复杂的数据结构，将数据从一种格式高效地转换为另一种格式。
- Constrains: 转换过程中需保持数据的准确性和完整性，确保新表格的格式符合用户要求。
- OutputFormat: 新表格将以清晰的列格式展示，包括试验类型、性别、时间、剂量和AUClast值。
- Workflow:
  1. 识别原始表格中的数据结构和关键信息。
  2. 根据用户要求，将数据按照性别、剂量和时间进行分类。
  3. 将分类后的数据按照给药剂量从小到大进行排序。
  4. 将排序后的数据填充到新表格中，无对应数据时填充 -。
  5. 按指定格式输出表格，并填入<ANSWER>标签中，不包含其他内容。
- Example:
  - input:
    | 给药天数 | 组别 | 给药剂量(mg/kg/day) | 性别 | Cmax | AUClast |
    | 给药天数 | 组别 | 给药剂量(mg/kg/day) | 性别 | (ng/ml) | (hr*ng/ml) |
    | 1 | 1 | 20 | 雄性 | 111 | 222 |
    | 1 | 1 | 10 | 雌性 | 222 | 333 |
    | 1 | 2 | 40 | 雄性 | 345 | 4444 |
    | 1 | 2 | 20 | 雌性 | 789 | 567 |
    | 14 | 1 | 20 | 雄性 | 123 | 222 |
    | 14 | 1 | 10 | 雌性 | 222 | 333 |
    | 14 | 2 | 40 | 雄性 | 345 | 4344 |
    | 14 | 2 | 20 | 雌性 | 789 | 567 |
  - output:
    <ANSWER>| 性别（雄/雌） | 性别（雄/雌） | 雄 | 雌 |
    | 时间 | 剂量(mg/kg) | AUClast (hr*ng/ml) | AUClast (hr*ng/ml) |
    | 1 | 10 | - | 333 |
    | 1 | 20 | 222 | - |
    | 1 | 20 | - | 567 |
    | 1 | 40 | 4444 | - |
    | 14 | 10 | - | 333 |
    | 14 | 20 | 222 | - |
    | 14 | 20 | - | 567 |
    | 14 | 40 | 4344 | - |</ANSWER>'''

    SMALL_HEADER = [
        ('性别（雄/雌）', '性别（雄/雌）', '雄', '雌'),
        ('时间', '剂量(mg/kg)', 'AUClast (hr*ng/ml)', 'AUClast (hr*ng/ml)'),
    ]

    @classmethod
    def format(cls, result: str) -> str:
        return extract_answer(result).replace('  \n', '\n')  # 去掉每行末尾的空格

    @classmethod
    def parse_small_table(cls, table_text: str) -> tuple[SmallTKTable, set[str]]:
        """解析小表，返回小表和日期列表"""
        last_days: set[str] = set()
        header: TKTableRows = []
        first_values: TKTableValues = {}
        last_values: TKTableValues = {}
        lines = table_text.split('\n')
        for line in lines[:2]:  # 前2行是表头，不需要解析
            parts = line.strip('|').split('|')
            if len(parts) != 4:
                raise ValueError(f'table column length is not 4: {line}')
            parts = tuple(part.strip() for part in parts)  # 去掉空格
            header.append(parts)  # type: ignore
        for line in lines[2:]:  # 后面是数值部分
            parts = line.strip('|').split('|')
            if len(parts) != 4:
                raise ValueError(f'table column length is not 4: {line}')
            day = parts[0].strip()  # 去掉空格
            if not day.isdigit():
                raise ValueError(f'day is not digit: {line}')
            dosage = parts[1].strip()  # 去掉空格
            try:
                dosage_value = float(dosage)  # TODO: 这里暂时未考虑包含备注的情况，例如 "123 ***"
                if dosage_value.is_integer():  # 去掉小数部分，避免 3.0 和 3 被当成不同剂量
                    dosage = str(int(dosage_value))
                elif dosage.endswith('0'):  # 去掉小数末尾的 0，避免 1.10 和 1.1 被当成不同剂量
                    dosage = str(dosage_value)
            except ValueError:
                raise ValueError(f'dosage is not digit: {line}')
            value = (parts[2].strip(), parts[3].strip())
            if day == '1':
                first_values[dosage] = value
            else:
                last_values[dosage] = value
                last_days.add(day)
        return (header, first_values, last_values), last_days

    @classmethod
    def generate_medium_header(
        cls,
        species: str,  # 种属
        route: str,  # 给药途径
        exp_type: str,  # 试验类型，实际是给药周期
    ) -> TKTableRows:
        """生成中表表头"""
        return [
            ('种属', '种属', species, species),
            ('给药途径', '给药途径', route, route),
            ('试验类型', '试验类型', exp_type, exp_type),
        ]

    @classmethod
    def merge_tables(
        cls,
        medium_headers: list[MediumHeader],
        small_tables: list[SmallTKTable],
        last_days: set[str],
    ) -> Table:
        # 格式化时间
        first_day_str = '第1天'
        last_days_str = f"第{'/'.join(sorted(last_days, key=int))}天"

        # 合并所有剂量并排序
        first_dosages: set[str] = set()
        last_dosages: set[str] = set()
        for small_header, first_values, last_values in small_tables:
            first_dosages.update(first_values.keys())
            last_dosages.update(last_values.keys())
        sorted_first_dosages = sorted(first_dosages, key=float)
        sorted_last_dosages = sorted(last_dosages, key=float)

        # 合并中表
        medium_tables: list[TKTableRows] = []
        for medium_header, (small_header, first_values, last_values) in zip(medium_headers, small_tables):
            expanded_first_values: TKTableRows = []
            expanded_last_values: TKTableRows = []
            # 填充空缺的剂量
            for dosage in sorted_first_dosages:
                value = first_values.get(dosage)
                if value is None:
                    expanded_first_values.append((first_day_str, dosage, '-', '-'))
                else:
                    expanded_first_values.append((first_day_str, dosage, *value))
            for dosage in sorted_last_dosages:
                value = last_values.get(dosage)
                if value is None:
                    expanded_last_values.append((last_days_str, dosage, '-', '-'))
                else:
                    expanded_last_values.append((last_days_str, dosage, *value))

            medium_tables.append(
                cls.generate_medium_header(*medium_header)
                + (small_header or cls.SMALL_HEADER)
                + expanded_first_values
                + expanded_last_values
            )

        # 合并大表
        large_table: Table = []
        if medium_tables:
            # 中表排序
            sorted_medium_tables = [table for _, table in sorted(zip(medium_headers, medium_tables))]

            medium_table = sorted_medium_tables[0]
            for row in medium_table:
                large_table.append(list(row))  # 第 0 个小表需要保留左列
            for medium_table in sorted_medium_tables[1:]:
                for medium_row, large_row in zip(medium_table, large_table):
                    large_row.extend(medium_row[2:])  # 后面的表不需要保留左列

        return large_table
