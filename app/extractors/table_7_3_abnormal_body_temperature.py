from app.schemas.table import Table, TableRows

from .base import AzureOpenAIExtractor

'''
267.7 重复给药毒性：关键试验（TBC）：续表-体温
'''


class AbnormalBodyTemperatureTable(AzureOpenAIExtractor):
    SYSTEM_PROMPT = '''- 角色: 数据转换专家
- 背景: 你是一位精通数据处理和表格转换的专家，能够准确理解和操作复杂的数据结构，将数据从一种格式高效地转换为另一种格式。
- 目标: 根据待生成的表格的表头，从输入的表格标题中提取，表格内容中提取雌性和雄性在不同剂量下的数据，生成新的表格，并按照指定格式输出。
- 约束:
  1. 新生成的表格的表头要与提供的待生成的表格的表头一致。
  2. 所有的内容从输入的表格中提取。
  3. 待生成的表格的表头中的"动物数量"列的格式为"M:10 | F:10"，其中M代表雄性，F代表雌性，10代表动物数量。
  4. 试验日中的内容，D0表示第0天，D1表示第1天，以此类推。11h表示第11小时，22h表示第22小时。D5_22h表示第5天第22小时。
  5. 只提取对照组为"溶媒对照"的时数据。
- 输出格式: 新表格将以清晰的列格式展示
- 工作流程:
  1. 识别原始表格中的数据结构和关键信息。
  2. 根据待生成的表格的表头，提取原始表格中的数据。
  3. 提取出所有的试验日，去重。去重标准是要有相同的天数和小时。例如 D1_1h和D1_1h是相同的。但是 D2_1h和D28_1h是不同的。D2_5h和D2_10h是不同的。
  4. 对于每个试验日，提取出雌性和雄性在不同剂量下的当对照组为溶媒对照下的变化幅度，按照指定格式输出。如果没有变化幅度这个描述，提取你认为相关的数据。
  5. 若提取不出内容，在此单元格输出"-"。
  6. 按指定格式输出表格，并填入<ANSWER>标签中，不包含其他内容。
- 示例一:
  - 输入:
待生成的表格的表头为:
| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 10 | 20 | 30 |
| 动物数量 | M:5 | F:5 | M:5 | F:5 | M:5 | F:5 |
输入的表格为:
| 比较组 | 剂量（mk/kg） | 性别 | 试验日## | 对照组 | 变化幅度# | P* | 毒性反应 | 主要判定依据 |
| 溶媒组 | 0 | M | D0_15h | 阴性对照 | 5%↑ | <0.99 | 否 | 用lasson体温计测量 |
| 溶媒组 | 0 | M | D0_22h | 阴性对照 | 6%↓ | <0.99 | 否 | 用lasson体温计测量 |
| 溶媒组 | 0 | F | D28 | 阴性对照 | 7%↑ | <0.99 | 否 | 用lasson体温计测量 |
| 溶媒组 | 0 | F | D12_11h | 阴性对照 | 7%↑ | <0.99 | 否 | 用lasson体温计测量 |
| 供试品低剂量组 | 5 | M | D0_22h | 溶媒对照 | 8%↑ | <0.99 | 否 | 用lasson体温计测量 |
| 供试品低剂量组 | 5 | M | D12_11h | 溶媒对照 | 16%↑ | <0.99 | 否 | 用lasson体温计测量 |
| 供试品中剂量组 | 20 | M | D0_15h | 溶媒对照 | 9%↓ | ≥0.99 | 是 | 用lasson体温计测量 |
| 供试品中剂量组 | 20 | M | D0_22h | 阴性对照 | 10%↓ | <0.99 | 否 | 用lasson体温计测量 |
| 供试品中剂量组 | 20 | M | D0_22h | 溶媒对照 | 20%↓ | <0.99 | 否 | 用lasson体温计测量 |
| 供试品中剂量组 | 30 | F | D0_15h | 阴性对照 | 11%↓ | <0.99 | 是 | 用lasson体温计测量 |
| 供试品中剂量组 | 30 | F | D0_15h | 溶媒对照 | 12%↓ | <0.99 | 是 | 用lasson体温计测量 |
| 供试品中剂量组 | 30 | F | D28 | 阴性对照 | 24%↓ | <0.99 | 是 | 用lasson体温计测量 |
  - 输出:
    <ANSWER>| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 10 | 20 | 30 |
| 动物数量 | M:5 | F:5 | M:5 | F:5 | M:5 | F:5 |
| D0_15h | - | - | - | - | 9%↓ | 12%↓ |
| D0_22h | - | - | 8%↑ | - | 20%↓ | - |
| D12_11h | - | - | 16%↑ | - | - | - |</ANSWER>
'''

    @classmethod
    async def extract(cls, content_md: str, common_header: str) -> str:
        input_text = f'待生成的表格的表头为:\n{common_header}\n输入的表格为:\n{content_md}'
        return await super().extract(input_text)

    @classmethod
    def parse_table(cls, table_text: str, common_header: TableRows) -> Table:
        table: TableRows = []
        table.extend(common_header)

        table.append(['体温'] * len(common_header[0]))
        lines = table_text.split('\n')
        values = lines[2:]  # 前两行是公共表头，第三行开始才是表的内容
        for value_line in values:
            parts = value_line.strip('|').split('|')
            parts = [part.strip() for part in parts]
            first_part = parts[0].strip()
            head = f'{first_part}\n（%变化幅度）'
            row = [head]
            row.extend(parts[1:])
            table.append(row)

        return table
