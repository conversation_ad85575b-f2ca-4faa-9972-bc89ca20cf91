import json
import re
from re import compile

from .base import AzureOpenAIExtractor

'''
表格生成分成 3 个阶段：
1. 小表：
    1. 表头：
| 受试物 | 给药（μg/mL） | 细胞毒性（%） | 计数细胞总数 | 含有多倍体细胞（%） | 含有核内复制细胞（%） | 裂隙（%） | 畸变染色体（%） | 畸变细胞（%） |
    2. 回填内容：
| S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 |
| DMSO (1%) | DMSO (1%) | 0 | 300 | 0.0  | 0.0  | 0.0 | 0.7  | 0.7  |
| 新药001 | 40 | 3 | 300 | 0.0  | 0.0  | 0.0 | 0.3 | 0.3 |
| 新药001 | 120 | 11 | 300 | 0.0  | 0.0  | 0.3  | 0.0  | 0.0  |
| 新药001 | 200 | 31 | 300 | 0.0  | 0.0  | 0.0 | 0.0  | 0.0  |
| 新药001 | 240 | 47 | 300 | 0.0  | 1.0  | 0.7 | 1.7  | 1.7  |
| CP 5 | CP 5 | ND | 300 | 0.0  | 0.7  | 0.3  | 13.7  | 13.0** |

3. 大表：
    1. 表头：
| 给药（μg/mL） | 给药（μg/mL） | 细胞毒性（%） | 计数细胞总数 | 含有多倍体细胞（%） | 含有核内复制细胞（%） | 裂隙（%） | 畸变染色体（%） | 畸变细胞（%） |
    2. 回填内容:将所有小表竖向合并，并且不要小表的表头；
'''

CHTableRow = tuple[str, str, str, str, str, str, str, str, str]  # 小表以及大表的表头或含有值的行
CHTableRows = list[CHTableRow]


class CHTable(AzureOpenAIExtractor):
    SYSTEM_PROMPT = '''- Role: 数据转换专家
- Profile: 你是一位精通数据处理和表格转换的专家，能够准确理解和操作复杂的数据结构，将数据从一种格式高效地转换为另一种格式。
- Constrains: 转换过程中需保持数据的准确性和完整性，确保新表格的格式符合用户要求。
- OutputFormat: 新表格将以清晰的列格式展示，包括 受试物、给药（μg/mL）、细胞毒性（%）、计数细胞总数、含有多倍体细胞（%）、含有核内复制细胞（%）、裂隙（%）、畸变染色体（%）、畸变细胞（%）
- Workflow:
  1. 识别原始表格中的数据结构和关键信息。
  2. 先提取所有的"给药处理系列"，如 S1代谢活化给药30小时系列、非代谢活化给药4天系列。
  3. 根据提取出的每组 "给药处理系列"，提取对应的 受试物、给药（μg/mL）、细胞毒性（%）、计数细胞总数、含有多倍体细胞（%）、含有核内复制细胞（%）、裂隙（%）、畸变染色体（%）、畸变细胞（%），并填入表格
  4. 出现在表尾中的对于溶媒和对照组的解释信息，无需分别提取其 受试物 和 给药（μg/mL），直接填入表格中，将表格原文对应位置的描述直接填入。
  5. 按指定格式输出表格，并填入<ANSWER>标签中，不包含其他内容。
- Example:
  - input:
| 给药        (µg/mL) | 细胞毒性   (%) | 计数细胞总数 | 含有多倍体细胞 (%) | 含有核内复制细胞  (%) | % 裂隙 | %畸变染色体 | %畸变细胞 |
| S100代谢活化给药10小时系列 | S100代谢活化给药10小时系列 | S100代谢活化给药10小时系列 | S100代谢活化给药10小时系列 | S100代谢活化给药10小时系列 | S100代谢活化给药10小时系列 | S100代谢活化给药10小时系列 | S100代谢活化给药10小时系列 |
| H2O2 (30%) | 0 | 520 | 0.7  | 0.8  | 0.9 | 0.6  | 0.6  |
|  |  |  |  |  |  |  |  |
| 新药xxx(µg/mL) | 新药xxx(µg/mL) | 新药xxx(µg/mL) | 新药xxx(µg/mL) | 新药xxx(µg/mL) | 新药xxx(µg/mL) | 新药xxx(µg/mL) | 新药xxx(µg/mL) |
| 10 | 2 | 100 | 0.5  | 0.2  | 0.1 | 0.4 | 0.3 |
| 20 | 8 | 100 | 0.6  | 0.3  | 0.0  | 0.4  | 0.3  |
|  |  |  |  |  |  |  |  |
| CP 10 | NOTHING | 100 | 0.1  | 0.2  | 0.3  | 24.7  | 26.0** |
| 非代谢活化给药5天系列 | 非代谢活化给药5天系列 | 非代谢活化给药5天系列 | 非代谢活化给药5天系列 | 非代谢活化给药5天系列 | 非代谢活化给药5天系列 | 非代谢活化给药5天系列 | 非代谢活化给药5天系列 |
| H2O2 (30%) | 0 | 100 | 0.0  | 0.0  | 0.3  | 0.3  | 0.3  |
|  |  |  |  |  |  |  |  |
| 新药xxx(µg/mL) | 新药xxx(µg/mL) | 新药xxx(µg/mL) | 新药xxx(µg/mL) | 新药xxx(µg/mL) | 新药xxx(µg/mL) | 新药xxx(µg/mL) | 新药xxx(µg/mL) |
| 55 | 6 | 100 | 0.0  | 0.0  | 0.3  | 0.0  | 0.0  |
|  |  |  |  |  |  |  |  |
| MMA 0.5 | NOTHING | 100 | 0.0  | 0.3  | 0.3  | 18.0  | 222.4** |
表尾:
% 不含裂隙畸变细胞：**, p≤0.001, 使用Fisher's Exact检验
CP=环磷酰胺一水合物; MMA=丝裂霉素A
NOTHING: 阳性对照组的细胞毒性未测定。

  - output:
    <ANSWER>| 受试物 | 给药（μg/mL） | 细胞毒性（%） | 计数细胞总数 | 含有多倍体细胞（%） | 含有核内复制细胞（%） | 裂隙（%） | 畸变染色体（%） | 畸变细胞（%） |
| S100代谢活化给药10小时系列 | S100代谢活化给药10小时系列 | S100代谢活化给药10小时系列 | S100代谢活化给药10小时系列 | S100代谢活化给药10小时系列 | S100代谢活化给药10小时系列 | S100代谢活化给药10小时系列 | S100代谢活化给药10小时系列 | S100代谢活化给药10小时系列 |
| H2O2 (30%) | H2O2 (30%) | 0 | 520 | 0.7 | 0.8 | 0.9 | 0.6 | 0.6 |
| 新药xxx(µg/mL) | 10 | 2 | 100 | 0.5 | 0.2 | 0.1 | 0.4 | 0.3 |
| 新药xxx(µg/mL) | 20 | 8 | 100 | 0.6 | 0.3 | 0.0 | 0.4 | 0.3 |
| CP 10 | CP 10 | NOTHING | 100 | 0.1 | 0.2 | 0.3 | 24.7 | 26.0** |
| 非代谢活化给药5天系列 | 非代谢活化给药5天系列 | 非代谢活化给药5天系列 | 非代谢活化给药5天系列 | 非代谢活化给药5天系列 | 非代谢活化给药5天系列 | 非代谢活化给药5天系列 | 非代谢活化给药5天系列 | 非代谢活化给药5天系列 |
| H2O2 (30%) | H2O2 (30%) | 0 | 100 | 0.0 | 0.0 | 0.3 | 0.3 | 0.3 |
| 新药xxx(µg/mL) | 55 | 6 | 100 | 0.0 | 0.0 | 0.3 | 0.0 | 0.0 |
| MMA 0.5 | MMA 0.5 | NOTHING | 100 | 0.0 | 0.3 | 0.3 | 18.0 | 222.4** |</ANSWER>'''

    @classmethod
    async def extract(cls, content_md: str, footer_text: str, test_product: str) -> str:
        footer_list = json.loads(footer_text)
        processed_footer_text = '\n'.join(footer_list)
        pattern = rf'\| [^|]*{re.escape(test_product)}[^| ]* '
        content_md = re.sub(pattern, f'| {test_product} ', content_md)
        input_text = f'{content_md}\n表尾:\n{processed_footer_text}'
        return await super().extract(input_text)

    @classmethod
    def parse_small_table(cls, table_text: str) -> CHTableRows:
        small_table: CHTableRows = []
        lines = table_text.split('\n')
        for line in lines:
            parts = line.strip('|').split('|')
            parts = tuple(part.strip() for part in parts)
            # assert len(parts) == 9 # 新规则下，无需注意表格有多少列
            small_table.append(parts)

        return small_table

    @classmethod
    def merge_tables(
        cls,
        small_tables: list[CHTableRows],
    ) -> CHTableRows:
        # 大表的表头是固定的
        large_table = [
            (
                '给药（μg/mL）',
                '给药（μg/mL）',
                '细胞毒性（%）',
                '计数细胞总数',
                '含有多倍体细胞（%）',
                '含有核内复制细胞（%）',
                '裂隙（%）',
                '畸变染色体（%）',
                '畸变细胞（%）',
            ),
        ]
        for small_table in small_tables:  # 表格内容合并, 且每个小表不要表头
            large_table.extend(small_table[1:])
        return large_table
