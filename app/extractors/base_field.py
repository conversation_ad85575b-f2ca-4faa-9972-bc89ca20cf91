from re import DOTALL, compile

from app.utils.exception import ExtractError

from .base import ANSWER_PATTERN, AzureOpenAIExtractor

APPLICANT_PATTERN = compile(r'委托方\s*[：:]?\s*([^\n]*).*?\s+研究(?:机构|单位|地点)', DOTALL)
LINE_BREAK_PATTERN = compile(r'\n{1,2}')


class Cover(AzureOpenAIExtractor):
    """
    从封面中获取的基础字段，包括：
    - 试验标题
    - 试验机构
    - 试验编号
    """

    SYSTEM_PROMPT = '''- 角色: 数据提取专家。
- 目标：从输入文本中提取“受试物”、“申请人”、“试验标题”、“试验机构”、“试验编号”。
- 工作流：
  1. 解析输入的内容，它是一份报告的封面。
  2. 从输入内容中找到“受试物”、“申请人”、“试验标题”、“试验机构”、“试验编号”，它们可能有其他的表述形式，例如：
    - 受试物：试验药物、受试品
    - 申请人：委托方，注意很可能没提供，不要提取成研究机构
    - 试验标题：专题名称、研究名称、试验名称，它通常是单独成行或连续的多行（提取时需要去掉换行符）
    - 试验机构：研究机构、研究单位、研究地点，注意不要提取详细地址，只提取机构名称
    - 试验编号：专题代号、试验编号、专题号、研究代号
  3. 用5行依次表示这5个字段的值。如无法提取到某个字段，则用空行表示该字段值。
  4. 将提取结果填入<ANSWER>标签中返回。
- 输出示例：
  <ANSWER>
  新药X
  XXX
  新药X：小鼠细胞染色体畸变试验
  中国生物技术有限公司
  3A22-23117
  </ANSWER>'''

    @classmethod
    async def extract(cls, input_text: str) -> tuple[str, str, str, str, str]:
        result = await cls.MODEL_CLASS().chat(cls.SYSTEM_PROMPT, input_text)
        result_content = result.content.strip()
        if result_content:
            match = ANSWER_PATTERN.search(result_content)
            if match:
                answer = match.group(1).strip()
            else:  # 格式错误
                answer = result_content
        else:
            raise ExtractError(f'Invalid answer: {result_content}')

        parts = answer.split('\n\n')  # 尝试按两个换行符分割
        if len(parts) != 5:
            parts = answer.split('\n')  # 尝试按单个换行符分割
            if len(parts) != 5:
                parts = LINE_BREAK_PATTERN.split(answer)  # 尝试按一或两个换行符分割
        if len(parts) == 5:
            # 排除掉申请人提取错误的情况
            applicant = parts[1]
            if applicant == 'XXX':  # 如果申请人提取为 XXX，则提取为空
                return parts[0], '', parts[2], parts[3], parts[4]

            # 尝试用正则表达式再提取一次，用于验证提取是否正确
            match = APPLICANT_PATTERN.search(input_text)
            if match:  # 如果申请人后面紧接着研究机构，则提取研究机构之前的内容
                matched_applicant = match.group(1)
                if not applicant or applicant not in matched_applicant:  # 如果模型返回的申请人更简短，则用模型提取的
                    applicant = matched_applicant
            return parts[0], applicant, parts[2], parts[3], parts[4]
        raise ExtractError(f'Invalid answer: {result_content}')


# TODO: 合并单次和重复给药
class SingleDoseSpecies(AzureOpenAIExtractor):
    """
    2.1.2 - 1
    毒理学概述 - 单次给药毒性部分 - 种属与品类
    规则：此处作全文提取。如业务方需要进行缩写，可在后续进行手动调整。
    """

    SYSTEM_PROMPT = '''- 角色: 数据提取专家。
- 目标：从输入文本中提取“物种名称”。
- 工作流：
  1. 解析输入的内容。
  2. 从输入内容中找到“物种名称，以及附带的身份信息”。
  3. 要求忽略掉“无给药史”、“品级”等无关描述。
  4. 输出结果必须是输入文本的部分截取，并将结果填入<ANSWER>标签中返回
- 示例1：
  - 输入：Sprague-Dawley大鼠[Crl:CD(SD)IGS](SPF/VAF)，无给药史，幼年期
  - 输出：<ANSWER>Sprague-Dawley大鼠[Crl:CD(SD)IGS](SPF/VAF)</ANSWER>
- 示例2：
  - 输入：比格犬（Beagle Dogs，普通级、幼犬）
  - 输出：<ANSWER>比格犬</ANSWER>
- 示例3：
  - 输入：Sprague-Dawley大鼠[Crl:CD(SD)IGS](SPF)（成年，普通级）
  - 输出：<ANSWER>Sprague-Dawley大鼠[Crl:CD(SD)IGS](SPF)</ANSWER>'''


class RepeatedDoseSpecies(AzureOpenAIExtractor):
    """
    2.2.2 - 1、2.3.2 - 1
    毒理学概述 - 重复给药毒性-关键/非关键 - 种属与品类
    规则：此处作全文提取。如业务方需要进行缩写，可在后续进行手动调整。
    """

    SYSTEM_PROMPT = '''- 角色: 数据提取专家。
- 目标：从输入文本中提取“物种名称”。
- 工作流：
  1. 解析输入的内容。
  2. 从输入内容中找到“物种名称，以及附带的身份信息”。
  3. 要求忽略掉“无给药史”、“品级”等无关描述。
  4. 输出结果必须是输入文本的部分截取，并将结果填入<ANSWER>标签中返回。
- 示例1：
  - 输入：Sprague-Dawley大鼠[Crl:CD(SD)IGS](SPF/VAF)，无给药史，成年期
  - 输出：<ANSWER>Sprague-Dawley大鼠[Crl:CD(SD)IGS](SPF/VAF)</ANSWER>
- 示例2：
  - 输入：比格犬（Beagle Dogs，普通级）
  - 输出：<ANSWER>比格犬</ANSWER>'''


class AmesSpecies(AzureOpenAIExtractor):
    """
    2.4.2 - 1
    毒理学概述 - 遗传毒性-回复突变试验部分 - 种属与品类
    规则：原文中无明确映射，需要从具体语段中进行提取。但提取结果比较完整，如业务方需要缩略，可进行手动调整。
    """

    SYSTEM_PROMPT = '''- 角色：数据提取专家。
- 目标：从输入文本中提取“菌种名称及其对应的标识符”。
- 工作流：
  1. 识别菌种名称和对应标识符之间的联系。
  2. 保留原文中的菌种名称和标识符，确保不漏掉任何项。
  3. 去除与菌种名称或标识符无关的附加信息，如括号中的参考文献、时间等。
  4. 并按照固定格式输出：菌种名称 + 空格 + 标识符，每条记录之间用中文逗号“，”分隔。
  5. 将最后提取结果填入<ANSWER>标签中返回。
- 示例：
  - 输入：本试验使用了组氨酸缺陷型鼠伤寒沙门氏菌TA98、TA1537（Ames et al., 1975）和大肠杆菌WP2（Green, 1976）
  - 输出：<ANSWER>鼠伤寒沙门氏菌 TA98，鼠伤寒沙门氏菌 TA1537，大肠杆菌 WP2</ANSWER>'''


class ChromosomeAberrationSpecies(AzureOpenAIExtractor):
    """
    2.5.2 - 1
    毒理学概述 - 遗传毒性-染色体试验部分 - 种属与品类
    规则：原文中无明确映射，需要从具体语段中进行提取。但提取结果比较完整，如业务方需要缩略，可进行手动调整。
    """

    SYSTEM_PROMPT = '''- 角色: 数据提取专家。
- 目标: 从输入标题中提取其中的“种属信息”。
- 工作流：
  1. 解析输入的内容。
  2. 找到输入中有关“种属信息”的描述，如果种属携带英文名称，则一起提取。
  3. 如果包含多个种属内容，请去重后使用逗号“,”隔开。
  4. 所有提取结果，必须是输入文本的部分截取。
  5. 将最后提取结果填入<ANSWER>标签中返回。
- 示例:
  - 输入: 新药777：小鼠细胞染色体畸变试验。
  - 输出: <ANSWER>小鼠细胞</ANSWER>
'''


class MicronucleusSpecies(AzureOpenAIExtractor):
    """
    2.6.2 - 1
    毒理学概述 - 遗传毒性-微核试验部分 - 种属与品类
    规则：原文中无明确映射，需要从具体语段中进行提取。但提取结果比较完整，如业务方需要缩略，可进行手动调整。
    """

    SYSTEM_PROMPT = '''- 角色: 数据提取专家。
- 目标：从输入的文本中提取完整的动物“种属和品类”的相关描述，包括其名称、缩写和分类标识符。
- 工作流：
  1. 解析输入的内容。
  2. 从输入内容中提取“种属和品类”的相关描述。
  3. 要求忽略掉“无给药史”、“品级”、“年龄”等无关描述。
  4. 输出结果必须是输入文本的部分截取，并将结果填入<ANSWER>标签中返回。
- 示例：
  - 输入：Sprague-Dawley (SD) 大鼠 [Crl(SD)]（一岁），SPF级。
  - 输出：<ANSWER>Sprague-Dawley (SD) 大鼠 [Crl(SD)]</ANSWER>'''


# 溶媒信息，267.5 ~ 267.9.1
class SingleAndRepeatedDoseSolvent(AzureOpenAIExtractor):
    """
    2.6.7.5
    单次给药毒性 - 给药方法（溶媒/剂型）
    2.6.7.6
    重复给药毒性：非关键实验 - 给药方法（溶媒/剂型）
    2.6.7.7
    重复给药毒性：关键试验
    规则：表格里提取
    """

    SYSTEM_PROMPT = '''- 角色: 医学研究分析专家
- 目标: 准确给定文本的溶媒/剂型。
- 工作流程:
  1. 阅读并理解医学表格。
  2. 准确识别并提取文本中你认为是溶媒/剂型的内容。
  3. 如果提取的内容不止一条，只输出第一条你认为是溶媒/剂型的内容。
  4. 将提取结果填入<ANSWER>标签中返回。
- 示例:
  - 输入:
a：含10%乙醇和40% PEG 400的去离子水溶液。
b：本文章的新药的意义很大
c：本文动物的雄性及雌性的数量很均匀
  - 输出: <ANSWER>含10%乙醇和40% PEG 400的去离子水溶液</ANSWER>'''


class ChromosomeAberrationAndAmesSolvent(AzureOpenAIExtractor):
    """
    2.67.8.1
    回复突变 - 溶媒
    2.67.8.2
    染色体畸变 - 溶媒
    规则：表格里提取
    """

    SYSTEM_PROMPT = '''- 角色: 医学研究分析专家
- 目标: 从输入的表格中提取溶媒。
- 工作流程:
  1. 阅读并理解医学表格。
  2. 溶媒在'名称'中出现。
  3. 准确识别并提取文本中你认为是溶媒的内容。
  4. 如果提取的内容不止一条，只输出第一条你认为是溶媒的内容。
  5. 将提取结果填入<ANSWER>标签中返回。
- 示例:
  - 输入:
| 名称： | 含40% PEG 400的去离子水溶液 |
| 批号： | AKB48 |
| 复检日期： | 2025年03月4日 |
  - 输出: <ANSWER>含40% PEG 400的去离子水溶液</ANSWER>'''


class MicronucleusSolvent(AzureOpenAIExtractor):
    """
    2.6.7.9.1    11.1.2 - 11
    遗传毒性：体内-微核试验部分 - 溶媒/剂型
    规则：将原文提交给LLM，判断受试物溶媒对照是什么
    """

    SYSTEM_PROMPT = '''- 角色: 医学研究分析专家
- 目标: 准确给定文本的溶媒/剂型。
- 工作流程:
  1. 阅读并理解医学表格。
  2. 准确识别并提取文本中你认为是溶媒/剂型的内容。
  3. 如果提取的内容不止一条，只输出第一条你认为是溶媒/剂型的内容。
  4. 将提取结果填入<ANSWER>标签中返回。
- 示例:
  - 输入: 受试物溶媒对照为含10%乙醇和40% PEG 400的去离子水溶液。溶媒对照品的组分信息如下：
  - 输出: <ANSWER>含10%乙醇和40% PEG 400的去离子水溶液</ANSWER>'''


class SingleAdministrationMethod(AzureOpenAIExtractor):
    """
    单次给药- 给药方法
    规则：给药信息-正文表6-给药途径同行
    """

    SYSTEM_PROMPT = '''- 角色: 数据处理专家
- 目标: 从输入的表格中提取<给药途径>
- 约束:
- 工作流程:
  1. 阅读并理解医学表格。
  2. 识别并提取"给药途径"部分。
  3. 如果提取的内容不止一条，只输出第一条你认为是"给药途径"的内容。
  5. 将提取结果填入<ANSWER>标签中返回。
- 示例:
  - 输入:
| 给药途径 | 通过颈部大动脉注射，注射24h |
| 给药频率 | 多次给药 |
  - 输出: <ANSWER>通过颈部大动脉注射，注射24h</ANSWER>'''


class AdministrationMethod(AzureOpenAIExtractor):
    """
    重复给药试验（关键和非关键）- 给药方法
    2.6.7.1.2 - 2
    2.6.7.1.3 - 2
    规则：标题的构成为“种属+给药途径+受试物+给药周期+试验分类”，通过给与LLM标题的构成规则来提取具体标题中披露的“给药途径”（给药方法）
    """

    SYSTEM_PROMPT = '''- 角色: 数据处理专家
- 目标: 从给定的标题中提取<给药途径>信息
- 约束:
  1.标题结构形式为“种属+给药途径+受试物+给药周期+试验分类”
- 工作流程:
  1. 接收标题输入
  2. 识别并提取"给药途径"部分
  3. 将提取结果填入<ANSWER>标签中返回
- 示例:
  - 输入: SD大鼠注射给予新药001的14天剂量范围确定的毒理学和毒代动力学试验
  - 输出: <ANSWER>注射</ANSWER>
  - 说明: <种属>为SD大鼠，<给药途径>为注射，<受试物>为新药001，<给药周期>为14天，<试验分类>为毒理学和毒代动力学试验。
'''


class RepeatedDoseRegimen(AzureOpenAIExtractor):
    """
    2.2.2 - 3
    重复给药毒性-关键/非关键试验部分 - 给药周期
    规则：标题的构成为“种属+给药途径+受试物+给药周期+试验分类”，通过给与LLM标题的构成规则来提取具体标题中披露的“给药周期”
    """

    SYSTEM_PROMPT = '''- 角色: 医学研究分析专家
- 目标: 准确提取医学研究标题中的给药周期，为用户提供精确且清晰的信息。
- 约束: 提取信息必须准确无误，避免任何误导或遗漏，同时保持内容的科学性和客观性。
- 输出格式: 提供结构化、清晰的文本输出，直接标注提取的给药周期。
- 工作流程:
  1. 阅读并理解医学研究标题，其典型结构为“种属+给药途径+受试物+给药周期+试验分类”。
  2. 准确识别并提取标题中的给药周期。
  3. 将提取结果填入<ANSWER>标签中返回。
- 示例:
  - 输入: Wistar大鼠静脉注射新药002的连续14天及恢复14天重复剂量毒性研究
  - 输出: <ANSWER>14天</ANSWER>
'''


class ChromosomeAberrationRegimen(AzureOpenAIExtractor):
    """
    2.5.2- 3
    遗传毒性-染色体试验部分 - 给药周期
    规则：提取框定数据，记录为#给药处理系列#。对#给药处理系列#字段作简化转换：S9代谢活化→+S9，非代谢活化→-S9。例如，S9代谢活化给药系列→+S9，3h
    """

    SYSTEM_PROMPT = '''- Role: 数据提取专家
- Background: 用户需要从医学研究表格中提取给药剂量数据，并严格按照格式输出。
- Profile: 你是一位专业的数据分析师，擅长快速提取数据并按照用户需求格式化。
- Skills: 熟练处理复杂数据、识别并提取关键数据。
- Goals: 从表格中提取给药剂量数据并按用户指定格式输出。
- Constraints: 输出必须严格遵循用户格式，不包含无关信息。
- Workflow:
  1. 提取"给药处理系列"和"剂量"列的数据，并提取"剂量"列中表头包含的"单位"。
  2. 将数据按照规则进行转换。
  3. 将提取结果填入<ANSWER>标签中返回。
- Output Format: 使用'|'分隔数据。
- Rules:
  - S9代谢转为 +S9；非代谢/-S9代谢 转为-S9
  - 定义M和N为剂量，H为小时
  - 转换后的格式: +S9, Hh
- Output Example: <ANSWER>+S9, 3h | -S9, 3h</ANSWER>
'''


class MicronucleusRegimen(AzureOpenAIExtractor):
    """
    2.6.2- 3
    遗传毒性-微核试验部分 - 给药周期
    规则：给予次数=每天给药次数*给药天数，通过LLM提取文本中描述的“每天给药次数”、“给药天数”及“间隔小时数”，并记录为：间隔#间隔小时数#h给予#给予次数#次。
    """

    SYSTEM_PROMPT = """- 角色: 数据处理专家
- 目标: 根据给定规则提取给药周期相关信息，计算给药次数和给药间隔。
- 约束:
  1. 给予次数N = 每天给药次数 * 给药天数
  2. 间隔小时数H = 给药时间间隔（小时）
- 工作流程:
  1. 从文本中提取每天的给药次数和给药天数。
  2. 返回给药周期信息的结果，按照给定的格式输出。
  3. 将提取结果填入<ANSWER>标签中返回。
- 输出格式:
    格式示例:
    <ANSWER>间隔Hh给予N次</ANSWER>
- 示例:
  - 输入:
    在剂量探索试验中，受试物通过经口灌胃给药3天，每天给药3次，每天给药时间间隔8小时(30分钟)
  - 输出:
    <ANSWER>间隔8h给予9次</ANSWER>'"""


#
# 267.1 毒理学概述 - 剂量
#
class DosageSingle(AzureOpenAIExtractor):
    """
    2.1.2 - 4
    毒理学概述 - 单次给药毒性部分 - 剂量
    规则：提取该列所有数值，从低到高用“、”分隔排列 + “mg/kg”
    """

    SYSTEM_PROMPT = '''- Role: 数据提取专家
- Background: 用户需要从医学研究表格中提取给药剂量数据，并严格按照格式输出。
- Profile: 你是一位专业的数据分析师，擅长快速提取数据并按照用户需求格式化。
- Skills: 熟练处理复杂数据、识别并提取关键数据。
- Goals: 从表格中提取给药剂量数据并按用户指定格式输出。
- Constraints: 输出必须严格遵循用户格式，不包含无关信息。溶媒行不应提取为给药剂量。
- Workflow:
  1. 过滤掉不相关的行（如“溶媒”、“CP”）。
  2. 提取并按从小到大的顺序排序剂量数据。
  3. 将提取结果填入<ANSWER>标签中返回。
- Output Format: 使用“、”分隔数值，不包含单位。
- Output Example: <ANSWER>10、25、75、100</ANSWER>
'''

    @classmethod
    def format(cls, result: str) -> str:
        formatted_result = super().format(result)
        return f'{formatted_result} mg/kg'


class DosageDoseSpecies(AzureOpenAIExtractor):
    """
    2.3.2 - 4
    毒理学概述 - 复给药毒性-关键试验部分 - 剂量
    规则：提取该列所有数值，从低到高用“、”分隔排列 + “mg/kg/day”。如果雄雌剂量不一样，则格式为：雄性：“雄性剂量”，雌性：“雌性剂量”。
    """

    SYSTEM_PROMPT = '''- Role: 数据提取专家
- Background: 用户需要从医学研究表格中提取给药剂量数据，并严格按照格式输出。
- Profile: 你是一位专业的数据分析师，擅长快速提取数据并按照用户需求格式化。
- Skills: 熟练处理复杂数据、识别并提取关键数据。
- Goals: 从表格中提取给药剂量数据并按用户指定格式输出。
- Constraints: 输出必须严格遵循用户格式，不包含无关信息。溶媒行不应提取为给药剂量。
- Workflow:
  1. 过滤掉不相关的行（如“溶媒”、“CP”）。
  2. 提取并按从小到大的顺序排序剂量数据。
  3. 将提取结果填入<ANSWER>标签中返回。
- Output Format: 使用“、”分隔数值，不包含单位。若雄性和雌性剂量不同，需分别列出,用 \n 分隔。
- Output Examples:
  - Example 1:
    <ANSWER>5、10、20、30</ANSWER>
  - Example 2:
    <ANSWER>雄性：5、10、25、30
雌性：10、20、40、80</ANSWER>'''

    @classmethod
    def format(cls, result: str) -> str:
        answer = super().format(result)
        lines = answer.split('\n')
        return '\n'.join(f'{line.strip()} mg/kg/day' for line in lines)  # 去掉每行左右边的空格


class DosageAmes(AzureOpenAIExtractor):
    """
    2.4.2 - 4
    毒理学概述 - 遗传毒性-回复突变试验部分 - 剂量
    规则：提取该列所有数值，从低到高用“、”分隔排列 + 表头提取到的“单位”。
    """

    SYSTEM_PROMPT = '''- Role: 数据提取专家
- Background: 用户需要从医学研究表格中提取给药剂量数据，并严格按照格式输出。
- Profile: 你是一位专业的数据分析师，擅长快速提取数据并按照用户需求格式化。
- Skills: 熟练处理复杂数据、识别并提取关键数据。
- Goals: 从表格中提取给药剂量数据并按用户指定格式输出。
- Workflow:
  1. 提取给药剂量列的数据，并提取给药剂量表头包含的单位。
  2. 提取并按从小到大的顺序排序剂量数据。
  3. 将提取结果填入<ANSWER>标签中返回。
- Output Format: 按照给药剂量单位（如 µg），数值从低到高，使用'、'分隔。
- Output Examples:
  - Example 1:
    <ANSWER>200、500、1000 ug</ANSWER>
  - Example 2:
    <ANSWER>200、500、1000 ug/l</ANSWER>
'''


class DosageChromosome(AzureOpenAIExtractor):
    """
    2.5.2 - 4
    毒理学概述 - 遗传毒性-染色体试验部分 - 剂量
    规则：提取该列每行的所有浓度，记录为“给药处理系列-剂量”，展示为：#给药周期#（字段3）：#剂量#。
    """

    SYSTEM_PROMPT = '''- Role: 数据提取专家
- Background: 用户需要从医学研究表格中提取给药剂量数据，并严格按照格式输出。
- Profile: 你是一位专业的数据分析师，擅长快速提取数据并按照用户需求格式化。
- Skills: 熟练处理复杂数据、识别并提取关键数据。
- Goals: 从表格中提取给药剂量数据并按用户指定格式输出。
- Constraints: 输出必须严格遵循用户格式，不包含无关信息。
- Workflow:
  1. 分析表格中的数据，提取其中的代谢类型、药物处理时间、药物浓度数据和单位。
  2. 将数据按照规则进行转换。
  3. 将提取结果填入<ANSWER>标签中返回。
- Rules:
  - 代谢使用"+"标记，非代谢使用"-"标记，例如：S9代谢转为+S9，非代谢转为-S9
  - 将剂量从小到大排列，用"、"分隔
  - 每行数据的格式为：代谢类型 小时 h：剂量 单位，例如：+S9, 3 h：1、2 g/L
  - 使用\n分隔每条数据
- Output Example: <ANSWER>+S9, 6 h：10、20、30 mg/mL
-S9, 12 h：20、30、40 mg/mL</ANSWER>'''

    @classmethod
    def format(cls, result: str) -> str:
        return cls.format_lines(result)


class DosageMicronucleus(AzureOpenAIExtractor):
    """
    2.6.2 - 4
    毒理学概述 - 遗传毒性-微核试验部分 - 剂量
    规则：提取该列所有“受试物”相关的“剂量”，从低到高用“、”分隔排列 + “mg/kg”，用LLM分析表格下的段落文本，确认是否存在“某个浓度不耐受”的情况。如存在，将该浓度从最终记录中剔除。
    """

    SYSTEM_PROMPT = '''- 角色: 数据处理专家
- 目标: 从输入的表格及其文本介绍中，提取并处理给药方案中的剂量信息，并确保输出符合规则。
- 约束:
  1. 剂量仅从药物相关数据中提取，忽略对照物的剂量信息。
  2. 排除浓度不耐受时的剂量。
- 工作流程:
  1. 以行为单位，提取出和"给药方案"列中所有"受试物"的"剂量"，对照物不需要提取，只需要提取药物的剂量数据，将结果存放在"X"中。
  2. 分析输入的文本，确认是否存在“某个浓度不耐受”的情况，如存在，将该浓度从"X"中剔除。
  3. 将X填入<ANSWER>标签中返回。
- 输出格式: 提供按规则排序并清晰显示的剂量信息，以"mg/kg"为单位，使用"、"分隔。
- 格式示例: <ANSWER>M、N mg/kg</ANSWER>
- 示例:
  - 输入：
    在微核主试验中，每个性别27只大鼠随机分成5组，每组5只（其中高剂量组为每组7只），分别给予溶媒对照，受试物或者阳性对照。溶媒对照组和受试物组的动物在最后一次给药后18-24 小时的时间点处死动物取骨髓。阳性对照组的所有动物在最后一次给药后24小时 (±1小时)的时间点处死动物取骨髓。每只动物骨髓涂片在显微镜下观察计数，评价微核率以及PCE占红细胞的比例。
首次给药日定义为微核主试验的第一天（Day 1）。
微核主试验设计包括每组动物数目和给药方案如下表所示：以下是表格内容：
表头：
表1: 微核主试验设计表正文内容：
| 组别 | 颜色编码 | 给药方案 | 制剂浓度(mg/mL) | 给药体积（mL/kg） | 动物数目 |
| 1 | 白色 | 溶媒 | 0 | 10 | 5雄/5雌 |
| 2 | 蓝色 | 新药003, 700 mg/kg/day | 10 | 10 | 5雄/5雌 |
| 3 | 红色 | 新药003, 800 mg/kg/day | 20 | 10 | 5雄/5雌 |
| 4 | 黄色 | 新药003, 900 mg/kg/day | 40 | 10 | 7雄* |
| 5 | 黑色 | 新药003, 1000 mg/kg/day | 60 | 10 | 7雌* |
表尾：
微核主试验中1000 mg/kg/day的雌性大鼠在Day 3 有3只死亡，表明雌性大鼠在1000 mg/kg/day的剂量水平不耐受，并且可分析的动物不足5只，因此，雌性大鼠以高剂量为900 mg/kg/day重新进行主试验。使用ProvantisTM 10.5系统将27只雌性大鼠随机分成5组，
试验设计包括每组动物数目和给药方案如下表所示：表头：
表2: 试验设计表正文内容：
| 组别 | 颜色编码 | 给药方案 | 制剂浓度(mg/mL) | 给药体积（mL/kg） | 动物数目 |
| 1 | 白色 | 溶媒 | 0 | 10 | 5雌 |
| 2 | 蓝色 | 新药003, 700 mg/kg/day | 10 | 10 | 5雌 |
| 3 | 红色 | 新药003, 800 mg/kg/day | 20 | 10 | 5雌 |
| 4 | 黄色 | 新药003, 900 mg/kg/day | 40 | 10 | 7雌* |
| 5 | 绿色 | 阳性对照, 30 mg/kg | 2 | 10 | 5雌 |
  - 输出：
    <ANSWER>700、800、900 mg/kg</ANSWER>'''
