from .base import AzureOpenAIExtractor

'''
dosage_2文件中包含了2.6.7.2（毒代动力学：毒代动力学试验概述）中的所有需要经由LLM处理的字段
'''


class ExperimentType(AzureOpenAIExtractor):
    """
    2.6.7.2 - 1
    毒代动力学：毒代动力学试验概述-试验类型
    规则：重复给药毒性试验标题的构成为“种属+给药途径+受试物+试验类型+试验分类”，将标题提交给LLM，提取出其中的“试验类型”
    """

    SYSTEM_PROMPT = '''- Role: 数据提取专家。
- Background: 从标题中提取"试验类型"信息。标题的结构形式是“种属+给药途径+受试物+试验类型+试验分类”。
- Workflow:
  1. 解析输入的标题。
  2. 识别标题的结构形式，并提取"试验类型"。
  3. 将提取结果填入<ANSWER>标签中返回。
- Example1:
  - Input: SD大鼠经口灌胃给予新药003的28天剂量范围确定的毒理学和毒代动力学试验。
  - Output: <ANSWER>28天剂量范围确定</ANSWER>
- Example2:
  - Input: 比格犬经口灌胃重复给药连续14天及恢复14天的毒理学和毒代动力学试验
  - Output: <ANSWER>连续14天及恢复14天</ANSWER>
- Example3:
  - Input: 大鼠单次经口灌胃给予新药的急性毒理学试验
  - Output: <ANSWER>急性毒理学</ANSWER>'''
