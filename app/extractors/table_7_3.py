import re

from app.schemas.table import Table, TableRows
from app.utils.table_7_3_format import format_percentage

from .base import AzureOpenAIExtractor, VolcEngineDeepSeekV3Extractor

'''
267.7 重复给药毒性：关键试验（TBC）：续表
'''

DOSE_PATTERN = re.compile(r'\d+(?:\.\d+)?')


# 267.7 续表 - 表头的生成
def generate_table_7_3_header(administration_dosage: str) -> TableRows:
    """
    administration_dosage：给药剂量，有两种形式：
        1. "2、5、7 mg/kg/day"
        2. "雄性：5、15 mg/kg/day\n雌性：2、7 mg/kg/day"
    return: 2行的表头，分别是日剂量和动物数量，例如：
        [
            ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2', '15', '7'],
            ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
        ]
    """
    # [1]:生成日剂量
    first_row: list[str] = ['日剂量（mg/kg）', '0（对照）', '0（对照）']
    administration_dosage_lines = administration_dosage.split('\n')
    if len(administration_dosage_lines) == 1:
        # 形式 1:雌雄剂量相同，添加两次
        doses = DOSE_PATTERN.findall(administration_dosage)
        for dose in doses:
            first_row.extend([dose, dose])
    else:
        # 形式 2
        male_doses = DOSE_PATTERN.findall(administration_dosage_lines[0])
        female_doses = DOSE_PATTERN.findall(administration_dosage_lines[1])
        for male_dose, female_dose in zip(male_doses, female_doses):
            first_row.extend([male_dose, female_dose])

    # [2]:生成动物数量
    second_row: list[str] = ['动物数量']
    second_row.extend(['M:', 'F:'] * ((len(first_row) - 1) // 2))

    return [first_row, second_row]


# TODO：提示词优化
# 动物数量 -> 性别
# 只返回数据，不返回表头
# 267.7 续表 - 摄食量 LLM提示词
class Get73FoodIntake(AzureOpenAIExtractor):
    """
    提取摄食量信息表格
    """

    SYSTEM_PROMPT = '''- Role: 数据提取专家。
- Background: 从"待解析文本"中分析并提取出"剂量组动物的摄食量"变化情况，然后根据"模板表"表头定位，将"摄食量变化情况"填入对应的位置。
- Skills: 熟练处理复杂数据、识别并提取关键数据，并完成数据的准确填入工作。
- Workflow:
  以下工作流中会用到的特殊名称说明：
  1. 解析"待解析文本"，确定剂量组动物和对照组动物的摄食量有无异常。
    - 如果在任意给药周期（分为"给药期"和"恢复期"两种）内剂量组的摄食量有变化，则按组分别提取以下信息：[给药周期，当前剂量，性别，摄食量变化描述]，每组信息四个元素必须同时在原文准确提及（例如不能把低剂量组当成10 mg/kg剂量组），否则忽略此条摄食量变化情况的提取。
    - 如果在所有的给药周期内，剂量组的摄食量都"无明显变化"、"基本一致"或类似表述，则不输出摄食量相关行。
  2. 解析"模板表"：
    - 记录表格第一行包含的"剂量"信息，和第二行包含的"性别"信息（'M'是雄性，'F'是雌性）。
  3. 表格生成规则：
    - 如果没有提取到任何摄食量变化信息，则不添加任何行，直接返回模板表。
    - 如果提取到摄食量变化信息，则为每个出现变化的给药周期创建一行。行标题格式为"摄食量（给药期）"或"摄食量（恢复期）"。
    - 将第一步提取的信息中的"当前剂量"、"性别"与第二步中记录的"剂量"、"性别"做匹配。
    - 匹配成功的，将"摄食量变化描述"填入对应的位置，其他位置填入"-"。
  4. 将修改后的表格填入<ANSWER>标签中返回。
- Explain: 
    - 不需要将症状出现的日期提取到表格中，例如：摄食量在Days1-8减少，只需提取"摄食量减少"即可。
    - 请注意文本中“≥”号的使用描述，结合'给药剂量'和“动物性别”，将满足条件的中间态信息全部抽取出来。没有“≥”号的片段只抽取一组中间态信息，举个例子
      假设出现：≥1mg/kg/day剂量雄性摄食量减少，那么表头剂量为1、2、3、4等（以实际的表头为准）,且性别为M的对应摄食量单元格填写减少。
- Example1:
  - input:
    - 待解析文本:
      给药期，各给药组雌雄动物摄食量基本一致。恢复期，雌鼠高、低剂量组摄食量较高。
    - 模板表:
      | 日剂量（mg/kg） | 0（对照） | 0（对照） | 10 | 10 | 25 | 25 | 50 | 50 |
      | 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |
  - output:<ANSWER>| 日剂量（mg/kg） | 0（对照） | 0（对照） | 10 | 10 | 25 | 25 | 50 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |</ANSWER>
  - explain: 高、低剂量组无法与具体的剂量组对应，因此不提取。

- Example2:
  - input:
    - 待解析文本:
      给药期，与同期对照组相比，25 mg/kg/day剂量组1只雄性动物与对照组动物相比摄食情况一般/不良（一般：饲料剩余1/2至2/3，不良：饲料剩余2/3以上）的发生频率较高，该剂量组其他动物以及其他剂量组动物摄食量无显著变化。
      恢复期，与同期对照组相比，各给药组雌雄动物摄食量基本一致。
    - 模板表:
      | 日剂量（mg/kg） | 0（对照） | 0（对照） | 10 | 10 | 25 | 25 | 50 | 50 |
      | 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |
  - output:<ANSWER>| 日剂量（mg/kg） | 0（对照） | 0（对照） | 10 | 10 | 25 | 25 | 50 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |
| 摄食量（给药期） | - | - | - | - | 一般/不良 | - | - | - |</ANSWER>

- Example3:
  - input:
    - 待解析文本:
      给药期，与同期对照组相比，50 mg/kg/day剂量组1只雌性动物与对照组动物相比摄食情况一般/不良发生频率较高，该剂量组其他动物以及其他剂量组动物摄食量无显著变化。
      恢复期，与同期对照组相比，10 mg/kg/day剂量组1只雌性动物与对照组动物相比摄食情况一般/不良， 25 mg/kg/day剂量组1只雄性动物与对照组动物相比摄食情况严重。
    - 模板表:
      | 日剂量（mg/kg） | 0（对照） | 0（对照） | 10 | 10 | 25 | 25 | 50 | 50 |
      | 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |
  - output:<ANSWER>| 日剂量（mg/kg） | 0（对照） | 0（对照） | 10 | 10 | 25 | 25 | 50 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |
| 摄食量（给药期） | - | - | - | - | - | - | - | 一般/不良 |
| 摄食量（恢复期） | - | - | - | 一般/不良 | 严重 | - | - | - |</ANSWER>'''


# 267.7 续表 - 摄食量
async def generate_table_food_intake(content: str, header: TableRows, header_md: str) -> tuple[TableRows, TableRows]:
    """
    content:摄食量章节下的文本信息。
    header：续表表头
    """
    # 拼接输入表格，md格式
    target_table = '| 摄食量 |'
    header_row = header_md.strip().split('\n')
    first_row = header_row[0].strip('|').split('|')
    add_num = len(first_row) - 1
    target_table_md = ''.join([target_table] + [' - |'] * add_num)
    input_table_md = header_md + '\n' + target_table_md

    # 提示输入，LLM得到结果
    input_prompt = f'待解析文本:\n{content}\n模板表:\n{input_table_md}'
    result_md = await Get73FoodIntake.extract(input_prompt)

    # 将结果转为列表，进行返回
    administration_table, recovery_table = [], []
    rows = result_md.strip().split('\n')
    for row in rows[2:]:
        if '给药期' in row:
            row_data = [cell.strip() for cell in row.strip('|').split('|')]
            row_data[0] = row_data[0].replace('（给药期）', '')
            administration_table = header + [row_data]
        elif '恢复期' in row:
            row_data = [cell.strip() for cell in row.strip('|').split('|')]
            row_data[0] = row_data[0].replace('（恢复期）', '')
            recovery_table = header + [row_data]
        else:
            row_data = [cell.strip() for cell in row.strip('|').split('|')]
            administration_table = header + [row_data]

    if not administration_table:
        target_table_list = ['摄食量']
        target_table_list.extend(['-'] * add_num)
        administration_table = header + [target_table_list]

    return (administration_table, recovery_table)


class GetClinicalObservationForTable(AzureOpenAIExtractor):
    SYSTEM_PROMPT = '''- Role: 表格处理专家。
- Background: 你是一位精通数据处理和表格转换的专家，能够准确理解和操作复杂的数据结构，将数据从一种格式高效地转换为另一种格式,并通过“输入的表格”中的信息做定位，将“异常症状”和“异常动物个数”填入待生成的表格的表头对应的位置
- Skills: 熟练处理复杂数据、识别并提取关键数据，并完成数据的准确填入工作。
- Constraint:
  1. 新生成的表格的表头要与提供的待生成的表格的表头一致。
  2. 所有的内容从输入的表格中提取。
  3. 待生成的表格的表头中的"动物数量"列的格式为"M:5| F:5"，其中M代表雄性，F代表雌性，10代表动物数量。
- Workflow:
  以下工作流中会用到的特殊名称说明：
    - 阶段：只包括“给药期”和“恢复期”。
    - 临床症状：包括脱毛、分泌物、软便、稀便、结痂等。
    - 剂量组：指的是给药剂量的组别，如5 mg/kg/day。
    - 发生率：指的是异常动物个数与总动物个数的比例，如1/5。
1. 识别原始表格中的数据结构和关键信息。
2. 根据待生成的表格的表头，提取原始表格中的数据。
3. 对于每项内容，基本提取的行数是变化的。但一般来讲，先提取阶段，再提取临床整症状，再提取对应剂量、性别下的发生率。
4. 对于表格中所提供的程度行所对应的数字，请分辨出哪个数字是雄性的，哪个数字是雌性的。
5. 每种症状需要输出对应的动物数。如果有披露总测试动物数，则输出为 "出现症状的动物数/总测试动物数"。
6. 对于每一行，若提取不出内容，在此单元格输出"-"。
7. 按指定格式输出表格，并填入<ANSWER>标签中，不包含其他内容。
- Example:
  - input:
    待生成的表格的表头格式为:
    | 日剂量（mg/kg） | 0（对照） | 0（对照） | 2 | 2 | 3 | 3 | 5 | 5 |
    | 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |
    输入的表格为:
    | 阶段 | 临床症状 | 剂量(mg/kg/day) | 雄性 | 雄性 | 雌性 | 雌性 |
    | 阶段 | 临床症状 | 剂量(mg/kg/day) | 发生率 | 症状起止日 | 发生率 | 症状起止日 |
    | 给药期 | 沾染（左右前爪/眼睛/鼻子/下巴/嘴巴，浅红色/红色/暗红色/清澈沾染） | 0 | 1/15 | Days 13-22 | - | - |
    | 给药期 | 沾染（左右前爪/眼睛/鼻子/下巴/嘴巴，浅红色/红色/暗红色/清澈沾染） | 2 | 12/15 | Days 8-27 | 11/15 | Days 12-26 |
    | 给药期 | 沾染（左右前爪/眼睛/鼻子/下巴/嘴巴，浅红色/红色/暗红色/清澈沾染） | 3 | 15/15 | Days 8-27 | 8/15 | Days 12-26 |
    | 给药期 | 沾染（左右前爪/眼睛/鼻子/下巴/嘴巴，浅红色/红色/暗红色/清澈沾染） | 5 | 12/15 | Days 8-27 | 8/15 | Days 12-26 |
    | 给药期 | 流涎 | 2 | 2/15 | Days 13-20 | 1/15 | Days 15-19 |
    | 给药期 | 流涎 | 3 | 6/15 | Days 8-29 | - | - |
    | 给药期 | 流涎 | 5 | 3/15 | Days 8-20 | 2/15 | Days 15-29 |
    | 恢复期 | 流涎 | 3 | - | - | 1/5 | Day 35 |
    | 恢复期 | 流涎 | 5 | - | - | 1/5 | Day 35 |
  - output:<ANSWER>| 日剂量（mg/kg） | 0（对照） | 0（对照） | 2 | 2 | 3 | 3 | 5 | 5 |
| 动物数量 | M:5 | F:5 | M:5 | F:5 | M:5 | F:5 | M:5 | F:5 |
| 沾染（给药期） | 1/15 | - | 12/15 | 11/15 | 15/15 | 8/15 | 12/15 | 8/15 |
| 流涎（给药期） | - | - | 2/15 | 1/15 | 6/15 | - | 3/15 | 2/15 |
| 流涎（恢复期） | - | - | - | - | - | 1/5 | - | 1/5 |</ANSWER>'''


async def generate_clinical_observation_from_table(
    content_md: str, header: TableRows, common_header: str
) -> tuple[TableRows, TableRows]:  # 给药期 / 恢复期
    # 提示输入，LLM得到结果
    input_prompt = f'待生成的表格的表头为:\n{common_header}\n输入的表格为:\n{content_md}'
    result_md = await GetClinicalObservationForTable.extract(input_prompt)

    administration_list: TableRows = []
    recovery_list: TableRows = []

    # 表头
    target_table = ['临床观察']
    add_num = len(header[0]) - 1
    target_table.extend(target_table * add_num)

    rows = result_md.strip().split('\n')
    for row in rows[2:]:
        if '给药期' in row:
            row_data = [cell.strip() for cell in row.strip('|').split('|')]
            row_data[0] = row_data[0].replace('（给药期）', '')
            administration_list.append(row_data)
        elif '恢复期' in row:
            row_data = [cell.strip() for cell in row.strip('|').split('|')]
            row_data[0] = row_data[0].replace('（恢复期）', '')
            recovery_list.append(row_data)
        else:
            row_data = [cell.strip() for cell in row.strip('|').split('|')]
            administration_list.append(row_data)

    # 给药期没结果也必须有，恢复期没结果就要省略
    if administration_list:
        administration_table = header + [target_table] + administration_list
    else:
        target_table_none = ['临床观察']
        target_table_none.extend(['-'] * add_num)
        administration_table = header + [target_table_none]

    if recovery_list:
        recovery_table = header + [target_table] + recovery_list
    else:
        recovery_table = []

    return (administration_table, recovery_table)


# TODO：提示词优化
# 267.7 续表 - 临床观察 LLM提示词
class Get73ClinicalObservation(AzureOpenAIExtractor):
    """
    提取临床观察信息表格
    """

    SYSTEM_PROMPT = '''- Role: 表格处理专家。
- Background: 从输入文本中提取“异常症状”和“异常动物个数”，并通过“模板表”中的信息做定位，将“异常症状”和“异常动物个数”填入表格对应的位置:
- Skills: 熟练处理复杂数据、识别并提取关键数据，并完成数据的准确填入工作。
- Workflow:
  以下工作流中会用到的特殊名称说明：
    - 给药周期：只包括“给药期”和“恢复期”。
    - 异常症状：包括脱毛、分泌物、软便、稀便、结痂等。
    - “中间态信息”：['给药周期', '给药剂量', '异常动物个数', '性别', '异常表现描述']，五个元素必须全部有文本对应才能算作一组中间态信息
  1. 解析“待解析文本”：
    - 提取所有异常症状的“中间态信息”，如果出现：与试验物无关、不与试验物相关，或无异常、未出现异常、相关性不确定等描述，则忽略当前中间态信息的提取，继续下一组的提取。
    - 如果在所有的给药周期内，都无异常，或都未提取出任意中间态信息，则直接去到第四步。
  2. “输入的表格”字段说明：
    - 表格第一行包含的是“剂量”信息，第二行包含的是“性别”信息，'M'是雄性，'F'是雌性，记录这两行数据。
  3. 表格生成规则：
    - 每一行的表头：将“异常症状”与“给药周期”做拼接作为每一行的起始。
    - 表内数据（填入异常动物个数）：利用中间态信息的“剂量”与“性别”，同第二步的“剂量”和“性别”定位表格位置，将中间态信息的“异常动物个数”填入表格对应的位置中。如提及总动物数，则将其记录为：“异常动物个数/总动物数”。
    - “剂量”与“性别”匹配不通过的放弃，继续处理剩余的中间态信息。直到所有中间态信息处理完成。
    - 当出现的异常症状但无透露异常动物个数时，则直接忽略该异常症状的提取。
    - 若出现对照组的异常症状，将其填入剂量为0mg/kg/day对应的性别单元格中。
    - 当出现“≥”和“≥”这样的表述时，比如流涎见于≥2 mg/kg/day雄性动物（1/15）和≥4 mg/kg/day雌性动物（2/10） 那么第一步：填充数据1/15至行：流涎且剂量列≥2 mg/kg/day且性别为雄性，第二步：填充数据2/10至行：流涎且剂量列≥4 mg/kg/day且性别为雌性。
    - 提取时需要把整个数据提取至对应位置中，如：流涎见于2 mg/kg/day雄性动物（1/15），那么在流涎这行中对应2 mg/kg/day的雌性动物这个单元格中填入1/15而不是1.
  4. 将修改后的“输入的表格”填入<ANSWER>标签中返回。
- Example1:
  - input:
    - 输入的文本:
    给药期，与同期对照组相比，25 mg/kg/day剂量组1只雄性动物与对照组动物相比观察到脱毛，该剂量组其他动物无异常。
    恢复期，在试验第28天，10 mg/kg/day剂量组仅2只雄性动物观察到脸部结痂。
    - 模板表:
    | 日剂量（mg/kg） | 0（对照） | 0（对照） | 10 | 10 | 25 | 25 | 50 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |
  - output:<ANSWER>| 日剂量（mg/kg） | 0（对照） | 0（对照） | 10 | 10 | 25 | 25 | 50 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |
| 脱毛（给药期） | - | - | - | - | 1 | - | - | - |
| 脱毛（恢复期） | - | - | - | 2 | - | - | - | - |</ANSWER>

- Example2:
  - input:
    - 输入的文本:
    给药期，有剂量组动物出现软便情况。
    恢复期，与同期对照组相比，50 mg/kg/day剂量组5只雌性动物与对照组动物相比观察到软便，与受试物AX-052相关性不确定。
    - 模板表:
    | 日剂量（mg/kg） | 0（对照） | 0（对照） | 10 | 10 | 25 | 25 | 50 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |
  - output:<ANSWER>| 日剂量（mg/kg） | 0（对照） | 0（对照） | 10 | 10 | 25 | 25 | 50 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |
| 软便（恢复期） | - | - | - | - | - | - | - | 5 |</ANSWER>

- Example3:
  - input:
    - 输入的文本:
    给药期，与同期对照组相比，10 mg/kg/day剂量组2只雌性动物与对照组动物相比观察到软便，该剂量组其他动物无异常。
    恢复期，与同期对照组相比，25 mg/kg/day剂量组5只雄性动物与对照组动物相比出现稀便，50 mg/kg/day剂量组2只雌性动物与对照组动物相比观察到分泌物
    - 模板表:
    | 日剂量（mg/kg） | 0（对照） | 0（对照） | 10 | 10 | 25 | 25 | 50 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |
  - output:<ANSWER>| 日剂量（mg/kg） | 0（对照） | 0（对照） | 10 | 10 | 25 | 25 | 50 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |
| 软便（给药期） | - | - | - | 2 | - | - | - | - |
| 稀便（恢复期） | - | - | - | - | 5 | - | - | - |
| 分泌物（恢复期） | - | - | - | - | - | - | - | 2 |</ANSWER>

- Example4:
  - input:
    - 输入的文本:
    给药期：异常临床症状包括：脱毛、分泌物（左眼）、软便和稀便。
    恢复期：在试验第26天，15 mg/kg/day剂量组有2只雄性动物观察到脸部结痂，随后消失，故不认为与新药001相关。
    - 模板表:
    | 日剂量（mg/kg） | 0（对照） | 0（对照） | 10 | 10 | 25 | 25 | 50 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |
  - output:<ANSWER>| 日剂量（mg/kg） | 0（对照） | 0（对照） | 10 | 10 | 25 | 25 | 50 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |</ANSWER>

- Example5:
  - input:
    - 输入的文本:
    给药期：给药期，异常临床症状包括：分泌物（左/右眼）、虚脱。分泌物（左/右眼）见于≥20 mg/kg/day雌雄性动物（5/20）；虚脱见于40 mg/kg/day雌性性动物（2/5）。
    恢复期：20 mg/kg/day雄性动物（1/5）出现呕吐。
    - 模板表:
    | 日剂量（mg/kg） | 0（对照） | 0（对照） | 10 | 10 | 20 | 20 | 40 | 40 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |
  - output:<ANSWER>| 日剂量（mg/kg） | 0（对照） | 0（对照） | 10 | 10 | 20 | 20 | 40 | 40 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |
| 分泌物（左/右眼）（给药期） | - | - | - | - | 5/20 | 5/20 | 5/20 | 5/20 |
| 虚脱（给药期） | - | - | - | - | - | - | - | 2/5 |
| 呕吐（恢复期） | - | - | - | - | 1/5 | - | - | - |</ANSWER>'''


# 267.7 续表 - 临床观察 表格生成
async def generate_table_clinical_observation(
    content: str, header: TableRows, header_md: str
) -> tuple[TableRows, TableRows]:  # 给药期 / 恢复期
    """
    content:临床观察章节下的文本信息。
    table_header：续表表头
    """
    target_table = ['临床观察']
    add_num = len(header[0]) - 1
    target_table.extend(target_table * add_num)

    # 提示输入，LLM得到结果
    input_prompt = f'输入的文本:\n{content}\n模板表:\n{header_md}'
    result_md = await Get73ClinicalObservation.extract(input_prompt)

    # 将结果转为列表，进行返回
    administration_list: TableRows = []
    recovery_list: TableRows = []
    rows = result_md.strip().split('\n')
    for row in rows[2:]:
        if '给药期' in row:
            row_data = [cell.strip() for cell in row.strip('|').split('|')]
            row_data[0] = row_data[0].replace('（给药期）', '')
            administration_list.append(row_data)
        elif '恢复期' in row:
            row_data = [cell.strip() for cell in row.strip('|').split('|')]
            row_data[0] = row_data[0].replace('（恢复期）', '')
            recovery_list.append(row_data)
        else:
            row_data = [cell.strip() for cell in row.strip('|').split('|')]
            administration_list.append(row_data)

    # 给药期没结果也必须有，恢复期没结果就要省略
    if administration_list:
        administration_table = header + [target_table] + administration_list
    else:
        target_table_none = ['临床观察']
        target_table_none.extend(['-'] * add_num)
        administration_table = header + [target_table_none]

    if recovery_list:
        recovery_table = header + [target_table] + recovery_list
    else:
        recovery_table = []

    return (administration_table, recovery_table)


# 267.7 续表 - 大体病理学 LLM提示词
class Get73GrossPathology(AzureOpenAIExtractor):
    """
    大体病理学
    """

    SYSTEM_PROMPT = '''- Role: 表格处理专家。
- Background: 从输入文本中提取“异常症状”和“异常动物个数”，并通过“模板表”中的信息做定位，将“异常症状”和“异常动物个数”填入表格对应的位置:
- Skills: 熟练处理复杂数据、识别并提取关键数据，并完成数据的准确填入工作。
- Workflow:
  以下工作流中会用到的特殊名称说明：
    - 试验周期：只包含终末期和恢复期两个周期。
    - 器官：包括胸腺、肝脏、尾皮肤等。
    - 异常症状：包括体积增大/减小、局灶性肿块等等对动物异常器官的描述。
    - 中间态信息：['试验周期', '给药剂量', '异常动物个数', '异常动物性别', '异常器官', '异常症状']，六个元素必须全部有文本对应才能算作一组中间态信息。
  1. 解析“待解析文本”：
    - 提取解剖日期：例如终末期解剖第29天，恢复期解剖第57天。
    - 分别提取两个试验周期内出现的所有中间态信息：如果文中有说明：未见与试验物相关的大体病理学改变，或病理与试验物无关，或未出现异常等描述，则忽略当前中间态信息的提取，继续下一组的提取。
    - 如果在两个试验周期内都未见与试验物相关的大体病理学改变，或未提取出中间态信息，则直接去到第四步。
  2. “输入的表格”字段说明：
    - 表格第一行包含的是“剂量”信息，第二行包含的是“性别”信息，'M'是雄性，'F'是雌性，记录这两行数据。
  3. 表格生成规则：
    - 一级表头：“大体病理学”拼接“解剖阶段”，例如：大体病理学（终末期解剖）、大体病理学（恢复期解剖），后面单元格全部填充一级表头内容，与表格长度保持相同。
    - 二级表头：“异常器官”。后面单元格全部填充“异常器官”，填充个数与表格长度保持相同。
    - 三级表头：“异常症状”。后面单元格全部填充“异常症状”，填充个数与表格长度保持相同。
    - 三级表头后面单元格填充“异常动物个数”：利用中间态信息的“给药剂量”、“异常动物性别”和第二步的“剂量”和“性别”严格做匹配，剂量和性别同时匹配通过的，将中间态信息的“异常动物个数”填入表格对应的单元格中。否则忽略当前匹配，继续处理剩余的中间态信息。直到所有中间态信息处理完成。
    - 特别说明：具有相同二级表头“异常器官”的三级数据，要放在同一个二级表头下，另外若异常症状所对应的剂量与表头中的剂量不匹配，则忽略该异常症状。
    - 当异常症状无对应的个数时，应该忽略该异常症状而进行下一个符合的异常症状进行提取。
  4. 只将修改后的“输入的表格”填入<ANSWER>标签中返回，其他内容请截断忽略。
- Example:
  - input:
    - 输入的文本:
    终末期解剖（第29天）
    与新药001相关的大体病理学改变包括：75 mg/kg/day剂量组5只雄性动物胸腺体积减小，2.5 mg/kg/day剂量组2只雌性动物尾皮肤局灶性肿块，5 mg/kg/day剂量组6只雄性动物胸腺积水，7.5 mg/kg/day剂量组2只雌性动物和50 mg/kg/day剂量组7只雌性动物肝所有叶体积增大。
    恢复期解剖（第57天）
    恢复期解剖未见与受试物相关的大体病理学改变。
    - 模板表:
    | 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |
  - output:<ANSWER>| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |
| 终末期解剖（第29天）| 终末期解剖（第29天）| 终末期解剖（第29天）| 终末期解剖（第29天）| 终末期解剖（第29天）| 终末期解剖（第29天）| 终末期解剖（第29天）| 终末期解剖（第29天）| 终末期解剖（第29天）|
| 胸腺 | 胸腺 | 胸腺 | 胸腺 | 胸腺 | 胸腺 | 胸腺 | 胸腺 | 胸腺 |
| 体积减小 | - | - | - | - | - | - | 5 | - |
| 积水 | - | - | 6 | - | - | - | - | - |
| 尾皮肤 | 尾皮肤 | 尾皮肤 | 尾皮肤 | 尾皮肤 | 尾皮肤 | 尾皮肤 | 尾皮肤 | 尾皮肤 |
| 局灶性肿块 | - | - | - | 2 | - | - | - | - |
| 肝脏 | 肝脏 | 肝脏 | 肝脏 | 肝脏 | 肝脏 | 肝脏 | 肝脏 | 肝脏 |
| 体积增大 | - | - | - | - | - | 2 | - | 7 |</ANSWER>
- Example2:
  - input:
    - 输入的文本:
   大体病理学检查结果总结和个体数据分别见附表15和附录20。综合病理报告见附录23。
终末期解剖（第29天）与NS-041相关的大体病理学改变包括：15mg/kg/day剂量组雄性动物胸腺体积减小，与镜检皮质和髓质淋巴细胞减少相一致。
恢复期解剖（第57天）未见与NS-041相关的大体病理学改变。所有其他大体病理学改变考虑为偶发的且与NS-041不相关，基于发生率较低、缺乏剂量反应关系，和/或仅在单一性别中存在。
所有其他大体病理学改变考虑为偶发的且与NS-041不相关，基于发生率较低、缺乏剂量反应关系，和/或仅在单一性别中存在。
    - 模板表:
    | 日剂量（mg/kg） | 0（对照） | 0（对照） | 4 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |
  - output:<ANSWER>| 日剂量（mg/kg） | 0（对照） | 0（对照） | 4 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |
| 大体病理学 | - | - | - | - | - | - | - | - |</ANSWER>
'''


# 267.7 续表 - 大体病理学 表格生成
async def generate_table_gross_pathology(
    content: str, header: list[list[str]], header_md: str
) -> tuple[TableRows, TableRows]:
    """
    content:大体病理学 章节下的文本信息。
    header:续表表头json
    header_md:续表表头markdown
    """
    # 提示输入，LLM得到结果
    input_prompt = f'输入的文本:\n{content}\n模板表:\n{header_md}'
    result_md = await Get73GrossPathology.extract(input_prompt)

    # 按行分割，并找到"大体病理学（第57天）"的行号
    split_index = 0
    lines = result_md.strip().split('\n')
    for i, line in enumerate(lines):
        if '恢复期解剖' in line:
            split_index = i
            break

    # 分割大体病理学第29天/57天，用以区分终末期和恢复期
    if split_index != 0:
        part1 = lines[2:split_index]
        part2 = lines[split_index:]
    else:
        part1 = lines[2:]
        part2 = []

    # 终末期表格拼接
    administration_list = []
    for line in part1:
        if '终末期解剖' in line:
            line = line.replace('终末期解剖', '大体病理学')
        cells = [cell.strip() for cell in line.strip('|').split('|')]
        administration_list.append(cells)

    if administration_list:
        administration_table = header + administration_list
    else:
        target_table_none = ['大体病理学']
        add_num = len(header[0]) - 1
        target_table_none.extend(['-'] * add_num)
        administration_table = header + [target_table_none]

    # 恢复期表格拼接
    recovery_list = []
    if part2:
        for line in part2:
            if '恢复期解剖' in line:
                line = line.replace('恢复期解剖', '大体病理学')
            cells = [cell.strip() for cell in line.strip('|').split('|')]
            recovery_list.append(cells)
        recovery_table = header + recovery_list
    else:
        recovery_table = []

    return (administration_table, recovery_table)


# 267.7 续表 - 濒死/死亡 LLM提示词
class Get73NearDeathOrDying(AzureOpenAIExtractor):
    """
    濒死/死亡
    """

    SYSTEM_PROMPT = '''- Role: 表格处理专家。
- Background: 从“数据表”中提取数据，并按规则修改“模板表”内的数据并返回。
- Skills: 熟练处理复杂数据、识别并提取关键数据，并完成数据的准确填入工作。
- Workflow:
  1. 解析“数据表”内容：
    - 排除掉所有有关“安乐死”的数据信息，保留处理剩下的数据。如果表格中全是有关“安乐死”的数据，则直接去到第四步。
    - 提取剩余所有数据的“给药剂量”和“组别/性别”。
  2. 解析“模板表”:
    - “模板表”第一行包含的是“剂量”信息，第二行包含的是“性别”信息，'M'是雄性，'F'是雌性，记录这两行数据。
  3. 表格生成规则：
    - 以第一步提取的所有“给药剂量”和“组别/性别”数据，同第二步“模板表”内的“剂量”、“性别”信息做匹配。
    - 匹配成功一个，在“模板表”第三行对应的单元格内累加一个计数。
    - 匹配失败，不累加计数。直到所有数据都遍历一遍。
  4. 只将修改后的“模板表”填入<ANSWER>标签中返回，其他内容请截断忽略。
- Example:
  - input:
    - 数据表：
    | 死亡编码 | 给药剂量 | 死亡日 | 组别/性别 | 动物号 |
    | 人道终点安乐死 | 15 mg/kg/day | 7 | 4/雌性 | 25340 |
    | 人道终点安乐死 | 15 mg/kg/day | 15 | 4/雌性 | 25312 |
    | 发现死亡 | 5 mg/kg/day | 15 | 4/雄性 | 25338 |
    | 发现死亡 | 5 mg/kg/day | 16 | 4/雌性 | 25339 |
    | 发现死亡 | 7.5 mg/kg/day | 17 | 4/雄性 | 25555 |
    | 发现死亡 | 7.5 mg/kg/day | 17 | 4/雄性 | 25556 |
    | 发现死亡 | 7.5 mg/kg/day | 17 | 4/雄性 | 25557 |
    | 发现死亡 | 7.5 mg/kg/day | 17 | 4/雌性 | 25558 |
    - 模板表：
    | 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 5 | 7.5 | 7.5 | 15 | 15 |
    | 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |
    | 濒死/死亡 | 0 | 0 | 0 | 0 | 0 | 0 | 0 | 0 |
  - output:<ANSWER>| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 5 | 7.5 | 7.5 | 15 | 15 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |
| 濒死/死亡 | 0 | 0 | 1 | 1 | 3 | 1 | 0 | 0 |</ANSWER>'''


# 267.7 续表 - 濒死/死亡 表格生成
async def generate_table_near_death_or_dying(
    content: str, tables: str, header: TableRows, header_md: str
) -> tuple[TableRows, TableRows]:
    """
    content: 文本字段
    tables:文本字段下的表格 - 濒死/死亡表格
    header:267.7续表表头，JSON格式
    header_md:267.7续表表头，MD格式
    """
    # 如果有表格，处理表格，无表格处理文字
    if tables:
        # 拼接输入表格，md格式
        target_table = '| 濒死/死亡 |'
        header_row = header_md.strip().split('\n')
        first_row = header_row[0].strip('|').split('|')
        add_num = len(first_row) - 1
        target_table_md = ''.join([target_table] + [' 0 |'] * add_num)
        input_table_md = header_md + '\n' + target_table_md

        # 提示输入，LLM得到结果
        input_prompt = f'数据表:\n{tables}\n模板表:\n{input_table_md}'
        result_md = await Get73NearDeathOrDying.extract(input_prompt)

        # 濒死/死亡只会出现在“值得注意的结果内”，
        administration_table = []
        rows = result_md.strip().split('\n')
        for row in rows:
            row_data = [cell.strip() for cell in row.strip('|').split('|')]
            administration_table.append(row_data)

    # TODO：无表格有文字描述的，暂未处理，统一按无死亡全0输出
    else:
        row_data = ['濒死/死亡']
        row_data.extend(['0'] * (len(header[0]) - 1))
        administration_table = header + [row_data]

    # 濒死/死亡不会有“恢复期内容”
    recovery_table = []

    return (administration_table, recovery_table)


# 267.7 续表 - 尿液分析 LLM提示词
class Get73Urinalysis(AzureOpenAIExtractor):
    """
    尿液分析
    """

    SYSTEM_PROMPT = '''- Role: 表格处理专家。
- Background: 从输入文本中提取“尿液变化”情况，并将“变化因素”和“变化值”填入表格对应的位置。
- Skills: 熟练处理复杂数据、识别并提取关键数据，并完成数据的准确填入工作。
- Workflow:
  以下工作流中会用到的特殊名称说明：
    - 试验周期：只包含终末期和恢复期两个周期。
    - 变化因素：包括PRO/LEU/URO等。
    - 变化值：升高、降低，升高就用“↑”表示，降低就用“↓”表示。
    - 中间态信息：['试验周期', '给药剂量', '异常动物性别', '变化因素', '变化值']，五个元素必须全部有文本对应才能算作一组中间态信息。
  1. “输入的表格”字段说明：
    - 表格第一行包含的是“剂量”信息，第二行包含的是“性别”信息，'M'是雄性，'F'是雌性，记录这两行重要数据。
  2. 解析“待解析文本”：
    - 提取试验周期：如终末期和恢复期。
    - 提取解剖日期：例如终末期解剖第29天，恢复期解剖第57天。
    - 分别提取两个试验周期内出现的所有中间态信息：如果文中有说明：未见与试验物相关的尿液参数变化，或未见尿液分析参数改变，或认为该变化与试验物不相关，则忽略当前中间态信息的提取，继续下一组的提取。
    - 如果在两个试验周期内都未见与试验物相关的尿液变化情况，或未提取出中间态信息，则返回“输入的表格”，不做任何操作，直接去到第四步。
  3. 表格生成规则：
    - 一级表头：“试验周期”拼接“解剖日期”，例如：终末期（第29天）、恢复期（第57天），后面单元格全部填充一级表头内容，与表格长度保持相同。
    - 二级表头：“变化因素”。
    - 二级表头后面单元格填充“变化值”：利用中间态信息的“给药剂量”与“异常动物性别”，同第二步的“剂量”和“性别”定位单元格位置，将中间态信息的“变化值”填入表格对应的单元格中。
    - “剂量”与“性别”匹配不通过的放弃，继续处理剩余的中间态信息。直到所有中间态信息处理完成。
  4. 只将修改后的“输入的表格”填入<ANSWER>标签中返回，其他内容请截断忽略。
- Attention:
    - 请注意文本中“≥”号的使用描述，结合'给药剂量'和“动物性别”，将满足条件的中间态信息全部抽取出来。没有“≥”号的片段只抽取一组中间态信息。
    - 当出现确定剂量的异常症状时，应查看该剂量是否在表头中，若不在则忽略该异常症状。
- Example1:
  - input:
    - 输入的文本:
    终末期解剖(第29天)
    与新药001相关的的尿液分析参数改变为:给予≥5mg/kg/day剂量组雄性动物和给予50mg/kg/day剂量组雌性动物PRO及LEU升高，10mg/kg/day剂量组雌性动物PRO降低;
    恢复期解剖(第57天)
    与新药001相关的尿液分析参数改变为：给予≥5 mg/kg/day剂量组雌性动物和给予15 mg/kg/day剂量组雄性动物尿胆原URO降低。
    - 模板表:
    | 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 5 | 10 | 10 | 15 | 15 |
    | 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |
  - output:<ANSWER>| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 5 | 10 | 10 | 15 | 15 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |
| 终末期（第29天）| 终末期（第29天）| 终末期（第29天）| 终末期（第29天）| 终末期（第29天）| 终末期（第29天）| 终末期（第29天）| 终末期（第29天）| 终末期（第29天）|
| PRO | - | - | ↑ | - | ↑ | ↓ | ↑ | - |
| LEU | - | - | ↑ | - | ↑ | - | ↑ | - |
| 恢复期（第57天）| 恢复期（第57天）| 恢复期（第57天）| 恢复期（第57天）| 恢复期（第57天）| 恢复期（第57天）| 恢复期（第57天）| 恢复期（第57天）| 恢复期（第57天）|
| URO | - | - | - | ↓ | - | ↓ | ↓ | ↓ |</ANSWER>
- Example2:
  - input:
    - 输入的文本:
    终末期解剖(第29天)
    与新药001相关的的尿液分析参数改变为:给予≥5mg/kg/day剂量组雄性动物和给予50mg/kg/day剂量组雌性动物URO升高
    恢复期解剖(第57天)
    未见与新药001相关的尿液分析参数改变
    - 模板表:
    | 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 5 | 7.5 | 7.5 | 15 | 15 | 50 | 50 |
    | 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: | M: | F: |
  - output:<ANSWER>| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 5 | 7.5 | 7.5 | 15 | 15 | 50 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: | M: | F: |
| 终末期（第29天）| 终末期（第29天）| 终末期（第29天）| 终末期（第29天）| 终末期（第29天）| 终末期（第29天）| 终末期（第29天）| 终末期（第29天）| 终末期（第29天）| 终末期（第29天）| 终末期（第29天）|
| URO | - | - | ↑ | - | ↑ | - | ↑ | - | ↑ | ↑ |</ANSWER>'''


# 267.7 续表 - 尿液分析 表格生成
async def generate_table_urinalysis(content: str, header: TableRows, header_md: str) -> tuple[TableRows, TableRows]:
    """
    content: 文本字段
    header:267.7续表表头，JSON格式
    header_md:267.7续表表头，MD格式
    """
    # 提示输入，LLM得到结果
    input_prompt = f'输入的文本:\n{content}\n模板表:\n{header_md}'
    result_md = await Get73Urinalysis.extract(input_prompt)

    # 按行分割，并找到"尿液分析（第57天）"的行号
    split_index = 0
    lines = result_md.strip().split('\n')
    for i, line in enumerate(lines):
        if '恢复期' in line:
            split_index = i
            break

    # 分割尿液分析第29天/57天，用以区分终末期和恢复期
    if split_index != 0:
        part1 = lines[2:split_index]
        part2 = lines[split_index:]
    else:
        part1 = lines[2:]
        part2 = []

    # 终末期表格拼接，尿液分析为非必选项，没有就不输出
    administration_list = []
    if part1:
        for line in part1:
            line = line.replace('终末期', '尿液分析')
            cells = [cell.strip() for cell in line.strip('|').split('|')]
            administration_list.append(cells)
        administration_table = header + administration_list
    else:
        administration_table = []

    # 恢复期表格拼接，尿液分析为非必选项，没有就不输出
    recovery_list = []
    if part2:
        for line in part2:
            line = line.replace('恢复期', '尿液分析')
            cells = [cell.strip() for cell in line.strip('|').split('|')]
            recovery_list.append(cells)
        recovery_table = header + recovery_list
    else:
        recovery_table = []

    # 尿液分析如果没有也需要输出一行
    if not (administration_table or recovery_table):
        target_table_none = ['尿液分析']
        add_num = len(header[0]) - 1
        target_table_none.extend(['-'] * add_num)
        administration_table = header + [target_table_none]

    return (administration_table, recovery_table)


# 血凝
class HemagglutinationTable(AzureOpenAIExtractor):
    SYSTEM_PROMPT = '''- 角色: 数据转换专家
- 背景: 你是一位精通数据处理和表格转换的专家，能够准确理解和操作复杂的数据结构，将数据从一种格式高效地转换为另一种格式。
- 目标: 根据待生成的表格的表头，从输入的表格中提取雌性和雄性在不同剂量下的数据，生成新的表格，并按照指定格式输出。
- 约束:
  1. 新生成的表格的表头要与提供的待生成的表格的表头一致。
  2. 所有的内容从输入的表格中提取。
  3. 待生成的表格的表头中的"动物数量"列的格式为"M:10 | F:10"，其中M代表雄性，F代表雌性，10代表动物数量。
- 输出格式: 新表格将以清晰的列格式展示
- 工作流程:
  1. 识别原始表格中的数据结构和关键信息。
  2. 根据待生成的表格的表头，提取原始表格中的数据。
  3. 对于每项内容，基本提取两行。第一行为原始血凝指标的值，第二行为变化百分比的值。如果表格里没有出现这种描述，那自行判断哪些属于原始血凝指标的值，哪些属于变化百分比。
  4. 对于原始血凝指标的描述一般有 GLU，TP，ALB，T-BIL，Cr，BUN，ALT等等。
  5. 对于每一行，若提取不出内容，在此单元格输出"-"。
  6. 按指定格式输出表格，并填入<ANSWER>标签中，不包含其他内容。
- 示例:
  - 输入:
待生成的表格的表头为:
| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 10 | 20 | 30 |
| 动物数量 | M:5 | F:5 | M:5 | F:5 | M:5 | F:5 |
输入的表格为:
| 性别 | 雄性 | 雄性 | 雄性 | 雌性 | 雌性 | 雌性 |
| 组别 | 1 | 2 | 3 | 4 | 5 | 6 |
| 受试物 | 溶媒 | KC1086 | KC1086 | 溶媒 | KC1086 | KC1086 |
| 剂量（mg/kg/day） | 0 | 5 | 15  | 0 | 2.5 | 7.5 |
| 检查动物数 | 10 | 10 | 10 | 10 | 10 | 10 |
| APTT（seconds） | 23.93 | 23.90 | 22.09 | 20.16 | 18.41 | 19.38 |
| （%差值） | - | -0.13 | -7.69 | - | -8.67 | -3.87 |
| PT（seconds） | 22.95 | 21.30 | 20.55 | 20.88 | 18.78 | 19.43 |
| （%差值） | - | -7.19 | -10.46 | - | -10.06 | -6.94 |
  - 输出:
    <ANSWER>| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 10 | 20 | 30 |
| 动物数量 | M:5 | F:5 | M:5 | F:5 | M:5 | F:5 |
| APTT（seconds） | 23.93 | 23.90 | 22.09 | 20.16 | 18.41 | 19.38 |
| （%差值） | - | -0.13 | -7.69 | - | -8.67 | -3.87 |
| PT（seconds） | 22.95 | 21.30 | 20.55 | 20.88 | 18.78 | 19.43 |
| （%差值） | - | -7.19 | -10.46 | - | -10.06 | -6.94 |</ANSWER>'''

    @classmethod
    async def extract(cls, content_md: str, common_header: str) -> str:
        input_text = f'待生成的表格的表头为:\n{common_header}\n输入的表格为:\n{content_md}'
        return await super().extract(input_text)

    @classmethod
    def parse_table(cls, table_text: str, common_header: TableRows, period: str = '终末期') -> Table:
        table: Table = []
        table.extend(common_header)

        table.append(['血凝'] * len(common_header[0]))
        lines = table_text.split('\n')
        values = lines[2:]  # 前两行是公共表头，第三行开始才是表的内容
        blood_value_lines = values[::2]  # 血生化变化值的行,偶数行
        blood_percent_lines = values[1::2]  # 血生化变化百分比的行，奇数行
        for blood_value_line, blood_percent_line in zip(blood_value_lines, blood_percent_lines):
            blood_parts = blood_value_line.strip('|').split('|')
            blood_parts = [part.strip() for part in blood_parts]

            blood_percent_parts = blood_percent_line.strip('|').split('|')
            blood_percent_parts = [part.strip() for part in blood_percent_parts]

            first_part = blood_parts[0]
            head = f'{first_part}\n（%变化百分比）'
            row = [head, blood_parts[1], blood_parts[2]]  # 第1、2列是对照组，直接添加
            for blood_part, blood_percent_part in zip(blood_parts[3:], blood_percent_parts[3:]):
                if blood_part == '-' or blood_percent_part == '-':
                    row.append(blood_part)
                else:
                    blood_percent_part = format_percentage(blood_percent_part)
                    row.append(f'{blood_part}\n（{blood_percent_part}）'.replace('<br>', '\n'))

            table.append(row)

        return table
