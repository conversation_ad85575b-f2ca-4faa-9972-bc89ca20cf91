from app.schemas.table import Table, TableRows

from .base import AzureOpenAIExtractor

# 267.7 重复给药毒性：关键试验（TBC）：续表-组织病理学
# | 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 10 | 20 | 30 |

'''
267.7 重复给药毒性：关键试验（TBC）：续表-组织病理学
'''


class HistopathologyTable(AzureOpenAIExtractor):
    SYSTEM_PROMPT = '''- 角色: 数据转换专家
- 背景: 你是一位精通数据处理和表格转换的专家，能够准确理解和操作复杂的数据结构，将数据从一种格式高效地转换为另一种格式。
- 目标: 根据待生成的表格的表头，从输入的表格中提取雌性和雄性在不同剂量下的数据，生成新的表格，并按照指定格式输出。
- 约束:
  1. 新生成的表格的表头要与提供的待生成的表格的表头一致。
  2. 所有的内容从输入的表格中提取。
  3. 待生成的表格的表头中的"动物数量"列的格式为"M:5| F:5"，其中M代表雄性，F代表雌性，10代表动物数量。
- 输出格式: 新表格将以清晰的列格式展示
- 工作流程:
  1. 识别原始表格中的数据结构和关键信息。
  2. 根据待生成的表格的表头，提取原始表格中的数据。
  3. 对于每项内容，基本提取的行数是变化的。但一般来讲，先提取器官，再提取症状，最后提取程度。
  4. 对于表格中所提供的程度行所对应的数字，请分辨出哪个数字是雄性的，哪个数字是雌性的。
  5. 症状对应行的数据的含义是：出现症状的的雄性动物数/出现症状的雌性动物数
  6. 每种症状需要输出对应的动物数。如果有披露总测试动物数，则输出为 "出现症状的动物数/总测试动物数"到对应的表头和性别单元格之下。
  7. 对于每一行，若提取不出内容，在此单元格输出"-"。
  8. 按指定格式输出表格，并填入<ANSWER>标签中，不包含其他内容。
- 示例:
  - 输入:
待生成的表格的表头为:
| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 10 | 20 | 30 | 40 | 50 |
| 动物数量 | M:5 | F:5 | M:5 | F:5 | M:5 | F:5 | M:5 | F:5 |
输入的表格为:
| 组别 | 1 | 2 | 3 | 4 |
| 受试物 | 溶媒 | 新药120 | 新药120 | 新药120 |
| 剂量（mg/kg/day） | 0/0 | 5/10 | 20/30 | 40/50 |
| 性别 | 雄性/雌性 | 雄性/雌性 | 雄性/雌性 | 雄性/雌性 |
| 检查动物数 | 5/5 | 5/5 | 5/5 | 5/5 |
| 睾丸 | 睾丸 | 睾丸 | 睾丸 | 睾丸 |
| 球部肿胀发炎 | 球部肿胀发炎 | 球部肿胀发炎 | 球部肿胀发炎 | 球部肿胀发炎 |
| 轻微 | -/- | 1/3 | 2/3 | 2/3 |
| 严重 | -/- | 1/1 | -/2 | 1/2 |
| 内部细菌感染 | 内部细菌感染 | 内部细菌感染 | 内部细菌感染 | 内部细菌感染 |
| 轻度 | -/- | -/- | 2/3 | 3/3 |
| 前列腺 | 前列腺 | 前列腺 | 前列腺 | 前列腺 |
| 钙化灶与增生 | 钙化灶与增生 | 钙化灶与增生 | 钙化灶与增生 | 钙化灶与增生 |
| 中度 | -/- | 3/1 | -/- | 2/2 |
  - 输出:
    <ANSWER>| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 10 | 20 | 30 | 40 | 50 |
| 动物数量 | M:5 | F:5 | M:5 | F:5 | M:5 | F:5 | M:5 | F:5 |
| 睾丸 | 睾丸 | 睾丸 | 睾丸 | 睾丸 | 睾丸 | 睾丸 | 睾丸 | 睾丸 |
| 球部肿胀发炎 | 球部肿胀发炎 | 球部肿胀发炎 | 球部肿胀发炎 | 球部肿胀发炎 | 球部肿胀发炎 | 球部肿胀发炎 | 球部肿胀发炎 | 球部肿胀发炎 |
| 轻微 | - | - | 1/5 | 3/5 | 2/5 | 3/5 | 2/5 | 3/5 |
| 严重 | - | - | 1/5 | 1/5 | - | 2/5 | 1/5 | 2/5 |
| 内部细菌感染 | 内部细菌感染 | 内部细菌感染 | 内部细菌感染 | 内部细菌感染 | 内部细菌感染 | 内部细菌感染 | 内部细菌感染 | 内部细菌感染 |
| 轻度 | - | - | - | - | 2/5 | 3/5 | 3/5 | 3/5 |
| 前列腺 | 前列腺 | 前列腺 | 前列腺 | 前列腺 | 前列腺 | 前列腺 | 前列腺 | 前列腺 |
| 钙化灶与增生 | 钙化灶与增生 | 钙化灶与增生 | 钙化灶与增生 | 钙化灶与增生 | 钙化灶与增生 | 钙化灶与增生 | 钙化灶与增生 | 钙化灶与增生 |
| 中度 | - | - | 3/5 | 1/5 | - | - | 2/5 | 2/5 |</ANSWER>'''

    @classmethod
    async def extract(cls, content_md: str, common_header: str) -> str:
        input_text = f'待生成的表格的表头为:\n{common_header}\n输入的表格为:\n{content_md}'
        return await super().extract(input_text)

    @classmethod
    def parse_table(cls, table_text: str, common_header: TableRows) -> Table:
        table: Table = []
        table.extend(common_header)

        table.append(['组织病理学'] * len(common_header[0]))
        lines = table_text.split('\n')
        values = lines[2:]  # 前两行是公共表头，第三行开始才是表的内容
        for value_line in values:
            parts = value_line.strip('|').split('|')
            row = [part.strip() for part in parts]
            table.append(row)

        return table
