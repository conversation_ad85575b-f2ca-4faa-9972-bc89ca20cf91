import re
from re import IGNORECASE, compile

from app.utils.table_7_3_format import format_percentage

from .base import AzureOpenAIExtractor

# 267.7 重复给药毒性：关键试验（TBC）：续表-血液学
# | 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 10 | 20 | 30 |

'''
267.7 重复给药毒性：关键试验（TBC）：续表-血液学
'''

TableRow = list[str]
TableRows = list[TableRow]


class HematologyTable(AzureOpenAIExtractor):
    SYSTEM_PROMPT = '''- 角色: 数据转换专家
- 背景: 你是一位精通数据处理和表格转换的专家，能够准确理解和操作复杂的数据结构，将数据从一种格式高效地转换为另一种格式。
- 目标: 根据待生成的表格的表头，从输入的表格中提取雌性和雄性在不同剂量下的数据，生成新的表格，并按照指定格式输出。
- 约束:
  1. 新生成的表格的表头要与提供的待生成的表格的表头一致。
  2. 所有的内容从输入的表格中提取。
  3. 待生成的表格的表头中的"动物数量"列的格式为"M:10 | F:10"，其中M代表雄性，F代表雌性，10代表动物数量。

- 输出格式: 新表格将以清晰的列格式展示
- 工作流程:
  1. 识别原始表格中的数据结构和关键信息。
  2. 根据待生成的表格的表头，提取原始表格中的数据。
  3. 有两种格式的表格，你需要根据表格的内容来判断是哪种格式的表格。
     示例1：每项内容，基本提取两行。第一行为原始血液学的指标的值，第二行为变化百分比的值。如果表格里没有出现这种描述，那自行判断哪些属于原始血液学的指标的值，哪些属于变化百分比；
     示例2：有Day -10a/-11类型这种Day日期的，提取流程如下：现在假如有三个指标：RBC（10^12/L）、HCT<br>（%）、HGB（g/dL），
           第一步：需要先把生成一行指标如：RBC（10^12/L）长度为表头的长度
           第二步：根据出现的时间与差值去提取值如：Day -10a/-11、Day -3a、Day 29、（% 差值），那么需要在RBC（10^12/L）下生成4行，生成的顺序和原始表格给出的顺序一致，每一行的第一列都是这四个其中一个，然后再从原始表格中提取对应值。
           其余的指标按照这两步流程往复提取即可。
  4. 对于原始血液学的指标的描述一般有 GLU，TP，ALB，T-BIL，Cr，BUN，ALT等等。
  5. 对于每一行，若提取不出内容，在此单元格输出"-"。
  6. 按指定格式输出表格，并填入<ANSWER>标签中，不包含其他内容。
  7. 若发现最后一行| |内为大量文字而不是数字类数据，即表明该行为表格的脚注，直接忽略即可。 
  
- 示例1:
  - 输入:
待生成的表格的表头为:
| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 10 | 20 | 30 |
| 动物数量 | M:5 | F:5 | M:5 | F:5 | M:5 | F:5 |
输入的表格为:
| 性别 | 雄性 | 雄性 | 雄性 | 雌性 | 雌性 | 雌性 |
| 组别 | 1 | 2 | 3 | 1 | 2 | 3 |
| 受试物 | 溶媒 | 新药001 | 新药001 | 新药001 | 溶媒 | 新药001 |
| 剂量（mg/kg/day） | 0 | 5 | 20 | 0 | 10 | 30 |
| 检查动物数 | 5 | 5 | 5 | 5 | 5 | 5 |
| CHOL（mg/mL） | 1.20 | 3.56 | 4.55 | 2.78 | 6.20 | 8.04<br>** |
| (% 差值) | - | +4.80 | -4.08 | - | -6.41 | +32.67 |
| ALP （kmol/mL） | 2.12 | 3.11 | 5.22 | 4.33 | 6.85 | 10.01 |
| (% 差值) | - | -5.5 | -7.2 | - | -2.2 | -14.4 |
  - 输出:
    <ANSWER>| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 10 | 20 | 30 |
| 动物数量 | M:5 | F:5 | M:5 | F:5 | M:5 | F:5 |
| CHOL（mg/mL） | 1.20 | 2.78 | 3.56 | 6.20 | 4.55 | 8.04<br>** |
| (% 差值) | - | - | +4.80 | -6.41 | -4.08 | +32.67 |
| ALP （kmol/mL） | 2.12 | 4.33 | 3.11 | 6.85 | 5.22 | 10.01 |
| (% 差值) | - | - | -5.5 | -2.2 | -7.2 | -14.4 |</ANSWER>

- 示例2:
  - 输入:
待生成的表格的表头为:
| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 10 | 20 | 30 |
| 动物数量 | M:5 | F:5 | M:5 | F:5 | M:5 | F:5 |
输入的表格为:
| 性别 | 雄性 | 雄性 | 雄性 | 雄性 | 雌性 | 雌性 | 雌性 | 雌性 |
| 组别 | 1 | 2 | 3 | 4 | 1 | 2 | 3 | 4 |
| 受试物 | 溶媒 | 新药001 | 新药001 | 新药001 | 溶媒 | 新药001 | 新药001 | 新药001 |
| 剂量（mg/kg/day） | 0 | 1 | 2 | 4 | 0 | 1 | 2 | 4 |
| 检查动物数 | 5 | 5 | 5 | 5 | 5 | 5 | 5 | 5 |
| RBC（10^12/L） | Day -10a/-11 | 5.602 | 5.948 | 5.708 | 5.468 | 5.898 | 5.362 | 5.374 | 5.398 | 
| RBC（10^12/L） | Day -3a | 5.326 | 5.726 | 5.332 | 5.356 | 5.520 | 4.998 | 5.060 | 5.164 | 
| RBC（10^12/L） | Day 29 | 5.160 | 5.092 | 4.756 | 4.728 | 5.392 | 4.568** | 4.378** | 4.344** | 
| RBC（10^12/L） | （% 差值） | – | -1 | -8 | -8 | – | -15 | -19 | -19 | 
| HCT<br>（%） | Day -10a/-11 | 42.72 | 44.78 | 43.64 | 41.90 | 44.42 | 41.50 | 41.40 | 41.36 | 
| HCT<br>（%） | Day -3a | 40.24 | 43.08 | 40.94 | 40.84 | 41.70 | 38.72 | 38.78 | 39.18 | 
| HCT<br>（%） | Day 29 | 39.26 | 38.78 | 36.82 | 36.60 | 41.08 | 35.68** | 34.22*** | 33.94*** | 
| HCT<br>（%） | （% 差值） | – | -1 | -6 | -7 | – | -13 | -17 | -17 |
| =与溶媒对照组数值差异显著（P＜0.05）Dunnett Test。**=与溶媒对照组数值差异显著 | 与溶媒对照组数值差异显著 | – | – | – | – | – | – | – |
  - 输出:
    <ANSWER>| 日剂量（mg/kg） | 0（对照） | 0（对照） | 1 | 1 | 2 | 2 | 4 | 4 |
| 动物数量 | M:5 | F:5 | M:5 | F:5 | M:5 | F:5 | M:5 | F:5 |
| RBC（10^12/L） | RBC（10^12/L） | RBC（10^12/L） | RBC（10^12/L） | RBC（10^12/L） | RBC（10^12/L） | RBC（10^12/L） | RBC（10^12/L） | RBC（10^12/L） | RBC（10^12/L） |
| Day -10a/-11 | 5.602 | 5.948 | 5.708 | 5.468 | 5.898 | 5.362 | 5.374 | 5.398 |
| Day -3a | 5.326 | 5.726 | 5.332 | 5.356 | 5.520 | 4.998 | 5.060 | 5.164 |
| Day 29 | 5.160 | 5.092 | 4.756 | 4.728 | 5.392 | 4.568** | 4.378** | 4.344** |
| （% 差值） | – | -1 | -8 | -8 | – | -15 | -19 | -19 |
| HCT<br>（%） | HCT<br>（%） | HCT<br>（%） | HCT<br>（%） | HCT<br>（%） | HCT<br>（%） | HCT<br>（%） | HCT<br>（%） | HCT<br>（%） | HCT<br>（%） |
| Day -10a/-11 | 42.72 | 44.78 | 43.64 | 41.90 | 44.42 | 41.50 | 41.40 | 41.36 |
| Day -3a | 40.24 | 43.08 | 40.94 | 40.84 | 41.70 | 38.72 | 38.78 | 39.18 |
| Day 29 | 39.26 | 38.78 | 36.82 | 36.60 | 41.08 | 35.68** | 34.22*** | 33.94*** |
| （% 差值） | – | -1 | -6 | -7 | – | -13 | -17 | -17 |</ANSWER>'''


    @classmethod
    async def extract(cls, content_md: str, common_header: str) -> str:
        input_text = f'待生成的表格的表头为:\n{common_header}\n输入的表格为:\n{content_md}'
        return await super().extract(input_text)

    @classmethod
    def parse_table(cls, table_text: str, common_header: TableRows) -> TableRows:
        table: TableRows = []
        table.extend(common_header)
        table.append(['血液学参数'] * len(common_header[0]))
        lines = table_text.split('\n')
        values = lines[2:]  # 前两行是公共表头，第三行开始才是表的内容

        for line in values:
            parts = line.strip('|').split('|')
            parts = [part.strip() for part in parts]
            table.append(parts)

        return table
