from re import IGNORECASE, compile

from app.schemas.table import Table, TableRows
from app.utils.table_7_3_format import format_percentage

from .base import AzureOpenAIExtractor

'''
267.7 重复给药毒性：关键试验（TBC）：续表-器官重量
'''


class OrganWeightTable(AzureOpenAIExtractor):
    SYSTEM_PROMPT = '''- 角色: 数据转换专家
- 背景: 你是一位精通数据处理和表格转换的专家，能够准确理解和操作复杂的数据结构，将数据从一种格式高效地转换为另一种格式。
- 目标: 根据待生成的表格的表头，从输入的表格标题中提取，表格内容中提取雌性和雄性在不同剂量下的数据，生成新的表格，并按照指定格式输出。
- 约束:
  1. 新生成的表格的表头要与提供的待生成的表格的表头一致。
  2. 所有的内容从输入的表格中提取。
  3. 待生成的表格的表头中的"动物数量"列的格式为"M:10 | F:10"，其中M代表雄性，F代表雌性，10代表动物数量。

- 输出格式: 新表格将以清晰的列格式展示
- 工作流程:
  1. 识别原始表格中的数据结构和关键信息。
  2. 根据待生成的表格的表头，提取原始表格中的数据。
  3. 从表格标题中提取出内脏，如"内脏变化-睾丸"中的"睾丸"。并且用生成在表格中。若没有提取出任何内脏，则输出"无内脏"。
  4. 对于每项内容，基本提取两行。第一行为器官的指标的值，第二行为变化百分比的值。如果表格里没有出现这种描述，那自行判断哪些属于原始器官的指标的值，哪些属于变化百分比。
  5. 对于原始血生化的指标的描述一般有 GLU，TP，ALB，T-BIL，Cr，BUN，ALT等等。
  6. 对于每一行，若提取不出内容，在此单元格输出"-"。
  7. 按指定格式输出表格，并填入<ANSWER>标签中，不包含其他内容。
- 示例一:
  - 输入:
待生成的表格的表头为:
| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 10 | 20 | 30 |
| 动物数量 | M:5 | F:5 | M:5 | F:5 | M:5 | F:5 |
输入的表格为:
表格标题:
正文表18：与cancer超级药005相关的内脏重量变化（前列腺）
表格内容:
| 性别 | 雄性 | 雄性 | 雄性 | 雌性 | 雌性 | 雌性 |
| 组别 | 1 | 2 | 3 | 1 | 2 | 3 |
| 受试物 | 溶媒 | 新药001 | 新药001 | 新药001 | 溶媒 | 新药001 |
| 剂量（mg/kg/day） | 0 | 5 | 20 | 0 | 10 | 30 |
| 检查动物数 | 5 | 5 | 5 | 5 | 5 | 5 |
| 内脏重量（mg） | 1.20 | 3.56 | 4.55 | 2.78 | 6.20 | 8.04<br>** |
| (% 差值) | - | +4.80 | -4.08 | - | -6.41 | +32.67 |
| 内脏与大脑比值 （%） | 2.12 | 3.11 | 5.22 | 4.33 | 6.85 | 10.01 |
| (% 差值) | - | -5.5 | -7.2 | - | -2.2 | -14.4 |
  - 输出:
    <ANSWER>| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 10 | 20 | 30 |
| 动物数量 | M:5 | F:5 | M:5 | F:5 | M:5 | F:5 |
| 前列腺 | 前列腺 | 前列腺 | 前列腺 | 前列腺 | 前列腺 | 前列腺 |
| 内脏重量（mg） | 1.20 | 2.78 | 3.56 | 6.20 | 4.55 | 8.04<br>** |
| (% 差值) | - | - | +4.80 | -6.41 | -4.08 | +32.67 |
| 内脏与大脑比值 （%） | 2.12 | 4.33 | 3.11 | 6.85 | 5.22 | 10.01 |
| (% 差值) | - | - | -5.5 | -2.2 | -7.2 | -14.4 |</ANSWER>
- 示例二:
  - 输入:
待生成的表格的表头为:
| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 10 | 20 | 30 |
| 动物数量 | M:10 | F:10 | M:10 | F:10 | M:10 | F:10 |
输入的表格为:
表格标题:
正文表18：与药120相关的内脏重量变化
表格内容:
| 性别 | 雄性 | 雄性 | 雄性 | 雌性 | 雌性 | 雌性 |
| 组别 | 1 | 2 | 3 | 1 | 2 | 3 |
| 受试物 | 溶媒 | 新药001 | 新药001 | 新药001 | 溶媒 | 新药001 |
| 剂量（mg/kg/day） | 0 | 5 | 20 | 0 | 10 | 30 |
| 检查动物数 | 10 | 10 | 10 | 10 | 10 | 10 |
| 器官（mg） | 1.70 | 3.35 | 4.55 | 1.72 | 6.20 | 8.55<br>** |
| (% 差值) | - | +5.99 | -3.02 | - | -7.55 | +21.55 |
  - 输出:
    <ANSWER>| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 10 | 20 | 30 |
| 动物数量 | M:10 | F:10 | M:10 | F:10 | M:10 | F:10 |
| 无内脏 | 无内脏 | 无内脏 | 无内脏 | 无内脏 | 无内脏 | 无内脏 |
| 器官重量（mg） | 1.70 | 1.72 | 3.35 | 6.20 | 4.55 | 8.55<br>** |
| (% 差值) | - | - | +5.99 | -7.55 | -3.02 | +21.55 |</ANSWER>'''

    @classmethod
    async def extract(cls, content_md: str, common_header: str, table_title: str = '') -> str:
        input_text = (
            f'待生成的表格的表头为:\n{common_header}\n输入的表格为:\n表格标题:\n{table_title}\n表格内容:\n{content_md}'
        )
        return await super().extract(input_text)

    @classmethod
    def parse_table(cls, table_text: str, common_header: TableRows) -> Table:
        table: TableRows = []
        table.extend(common_header)

        table.append(['器官重量'] * len(common_header[0]))
        lines = table_text.split('\n')
        specific_organ_parts = lines[2].strip('|').split('|')
        specific_organ = specific_organ_parts[0]
        if specific_organ == '无内脏':  # 如果第三行是无内脏，则新表里添加一整个空行
            table.append([''] * len(common_header[0]))
        else:
            table.append([specific_organ] * len(common_header[0]))  # 否则添加器官名称
        values = lines[3:]  # 前两行是公共表头，第三行是具体器官，第四行开始才是表的内容
        organ_weight_value_lines = values[::2]  # 器官变化值的行,偶数行
        organ_weight_percent_lines = values[1::2]  # 器官变化百分比的行，奇数行
        for organ_weight_value_line, organ_weight_percent_line in zip(
            organ_weight_value_lines, organ_weight_percent_lines
        ):
            organ_weight_parts = organ_weight_value_line.strip('|').split('|')
            organ_weight_parts = [part.strip() for part in organ_weight_parts]

            organ_weight_percent_parts = organ_weight_percent_line.strip('|').split('|')
            organ_weight_percent_parts = [part.strip() for part in organ_weight_percent_parts]

            first_part = organ_weight_parts[0]
            head = f'{first_part}\n（%变化百分比）'
            row = [head, organ_weight_parts[1], organ_weight_parts[2]]  # 第1、2列是对照组，直接添加
            for organ_weight_part, organ_weight_percent_part in zip(
                organ_weight_parts[3:], organ_weight_percent_parts[3:]
            ):
                if organ_weight_part == '-' or organ_weight_percent_part == '-':
                    row.append(organ_weight_part)
                else:
                    organ_weight_percent_part = format_percentage(organ_weight_percent_part)
                    row.append(f'{organ_weight_part}\n（{organ_weight_percent_part}）'.replace('<br>', '\n'))

            table.append(row)

        return table
