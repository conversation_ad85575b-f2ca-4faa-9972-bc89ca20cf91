from re import compile

from app.utils.exception import ExtractError

from .base import AzureOpenAIExtractor

REFERENCE_PATTERN = compile(
    r'\n(参考文献|参考|引用|附录|附表|References?|Bibliography|Citation|Appendix|Appendices)\n.+'
)
DOSAGE_MAX_PATTERN = compile(r'最大耐受|不良耐受|NOAEL')


class GenderAndNumberPerGroup(AzureOpenAIExtractor):
    """
    性别和数量/组
    文档：《单次给药毒性报告》
    位置：7.4.1.组别设计-正文表5-动物数列
    文档：《重复给药毒性-非关键试验报告》
    位置：7.4.1.组别设计-正文表4-主试验列
    规则：提取上图框定数据中的“雄性数量”及“雌性数量”，表达为：
    性别和数量组 = #雄性数量#M+#雌性数量#F/组
    """

    SYSTEM_PROMPT = '''- 角色: 医学研究分析专家
- 目标: 提取表格中的"雄性数量"及"雌性数量"。
- 输出格式: "雄性数量"M+"雌性数量"F/组。
- 工作流程:
  1. 阅读并理解医学表格。
  2. 如果表格包含TSb列或RSc列，则提取为空字符串。
  3. 否则，如果表格不包含主试验列，则提取表格中的"雄性数量"及"雌性数量"。
  4. 否则，如果表格包含主试验列，则提取该列所对应的"雄性数量"及"雌性数量"。
  5. 如果有多个组别，则以第一组数据为准。
  6. 提取到动物数量时，结果格式为: "雄性数量"M+"雌性数量"F/组。
  7. 将结果填入<ANSWER>标签中返回。
- 示例一:
  - 输入:
| 组别 | 受试物 | 给药剂量 (mg/kg/day) | 浓度 (mg/mL) | 动物数（雄性/雌性） |
| 组别 | 受试物 | 给药剂量 (mg/kg/day) | 浓度 (mg/mL) | 动物数（雄性/雌性） |
| 1 | 新药x | 400 | 40 | 4/4 |
| 2 | 新药x | 1000 | 100 | 4/4 |
| 3 | 新药x | 600 | 60 | 4/4 |
| 4 | 新药x | 800 | 80 | 4/4 |
  - 输出: <ANSWER>4M+4F/组</ANSWER>
- 示例二:
  - 输入:
| 组别 | 受试物 | 给药剂量 (mg/kg/day) | 浓度 (mg/mL) | 动物数（雄性/雌性） | 动物数（雄性/雌性） |
| 组别 | 受试物 | 给药剂量 (mg/kg/day) | 浓度 (mg/mL) | 动物数（雄性/雌性） | 动物数（雄性/雌性） |
| 组别 | 受试物 | 给药剂量 (mg/kg/day) | 浓度 (mg/mL) | 主试验 | TK |
| 1 | 溶媒a | 0 | 0 | 5/5 | 3/3 |
| 2 | 新药x | 10 | 1 | 5/5 | 3/3 |
| 3 | 新药x | 25 | 2.5 | 5/5 | 3/3 |
| 4 | 新药x | 75 | 7.5 | 5/5 | 3/3 |
| 5 | 新药x | 150 | 15 | 5/5 | 3/3 |
  - 输出: <ANSWER>5M+5F/组</ANSWER>
- 示例三:
  - 输入:
| 组别 | 受试物 | 给药剂量(mg/kg/day) | 浓度<br>(mg/mL) | 动物数(雄性/雌性) | 动物数(雄性/雌性) | 动物号(雄性/雌性) | 动物号(雄性/雌性) |
| 组别 | 受试物 | 给药剂量(mg/kg/day) | 浓度<br>(mg/mL) | TSb | RSC | TSb | RSC |
| 1 | 溶媒a | 0 | 0 | 1/1 | 1/1 | YD123/YD456 | YD288/YD299 |
| 2 | XXX | 0.3 | 0.06 | 2/2 | - | YD2111, YD4444/YD25555, YD20220 | - |
| 3 | XXX | 1.0 | 0.2 | 1/1 | 1/1 | YD2222/YD333 | YD20569/YD20616 |
  - 输出: <ANSWER></ANSWER>'''

    @classmethod
    def format(cls, result: str) -> str:
        return super().format(result) or '-'  # 为空时需要输出 -


class DosageMaxAndNotableResult(AzureOpenAIExtractor):
    """
    最大耐受剂量
    文档：《单次给药毒性报告》
    位置：9.结论-最后一段
    文档：《重复给药毒性-非关键试验报告》
    位置：9.结论
    规则：将上述文本提交给LLM，判断出“雄性最大耐受剂量”和“雌性最大耐受剂量”，如两者不相等，表达为：
    雄性（M）：#雄性最大耐受剂量#
    雌性（F）：#雌性最大耐受剂量#
    如相等，表达为：
    #雄性最大耐受剂量#

    值得注意的结果
    文档：《单次给药毒性报告》
    位置：9.结论-最后一段
    文档：《重复给药毒性-非关键试验报告》
    位置：9.结论
    规则：有多个段落时，返回不包含最大耐受剂量的描述的所有段落
    注意:
    只有一个段落时，不需要调用LLM接口，应该直接返回
    """

    SYSTEM_PROMPT = '''- 角色: 医学研究分析专家
- 目标: 准确提取出给定文本中的"最大耐受剂量"或"可见不良反应剂量"，以及值得注意的结果。
- 工作流程:
  1. 阅读并理解文本
  2. 准确识别并提取"最大耐受剂量"
  3. 如果雄性和雌雄的"最大耐受剂量"不同，则分别提取
  4. 如果找不到上述最大耐受剂量，则提取"无可见不良反应剂量"
  5. 将结果填入<ANSWER>标签中返回
- 输出格式:
  - 若雌雄剂量相同，输出格式为: <ANSWER>最大耐受剂量或无可见不良反应剂量</ANSWER>
  - 若雌雄剂量不同，输出格式为: <ANSWER>M:雄性最大耐受剂量
F:雌性最大耐受剂量</ANSWER>
- 示例一:
  - 输入: 因此，本试验中新药x单次给药时，大鼠的最大耐受剂量（MTD）分别为80 mg/kg/day（雄性动物）和60 mg/kg/day（雌性动物）。
  - 输出: <ANSWER>M:80
F:60</ANSWER>
- 示例二:
  - 输入: 14天重复经口灌胃给予大鼠剂量为1、2、7.5和15 mg/kg/day的新药x，动物耐受良好。与受试物相关的改变包括体重增量降低，第1-8天摄食量降低（雄性7.5 mg/kg/day，雌性15 mg/kg/day）。本试验条件下，重复给予雄性和雌性大鼠新药x的最大耐受剂量（MTD）定为7.5 mg/kg/day。第14天血浆中Cmax和AUClast的值，雄性分别为1800 ng/mL和15402 hr*ng/mL，雌性分别为1567 ng/mL 和29198 hr*ng/mL。
  - 输出: <ANSWER>7.5</ANSWER>'''

    @classmethod
    async def extract(cls, input_text: str) -> tuple[str, str]:
        input_text = input_text.strip()
        parts = REFERENCE_PATTERN.split(input_text, 2)  # 去掉参考文献
        input_text = parts[0].strip()

        exclude_last_part = False
        dosage_max_text = input_text
        parts = input_text.rsplit('\n', 1)
        if len(parts) == 2:
            last_part = parts[-1]
            if DOSAGE_MAX_PATTERN.search(last_part):
                dosage_max_text = last_part
                exclude_last_part = True
        answer = await super().extract(dosage_max_text)
        if not answer:
            raise ExtractError()

        if exclude_last_part:
            return answer, parts[0]  # 有多段时，值得注意的结果需要排除包含剂量的段落，即最后一段
        return answer, input_text
