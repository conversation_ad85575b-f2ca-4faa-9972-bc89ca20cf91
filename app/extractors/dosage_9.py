from .base import AzureOpenAIExtractor

'''
dosage_9文件中包含了2.6.7.9.1 遗传毒性：体内-微核试验部分中所有需要经由LLM处理的字段
'''


class SamplingTime(AzureOpenAIExtractor):
    """
    11.1.2 - 5
    遗传毒性：体内-微核试验 - 采样时间
    规则：将原文提交给LLM，判断受试物组动物在什么时候被采集骨髓
    """

    SYSTEM_PROMPT = '''- 角色: 数据提取专家。
- 目标: 从输入内容中提取“受试物组动物”采集骨髓的时间。
- 工作流:
  1. 解析输入的内容。
  2. 找到有关对“受试物组动物”的描述。
  3. 提取“受试物组动物”采集骨髓的时间，提取的内容要与严格根据原文描述，不做任何修改。
  4. 将提取结果填入<ANSWER>标签中。
- 示例:
  - 输入: 第一次微核主试验中的雄性大鼠和第二次微核主试验中的雌性大鼠，溶媒对照和受试物组动物在最后一次给药后18-24 小时，按计划实施安乐死以采集骨髓。阳性药组动物在最后一次给药后24±1 小时，按计划实施安乐死以采集骨髓。
  - 输出: <ANSWER>最后一次给药后18-24 小时</ANSWER>'''


class GetAge(AzureOpenAIExtractor):
    """
    11.1.2 - 7
    遗传毒性：体内-微核试验 - 年龄
    规则：将原文提交给LLM，判断微核主试验开始时的动物周龄
    """

    SYSTEM_PROMPT = '''- 角色: 数据提取专家。
- 目标: 从输入内容中提取“微核主试验开始时动物的周龄”。
- 工作流:
  1. 解析输入的内容。
  2. 找到有关“微核主试验”的描述。
  3. 提取“微核主试验开始时动物的周龄”，提取的内容要与严格根据原文描述，不做任何修改。
  4. 将提取结果填入<ANSWER>标签中。
- 示例:
  - 输入:
    剂量探索试验给药第一天，动物周龄为6至9周，体重为雄性266.7克至339.7克，雌性为175.0克至227.9克。
    微核主试验给药第一天，动物周龄为6至8周，体重为雄性248.5克至287.1克，雌性为191.3克至217.3克。
    雌性重复微核主试验给药第一天，动物周龄为6至8周，体重为雌性178.8克至197.7克。
  - 输出: <ANSWER>6至8周</ANSWER>'''


class AdministrationMethod9(AzureOpenAIExtractor):
    """
    11.1.2 - 8
    遗传毒性：体内-微核试验 - 给药方法
    规则：将原文提交给LLM，判断受试物给药的方法
    """

    SYSTEM_PROMPT = '''- 角色: 数据提取专家。
- 目标: 从输入内容中提取“受试物的给药途径”。
- 工作流:
  1. 解析输入的内容。
  2. 提取“受试物的给药途径”，提取的内容要与严格根据原文描述，不做任何修改。
  3. 将提取结果填入<ANSWER>标签中。
- 示例:
  - 输入: 受试物通过经口灌胃给药。本试验选用口服给药因其与临床拟定的给药方式相同。
  - 输出: <ANSWER>经口灌胃</ANSWER>'''


class AdministrationDate(AzureOpenAIExtractor):
    """
    11.1.2 - 9
    遗传毒性：体内-微核试验 - 给药日期
    规则：试验开始日期
    """

    SYSTEM_PROMPT = '''- 角色: 数据提取专家。
- 目标: 从输入内容中提取“试验开始日期”。
- 工作流:
  1. 解析输入的内容。
  2. 找到“试验开始日期”。
  3. 将提取结果填入<ANSWER>标签中。
- 示例:
  - 输入:
    研究开始日期：	2023年12月18日
    实验开始日期：	2023年12月27日
    实验完成日期：	2024年02月15日
  - 输出: <ANSWER>2023年12月27日</ANSWER>'''


class EvaluateCells(AzureOpenAIExtractor):
    """
    11.1.2 - 10
    遗传毒性：体内-微核试验 - 评价细胞
    规则：将原文提交给LLM，判断该试验评价的是什么细胞
    """

    SYSTEM_PROMPT = '''- 角色: 数据提取专家。
- 目标: 根据输入内容，提取其中的“评价细胞”是什么。
- 工作流:
  1. 解析输入的内容。
  2. 根据“检测xx细胞的微核率”找到代指的“评价细胞”。
  3. “评价细胞”需和原文一致，无需英文解释。
  4. 将提取结果填入<ANSWER>标签中。
- 示例:
  - 输入: 本试验目的是通过对SD大鼠灌胃给予新药xxx，检测其体内白细胞（Leukocytes）的微核率，以评价它在体内的破坏作用。
  - 输出: <ANSWER>白细胞</ANSWER>'''


class CellNumber(AzureOpenAIExtractor):
    """
    11.1.2 - 12
    遗传毒性：体内-微核试验 - 分析细胞数量/每动物
    规则：将原文提交给LLM，判断该试验评价的是什么细胞
    """

    SYSTEM_PROMPT = '''- 角色: 数据提取专家。
- 目标: 根据输入内容，提取“分析细胞数量/每动物”。
- 工作流:
  1. 解析输入的内容。
  2. 找到每只动物计数分析的细胞数量。
  3. 将提取结果填入<ANSWER>标签中。
- 示例:
  - 输入: 在显微镜下放大200倍分析染色后的涂片。每张涂片计数至少1000个嗜多染红细胞（PCE），每只动物计数至少3000个PCE，以评估微核率。同时每张片子计数至少100个红细胞，每只动物计数至少500个红细胞，以评估每只动物PCE占红细胞的比例。
  - 输出: <ANSWER>至少3000个</ANSWER>'''


class ToxicEffects(AzureOpenAIExtractor):
    """
    11.1.2 - 13
    遗传毒性：体内-微核试验 - 毒性/细胞毒性作用
    规则：将原文提交给LLM，判断受试物是否有细胞毒性及具体的毒性作用，表达格式：
        - 当有细胞毒性时：显示#毒性作用#（例如PCE下降）
        - 当无毒性作用时：显示“无”
    """

    SYSTEM_PROMPT = '''- 角色: 数据提取专家。
- 目标: 根据输入内容，提取有关细胞毒性的内容。
- 工作流:
  1. 解析输入的内容，找到有关“细胞毒性”的内容。
  2. 如果无细胞毒性，直接返回“无”；
  3. 如果有细胞毒性描述，则只返回有关细胞毒性作用的那句话内容，不返回额外内容，且返回的内容要严格按照原文描述，不做任何修改。
  4. 不用添加最后的标点符号。
  5. 将提取结果填入<ANSWER>标签中。
- 示例1:
  - 输入: 和溶媒对照相比，大鼠在所有给药剂量水平上均未观察到NCE占红细胞总数百分比的显著降低，这提示新药xxx在所测剂量均无细胞毒性。
  - 输出: <ANSWER>无</ANSWER>
- 示例2:
  - 输入: 实验数据显示，在100 mg/kg剂量组，观察到PCE占红细胞总数百分比显著低于溶媒对照组，这表明新药xxx在该剂量对大鼠骨髓具有细胞毒性。
  - 输出: <ANSWER>在100 mg/kg剂量组，观察到PCE占红细胞总数百分比显著低于溶媒对照组</ANSWER>'''


class GenotoxicEffects9(AzureOpenAIExtractor):
    """
    11.1.2 - 14
    遗传毒性：体内-微核试验 - 遗传毒性作用
    规则：将原文提交给LLM，判断#受试物#的结果为阴性或阳性，如有，返回“具体毒性表现”
    """

    SYSTEM_PROMPT = '''- 角色: 数据提取专家。
- 目标: 根据输入内容，判断受试物的“遗传毒性”结果，并返回相应的描述。
- 工作流:
    1. 解析输入的内容，找到有关受试物“遗传毒性”测试结果的描述。
    2. 如果结果为“阴性”或“无遗传毒性”，则返回“无”。
    3. 如果结果为“阳性”或“有遗传毒性”，则截取原文中描述遗传毒性相关结果的片段。
    4. 将最后结果填入<ANSWER>标签中。
- 示例:
    - 输入: 试验有效，新药aaa在本次试验中的测试结果为阴性。
    - 输出: <ANSWER>无</ANSWER>
    - 输入：实验数据显示，受试物新药005在高剂量组引起了染色体断裂，超过阴性对照范围，符合阳性标准。因此，受试物新药005被判定为阳性。
    - 输出：<ANSWER>受试物新药005在高剂量组引起了染色体断裂，超过阴性对照范围</ANSWER>'''
