import json
import re

from app.schemas.table import TableRows

from .base import AzureOpenAIExtractor

'''
267.7 重复给药毒性：关键试验（TBC）：主表-下半部分生成
'''

DOSE_PATTERN = re.compile(r'\d+(?:\.\d+)?')


# 267.7 主表 - 下半 - 表头的生成 LLM
class Get7TKData(AzureOpenAIExtractor):
    """
    获取正文表中有关TK行/列的数据
    """

    SYSTEM_PROMPT = '''- Role: 数据提取专家。
- Background: 用户需要从医学研究表格中提取“TK”数据，并严格按照格式输出。
- Skills: 熟练处理复杂数据、识别并提取关键数据。
- Goals: 从表格中提取“TK”数据并按用户指定格式输出。
- Constraints: 输出必须严格遵循用户格式，不包含无关信息。
- Workflow:
  1. 检查表格中是否包含“TK”数据，如果检查不到“TK”字样，返回为空。
  2. 如果表格中有“TK”字样，找到“TK”的上级目录（如“动物数”），根据“TK”的上级目录和“TK”的位置关系，判断是输出“TK”所在的行数据还是列数据。
  3. 将“TK”所在行或列下对应的动物数数值存入一个列表中。
  4. 最后将列表填入<ANSWER>标签中返回。
- Examples:
  - input 1:
    | 组别 | 动物数 (雄/雌) | 动物数 (雄/雌) | 动物数 (雄/雌) |
    | 组别 | 主试验动物 | 主试验动物 | TK |
    | 组别 | TSb | RSc | TK |
    | 1 | 10/10 | 5/5 | 3/3 |
    | 2 | 10/10 | 5/5 | 3/3 |
    | 3 | 10/10 | 5/5 | 3/3 |
  - output 1:
    <ANSWER>["3/3", "3/3", "3/3"]</ANSWER>
  - input 2:
    | 组别 | 受试物 | 给药剂量 | 动物数 (M/F) | 动物数 (M/F) |
    | 组别 | 受试物 | 给药剂量| TSa | RSb |
    | 1 | 溶媒c | 0 | 3/3 | 2/2 |
    | 2 | 新药001 | 1.5 | 3/3 | 2/2 |
  - output 2:
    <ANSWER></ANSWER>
  - input 3:
    | 组别 | 组别 | 1 | 2 | 3 |
    | 受试物 | 受试物 | 新药002 | 新药002 | 新药002 |
    | 给药剂量 | 给药剂量 | 3 | 5 | 10 |
    | 动物数 | TSa | 5/5 | 5/5 | 5/5 |
    | 动物数 | RSb | 10/10 | 10/10 | 10/10 |
    | 动物数 | TK | 10/10 | 10/10 | 10/10 |
  - output 3:
    <ANSWER>["10/10", "10/10", "10/10"]</ANSWER>'''


# 267.7 主表 - 下半 - 表头的生成
async def generate_table_header(table_chunk: str, administration_dosage: str) -> TableRows:
    """
    table_chunk：是正文表数据，需要解析“TK”数值
    administration_dosage：给药剂量，有两种形式：
        1. “2、5、7 mg/kg/day”
        2. “雄性：5、15 mg/kg/day\n雌性：2、7 mg/kg/day”
    return: 2行的表头，分别是日剂量和动物数量，例如：
        [
            ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2', '15', '7'],
            ['动物数量', 'M:3', 'F:3', 'M:3', 'F:3', 'M:3', 'F:3'],
        ]
    """
    # 生成日剂量
    first_row: list[str] = ['日剂量（mg/kg）', '0（对照）', '0（对照）']
    administration_dosage_lines = administration_dosage.split('\n')
    if len(administration_dosage_lines) == 1:
        # 形式 1
        doses = DOSE_PATTERN.findall(administration_dosage)
        for dose in doses:  # 雌雄剂量相同，添加两次
            first_row.extend([dose, dose])
    else:
        # 形式 2
        male_doses = DOSE_PATTERN.findall(administration_dosage_lines[0])
        female_doses = DOSE_PATTERN.findall(administration_dosage_lines[1])
        for male_dose, female_dose in zip(male_doses, female_doses):
            first_row.extend([male_dose, female_dose])

    # 生成动物数量
    second_row: list[str] = ['动物数量']
    animal_count_str = await Get7TKData.extract(table_chunk)
    if animal_count_str:
        animal_counts = json.loads(animal_count_str)
        for animal_count in animal_counts:
            male_count, female_count = animal_count.split('/')  # TODO: 确保是两行
            second_row.extend([f'M:{male_count}', f'F:{female_count}'])
    else:
        # 获取失败时，数量为空
        second_row.extend(['M:', 'F:'] * ((len(first_row) - 1) // 2))

    return [first_row, second_row]


# 267.7 主表 - 下半 - 生成AUClast和Cmax数据 LLM
class Get7AUClastAndCmaxData(AzureOpenAIExtractor):
    """
    根据正文表获取AUClast、Cmax数据，并填入相应的表格内
    """

    SYSTEM_PROMPT = '''- Role: 数据提取专家。
- Background: 用户需要从数据表中提取出需要的信息，并把提取到的信息准确填入模板表相关位置，并严格按照格式输出。
- Skills: 熟练处理复杂数据、识别并提取关键数据，并完成数据的准确填入工作。
- Workflow:
  1. 将模板表中的“日剂量”、“动物数量”直接保留。
  2. 在数据表中准确识别出“给药天数”、“给药剂量”、“AUClast”、“Cmax”数据。
  3. 分析数据表和模板表在“给药天数”（如1、28天）和“给药剂量”（如5、2.5）上的对应关系。
    - 如果模板表中的数据位置对应的“给药天数”、“给药剂量”在数据表中存在匹配，则填充数据表中对应的具体数值。
    - 否则，填充"-"。
  4. 如果数据表中的“AUClast”、“Cmax”数据内保留有单位（如'(hr*ng/ml)', '(ng/ml)'），就将单位追加在对应的“指标标题”（如'毒代动力学AUClast'、'毒代动力学Cmax'）中。
  5. 最后将输出表填入<ANSWER>标签中返回。
- Examples:
  - input:
    - 数据表:
    | 给药天数 | 组别 | 给药剂量(mg/kg/day) | 性别 | Cmax | AUClast |
| 给药天数 | 组别 | 给药剂量(mg/kg/day) | 性别 | (ng/ml) | (hr*ng/ml) |
| 1 | 2 | 5 | 雄性 | 22 | 33 |
| 1 | 2 | 2.5 | 雌性 | 44 | 55 |
| 28 | 2 | 5 | 雄性 | 66 | 77 |
| 28 | 2 | 2.5 | 雌性 | 88 | 99 |
    - 模板表:
    | 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 |
| 动物数量 | M:4 | F:4 | M:4 | F:4 |
| 毒代动力学AUClast | - | - | - | - |
| 第1天 | - | - | - | - |
| 第28天 | - | - | - | - |
| 毒代动力学Cmax | - | - | - | - |
| 第1天 | - | - | - | - |
| 第28天 | - | - | - | - |
  - output:
  <ANSWER>| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 |
| 动物数量 | M:4 | F:4 | M:4 | F:4 |
| 毒代动力学AUClast(hr*ng/ml) | 毒代动力学AUClast(hr*ng/ml) | 毒代动力学AUClast(hr*ng/ml) | 毒代动力学AUClast(hr*ng/ml) | 毒代动力学AUClast(hr*ng/ml) |
| 第1天 | - | - | 33 | 55 |
| 第28天 | - | - | 77 | 99 |
| 毒代动力学Cmax(ng/ml) | 毒代动力学Cmax(ng/ml) | 毒代动力学Cmax(ng/ml) | 毒代动力学Cmax(ng/ml) | 毒代动力学Cmax(ng/ml) |
| 第1天 | - | - | 22 | 44 |
| 第28天 | - | - | 66 | 88 |</ANSWER>'''


# 267.7 主表 - 下半 - 生成AUClast和Cmax数据
async def generate_table_auc_last_c_max_data(
    table_chunk: str, dosing_regimen: str, table_header: TableRows
) -> TableRows:
    """
    table_chunk：正文表格数据，用于提取AUClast、Cmax的值
    dosing_regimen：给药周期
    table_header:表头，如：
      [
        ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
        ['动物数量', 'M:4', 'F:4', 'M:4', 'F:4', 'M:4', 'F:4', 'M:4', 'F:4']
      ]
    """
    # 将表头由list转为md
    table_header_md = f"| {' | '.join(table_header[0])} |\n| {' | '.join(table_header[1])} |"

    # 模板表的生成模板
    base_rows = [
        '| 毒代动力学AUClast |',
        '| 第1天 |',
        f'| 第{dosing_regimen} |',
        '| 毒代动力学Cmax |',
        '| 第1天 |',
        f'| 第{dosing_regimen} |',
    ]

    # 生成md格式的模板表
    add_num = len(table_header[0]) - 1
    base_rows_md = '\n'.join([row + ''.join([' - |'] * add_num) for row in base_rows])
    base_table = table_header_md + '\n' + base_rows_md

    # LLM提示词，得到输出结果
    input_prompt = f'数据表:\n{table_chunk}\n模板表:\n{base_table}'
    result_md = await Get7AUClastAndCmaxData.extract(input_prompt)

    # MD转为list，输出
    result_table = []
    rows = result_md.strip().split('\n')
    for row in rows:
        row_data = [cell.strip() for cell in row.strip('|').split('|')]
        result_table.append(row_data)

    return result_table
