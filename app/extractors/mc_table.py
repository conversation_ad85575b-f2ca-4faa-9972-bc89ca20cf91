import json
from re import compile

from app.schemas.table import Table

from .base import AzureOpenAIExtractor

'''
表格生成分成 3 个阶段：
1. 小表：
    1. 表头：
        | 受试物 | 剂量（mg/kg） |
    2. 回填内容：
        | 含5%DMSO+10%Solutol HS15的20% HP-β-CD去离子水溶液 | |
        | 新药001 | 100 |
        | 新药001 | 200 |
        | 新药001 | 400 |
        | 环磷酰胺一水合物 | 20 |

2. 中表: 在小表的左边 性别，小表有多少行，就生成多少行; 在表的右方，且按顺序回填数据; 不需要表头：
    | 雄性 | 含5%DMSO+10%Solutol HS15的20% HP-β-CD去离子水溶液 | | 58.21 ± 0.40 | 20000 | 23 | 0.12 ± 0.01 |
    | 雄性 | 新药001 | 100 | 57.85 ± 0.85 | 20000 | 21 | 0.11 ± 0.01 |
    | 雄性 | 新药001 | 200 | 58.58 ± 0.52 | 20000 | 22 | 0.11 ± 0.01 |
    | 雄性 | 新药001 | 400 | 58.56 ± 0.94 | 20000 | 24 | 0.12 ± 0.01 |
    | 雄性 | 环磷酰胺一水合物 | 20 | 50.31 ± 0.56*** | 20000 | 655 | 3.28 ± 0.14*** |

3. 大表：
    将所有中表竖向合并，表头只要一个，但性别要保留；
'''
SmallMCTableRow = tuple[str, str]  # 小表的表头或含有值的行
SmallMCTable = list[SmallMCTableRow]  # 小表
LargeOrMediumMCTableRow = tuple[str, str, str, str, str, str, str]  # 中表的行或大表的表头和行
MediumMCTable = list[LargeOrMediumMCTableRow]  # 中表
LargeMCTable = list[LargeOrMediumMCTableRow]  # 大表

PLUS_MINUS_PATTERN = compile(r'\s*±\s*')


def fix_plus_minus(text: str) -> str:
    return PLUS_MINUS_PATTERN.sub(' ± ', text)


class MCTable(AzureOpenAIExtractor):
    SYSTEM_PROMPT = '''- Role: 数据转换专家
- Profile: 你是一位精通数据处理和表格转换的专家，能够准确理解和操作复杂的数据结构，将数据从一种格式高效地转换为另一种格式。
- Constrains: 转换过程中需保持数据的准确性和完整性，确保新表格的格式符合用户要求。
- OutputFormat: 新表格将以清晰的列格式展示，包括 受试物、剂量（mg/kg）
- Workflow:
  1. 识别原始表格中的数据结构和关键信息。
  2. 受试物和剂量都从表格的给药组中提取，并且填到新表格中
  3. 将排序后的数据填充到新表格中。
  4. 按指定格式输出表格，并填入<ANSWER>标签中，不包含其他内容。
- Example:
  - input:
| 给药组 |
| 组 1含20%H2O2+30%AU CL4的60% β-XX医药酒精溶液 |
| 组 2 (新药33, 200 mg/kg/day) |
| 组 3 (新药33, 300 mg/kg/day) |
| 组 6 (聚乙二醇水合物, 40 mg/kg) |
| ***与平行溶媒对照比有显著差异, 方差分析(ANOVA), P<0.001 |
  - output:
    <ANSWER>| 受试物 | 剂量（mg/kg） |
| 含20%H2O2+30%AU CL4的60% β-XX医药酒精溶液 | |
| 新药33 | 200 |
| 新药33 | 300 |
| 聚乙二醇水合物 | 40 |</ANSWER>'''

    @classmethod
    async def extract(cls, input_text: str) -> str:
        input_table: Table = json.loads(input_text)
        input_small_table = []
        for i, row in enumerate(input_table):
            if i == 0 or i % 2:  # 偶数行是受试物信息，第 0 行是表头，都需要保留
                input_small_table.append(f'| {row[0].strip()} |')
        input_text = '\n'.join(input_small_table)
        return await super().extract(input_text)

    @classmethod
    def parse_small_table(cls, table_text: str, solvent: str) -> SmallMCTable:
        """解析小表，返回成列表的形式"""
        values: list[SmallMCTableRow] = []
        small_table: SmallMCTable = []
        lines = table_text.split('\n')
        for line in lines[1:]:  # 后面是数值部分
            parts = line.strip('|').split('|')
            if len(parts) != 2:
                raise ValueError(f'small table column length is not 2: {line}')
            part0 = parts[0].strip()
            if part0 == solvent:
                value0 = '溶媒'
            else:
                value0 = part0
            values.append((value0, parts[1].strip()))

        small_table.extend(values)
        return small_table

    @classmethod
    def generate_medium_table(cls, header_text: str, small_table: SmallMCTable, table_text: str) -> MediumMCTable:
        """生成中表，在最左方拼接性别，在右方拼接数据"""
        if not table_text:
            return []

        src_table: Table = json.loads(table_text)
        if len(src_table) <= 2:  # 第 0 行是表头，第 1 行是组名，第 2 行是数据，因此至少有 3 行才能构成有效的表格
            return []
        src_table = src_table[1:]  # 去掉表头

        if '雄性' in header_text:  # 根据数据切片的表头来获取性别
            gender = '雄性'
        elif '雌性' in header_text:
            gender = '雌性'
        else:
            gender = ''

        medium_table: MediumMCTable = []

        for i, small_row in enumerate(small_table):
            medium_row = [gender, *small_row]  # 第 0 列是性别，第 1、2 列是小表的受试物和剂量
            # 其余数据拿 src_table 的后 4 列
            src_row = src_table[i * 2 + 1]  # 每个组会占 2 行，且第 0 行是表头
            for value in src_row[-4:]:
                if value:
                    value = value.strip()
                    if value:
                        value = fix_plus_minus(value)
                medium_row.append(value)
            row = tuple(medium_row)
            assert len(row) == 7
            medium_table.append(row)

        return medium_table

    @classmethod
    def merge_tables(
        cls,
        medium_tables: list[MediumMCTable],
    ) -> LargeMCTable:
        # 大表的表头是固定的
        large_table = [
            (
                '性别',
                '受试物',
                '剂量（mg/kg）',
                'PCE百分率（平均值 ± 标准误，%）',
                'PCE计数',
                '观察到的MN-PCE总数',
                'MN-PCE频率 (平均值 ± 标准误，%)',
            )
        ]
        for medium_table in medium_tables:  # 表格内容合并
            large_table.extend(medium_table)
        return large_table


class MCTableFooter(AzureOpenAIExtractor):
    SYSTEM_PROMPT = '''- 角色: 数据表格处理专家
- 目标: 在给定的表格当中提取出尾注部分
- 输出格式: 清晰的文本输出,直接标注提取的"尾注"。若"尾注"不止一条,需分别列出,换行输出。若没有尾注返回空
- 工作流程:
  1. 阅读并理解医学表格。
  2. 准确识别并提取你认为是"尾注"的内容。
- 示例一:
  - 输入:
组 1含20%H2O2+30%AU CL4的60% β-XX医药酒精溶液
组 2 (新药380, 10000 mg/kg/day)
组 10 (新药380, 20000 mg/kg/day)
组 20 (聚乙二醇水合物, 300000 mg/kg)
a: 组6 为Day 3 – Day 2. -: 不适用
#与平行对照品比有显著差异, Dunnett, P < 0.05
##与平行对照品比有显著差异, Dunnett, P < 0.01
程度: N=正常，X = 存在，- = 不存在；
***与CP阳性对照比较有差异, 线性回归分析(Linear regresssion)，, P<0.95
**与其它对照组的差别不大, 随机森林（random forest）, P>0.55
  - 输出:
    <ANSWER>a: 组6 为Day 3 – Day 2. -: 不适用
#与平行对照品比有显著差异, Dunnett, P < 0.05
##与平行对照品比有显著差异, Dunnett, P < 0.01
程度: N=正常，X = 存在，- = 不存在；
***与CP阳性对照比较有差异, 线性回归分析(Linear regresssion)，, P<0.95
**与其它对照组的差别不大, 随机森林（random forest）, P>0.55</ANSWER>
- 示例二:
  - 输入:
组 1含20%H2O2+30%AU CL4的60% β-XX医药酒精溶液
组 2 (新药380, 10000 mg/kg/day)
组 10 (新药380, 20000 mg/kg/day)
  - 输出:
    <ANSWER></ANSWER>
'''

    @classmethod
    async def extract(cls, input_text: str, origin_footer_text: str) -> str:
        text_list = json.loads(input_text)
        temp_list = []
        # 放入表格的内容
        for i, row in enumerate(text_list):
            if i % 2 != 0 or i == 0:
                temp = row[0].strip()
                temp_list.append(temp)
        # 放入尾注的内容
        footer_text_list = json.loads(origin_footer_text)
        for i, row in enumerate(footer_text_list):
            temp = row.strip()
            temp_list.append(temp)
        input_text = '\n'.join(temp_list)
        return await super().extract(input_text)

    @classmethod
    def format(cls, result: str) -> str:
        formatted_result = super().format(result)
        lines = formatted_result.split('|')
        if formatted_result == 'error':
            return ''
        else:
            return '\n'.join(line.strip() for line in lines)  # 去掉每行左右边的空格

    @classmethod
    def merge_footers(
        cls,
        footer_list: list[str],
    ) -> str:
        seen = set()  # 用来跟踪已经处理过的尾注
        ordered_footers = []  # 用来保留尾注的顺序

        for footer in footer_list:
            parts = footer.split('\n')
            for part in parts:
                stripped_part = part.strip()
                if stripped_part and stripped_part not in seen:  # 确保去重且不为空
                    ordered_footers.append(stripped_part)  # 添加到顺序列表
                    seen.add(stripped_part)  # 标记为已处理
        # 检查ordered_footers 是否为空
        if not ordered_footers:
            return ''

        return '\n'.join(ordered_footers)
