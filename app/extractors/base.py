from abc import ABC
from re import DOTAL<PERSON>, MULTILINE, compile
from typing import ClassVar, Protocol, Type

from app.schemas.completion_result import CompletionResult
from app.utils.azure_openai import AzureOpenAI
from app.utils.exception import ExtractError
from app.utils.volcengine_deepseek import VolcEngineDeepSeekR1, VolcEngineDeepSeekV3

ANSWER_PATTERN = compile(r'<ANSWER>(.*?)</ANSWER>', DOTALL)
LINES_PATTERN = compile(r'^\s+|\s+$', MULTILINE)


def extract_answer(text: str) -> str:
    if text:
        match = ANSWER_PATTERN.search(text)
        if match:
            return match.group(1)
    raise ExtractError(f'extract answer failed: {text}')


def trim_lines(text: str) -> str:
    return LINES_PATTERN.sub('', text)


class ModelProtocol(Protocol):
    async def chat(self, system_message: str = '', user_message: str = '', **kwargs) -> CompletionResult: ...


class Extractor(ABC):
    MODEL_CLASS: ClassVar[Type[ModelProtocol]]

    SYSTEM_PROMPT: ClassVar[str] = ''
    USER_PROMPT: ClassVar[str] = ''

    @classmethod
    async def extract(cls, input_text: str) -> str:
        result = await cls.MODEL_CLASS().chat(cls.SYSTEM_PROMPT, input_text)
        return cls.format(result.content)

    @classmethod
    def format(cls, result: str) -> str:
        return extract_answer(result)

    @classmethod
    def format_lines(cls, result: str) -> str:
        answer = extract_answer(result)
        if answer:
            return trim_lines(answer)
        return answer


class AzureOpenAIExtractor(Extractor):
    MODEL_CLASS = AzureOpenAI


class VolcEngineDeepSeekR1Extractor(Extractor):
    MODEL_CLASS = VolcEngineDeepSeekR1


class VolcEngineDeepSeekV3Extractor(Extractor):
    MODEL_CLASS = VolcEngineDeepSeekV3
