from enum import Enum
from pydantic import BaseModel
from app.schemas.doc_base_field import TrialBaseFieldInfo


class GenerateBaseData(BaseModel):
    single_data: list[TrialBaseFieldInfo] = []
    repetition_data: list[TrialBaseFieldInfo] = []
    back_mutation_data: TrialBaseFieldInfo | None = None
    chromosome_data: TrialBaseFieldInfo | None = None
    micronucleus_data: TrialBaseFieldInfo | None = None

class CellHorizontallyAlign(Enum):
    LEFT = 'left'
    CENTER = 'center'
    RIGHT = 'right'

class CellVerticalAlign(Enum):
    TOP = 'top'
    CENTER = 'center'
    BOTTOM = 'bottom'
    BASELINE = 'baseline'

class GenerateContent(BaseModel):
    key: str = ''
    title: str = ''
    value: str = ''
    sub_key: str | None = None
    sup_key: str | None = None
    font_size: int = 21
    font_color: str = ''
    is_title_bold: bool = False
    is_value_bold: bool = False

class GenerateCol(BaseModel):
    content_line: list[GenerateContent] = []
    is_title_bold: bool = False
    is_value_bold: bool = False
    align_vertical: CellVerticalAlign = CellVerticalAlign.CENTER
    align_horizontally: CellHorizontallyAlign = CellHorizontallyAlign.LEFT
    row_span: bool = False # Word标准
    row_span_first_row: bool = False # Word标准
    row_span_count: int = 1 # 前端标准
    col_span: bool = False  # Word标准
    col_span_count: int = 1 # Word标准 & 前端标准
    is_error: bool = False

class GenerateRow(BaseModel):
    cols: list[GenerateCol] = []
    is_bold: bool = False

class GenerateTable(BaseModel):
    rows: list[GenerateRow] = []
    pass_before_row: int = -1
    footer: str = ''
    is_error: bool = False