from enum import Enum
from typing import Literal

from pydantic import BaseModel


class DocPageDataType(Enum):
    PARAGRAPH = 1
    TABLE = 2


class DocVMerge(Enum):
    NONE = 1
    RESTART = 2


class DocChunkStatus(Enum):
    NOT_STARTED = 0
    CHUNKING = 1
    COMPLETED = 2
    FAILED = 3


# RUN
class RunStyleData(BaseModel):
    font_size: float
    font_bold: bool
    font_italic: bool
    font_color: str
    font_underline: bool
    font_strike: bool
    background_color: str


class RunData(BaseModel):
    text: str
    text_en: str
    text_zh: str
    text_es: str
    style: RunStyleData


# Paragraph
class ParagraphData(BaseModel):
    id: str
    run: list[RunData]


# Table
class TableCellData(BaseModel):
    id: str
    is_head: Literal[1, 0]
    row_span: int
    col_span: int
    v_merge: DocVMerge | None = None
    paragraphs: list[ParagraphData]


class TableRowData(BaseModel):
    id: str
    cells: list[TableCellData]


class TableData(BaseModel):
    id: str
    rows: list[TableRowData]


# Page Data
class PageData(BaseModel):
    type: DocPageDataType
    data: ParagraphData | TableData | None
