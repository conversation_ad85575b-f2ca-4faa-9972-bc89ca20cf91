from enum import Enum

from pydantic import BaseModel, Field

from app.schemas.doc_generate import CellHorizontallyAlign, CellVerticalAlign


class TableKey(Enum):
    TABLE_2671 = 'TABLE_2671'
    TABLE_2672 = 'TABLE_2672'
    TABLE_2673 = 'TABLE_2673'
    TABLE_2675 = 'TABLE_2675'
    TABLE_2676 = 'TABLE_2676'
    TABLE_2677 = 'TABLE_2677'
    TABLE_2678 = 'TABLE_2678'
    TABLE_2679 = 'TABLE_2679'


class BuilderCol(BaseModel):
    title: str = ''
    value: str = ''
    font_color: str = ''
    align_vertical: CellVerticalAlign = CellVerticalAlign.CENTER
    align_horizontally: CellHorizontallyAlign = CellHorizontallyAlign.CENTER
    row_span: bool = False  # Word标准
    row_span_first_row: bool = False  # Word标准
    row_span_count: int = 1  # 前端标准
    col_span: bool = False  # Word标准
    col_span_count: int = 1  # Word标准 & 前端标准
    is_error: bool = False


class BuilderTableRow(BaseModel):
    cols: list[BuilderCol] = []
    is_bold: bool = False


class BuilderTable(BaseModel):
    table_key: TableKey
    rows: list[BuilderTableRow] = []
    pass_before_row: int = -1
    header_title: str = ''
    left_fixed_header_title: str = ''
    subheader_title: str = ''
    left_fixed_subheader_title: str = ''
    test_product: str = ''
    trial_number: str = ''
    footer: str = ''
    is_error: bool = False
    is_empty: bool = False


class BuilderChapter(BaseModel):
    table_key: TableKey
    name: str


class BuilderDetail(BaseModel):
    id: int = Field(ge=1)
    project_name: str
    test_product_list: list[str] = []
    applicant_list: list[str] = []
    chapter_list: list[BuilderChapter]
    table_list: list[BuilderTable]


class BuilderSaveDetail(BaseModel):
    id: int = Field(ge=1)
    test_product_list: list[str] = []
    applicant_list: list[str] = []
    table_list: list[BuilderTable]


class BuilderExportDetail(BaseModel):
    id: int = Field(ge=1)
    test_product_list: list[str] = []
    applicant_list: list[str] = []
    table_list: list[BuilderTable] = []
