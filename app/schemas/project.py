from datetime import datetime
from enum import Enum, IntEnum
from typing import List, Sequence

from fastapi import Query
from pydantic import BaseModel, Field


# 用于分页的响应模型
class ProjectResponse(BaseModel):
    """
    项目信息 Schema
    """

    id: int
    project_name: str | None = None
    template: str | None = None
    version: str | None = None
    status: int
    creator_id: int
    is_delete: int
    files: str
    creator_name: str
    create_time: datetime
    update_time: datetime


# 用于查询请求的分页参数
class ProjectListRequest(BaseModel):
    page: int = Query(1, ge=1)
    page_size: int = Query(10, ge=1, le=100)
    keyword: str | None = None


class ProjectListData(BaseModel):
    """
    项目信息 Schema
    """

    id: int
    project_name: str | None = None
    template: str | None = None
    version: str | None = None
    status: int
    creator_name: str
    create_time: datetime
    update_time: datetime


class ProjectListResponse(BaseModel):
    total: int
    projects: Sequence[ProjectListData]


class TrialData(BaseModel):
    trial_main_type: int  # 试验主类型
    trial_subtype: int | None = None  # 试验子类型
    trial_title: str  # 试验标题
    trial_institution: str  # 试验机构
    trial_number: str  # 试验编号
    species: str  # 种属
    solvent_and_dosage_form: str  # 溶媒与剂型
    glp_compliance: bool  # GLP依从性
    administration_method: str  # 给药方法
    administration_unit: str  # 给药计量单位
    dosing_regimen: str  # 给药周期
    administration_dosage: str  # 给药剂量
    test_product: str  # 受试物
    applicant: str  # 申请人
    doc_file_path: str  # 文档地址


class ProjectFileItem(BaseModel):
    id: str  # 文件id
    type: int  # 文件类型
    file_name: str  # 文件名
    doc_id: int  # doc id
    isKeyTrial: bool | None = None  # 是否关键试验
    isValid: bool | None = None  # 是否合规


class ProjectFileData(BaseModel):
    singleDose: List[ProjectFileItem]
    repeatedDose: List[ProjectFileItem]
    repeatedDoseNonKey: List[ProjectFileItem]
    mutagenicity: List[ProjectFileItem]
    chromosome: List[ProjectFileItem]
    micronucleus: List[ProjectFileItem]


class ProjectSaveRequest(BaseModel):
    project_id: int | None = Field(None, ge=1)
    project_name: str | None = None
    template: str | None = None
    version: str | None = None
    is_temp: bool = False
    files: ProjectFileData


class ProjectFileResponse(BaseModel):
    doc_id: int


class ProjectCreateRequest(BaseModel):
    project_id: int | None = Field(None, ge=1)
    project_name: str
    template: str
    version: str
    files: ProjectFileData


class ProjectStopGenerateRequest(BaseModel):
    project_id: int = Field(ge=1)


class ProjectFileValidResponse(BaseModel):
    is_valid: bool
    error_msg: str | None = None


class ProjectStatus(IntEnum):
    NOT_READY = 0
    READY = 1
    GENERATING = 2
    GENERATED = 3
    FAILED = 4


class ProductDoucumetProduceStatus(IntEnum):
    NOT_READY = 1
    GENERATING = 2
    GENERATED = 3
    FAILED = 4
    CANCEL = 5


class ProductDoucumetStep(IntEnum):
    FIRST_STEP = 1
    SECOND_STEP = 2


class ProductDoucumetProduceType(Enum):
    PROTOCOL_SUMMARY = 'protocol_summary'
    PROTOCOL_PROGRAMME = 'protocol_programme'
    ICF = 'icf'
    IND = 'ind'
    SKD = 'skd'
    IND267 = 'ind267'


class ProductDoucumetType(IntEnum):
    INFORMED_CONSENT_FORM = 1
    TRIAL_PROTOCOL = 2
    INVESTIGATIONAL_NEW_DRUG = 3
    SIMILAR_DRUGS = 4
    IND267 = 5


class ProductDocumentProduceMode(IntEnum):
    SINGLE = 1
    MULTI = 2  # 固定多个


class ProjectProgressResponse(BaseModel):
    progress: float
    status: int
