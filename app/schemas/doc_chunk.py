from pydantic import BaseModel

from .table import Table, TableRows, Tables


class ChunkTableData(BaseModel):
    # 表头文本
    header: list[str] = []
    # 表格内容
    content: str = ''
    # 表格数组数据
    content_array: TableRows = []
    # 尾注文本
    footer: list[str] = []


class ChunkHeadlineData(BaseModel):
    # 唯一ID
    uuid: str = ''
    # 父亲ID
    parent_uuid: str = ''
    # 章节名称
    name: str
    # 文章内容
    article: list[str] | None = None
    # 表格数据
    tables: list[ChunkTableData] = []
    # 视窗层级，用于指定标题的层级，可选字段，命名规范遵循word规范
    outline_lvl: str | None = None


class ChunkCoverData(BaseModel):
    paragraph_list: list[str] = []
    tables_md: Table = []
    tables: Tables = []


# 标准表头段落判断条件之一：包含关键字 表
HEADLINE_TEXT_SIGN = '表'
