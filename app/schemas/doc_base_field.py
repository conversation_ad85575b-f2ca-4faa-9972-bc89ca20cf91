from enum import Enum
from typing import Dict
from pydantic import BaseModel


class TrialMainType(Enum):
    SINGLE_DOSE = 1
    REPEATED_DOSE = 2
    GENOTOXICITY = 3


class TrialSubType(Enum):
    SINGLE_DOSE = 1
    REPEATED_DOSE_SPECIFICITY = 2
    REPEATED_DOSE = 3
    AMES_TEST = 4
    CHROMOSOME_ABERRATION_TEST = 5
    MICRONUCLEUS_TEST = 6


TRIAL_MAIN_TYPE_DATA: Dict[TrialMainType, str] = {
    TrialMainType.SINGLE_DOSE: "单次给药毒性",
    TrialMainType.REPEATED_DOSE: "重复给药毒性",
    TrialMainType.GENOTOXICITY: "遗传毒性",
}

ADMINISTRATION_UNIT_DATA: Dict[TrialSubType, str] = {
    TrialSubType.SINGLE_DOSE: "mg/kg",
    TrialSubType.REPEATED_DOSE_SPECIFICITY: "mg/kg/day",
    TrialSubType.REPEATED_DOSE: "mg/kg/day",
    TrialSubType.AMES_TEST: "µg/皿",
    TrialSubType.CHROMOSOME_ABERRATION_TEST: "μg/mL",
    TrialSubType.MICRONUCLEUS_TEST: "mg/kg",
}

TRIAL_SUB_TYPE_DATA: Dict[TrialSubType, str] = {
    TrialSubType.SINGLE_DOSE: "-",
    TrialSubType.REPEATED_DOSE_SPECIFICITY: "关键试验",
    TrialSubType.REPEATED_DOSE: "非关键试验",
    TrialSubType.AMES_TEST: "回复突变试验",
    TrialSubType.CHROMOSOME_ABERRATION_TEST: "染色体试验",
    TrialSubType.MICRONUCLEUS_TEST: "微核试验",
}


class TrialBaseFieldInfo(BaseModel):
    id: int = 0
    trial_title: str = ""
    trial_institution: str = ""
    trial_number: str = ""
    species: str = ""
    solvent_and_dosage_form: str = ""
    glp_compliance: bool = False
    trial_main_type: int = 0
    trial_subtype: int = 0
    administration_method: str = ""
    administration_unit: str = ""
    dosing_regimen: str = ""
    administration_dosage: str = ""
    test_product: str = ""
    applicant: str = ""


class TrialResultTask(BaseModel):
    task: str = ""
    value: str = ""
