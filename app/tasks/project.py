import json
import logging

from sqlmodel import col
from taskiq import AsyncTaskiqTask

from app.clients.mysql import get_session
from app.models import Values
from app.models.doc import Doc
from app.models.generated_field import GeneratedField
from app.models.project import Project
from app.schemas.doc_base_field import TrialSubType
from app.schemas.project import ProjectStatus
from app.task import broker

from .check import check_project
from .extractor.base_field import get_all_base_fields_for_doc
from .extractor.report2 import get_all_fields_of_report2_for_doc
from .extractor.report3 import get_all_fields_of_report3_for_project
from .extractor.report5_6 import (
    get_all_fields_of_report5_for_project,
    get_all_fields_of_report6_for_project,
)
from .extractor.report7 import get_all_fields_of_report7_for_project
from .extractor.report8 import get_all_fields_of_report8_for_project
from .extractor.report8_2 import get_all_fields_of_report8_2_for_project
from .extractor.report9 import get_all_fields_of_report9_for_project


async def finish_project(project_id: int) -> None:
    async with get_session() as mysql:
        doc_ids = await Doc.get_by_project(mysql, project_id, columns=col(Doc.id))
        for doc_id in doc_ids:
            assert isinstance(doc_id, int)
            doc_data = await GeneratedField.get_all_by_doc_id(doc_id)
            if doc_data:
                await Doc.update_by_id(mysql, doc_id, {Doc.data.name: json.dumps(doc_data, ensure_ascii=False)})

        project_status = await Project.get_by_id(mysql, project_id, col(Project.status), for_update=True)
        if project_status == ProjectStatus.GENERATING:  # 判断状态是生成中时才更新
            values: Values = {
                Project.status.name: ProjectStatus.GENERATED,
            }
            project_data = await GeneratedField.get_all_by_project_id(project_id)
            if project_data:
                values[Project.data.name] = json.dumps(project_data, ensure_ascii=False)
            await Project.update_by_id(mysql, project_id, values)
        await mysql.commit()

    await GeneratedField.delete(project_id, doc_ids)


@broker.task
@check_project('')
async def get_all_fields_for_project(project_id: int) -> None:
    async with get_session() as mysql:
        docs = await Doc.get_by_project(mysql, project_id, columns=(Doc.id, Doc.trial_subtype))
        if not docs:
            logging.error(f'project {project_id} has no docs')
            await Project.update_by_id(mysql, project_id, {Project.status.name: ProjectStatus.FAILED})
            await mysql.commit()
            return

    logging.info('get_all_fields_for_project %d started', project_id)

    # 先获取基础字段
    tasks: list[AsyncTaskiqTask] = []
    for doc in docs:
        tasks.append(await get_all_base_fields_for_doc.kiq(project_id, doc.id, TrialSubType(doc.trial_subtype)))
    for task in tasks:
        try:
            await task.wait_result()
        except Exception:
            logging.exception('task failed')

    # 再获取其他字段
    tasks = []
    for doc in docs:
        tasks.append(await get_all_fields_of_report2_for_doc.kiq(project_id, doc.id, TrialSubType(doc.trial_subtype)))
    tasks.append(await get_all_fields_of_report3_for_project.kiq(project_id))
    tasks.append(await get_all_fields_of_report5_for_project.kiq(project_id))
    tasks.append(await get_all_fields_of_report6_for_project.kiq(project_id))
    tasks.append(await get_all_fields_of_report7_for_project.kiq(project_id))
    tasks.append(await get_all_fields_of_report8_for_project.kiq(project_id))
    tasks.append(await get_all_fields_of_report8_2_for_project.kiq(project_id))
    tasks.append(await get_all_fields_of_report9_for_project.kiq(project_id))
    for task in tasks:
        try:
            await task.wait_result()
        except Exception:
            logging.exception('task failed')

    await finish_project(project_id)
    logging.info('get_all_fields_for_project %d finished', project_id)
