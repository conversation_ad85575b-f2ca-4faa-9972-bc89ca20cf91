import asyncio
import io
import json
import logging
import time
from typing import Any

from docx import Document
from sqlmodel import col

from app.clients.mysql import get_session
from app.models.doc import Doc
from app.models.doc_chunk import DocChunk as DocChunkModels
from app.models.project import Project
from app.models.table_chunk import TableChunk
from app.schemas.doc import DocChunkStatus
from app.schemas.project import ProjectStatus
from app.schemas.taskiq_result import TaskIQResult
from app.task import broker, broker_chunk
from app.tasks.project import get_all_fields_for_project
from app.utils.doc.doc_chunk import chunk_process_document
from app.utils.download import Download
from app.utils.file_to_stream import file_to_stream
from app.utils.uuid import generate_uuid

CHECK_INTERVAL: int = 4


async def set_result(doc_id: int, result: TaskIQResult, start_time: float) -> None:
    """
    异步函数，用于将任务结果存储到结果后端。
    参数:
    - doc_id: 文档ID，用于标识任务的整数。
    - result: TaskIQResult对象，包含任务的执行结果。
    - start_time: 任务开始执行的时间戳（float）。
    返回值:
    无返回值。
    功能描述:
    本函数负责将任务的执行结果存储到结果后端。首先，它会生成一个唯一的Redis结果键，
    然后计算任务的执行时间并将其设置在结果对象上。接着，获取消息代理的分块结果后端，
    并使用它来存储结果对象。最后，打印出生成的Redis结果键以便于调试或日志记录。
    """
    redis_result_key: str = f'chunk_doc:chunk_result_{doc_id}_{generate_uuid()}'
    result.execution_time = time.time() - start_time
    result_backend = broker_chunk.result_backend

    await result_backend.set_result(redis_result_key, result)


@broker_chunk.task(retry_on_error=True)
async def doc_chunk_task(doc_id: int, file_url: str = ''):
    """
    文档切片任务异步函数
    本函数通过接收文档ID，从数据库中获取文档信息，然后对文档进行切片处理，
    并将切片结果保存到数据库中。如果文档ID无效或文档未找到，则返回相应的错误信息。
    参数:
    - doc_id (int): 文档ID，用于标识需要进行切片处理的文档
    - file_url (str): 使用地址读取文件
    返回:
    无返回值，但会通过broker_chunk.task异步任务队列执行
    """
    start_time = time.time()

    # 规避异常情况
    if not doc_id:
        result = TaskIQResult(is_err=True, return_value={'msg': '缺少doc_id'})
        await set_result(doc_id, result, start_time)
        return False

    async with get_session() as session:
        doc = await Doc.get_by_id(
            session,
            doc_id,
            columns=[Doc.project_id, Doc.chunk_status, Doc.doc_file_path, Doc.trial_subtype],
        )
        project_id: int = -1
        chunk_status: int = -1
        doc_file_path: str = ''

        if doc:
            project_id, chunk_status, doc_file_path, trial_subtype = doc

        # 规避异常情况
        if not doc or not doc_file_path:
            result = TaskIQResult(is_err=True, return_value={'msg': 'doc未找到'})
            await set_result(doc_id, result, start_time)

            await Doc.update_by_id(session, doc_id, {Doc.chunk_status.name: DocChunkStatus.FAILED.value})
            await session.commit()
            return False

        # 已完成的不需要重复切片
        if chunk_status == DocChunkStatus.COMPLETED.value:
            result = TaskIQResult(is_err=True, return_value={'msg': '文档已经切片完成，无需重复执行切片'})
            await set_result(doc_id, result, start_time)

            return False

        # 规避异常情况 (手动doc重命名为docx的异常)
        if file_url:
            file_bytes = await Download.download_file_bytes(file_url)
            if file_bytes is None:
                raise ValueError('文件下载失败')
            file: io.BytesIO = file_bytes
        else:
            file: io.BytesIO = file_to_stream(doc_file_path)
        try:
            Document(file)
        except Exception as e:
            logging.exception('文档读取异常')
            result = TaskIQResult(is_err=True, return_value={'msg': f'文档读取异常: {e}'})
            await set_result(doc_id, result, start_time)
            await Doc.update_by_id(session, doc_id, {Doc.chunk_status.name: DocChunkStatus.FAILED.value})
            await session.commit()
            return False

        # 切片中
        await Doc.update_by_id(session, doc_id, {Doc.chunk_status.name: DocChunkStatus.CHUNKING.value})
        await session.commit()

    failed: bool = False
    async with get_session() as session:
        try:
            file_real_path: str = file_url if file_url else doc_file_path
            chunk_data_list, cover_data, table_of_contents = chunk_process_document(file_real_path, trial_subtype)

            ids: dict[str, int] = {}

            # 写入目录
            await Doc.update_by_id(
                session,
                doc_id,
                {
                    Doc.table_of_contents.name: json.dumps(table_of_contents, ensure_ascii=False),
                    Doc.cover_paragraphs.name: json.dumps(cover_data.paragraph_list, ensure_ascii=False),
                    Doc.cover_tables.name: json.dumps(cover_data.tables, ensure_ascii=False),
                    Doc.cover_tables_md.name: json.dumps(cover_data.tables_md, ensure_ascii=False),
                },
            )

            for chunk in chunk_data_list:
                # 写入 Doc Chunk
                new_doc_chunk = DocChunkModels(
                    parent_id=0,
                    project_id=project_id,
                    doc_id=doc_id,
                    title=chunk.name,
                    paragraph=json.dumps(chunk.article, ensure_ascii=False),
                )
                session.add(new_doc_chunk)
                await session.flush()

                doc_chunk_id: int = new_doc_chunk.id
                ids[chunk.uuid] = doc_chunk_id

                # 如果没有父亲元素，则设置父亲ID为0
                if chunk.uuid == chunk.parent_uuid:
                    new_doc_chunk.parent_id = 0
                else:
                    # 查找父亲元素
                    parent_chunk = None
                    for c in chunk_data_list:
                        if c.uuid == chunk.parent_uuid:
                            parent_chunk = c
                            break
                    # 设置父亲ID, 如果没有找到父亲元素，则设置父亲ID为0
                    if parent_chunk is not None and parent_chunk.uuid in ids:
                        new_doc_chunk.parent_id = ids[parent_chunk.uuid]
                    else:
                        new_doc_chunk.parent_id = doc_chunk_id
                    # 写入 Table Chunk
                new_table_chunk_list: list[dict[str, Any]] = []
                for table in chunk.tables:
                    new_table_chunk_list.append(
                        {
                            'project_id': project_id,
                            'doc_id': doc_id,
                            'doc_chunk_id': doc_chunk_id,
                            'header': json.dumps(table.header, ensure_ascii=False),
                            'footer': json.dumps(table.footer, ensure_ascii=False),
                            'content_md': table.content,
                            'content_json': json.dumps(table.content_array, ensure_ascii=False),
                            'title': chunk.name,
                            'table_title': table.header[0] if table.header else '',
                        }
                    )
                await TableChunk.batch_insert(session, new_table_chunk_list)

        except Exception as e:
            logging.exception('ID为 %d 的切片过程异常', doc_id)

            failed = True
            result = TaskIQResult(is_err=True, return_value={'msg': str(e)})
            await set_result(project_id, result, start_time)
            # 改成异常状态
            await Doc.update_by_id(session, doc_id, {Doc.chunk_status.name: DocChunkStatus.FAILED.value})
            await session.commit()

        if not failed:
            # 切片完成
            await Doc.update_by_id(session, doc_id, {Doc.chunk_status.name: DocChunkStatus.COMPLETED.value})

            await session.commit()

            chunk_headline_dict_list = [data.model_dump() for data in chunk_data_list]
            result = TaskIQResult(is_err=False, return_value={'doc_chunk_data': chunk_headline_dict_list})
            await set_result(doc_id, result, start_time)

        return not failed


@broker_chunk.task
async def doc_start_generating_task(project_id: int) -> None:
    """
    异步任务函数，用于启动文档生成任务。
    参数:
    project_id (int): 项目ID，用于标识和操作特定项目。
    返回:
    None
    """
    start_time = time.time()

    async with get_session() as session:
        # 检查有没有停止生成信号
        status = await Project.get_by_id(session, project_id, columns=col(Project.status))
        if status is None:
            logging.error('项目不存在，任务无法执行')
            result = TaskIQResult(
                is_err=True, return_value={'msg': '项目不存在，任务无法执行', 'project_id': project_id}
            )
            await set_result(project_id, result, start_time)
            return

        # 因Mysql连接失败导致的漏切文档重新切片
        doc_list = await Doc.get_by_project(session, project_id, columns=[col(Doc.id), col(Doc.chunk_status)])
        for doc_id, chunk_status in doc_list:
            if chunk_status == DocChunkStatus.NOT_STARTED.value:
                await doc_chunk_task.kicker().with_task_id(f'chunk_doc:doc_{doc_id}_{generate_uuid()}').kiq(doc_id)

    try:
        while True:
            async with get_session() as session:
                # 若时间超过5分钟，则判定为异常状态，并立即终止轮询
                if time.time() - start_time > 5 * 60:
                    logging.error('任务执行超时，已中止')
                    result = TaskIQResult(
                        is_err=True, return_value={'msg': '任务执行超时，已中止', 'project_id': project_id}
                    )
                    await Project.update_by_id(session, project_id, {Project.status.name: ProjectStatus.FAILED})
                    await set_result(project_id, result, start_time)
                    return

                # 项目当前未处于生成状态
                if status != ProjectStatus.GENERATING:
                    logging.error('项目当前未处于生成状态，任务已中止')
                    result = TaskIQResult(
                        is_err=False,
                        return_value={'msg': '项目当前未处于生成状态，任务已中止', 'project_id': project_id},
                    )
                    await Project.update_by_id(session, project_id, {Project.status.name: ProjectStatus.FAILED})
                    await set_result(project_id, result, start_time)
                    return

                doc_status_list = await Doc.get_by_project(session, project_id, columns=col(Doc.chunk_status))

                # 当前启用的文档数量为0
                if not doc_status_list or len(doc_status_list) == 0:
                    logging.error('因启用文档数量为0，任务已中止')
                    result = TaskIQResult(
                        is_err=False, return_value={'msg': '因启用文档数量为0，任务已中止', 'project_id': project_id}
                    )
                    await Project.update_by_id(session, project_id, {Project.status.name: ProjectStatus.FAILED})
                    await set_result(project_id, result, start_time)
                    return

                # 核实文档数量是否已达到要求（不包括切片失败的文档）
                check_number: bool = check_number_of_documents(doc_status_list)
                if not check_number:
                    logging.error('由于切片中非失败文档数量未达到要求，等待中')
                    result = TaskIQResult(
                        is_err=False,
                        return_value={'msg': '由于切片中非失败文档数量未达到要求，等待中', 'project_id': project_id},
                    )
                    await Project.update_by_id(session, project_id, {Project.status.name: ProjectStatus.FAILED})
                    await set_result(project_id, result, start_time)
                    continue

                # 确认除切片失败文档外，其余文档均已成功完成切片
                completed = check_document_completion(doc_status_list)
                if completed:
                    logging.info('文档切片完成，已启动生成任务')
                    await (
                        get_all_fields_for_project.kicker()
                        .with_task_id(f'project:get_all_fields_{project_id}')
                        .with_broker(broker=broker)
                        .kiq(project_id)
                    )
                    result = TaskIQResult(
                        is_err=False, return_value={'msg': '文档切片完成，已启动生成任务', 'project_id': project_id}
                    )
                    await set_result(project_id, result, start_time)
                    return

            await asyncio.sleep(CHECK_INTERVAL)

    except Exception as e:
        logging.exception('项目ID为 %d 的生成前的等待过程出现异常', project_id)
        result = TaskIQResult(is_err=True, return_value={'msg': str(e)})
        await set_result(project_id, result, start_time)


def check_number_of_documents(doc_status_list) -> bool:
    """
    检查文档数量是否满足要求。
    参数:
    doc_status_list: 包含文档状态的列表，每个元素是一个包含文档状态代码和文档类型代码的元组。
    返回:
    如果每种必要的文档类型(不判断遗传毒性)都至少有一个，则返回True，否则返回False。
    """
    non_failed_doc_count: int = 0

    for doc_status in doc_status_list:
        chunk_status = doc_status
        # 排除切片失败的文档
        if chunk_status != DocChunkStatus.FAILED.value:
            non_failed_doc_count += 1

    return non_failed_doc_count > 0


def check_document_completion(doc_status_list) -> bool:
    """
    检查文档处理的完成情况。
    本函数通过检查文档状态列表来确定文档处理是否完成。除了FAILED状态，所有其他状态都必须是COMPLETED。
    参数:
    doc_status_list (list): 包含文档状态的列表。
    返回:
    bool: 如果除了FAILED状态外，所有状态都是COMPLETED，则返回True，否则返回False。
    """
    for doc_status in doc_status_list:
        status: int = doc_status
        if status not in (DocChunkStatus.COMPLETED.value, DocChunkStatus.FAILED.value):
            return False
    return True
