import json
import logging
from functools import wraps
from time import time
from typing import Any, Callable, Protocol

from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import col

from app.clients.mysql import get_session
from app.models.generated_field import GeneratedField
from app.models.project import Project
from app.schemas.project import ProjectStatus
from app.utils.exception import (
    DocError,
    GenerateCancelled,
    GenerateError,
    GenerateFailed,
    MissingProjectError,
)
from app.utils.test import IS_TESTING


class DocTaskHandler(Protocol):
    __name__: str

    async def __call__(
        self, project_id: int, doc_id: int, *args: Any, **kwargs: Any
    ) -> str | tuple | list | None: ...  # 当项目被取消返回 None，有异常情况会抛异常


class ProjectTaskHandler(Protocol):
    __name__: str

    async def __call__(
        self, project_id: int, *args: Any, **kwargs: Any
    ) -> str | tuple | list | None: ...  # 当项目被取消返回 None，有异常情况会抛异常


async def check_project_status(mysql: AsyncSession, key: str, project_id: int, doc_id: int | None = None) -> bool:
    if IS_TESTING:  # 单元测试时不检查项目状态
        return True

    status = await Project.get_by_id(mysql, project_id, columns=col(Project.status))
    if status is None:
        logging.error('project %d not found', project_id)
        if key:
            await GeneratedField.set_for_project_id(project_id, key, MissingProjectError.message())
            if doc_id:
                await GeneratedField.set_for_doc_id(doc_id, key, MissingProjectError.message())
        return False
    if status == ProjectStatus.READY:
        logging.warning('project %d has been cancelled', project_id)
        if key:
            await GeneratedField.set_for_project_id(project_id, key, GenerateCancelled.message())
            if doc_id:
                await GeneratedField.set_for_doc_id(doc_id, key, GenerateCancelled.message())
        return False
    if status != ProjectStatus.GENERATING:
        logging.warning('project %d is not generating', project_id)
        return False
    return True


def check_doc(*field_names: str, field_type: type = str) -> Callable[[DocTaskHandler], DocTaskHandler]:
    """
    field_type: str | list | tuple，是单个字段的类型
    """
    assert field_type in (str, list, tuple)
    first_field_name = field_names[0]

    def wrapper(func: DocTaskHandler) -> DocTaskHandler:
        @wraps(func)
        async def inner(project_id: int, doc_id: int, *args, **kwargs) -> str | tuple | list | None:
            start_time = time()
            for retry_count in range(3):
                try:
                    if not IS_TESTING:  # 单元测试时不读取缓存和检查项目状态
                        if len(field_names) == 1:
                            if first_field_name:
                                field_value = await GeneratedField.get_by_doc_id(doc_id, first_field_name)
                                if field_value is not None and not GenerateError.is_error(field_value):
                                    if field_type is str:
                                        return field_value
                                    else:
                                        field_values = json.loads(field_value)
                                        if field_type is list:
                                            return field_values
                                        else:  # tuple
                                            return tuple(field_values)
                        else:
                            field_values = await GeneratedField.get_multi_by_doc_id(doc_id, *field_names)
                            if field_values:
                                cached = True
                                for field_value in field_values:
                                    if field_value is None or GenerateError.is_error(field_value):
                                        cached = False
                                        break
                                if cached:
                                    if field_type is str:
                                        return field_values
                                    else:
                                        assert isinstance(field_value, str)
                                        field_values = json.loads(field_value)
                                        if field_type is list:
                                            return field_values
                                        else:  # tuple
                                            return tuple(field_values)

                        async with get_session() as mysql:
                            if not await check_project_status(mysql, first_field_name, project_id, doc_id):
                                return None

                    try:
                        result = await func(project_id, doc_id, *args, **kwargs)
                    except GenerateError as e:
                        logging.exception(
                            'project %d doc %d generate %s failed',
                            project_id,
                            doc_id,
                            '+'.join(field_names) or func.__name__,
                        )
                        for field_name in field_names:
                            if field_name:
                                await GeneratedField.set_for_doc_id(doc_id, field_name, e.message())
                        if not isinstance(e, DocError):
                            raise  # 重试
                        return None
                    except Exception:
                        logging.exception(
                            'project %d doc %d generate %s failed',
                            project_id,
                            doc_id,
                            '+'.join(field_names) or func.__name__,
                        )
                        for field_name in field_names:
                            if field_name:
                                await GeneratedField.set_for_doc_id(doc_id, field_name, GenerateFailed.message())
                        raise  # 重试
                    else:
                        if result is None:
                            logging.error(
                                'project %d doc %d generate %s failed',
                                project_id,
                                doc_id,
                                '+'.join(field_names) or func.__name__,
                            )
                            for field_name in field_names:
                                if field_name:
                                    await GeneratedField.set_for_doc_id(doc_id, field_name, GenerateFailed.message())
                            return None
                        else:
                            if len(field_names) == 1:  # 返回值是单个元素，需要转成 tuple 以便后续 zip 遍历
                                field_values = (result,)
                            else:
                                field_values = result
                            for field_name, field_value in zip(field_names, field_values):
                                if field_name:
                                    if field_type is str:
                                        assert isinstance(field_value, str)
                                        await GeneratedField.set_for_doc_id(doc_id, field_name, field_value)
                                    else:  # list or tuple
                                        await GeneratedField.set_for_doc_id(
                                            doc_id, field_name, json.dumps(field_value, ensure_ascii=False)
                                        )
                            return result
                except Exception:
                    if IS_TESTING or retry_count == 2:  # 单元测试时或已满3次不重试
                        logging.error(
                            'project %d doc %d %s failed after 3 retries in %.3f seconds',
                            project_id,
                            doc_id,
                            func.__name__,
                            time() - start_time,
                        )
                        raise
                finally:
                    logging.info(
                        'project %d doc %d %s finished in %.3f seconds',
                        project_id,
                        doc_id,
                        func.__name__,
                        time() - start_time,
                    )

        return inner

    return wrapper


def check_project(field_name: str, field_type: type = str) -> Callable[[ProjectTaskHandler], ProjectTaskHandler]:
    assert field_type in (str, list, tuple)

    def wrapper(func: ProjectTaskHandler) -> ProjectTaskHandler:
        @wraps(func)
        async def inner(project_id: int, *args, **kwargs) -> str | tuple | list | None:
            start_time = time()
            for retry_count in range(3):
                try:
                    if IS_TESTING:  # 单元测试时不读取缓存和检查项目状态
                        if field_name:
                            field_value = await GeneratedField.get_by_project_id(project_id, field_name)
                            if field_value is not None and not GenerateError.is_error(field_value):
                                if field_type is str:
                                    return field_value
                                else:
                                    field_values = json.loads(field_value)
                                    if field_type is list:
                                        return field_values
                                    else:  # tuple
                                        return tuple(field_values)

                        async with get_session() as mysql:
                            if not await check_project_status(mysql, field_name, project_id):
                                return None

                    try:
                        field_value = await func(project_id, *args, **kwargs)
                    except GenerateError as e:
                        logging.exception('project %d generate %s failed', project_id, field_name or func.__name__)
                        if field_name:
                            await GeneratedField.set_for_project_id(project_id, field_name, e.message())
                        if not isinstance(e, DocError):
                            raise  # 重试
                        return None
                    except Exception:
                        logging.exception('project %d generate %s failed', project_id, field_name or func.__name__)
                        if field_name:
                            await GeneratedField.set_for_project_id(project_id, field_name, GenerateFailed.message())
                        raise  # 重试
                    else:
                        if field_value is None:
                            if field_name:
                                logging.error('project %d generate %s failed', project_id, field_name or func.__name__)
                                await GeneratedField.set_for_project_id(
                                    project_id, field_name, GenerateFailed.message()
                                )
                            return None
                        else:
                            if field_name:
                                if field_type is str:
                                    assert isinstance(field_value, str)
                                    await GeneratedField.set_for_project_id(project_id, field_name, field_value)
                                else:  # list or tuple
                                    await GeneratedField.set_for_project_id(
                                        project_id, field_name, json.dumps(field_value, ensure_ascii=False)
                                    )
                            return field_value
                except Exception:
                    if IS_TESTING or retry_count == 2:  # 单元测试时或已满3次不重试
                        logging.error(
                            'project %d %s failed after 3 retries in %.3f seconds',
                            project_id,
                            func.__name__,
                            time() - start_time,
                        )
                        raise
                finally:
                    logging.info(
                        'project %d %s finished in %.3f seconds', project_id, func.__name__, time() - start_time
                    )

        return inner

    return wrapper
