import json
import logging
from asyncio import sleep

from sqlmodel import col
from taskiq import AsyncTaskiqTask

from app.clients.mysql import get_session
from app.extractors.base_field import (
    AdministrationMethod,
    AmesSpecies,
    ChromosomeAberrationAndAmesSolvent,
    ChromosomeAberrationRegimen,
    ChromosomeAberrationSpecies,
    Cover,
    DosageAmes,
    DosageChromosome,
    DosageDoseSpecies,
    DosageMicronucleus,
    DosageSingle,
    MicronucleusRegimen,
    MicronucleusSolvent,
    MicronucleusSpecies,
    RepeatedDoseRegimen,
    SingleAdministrationMethod,
    SingleAndRepeatedDoseSolvent,
    SingleDoseSpecies,
)
from app.models.doc import Doc
from app.models.doc_chunk import DocChunk
from app.models.generated_field import GeneratedField
from app.models.table_chunk import TableChunk
from app.schemas.doc_base_field import TrialSubType
from app.task import broker
from app.utils.bce_rerank import BCERerank
from app.utils.exception import (
    ExtractError,
    GenerateError,
    <PERSON><PERSON><PERSON><PERSON>hunkError,
    <PERSON><PERSON>ieldError,
    MissingTableChunkError,
)

from ..check import check_doc


async def get_cover(doc_id: int) -> str:
    async with get_session() as mysql:
        doc = await Doc.get_by_id(mysql, doc_id, (Doc.cover_paragraphs, Doc.cover_tables_md))

    content = ''
    if doc.cover_paragraphs:
        cover_paragraphs = json.loads(doc.cover_paragraphs)
        content = '\n\n'.join(cover_paragraphs)
    if doc.cover_tables_md:
        cover_tables: list[list[str]] = json.loads(doc.cover_tables_md)
        cover_tables_md = '\n\n'.join(['\n'.join(table) for table in cover_tables])
        if content:
            content = f'{content}\n\n{cover_tables_md}'
        else:
            content = cover_tables_md

    if content:
        return content

    logging.error('cover is missing')
    raise MissingFieldError()


@broker.task
@check_doc(
    GeneratedField.TEST_PRODUCT,
    GeneratedField.APPLICANT,
    GeneratedField.TRIAL_TITLE,
    GeneratedField.TRIAL_INSTITUTION,
    GeneratedField.TRIAL_NUMBER,
)
async def parse_cover(project_id: int, doc_id: int) -> tuple[str, str, str, str, str]:
    cover = await get_cover(doc_id)
    cover = cover.split('\n目录\n')[0]  # 只取目录前的内容
    for i in range(3):
        try:
            return await Cover.extract(cover)
        except Exception:
            logging.exception('parse cover failed: %s', cover)
            if i == 2:  # 重试3次后抛出异常
                raise
            await sleep(1)
    raise ExtractError()


@broker.task
@check_doc(GeneratedField.SPECIES)
async def get_species(project_id: int, doc_id: int, trial_subtype: TrialSubType) -> str | None:
    if trial_subtype in (TrialSubType.SINGLE_DOSE, TrialSubType.REPEATED_DOSE, TrialSubType.REPEATED_DOSE_SPECIFICITY):
        async with get_session() as mysql:
            doc_chunks = await DocChunk.query_v2(
                mysql, doc_id, titles=('动物', '种属', '种系', '品系'), columns=(DocChunk.id, DocChunk.paragraph)
            )

            filtered_doc_chunks = await DocChunk.filter_content(
                mysql,
                doc_id,
                doc_chunks,
                contents=('种属', '品种', '品系', '品类', '种系'),
                columns=(DocChunk.id, DocChunk.paragraph),
            )

            table_chunks = await TableChunk.query_v2(
                mysql,
                doc_id,
                doc_chunk_ids=[doc_chunk.id for doc_chunk in doc_chunks],
                titles='动物',
                columns=(TableChunk.table_title, TableChunk.content_md),
            )

        if filtered_doc_chunks:
            doc_chunks = filtered_doc_chunks

        if doc_chunks:
            doc_content = await BCERerank().most_relatived_doc_chunk('物种的种属与品类', doc_chunks)
        else:
            doc_content = ''

        if table_chunks:
            table_content = await BCERerank().most_relatived_table_chunk('物种的种属与品类', table_chunks)
        else:
            table_content = ''

        if doc_content or table_content:
            content = '\n\n'.join((doc_content, table_content))
            return await SingleDoseSpecies.extract(content)

        logging.error('species doc chunk is missing')
        raise MissingDocChunkError()

    elif trial_subtype == TrialSubType.AMES_TEST:
        async with get_session() as mysql:
            doc_contents = await DocChunk.query_v2(mysql, doc_id, titles='菌株', columns=col(DocChunk.paragraph))
            filtered_doc_contents = await DocChunk.filter_content(
                mysql,
                doc_id,
                doc_contents,
                contents='菌株',
                columns=col(DocChunk.paragraph),
            )
            if filtered_doc_contents:
                doc_contents = filtered_doc_contents

        if doc_contents:
            content = await BCERerank().most_relatived_paragraph('菌种名称及其对应的标识符', doc_contents)
            return await AmesSpecies.extract(content)

        logging.error('species doc chunk is missing')
        raise MissingDocChunkError()

    elif trial_subtype == TrialSubType.CHROMOSOME_ABERRATION_TEST:
        trial_title = await GeneratedField.get_by_doc_id(doc_id, GeneratedField.TRIAL_TITLE)
        if trial_title:
            return await ChromosomeAberrationSpecies.extract(trial_title)

    elif trial_subtype == TrialSubType.MICRONUCLEUS_TEST:
        async with get_session() as mysql:
            paragraphs = await DocChunk.query_v2(
                mysql, doc_id, titles=('动物', '种属', '种系', '品系'), columns=col(DocChunk.paragraph)
            )

        if paragraphs:
            content = await BCERerank().most_relatived_paragraph('物种的种属与品类', paragraphs)
            return await MicronucleusSpecies.extract(content)

        logging.error('species doc chunk is missing')
        raise MissingDocChunkError()

    else:
        logging.error('trial subtype %s is not supported', trial_subtype)
        raise GenerateError(f'trial subtype {trial_subtype} is not supported')


@broker.task
@check_doc(GeneratedField.SOLVENT_AND_DOSAGE_FORM)
async def get_solvent_and_dosage_form(project_id: int, doc_id: int, trial_subtype: TrialSubType) -> str | None:
    if trial_subtype in (TrialSubType.SINGLE_DOSE, TrialSubType.REPEATED_DOSE, TrialSubType.REPEATED_DOSE_SPECIFICITY):
        async with get_session() as mysql:
            doc_chunk_ids = await DocChunk.query_v2(
                mysql,
                doc_id,
                titles=('组别', '试验', '设计'),
                columns=col(DocChunk.id),
            )
            if doc_chunk_ids:
                doc_chunk_ids = await DocChunk.extend_with_sub_ids(mysql, doc_id, doc_chunk_ids)

            table_chunks = await TableChunk.query_v2(
                mysql,
                doc_id,
                doc_chunk_ids=doc_chunk_ids,
                titles=('试验', '实验'),
                contents='溶媒',
                columns=(TableChunk.table_title, TableChunk.content_md, TableChunk.footer),
            )

        if table_chunks:
            contents = []
            for table_chunk in table_chunks:
                contents.append(f'{table_chunk.content_md}\n\n{table_chunk.footer}')
            content = await BCERerank().most_relatived_content('溶媒', contents)
            return await SingleAndRepeatedDoseSolvent.extract(content)

        logging.error('solvent_and_dosage_form table chunk is missing')
        raise MissingTableChunkError()
    elif trial_subtype in (TrialSubType.AMES_TEST, TrialSubType.CHROMOSOME_ABERRATION_TEST):
        async with get_session() as mysql:
            doc_chunk_ids = await DocChunk.query_v2(
                mysql,
                doc_id,
                titles='溶媒',
                columns=col(DocChunk.id),
            )
            if doc_chunk_ids:
                doc_chunk_ids = await DocChunk.extend_with_sub_ids(mysql, doc_id, doc_chunk_ids)

            table_contents = await TableChunk.query_v2(
                mysql,
                doc_id,
                doc_chunk_ids=doc_chunk_ids,
                contents='名称',
                columns=col(TableChunk.content_md),
            )
        if table_contents:
            content = await BCERerank().most_relatived_content('溶媒', table_contents)
            return await ChromosomeAberrationAndAmesSolvent.extract(content)

        logging.error('solvent_and_dosage_form table chunk is missing')
        raise MissingTableChunkError()
    elif trial_subtype == TrialSubType.MICRONUCLEUS_TEST:
        async with get_session() as mysql:
            contents = await DocChunk.query_v2(mysql, doc_id, titles=('溶媒'), columns=col(DocChunk.paragraph))
            filtered_contents = await DocChunk.filter_content(
                mysql, doc_id, contents, contents=('溶媒', '名称'), columns=col(DocChunk.paragraph)
            )
            if filtered_contents:
                contents = filtered_contents
        if contents:
            content = await BCERerank().most_relatived_content('溶媒', contents)
            return await MicronucleusSolvent.extract(content)

        logging.error('solvent_and_dosage_form doc chunk is missing')
        raise MissingDocChunkError()
    else:
        logging.error('trial subtype %s is not supported', trial_subtype)
        raise GenerateError(f'trial subtype {trial_subtype} is not supported')


@broker.task
@check_doc(GeneratedField.ADMINISTRATION_METHOD)
async def get_administration_method(project_id: int, doc_id: int, trial_subtype: TrialSubType) -> str | None:
    if trial_subtype == TrialSubType.SINGLE_DOSE:
        async with get_session() as mysql:
            doc_chunk_ids = await DocChunk.query_v2(
                mysql,
                doc_id,
                titles='给药',
                columns=col(DocChunk.id),
            )
            if doc_chunk_ids:
                doc_chunk_ids = await DocChunk.extend_with_sub_ids(mysql, doc_id, doc_chunk_ids)

            table_contents = await TableChunk.query_v2(
                mysql,
                doc_id,
                doc_chunk_ids=doc_chunk_ids,
                contents=('给药途径', '给药方法'),
                columns=col(TableChunk.content_md),
            )
        if table_contents:
            content = await BCERerank().most_relatived_content('给药途径', table_contents)
            result = await SingleAdministrationMethod.extract(content)
            if result:
                return result

        # 如果提取不到，从标题获取
        trial_title = await GeneratedField.get_by_doc_id(doc_id, GeneratedField.TRIAL_TITLE)
        if trial_title:
            return await AdministrationMethod.extract(trial_title)

        logging.error('administration_method table chunk is missing')
        raise MissingTableChunkError()
    elif trial_subtype in (TrialSubType.REPEATED_DOSE, TrialSubType.REPEATED_DOSE_SPECIFICITY):
        trial_title = await GeneratedField.get_by_doc_id(doc_id, GeneratedField.TRIAL_TITLE)
        if trial_title:
            return await AdministrationMethod.extract(trial_title)
    elif trial_subtype in (TrialSubType.AMES_TEST, TrialSubType.CHROMOSOME_ABERRATION_TEST):
        return '体外'
    elif trial_subtype == TrialSubType.MICRONUCLEUS_TEST:
        return '体内'
    else:
        logging.error('trial subtype %s is not supported', trial_subtype)
        raise GenerateError(f'trial subtype {trial_subtype} is not supported')


@broker.task
@check_doc(GeneratedField.DOSING_REGIMEN)
async def get_dosing_regimen(project_id: int, doc_id: int, trial_subtype: TrialSubType) -> str | None:
    if trial_subtype in (TrialSubType.SINGLE_DOSE, TrialSubType.AMES_TEST):
        return '-'
    elif trial_subtype in (TrialSubType.REPEATED_DOSE, TrialSubType.REPEATED_DOSE_SPECIFICITY):
        trial_title = await GeneratedField.get_by_doc_id(doc_id, GeneratedField.TRIAL_TITLE)
        if trial_title:
            return await RepeatedDoseRegimen.extract(trial_title)
        raise MissingFieldError()
    elif trial_subtype == TrialSubType.CHROMOSOME_ABERRATION_TEST:
        async with get_session() as mysql:
            doc_chunk_ids = await DocChunk.query_v2(
                mysql,
                doc_id,
                titles='染色体',
                columns=col(DocChunk.id),
            )
            if doc_chunk_ids:
                doc_chunk_ids = await DocChunk.extend_with_sub_ids(mysql, doc_id, doc_chunk_ids)

            table_contents = await TableChunk.query_v2(
                mysql,
                doc_id,
                doc_chunk_ids=doc_chunk_ids,
                contents='给药处理系列',
                columns=col(TableChunk.content_md),
            )

        if table_contents:
            content = await BCERerank().most_relatived_content('给药处理系列', table_contents)
            return await ChromosomeAberrationRegimen.extract(content)
        logging.error('dosing_regimen table chunk is missing')
        raise MissingTableChunkError()
    elif trial_subtype == TrialSubType.MICRONUCLEUS_TEST:
        async with get_session() as mysql:
            contents = await DocChunk.query_v2(mysql, doc_id, titles='给药方法', columns=col(DocChunk.paragraph))
            filtered_contents = await DocChunk.filter_content(
                mysql, doc_id, contents, contents='给药', columns=col(DocChunk.paragraph)
            )
            if filtered_contents:
                contents = filtered_contents

        if contents:
            content = await BCERerank().most_relatived_content('给药周期', contents)
            temp_list = json.loads(content)
            content = temp_list[0]  # 只取第一段
            return await MicronucleusRegimen.extract(content)
        logging.error('dosing_regimen doc chunk is missing')
        raise MissingDocChunkError()
    else:
        logging.error('trial subtype %s is not supported', trial_subtype)
        raise GenerateError(f'trial subtype {trial_subtype} is not supported')


@broker.task
@check_doc(GeneratedField.ADMINISTRATION_DOSAGE)
async def get_administration_dosage(project_id: int, doc_id: int, trial_subtype: TrialSubType) -> str | None:
    if trial_subtype == TrialSubType.SINGLE_DOSE:
        async with get_session() as mysql:
            doc_chunk_ids = await DocChunk.query_v2(
                mysql,
                doc_id,
                titles='组别',
                columns=col(DocChunk.id),
            )
            if doc_chunk_ids:
                doc_chunk_ids = await DocChunk.extend_with_sub_ids(mysql, doc_id, doc_chunk_ids)

            table_chunks = await TableChunk.query_v2(
                mysql,
                doc_id,
                doc_chunk_ids=doc_chunk_ids,
                titles=('试验', '实验'),
                contents=('剂量', '给药%量'),
                columns=(TableChunk.table_title, TableChunk.content_md),
            )

        if table_chunks:
            content = await BCERerank().most_relatived_table_chunk('给药剂量', table_chunks)
            return await DosageSingle.extract(content)
        logging.error('administration_dosage table chunk is missing')
        raise MissingTableChunkError()
    elif trial_subtype in (TrialSubType.REPEATED_DOSE, TrialSubType.REPEATED_DOSE_SPECIFICITY):
        async with get_session() as mysql:
            doc_chunk_ids = await DocChunk.query_v2(
                mysql,
                doc_id,
                titles=('组别', '剂量', '试验', '实验'),
                columns=col(DocChunk.id),
            )
            if doc_chunk_ids:
                doc_chunk_ids = await DocChunk.extend_with_sub_ids(mysql, doc_id, doc_chunk_ids)

            table_chunks = await TableChunk.query_v2(
                mysql,
                doc_id,
                doc_chunk_ids=doc_chunk_ids,
                titles=('试验', '实验'),
                contents=('剂量', '给药%量'),
                columns=(TableChunk.table_title, TableChunk.content_md),
            )

        if table_chunks:
            content = await BCERerank().most_relatived_table_chunk('给药剂量', table_chunks)
            return await DosageDoseSpecies.extract(content)
        logging.error('administration_dosage table chunk is missing')
        raise MissingTableChunkError()
    elif trial_subtype == TrialSubType.AMES_TEST:
        async with get_session() as mysql:
            doc_chunks = await DocChunk.query_v2(
                mysql,
                doc_id,
                titles=('回复突变', '主%验'),
                columns=(DocChunk.id, DocChunk.paragraph),
            )

            table_chunks = await TableChunk.query_v2(
                mysql,
                doc_id,
                doc_chunk_ids=[doc_chunk.id for doc_chunk in doc_chunks],
                titles='回复%量',
                contents='剂量',
                columns=(TableChunk.table_title, TableChunk.content_md),
            )

        if table_chunks:
            content = await BCERerank().most_relatived_table_chunk('给药剂量', table_chunks)
            return await DosageAmes.extract(content)
        logging.error('administration_dosage table chunk is missing')
        raise MissingTableChunkError()
    elif trial_subtype == TrialSubType.CHROMOSOME_ABERRATION_TEST:
        async with get_session() as mysql:
            doc_chunk_ids = await DocChunk.query_v2(
                mysql,
                doc_id,
                titles='染色体',
                columns=col(DocChunk.id),
            )
            if doc_chunk_ids:
                doc_chunk_ids = await DocChunk.extend_with_sub_ids(mysql, doc_id, doc_chunk_ids)

            table_chunks = await TableChunk.query_v2(
                mysql,
                doc_id,
                doc_chunk_ids=doc_chunk_ids,
                titles=('染色体', '畸变浓度'),
                contents='给药处理系列',
                columns=(TableChunk.table_title, TableChunk.content_md),
            )

        if table_chunks:
            content = await BCERerank().most_relatived_table_chunk('给药剂量', table_chunks)
            return await DosageChromosome.extract(content)
        logging.error('administration_dosage table chunk is missing')
        raise MissingTableChunkError()
    elif trial_subtype == TrialSubType.MICRONUCLEUS_TEST:
        async with get_session() as mysql:
            doc_chunks = await DocChunk.query_v2(
                mysql, doc_id, titles='概要', columns=(DocChunk.id, DocChunk.paragraph)
            )

            table_chunks = await TableChunk.query_v2(
                mysql,
                doc_id,
                doc_chunk_ids=[doc_chunk.id for doc_chunk in doc_chunks],
                titles=('微核', '试验'),
                contents='给药方案',
                columns=(TableChunk.table_title, TableChunk.content_md, TableChunk.footer),
            )

        if doc_chunks:
            doc_content = await BCERerank().most_relatived_doc_chunk('微核试验', doc_chunks)
        else:
            doc_content = ''

        if table_chunks:
            table_contents = []
            for table_chunk in table_chunks:
                table_contents.append(f'{table_chunk.content_md}\n\n{table_chunk.footer}')
            table_content = await BCERerank().most_relatived_content('微核试验', table_contents)
        else:
            table_content = ''

        if doc_content or table_content:
            content = '\n\n'.join((doc_content, table_content))
            return await DosageMicronucleus.extract(content)

        logging.error('administration_dosage doc chunk is missing')
        raise MissingDocChunkError()
    else:
        logging.error('trial subtype %s is not supported', trial_subtype)
        raise GenerateError(f'trial subtype {trial_subtype} is not supported')


@broker.task
@check_doc('')
async def get_all_base_fields_for_doc(project_id: int, doc_id: int, trial_subtype: TrialSubType) -> None:
    task = await parse_cover.kiq(project_id, doc_id)
    await task.wait_result()

    tasks: list[AsyncTaskiqTask] = [
        await get_species.kiq(project_id, doc_id, trial_subtype),
        await get_solvent_and_dosage_form.kiq(project_id, doc_id, trial_subtype),
        await get_administration_method.kiq(project_id, doc_id, trial_subtype),
        await get_dosing_regimen.kiq(project_id, doc_id, trial_subtype),
        await get_administration_dosage.kiq(project_id, doc_id, trial_subtype),
    ]
    for task in tasks:
        await task.wait_result()

    task = await update_base_fields_for_doc.kiq(project_id, doc_id)
    await task.wait_result()


@broker.task
@check_doc('')
async def update_base_fields_for_doc(project_id: int, doc_id: int) -> None:
    fields = await GeneratedField.get_all_by_doc_id(doc_id=doc_id)
    async with get_session() as mysql:
        await Doc.update_by_id(
            mysql,
            doc_id,
            {
                Doc.trial_title.name: fields.get(GeneratedField.TRIAL_TITLE, ''),
                Doc.trial_institution.name: fields.get(GeneratedField.TRIAL_INSTITUTION, ''),
                Doc.trial_number.name: fields.get(GeneratedField.TRIAL_NUMBER, ''),
                Doc.species.name: fields.get(GeneratedField.SPECIES, ''),
                Doc.solvent_and_dosage_form.name: fields.get(GeneratedField.SOLVENT_AND_DOSAGE_FORM, ''),
                Doc.administration_method.name: fields.get(GeneratedField.ADMINISTRATION_METHOD, ''),
                Doc.administration_dosage.name: fields.get(GeneratedField.ADMINISTRATION_DOSAGE, ''),
                Doc.dosing_regimen.name: fields.get(GeneratedField.DOSING_REGIMEN, ''),
                Doc.test_product.name: fields.get(GeneratedField.TEST_PRODUCT, ''),
                Doc.applicant.name: fields.get(GeneratedField.APPLICANT, ''),
            },
        )
        await mysql.commit()
