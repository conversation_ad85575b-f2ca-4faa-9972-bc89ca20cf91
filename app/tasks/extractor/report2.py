import logging

from sqlmodel import col

from app.clients.mysql import get_session
from app.extractors.dosage_2 import ExperimentType
from app.models.doc import Doc
from app.models.generated_field import GeneratedField
from app.schemas.doc_base_field import ADMINISTRATION_UNIT_DATA, TrialSubType
from app.task import broker
from app.utils.exception import MissingFieldError

from ..check import check_doc

EXPERIMENT_TYPE_OF_TRIAL_SUBTYPE: dict[TrialSubType, str] = {
    TrialSubType.AMES_TEST: '回复突变',
    TrialSubType.CHROMOSOME_ABERRATION_TEST: '染色体畸变',
    TrialSubType.MICRONUCLEUS_TEST: '微核',
}


@broker.task
@check_doc(GeneratedField.EXPERIMENT_TYPE)
async def get_experiment_type(project_id: int, doc_id: int, trail_sub_type: TrialSubType) -> str | None:
    experiment_type = EXPERIMENT_TYPE_OF_TRIAL_SUBTYPE.get(trail_sub_type)
    if experiment_type:
        return experiment_type

    trial_title = await GeneratedField.get_by_doc_id(doc_id, GeneratedField.TRIAL_TITLE)
    if trial_title:
        return await ExperimentType.extract(trial_title)
    else:
        logging.error('trial_title is missing')
        raise MissingFieldError()


get_all_fields_of_report2_for_doc = get_experiment_type
