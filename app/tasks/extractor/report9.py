import json
import logging

from sqlmodel import col
from taskiq import AsyncTaskiqTask

from app.clients.mysql import get_session
from app.extractors.dosage_9 import (
    AdministrationDate,
    AdministrationMethod9,
    CellNumber,
    EvaluateCells,
    GenotoxicEffects9,
    GetAge,
    SamplingTime,
    ToxicEffects,
)
from app.extractors.mc_table import LargeMCTable, MCTable, MediumMCTable, SmallMCTable
from app.models.doc import Doc
from app.models.doc_chunk import DocChunk
from app.models.generated_field import GeneratedField
from app.models.table_chunk import TableChunk
from app.schemas.doc_base_field import TrialSubType
from app.task import broker
from app.utils.bce_rerank import BCERerank
from app.utils.exception import (
    GenerateError,
    GenerateFailed,
    MissingDocChunkError,
    MissingTableChunkError,
)

from ..check import check_doc, check_project

SmallMCTableTuple = tuple[str, str, str]
MaleFemaleMCTableTuple = tuple[SmallMCTableTuple, SmallMCTableTuple]


# 9.1 遗传毒性：体内-微核试验部分 - 5-采样时间 字段
@broker.task
@check_doc(GeneratedField.SAMPLING_TIME)
async def get_sampling_time(project_id: int, doc_id: int) -> str | None:
    async with get_session() as mysql:
        contents = await DocChunk.query_v2(mysql, doc_id, titles='摘要', columns=col(DocChunk.paragraph))

    if contents:
        content = await BCERerank().most_relatived_content('溶媒对照和受试物组', contents)

        temp_list = json.loads(content)

        content = '\n'.join(temp_list)

        return await SamplingTime.extract(content)
    else:
        logging.error('sampling_time is missing')
        raise MissingDocChunkError()


# 9.1 遗传毒性：体内-微核试验部分 - 7-年龄 字段
@broker.task
@check_doc(GeneratedField.AGE)
async def get_age(project_id: int, doc_id: int) -> str | None:
    async with get_session() as mysql:
        contents = await DocChunk.query_v2(mysql, doc_id, titles='体重和年龄', columns=col(DocChunk.paragraph))
    if contents:
        content = await BCERerank().most_relatived_content('微核主试验', contents)

        temp_list = json.loads(content)

        content = '\n'.join(temp_list)

        return await GetAge.extract(content)
    else:
        logging.error('age is missing')
        raise MissingDocChunkError()


# 9.1 遗传毒性：体内-微核试验部分 - 8-给药方法 字段
@broker.task
@check_doc(GeneratedField.ADMINISTRATION_METHOD_9)
async def get_administration_method_9(project_id: int, doc_id: int) -> str | None:
    async with get_session() as mysql:
        contents = await DocChunk.query_v2(mysql, doc_id, titles='给药途径和选择理由', columns=col(DocChunk.paragraph))

    if contents:
        content = await BCERerank().most_relatived_content('给药', contents)

        temp_list = json.loads(content)

        content = '\n'.join(temp_list)

        return await AdministrationMethod9.extract(content)
    else:
        logging.error('administration_method_9 is missing')
        raise MissingDocChunkError()


# 9.1 遗传毒性：体内-微核试验部分 - 9-给药日期 字段
@broker.task
@check_doc(GeneratedField.ADMINISTRATION_DATE)
async def get_administration_date(project_id: int, doc_id: int) -> str | None:
    async with get_session() as mysql:
        doc_chunk_ids = await DocChunk.query_v2(mysql, doc_id, titles='试验信息', columns=col(DocChunk.id))
        if not doc_chunk_ids:
            raise MissingDocChunkError()

        doc_chunk_ids = await DocChunk.extend_with_sub_ids(mysql, doc_id, doc_chunk_ids)

        table_chunks = await TableChunk.query_v2(
            mysql, doc_id, doc_chunk_ids, contents='试验开始日期', columns=col(TableChunk.content_md)
        )

        content = await BCERerank().most_relatived_content('试验开始日期', table_chunks)

    if content:
        return await AdministrationDate.extract(content)
    else:
        logging.error('administration_date is missing')
        raise MissingTableChunkError()


# 9.1 遗传毒性：体内-微核试验部分 - 10-评价细胞 字段
@broker.task
@check_doc(GeneratedField.EVALUATE_CELLS)
async def get_evaluate_cells(project_id: int, doc_id: int) -> str | None:
    async with get_session() as mysql:
        contents = await DocChunk.query_v2(mysql, doc_id, titles='目的', columns=col(DocChunk.paragraph))

    if contents:
        content = await BCERerank().most_relatived_content('微核率', contents)

        temp_list = json.loads(content)

        content = '\n'.join(temp_list)

        return await EvaluateCells.extract(content)
    else:
        logging.error('evaluate_cells is missing')
        raise MissingDocChunkError()


# 9.1 遗传毒性：体内-微核试验部分 - 12-分析细胞数量/每动物 字段
@broker.task
@check_doc(GeneratedField.CELL_NUMBER)
async def get_cell_number(project_id: int, doc_id: int) -> str | None:
    async with get_session() as mysql:
        contents = await DocChunk.query_v2(mysql, doc_id, titles='涂片分析', columns=col(DocChunk.paragraph))

    if contents:
        content = await BCERerank().most_relatived_content('PCE', contents)

        temp_list = json.loads(content)

        content = '\n'.join(temp_list)

        return await CellNumber.extract(content)
    else:
        logging.error('cell_number is missing')
        raise MissingDocChunkError()


# 9.1 遗传毒性：体内-微核试验部分 - 13-毒性/细胞毒性作用 字段
@broker.task
@check_doc(GeneratedField.TOXIC_EFFECTS)
async def get_toxic_effects(project_id: int, doc_id: int) -> str | None:
    async with get_session() as mysql:
        contents = await DocChunk.query_v2(mysql, doc_id, titles='骨髓的细胞毒性', columns=col(DocChunk.paragraph))

    if contents:
        content = await BCERerank().most_relatived_content('细胞毒性', contents)

        temp_list = json.loads(content)

        content = '\n'.join(temp_list)

        return await ToxicEffects.extract(content)
    else:
        logging.error('toxic_effects is missing')
        raise MissingDocChunkError()


# 9.1 遗传毒性：体内-微核试验部分 - 14-遗传毒性作用 字段
@broker.task
@check_doc(GeneratedField.GENOTOXIC_EFFECTS_9)
async def get_genotoxic_effects_9(project_id: int, doc_id: int) -> str | None:
    async with get_session() as mysql:
        contents = await DocChunk.query_v2(mysql, doc_id, titles='结论', columns=col(DocChunk.paragraph))

    if contents:
        content = await BCERerank().most_relatived_content('阴性', contents)

        temp_list = json.loads(content)

        content = '\n'.join(temp_list)

        return await GenotoxicEffects9.extract(content)
    else:
        logging.error('genotoxic_effects_9 is missing')
        raise MissingDocChunkError()


@broker.task
@check_doc(GeneratedField.MALE_MC_TABLE)
async def get_male_small_mc_table(project_id: int, doc_id: int, content_json: str) -> str | None:
    return await MCTable.extract(content_json)


@broker.task
@check_doc(GeneratedField.FEMALE_MC_TABLE)
async def get_female_small_mc_table(project_id: int, doc_id: int, content_json: str) -> str | None:
    return await MCTable.extract(content_json)


@broker.task
@check_doc(GeneratedField.SMALL_MC_TABLE, field_type=tuple)
async def get_small_mc_table(project_id: int, doc_id: int) -> MaleFemaleMCTableTuple | None:
    async with get_session() as mysql:
        table_chunks = await TableChunk.query_v2(
            mysql,
            doc_id,
            [],
            titles=('微核总结数据'),
            columns=(TableChunk.content_json, TableChunk.header),
        )

    if table_chunks:
        male_table_chunks = [table_chunk for table_chunk in table_chunks if '雄' in table_chunk.header]
        female_table_chunks = [table_chunk for table_chunk in table_chunks if '雌' in table_chunk.header]
        male_contents = []
        female_contents = []
        for table_chunk in male_table_chunks:
            male_contents.append(table_chunk.content_json + '\n###\n' + table_chunk.header)
        for table_chunk in female_table_chunks:
            female_contents.append(table_chunk.content_json + '\n###\n' + table_chunk.header)
        male_content = await BCERerank().most_relatived_content('雄性微核总结数据', male_contents)
        female_content = await BCERerank().most_relatived_content('雄性微核总结数据', female_contents)

        male_content_json, table_1_header = male_content.split('\n###\n')
        female_content_json, table_2_header = female_content.split('\n###\n')
        if male_content_json and table_1_header and female_content_json and table_2_header:
            tasks: list[AsyncTaskiqTask] = [
                await get_male_small_mc_table.kiq(project_id, doc_id, male_content_json),
                await get_female_small_mc_table.kiq(project_id, doc_id, female_content_json),
            ]
            results = []
            for task in tasks:
                results.append(await task.wait_result())
            return (
                (results[0].return_value, male_content_json, table_1_header),
                (results[1].return_value, female_content_json, table_2_header),
            )

    logging.error('mc table chunk is missing')
    raise MissingTableChunkError()


@broker.task
@check_doc(GeneratedField.MC_TABLE, field_type=list)
async def get_merged_mc_table(project_id: int, doc_id: int, solvent_and_dosage_form: str) -> LargeMCTable | None:
    task = await get_small_mc_table.kiq(project_id, doc_id)
    result = await task.wait_result()
    man_or_woman_mc_table_tuple = result.return_value
    if man_or_woman_mc_table_tuple:
        medium_tables: list[MediumMCTable] = []
        for small_table_tuple in man_or_woman_mc_table_tuple:
            small_table_text, small_table_json_text, small_table_header_text = small_table_tuple

            small_table: SmallMCTable = []  # 默认值，生成失败时，使用空表
            if small_table_text and not GenerateError.is_error(small_table_text):
                try:
                    small_table = MCTable.parse_small_table(small_table_text, solvent_and_dosage_form)

                except Exception:
                    logging.exception('parse_small_table failed: %d', doc_id)
                    # 这个位置 同一篇文章有两个表的话怎么办？
                    await GeneratedField.set_for_doc_id(doc_id, GeneratedField.MC_TABLE, GenerateFailed.message())
                else:
                    medium_table = MCTable.generate_medium_table(
                        small_table_header_text, small_table, small_table_json_text
                    )
            medium_tables.append(medium_table)

        return MCTable.merge_tables(medium_tables)


@broker.task
@check_project('')
async def get_all_fields_of_report9_for_project(project_id: int) -> None:
    async with get_session() as mysql:
        docs = await Doc.get_by_project(
            mysql,
            project_id,
            trial_subtype=TrialSubType.MICRONUCLEUS_TEST.value,
            columns=(Doc.id, Doc.solvent_and_dosage_form),
        )

    if docs:
        doc = docs[0]  # 只有一份微核报告
        doc_id = doc.id
        tasks: list[AsyncTaskiqTask] = [
            await get_sampling_time.kiq(project_id, doc_id),
            await get_age.kiq(project_id, doc_id),
            await get_administration_method_9.kiq(project_id, doc_id),
            await get_administration_date.kiq(project_id, doc_id),
            await get_evaluate_cells.kiq(project_id, doc_id),
            await get_cell_number.kiq(project_id, doc_id),
            await get_toxic_effects.kiq(project_id, doc_id),
            await get_genotoxic_effects_9.kiq(project_id, doc_id),
            await get_merged_mc_table.kiq(project_id, doc.id, doc.solvent_and_dosage_form),
        ]
        for task in tasks:
            await task.wait_result()
    else:
        logging.error('micronucleus doc is missing')
