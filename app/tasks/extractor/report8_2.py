import json
import logging
import re

from sqlmodel import col
from taskiq import AsyncTaskiqTask

from app.clients.mysql import get_session
from app.extractors.ch_table import CHTable, CHTableRows
from app.extractors.dosage_8_2 import (
    AdministrationDateChromosome,
    AnalyzeCellNumberChromosome,
    GenotoxicEffectsChromosome,
    HandleChromosome,
    MetabolicSystemChromosome,
    NumberOfParallelCulturesChromosome,
    PositiveControlSampleChromosome,
    ToxicEffectsChromosome,
)
from app.models.doc import Doc
from app.models.doc_chunk import DocChunk
from app.models.generated_field import GeneratedField
from app.models.table_chunk import TableChunk
from app.schemas.doc_base_field import TrialSubType
from app.task import broker
from app.utils.bce_rerank import BCERerank
from app.utils.digit_tools import contains_digits
from app.utils.exception import (
    GenerateError,
    GenerateFailed,
    MissingDocChunkError,
    MissingTableChunkError,
)

from ..check import check_doc, check_project


# 8.2 遗传毒性：染色体畸变试验部分 - 3-平行培养物数量 字段
@broker.task
@check_doc(GeneratedField.NUMBER_OF_PARALLEL_CULTURES_CHROMOSOME)
async def get_number_of_parallel_cultures_chromosome(project_id: int, doc_id: int) -> str | None:
    async with get_session() as mysql:
        contents = await DocChunk.query_v2(
            mysql, doc_id, titles=('实验', '试验', '摘要'), columns=col(DocChunk.paragraph)
        )
        filtered_contents = await DocChunk.filter_content(
            mysql, doc_id, contents, contents='平行%细胞%数', columns=col(DocChunk.paragraph)
        )
        if filtered_contents:
            contents = filtered_contents

    if contents:
        content = await BCERerank().most_relatived_content('平行溶媒对照细胞数量', contents)
        temp_list = json.loads(content)
        content = '\n'.join(temp_list)
        result = await NumberOfParallelCulturesChromosome.extract(content)
        if result and contains_digits(result):
            return result
    else:
        logging.error('doc_chunk for number_of_parallel_cultures_chromosome is missing')
        raise MissingDocChunkError()


# 8.2 遗传毒性：染色体畸变试验部分 - 7-代谢系统 字段
@broker.task
@check_doc(GeneratedField.METABOLIC_SYSTEM_CHROMOSOME)
async def get_metabolic_system_chromosome(project_id: int, doc_id: int) -> str | None:
    async with get_session() as mysql:
        contents = await DocChunk.query_v2(mysql, doc_id, titles=('目的'), columns=col(DocChunk.paragraph))
        filtered_contents = await DocChunk.filter_content(
            mysql, doc_id, contents, contents='代谢%系统', columns=col(DocChunk.paragraph)
        )
        if filtered_contents:
            contents = filtered_contents

    if contents:
        content = await BCERerank().most_relatived_content('代谢系统', contents)
        temp_list = json.loads(content)
        content = '\n'.join(temp_list)
        result = await MetabolicSystemChromosome.extract(content)
        if result:
            return result
    else:
        logging.error('doc_chunk for metabolic_system_chromosome is missing')
        raise MissingDocChunkError()


# 8.2 遗传毒性：染色体畸变试验部分 - 8-分析细胞数 字段
@broker.task
@check_doc(GeneratedField.ANALYZE_CELL_NUMBER_CHROMOSOME)
async def get_analyze_cell_number_chromosome(project_id: int, doc_id: int) -> str | None:
    async with get_session() as mysql:
        contents = await DocChunk.query_v2(mysql, doc_id, titles=('染色体'), columns=col(DocChunk.paragraph))
        filtered_contents = await DocChunk.filter_content(
            mysql, doc_id, contents, contents='细胞', columns=col(DocChunk.paragraph)
        )
        if filtered_contents:
            contents = filtered_contents

    if contents:
        content = await BCERerank().most_relatived_content('每一组分析的细胞数量', contents)
        temp_list = json.loads(content)
        content = '\n'.join(temp_list)
        result = await AnalyzeCellNumberChromosome.extract(content)
        if result and contains_digits(result):
            return result
    else:
        logging.error('doc_chunk for analyze_cell_number_chromosome is missing')
        raise MissingDocChunkError()


# 8.2 遗传毒性：染色体畸变试验部分 - 9-给药日期 字段
@broker.task
@check_doc(GeneratedField.ADMINISTRATION_DATE_CHROMOSOME)
async def get_administration_date_chromosome(project_id: int, doc_id: int) -> str | None:
    async with get_session() as mysql:
        doc_chunk_ids = await DocChunk.query_v2(
            mysql,
            doc_id,
            titles=('试验信息', '实验信息'),
            columns=col(DocChunk.id),
        )
        assert isinstance(doc_chunk_ids, list)

        # 添加子章节
        if doc_chunk_ids:
            doc_chunk_ids = await DocChunk.extend_with_sub_ids(mysql, doc_id, doc_chunk_ids)

        table_chunks = await TableChunk.query_v2(
            mysql,
            doc_id,
            doc_chunk_ids,
            contents=('试验开始日期', '实验开始日期'),
            columns=(TableChunk.content_md, TableChunk.table_title),
        )

    if table_chunks:
        content = await BCERerank().most_relatived_table_chunk('试验开始日期', table_chunks)
        result = await AdministrationDateChromosome.extract(content)
        if result and contains_digits(result):
            return result
    else:
        logging.error('table chunk for administration_date_chromosome is missing')
        raise MissingTableChunkError()


# 8.2 遗传毒性：染色体畸变试验部分 - 11-阳性对照 字段
@broker.task
@check_doc(GeneratedField.POSITIVE_CONTROL_SAMPLE_CHROMOSOME)
async def get_positive_control_sample_chromosome(project_id: int, doc_id: int) -> str | None:
    async with get_session() as mysql:
        doc_chunk_ids = await DocChunk.query_v2(mysql, doc_id, titles=('阳性对照'), columns=col(DocChunk.id))
        assert isinstance(doc_chunk_ids, list)

        # 添加子章节
        if doc_chunk_ids:
            doc_chunk_ids = await DocChunk.extend_with_sub_ids(mysql, doc_id, doc_chunk_ids)

        table_chunks = await TableChunk.query_v2(
            mysql,
            doc_id,
            doc_chunk_ids,
            columns=(TableChunk.content_md, TableChunk.table_title),
        )

    if table_chunks:
        content = await BCERerank().most_relatived_table_chunk('阳性对照品名称', table_chunks)
        result = await PositiveControlSampleChromosome.extract(content)
        if result:
            return result
    else:
        logging.error('table chunk for positive_control_sample_chromosome is missing')
        raise MissingTableChunkError()


# 8.2 遗传毒性：染色体畸变试验部分 - 12-处理 字段
@broker.task
@check_doc(GeneratedField.HANDLE_CHROMOSOME)
async def get_handle_chromosome(project_id: int, doc_id: int) -> str | None:
    async with get_session() as mysql:
        contents = await DocChunk.query_v2(mysql, doc_id, titles=('试验', '实验'), columns=col(DocChunk.paragraph))
        filtered_contents = await DocChunk.filter_content(
            mysql, doc_id, contents, contents=(('处理', '给药'),), columns=col(DocChunk.paragraph)
        )
        if filtered_contents:
            contents = filtered_contents

    if contents:
        content = await BCERerank().most_relatived_content('给药处理系列', contents)
        temp_list = json.loads(content)
        content = '\n'.join(temp_list)
        result = await HandleChromosome.extract(content)
        if result:
            return result
    else:
        logging.error('doc_chunk for handle_chromosome is missing')
        raise MissingDocChunkError()


# 8.2 遗传毒性：染色体畸变试验部分 - 13-细胞毒性作用 字段
@broker.task
@check_doc(GeneratedField.TOXIC_EFFECTS_CHROMOSOME)
async def get_toxic_effects_chromosome(project_id: int, doc_id: int) -> str | None:
    async with get_session() as mysql:
        doc_chunks = await DocChunk.query_v2(
            mysql, doc_id, titles='染色体畸变主试验', columns=(DocChunk.paragraph, DocChunk.id)
        )
        filtered_doc_chunks = await DocChunk.filter_content(
            mysql, doc_id, doc_chunks, contents='细胞毒性', columns=(DocChunk.paragraph, DocChunk.id)
        )
        if filtered_doc_chunks:
            doc_chunks = filtered_doc_chunks

        # Include sub-chapters
        if doc_chunks:
            contents = []
            for doc_chunk in doc_chunks:
                doc_chunk_id = doc_chunk.id
                doc_chunk_paragraph = doc_chunk.paragraph
                sub_contents = await DocChunk.get_subs(
                    mysql, doc_id, doc_chunk_id, columns=(DocChunk.paragraph, DocChunk.title)
                )
                merged_contents = []
                doc_chunk_paragraph_list = json.loads(doc_chunk_paragraph)
                merged_contents.extend(doc_chunk_paragraph_list)
                if sub_contents:
                    for sub_content in sub_contents:
                        merged_contents.append(sub_content.title)
                        merged_contents.extend(json.loads(sub_content.paragraph))
                contains_end_of_administration = False
                for temp_content in merged_contents:
                    if '给药结束' in temp_content:
                        contains_end_of_administration = True
                if contains_end_of_administration:
                    temp_content = json.dumps(merged_contents)
                    contents.append(temp_content)

    if contents:
        content = await BCERerank().most_relatived_content('不同给药系列下所对应的特定浓度中的细胞毒性', contents)
        temp_list = json.loads(content)
        content = '\n'.join(temp_list)
        result = await ToxicEffectsChromosome.extract(content)
        if result:
            return result
    else:
        logging.error('doc_chunk for toxic_effects_chromosome is missing')
        raise MissingDocChunkError()


# 8.2 遗传毒性：染色体畸变试验部分 - 14-遗传毒性作用 字段
@broker.task
@check_doc(GeneratedField.GENOTOXIC_EFFECTS_CHROMOSOME)
async def get_genotoxic_effects_chromosome(project_id: int, doc_id: int) -> str | None:
    async with get_session() as mysql:
        contents = await DocChunk.query_v2(mysql, doc_id, titles=('结论', '总结'), columns=col(DocChunk.paragraph))

    if contents:
        content = await BCERerank().most_relatived_content('遗传毒性作用', contents)
        temp_list = json.loads(content)
        content = '\n'.join(temp_list)
        result = await GenotoxicEffectsChromosome.extract(content)
        if result and result != None:
            return result
    else:
        logging.error('doc_chunk for genotoxic_effects_chromosome is missing')
        raise MissingDocChunkError()


@broker.task
@check_doc(GeneratedField.SMALL_CH_TABLE_TEXT)
async def get_small_ch_table_text(project_id: int, doc_id: int, test_product: str) -> str | None:
    async with get_session() as mysql:
        doc_chunk_ids = await DocChunk.query_v2(
            mysql,
            doc_id,
            titles=('参考文献'),
            columns=col(DocChunk.id),
        )
        assert isinstance(doc_chunk_ids, list)

        # 添加子章节
        if doc_chunk_ids:
            doc_chunk_ids = await DocChunk.extend_with_sub_ids(mysql, doc_id, doc_chunk_ids)

        table_chunks = await TableChunk.query_v2(
            mysql,
            doc_id,
            doc_chunk_ids,
            titles=('%染色体%试验%摘要%', '染色体%实验%摘要'),
            columns=(TableChunk.content_md, TableChunk.table_title, TableChunk.footer),
        )

    if table_chunks:
        contents = []
        for table_chunk in table_chunks:
            contents.append(table_chunk.content_md + '\n###\n' + table_chunk.footer)
        content = await BCERerank().most_relatived_content('每个给药处理系列对应的细胞毒性', contents)
        content_md, footer = content.split('\n###\n')
        result = await CHTable.extract(content_md, footer, test_product)
        if result:
            return result
    else:
        logging.error('table chunk for ch_table is missing')
        raise MissingTableChunkError()


# 8.2 遗传毒性：染色体畸变试验部分 - 下表动态表格
@broker.task
@check_doc(GeneratedField.CH_TABLE, field_type=list)
async def get_merged_ch_table(project_id: int, doc_id: int, test_product: str) -> CHTableRows | None:
    tasks: list[AsyncTaskiqTask] = []
    results = []
    small_tables: list[CHTableRows] = []  # 默认值，生成失败时，使用空表
    task = await get_small_ch_table_text.kiq(project_id, doc_id, test_product)
    tasks.append(task)
    for task in tasks:
        results.append(await task.wait_result())
    for result in results:
        small_table_text = result.return_value
        if small_table_text and not GenerateError.is_error(small_table_text):
            try:
                small_table = CHTable.parse_small_table(small_table_text)
            except:
                logging.exception('parse_small_table failed: %d', doc_id)
                await GeneratedField.set_for_doc_id(doc_id, GeneratedField.CH_TABLE, GenerateFailed.message())
            else:
                small_tables.append(small_table)
    return CHTable.merge_tables(small_tables)


@broker.task
@check_project('')
async def get_all_fields_of_report8_2_for_project(project_id: int) -> None:
    async with get_session() as mysql:
        docs = await Doc.get_by_project(
            mysql,
            project_id,
            trial_subtype=TrialSubType.CHROMOSOME_ABERRATION_TEST.value,
            columns=(Doc.id, Doc.test_product),
        )

    if docs:
        doc = docs[0]  # 只有一份染色体报告
        doc_id = doc.id
        tasks: list[AsyncTaskiqTask] = [
            await get_number_of_parallel_cultures_chromosome.kiq(project_id, doc_id),
            await get_metabolic_system_chromosome.kiq(project_id, doc_id),
            await get_analyze_cell_number_chromosome.kiq(project_id, doc_id),
            await get_administration_date_chromosome.kiq(project_id, doc_id),
            await get_positive_control_sample_chromosome.kiq(project_id, doc_id),
            await get_handle_chromosome.kiq(project_id, doc_id),
            await get_toxic_effects_chromosome.kiq(project_id, doc_id),
            await get_genotoxic_effects_chromosome.kiq(project_id, doc_id),
            await get_merged_ch_table.kiq(project_id, doc_id, doc.test_product),
        ]

        for task in tasks:
            await task.wait_result()
    else:
        logging.error('chromosome doc is missing')
