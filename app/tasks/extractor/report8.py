import json
import logging

from sqlmodel import col
from taskiq import AsyncTaskiqTask

from app.clients.mysql import get_session
from app.extractors.dosage_8 import (
    CytotoxicEffects8,
    GenotoxicEffects8,
    MetabolicSystem,
    NumberOfParallelCultures,
    PositiveControlSample,
)
from app.extractors.table_8_1 import generate_table_8_1
from app.models.doc import Doc
from app.models.doc_chunk import DocChunk
from app.models.generated_field import GeneratedField
from app.models.table_chunk import TableChunk
from app.schemas.doc_base_field import TrialSubType
from app.schemas.table import Table
from app.task import broker
from app.utils.bce_rerank import BCERerank
from app.utils.exception import (
    GenerateFailed,
    MissingDocChunkError,
    MissingTableChunkError,
)

from ..check import check_doc, check_project


# 8.1 遗传毒性：体外-回复突变试验部分 - 3-平行培养物数量 字段
@broker.task
@check_doc(GeneratedField.NUMBER_OF_PARALLEL_CULTURES)
async def get_number_of_parallel_cultures(project_id: int, doc_id: int) -> str | None:
    async with get_session() as mysql:
        contents = await DocChunk.query_v2(mysql, doc_id, titles='阳性对照值', columns=col(DocChunk.paragraph))

    if contents:
        content = await BCERerank().most_relatived_content('阳性对照组', contents)

        temp_list = json.loads(content)

        content = '\n'.join(temp_list)

        return await NumberOfParallelCultures.extract(content)
    else:
        logging.error('number_of_parallel_cultures is missing')

        raise MissingDocChunkError()


# 8.1 遗传毒性：体外-回复突变试验部分 - 8-阳性对照品 字段
@broker.task
@check_doc(GeneratedField.POSITIVE_CONTROL_SAMPLE)
async def get_positive_control_sample(project_id: int, doc_id: int) -> str | None:
    async with get_session() as mysql:
        doc_chunk_ids = await DocChunk.query_v2(mysql, doc_id, titles='阳性对照品信息', columns=col(DocChunk.id))

        if doc_chunk_ids:
            doc_chunk_ids = await DocChunk.extend_with_sub_ids(mysql, doc_id, doc_chunk_ids)

        table_contents = await TableChunk.query_v2(
            mysql, doc_id, doc_chunk_ids, titles='阳性对照品', columns=(col(TableChunk.content_md))
        )

        if not table_contents:
            raise MissingDocChunkError()

        content = '\n'.join(table_contents)

    if content:
        return await PositiveControlSample.extract(content)
    else:
        logging.error('positive_control_sample table_chunk is missing')
        raise MissingTableChunkError()


# 8.1 遗传毒性：体外-回复突变试验部分 - 9-代谢系统 字段
@broker.task
@check_doc(GeneratedField.METABOLIC_SYSTEM)
async def get_metabolic_system(project_id: int, doc_id: int) -> str | None:
    async with get_session() as mysql:
        contents = await DocChunk.query_v2(mysql, doc_id, titles='研究目的', columns=col(DocChunk.paragraph))

    if contents:
        content = await BCERerank().most_relatived_content('代谢', contents)

        temp_list = json.loads(content)

        content = '\n'.join(temp_list)

        return await MetabolicSystem.extract(content)
    else:
        logging.error('metabolic_system is missing')
        raise MissingDocChunkError()


# 8.1 遗传毒性：体外-回复突变试验部分 - 10-处理 字段
@broker.task
@check_doc(GeneratedField.HANDLE_8)
async def get_handle_8(project_id: int, doc_id: int) -> str | None:
    async with get_session() as mysql:
        contents = await DocChunk.query_v2(mysql, doc_id, titles='浇板步骤', columns=col(DocChunk.paragraph))

    if contents:
        content = await BCERerank().most_relatived_content('代谢系统', contents)

        temp_list = json.loads(content)

        content = '\n'.join(temp_list)

        if content:
            return content

        logging.error('handle_8 extract failed')
        raise GenerateFailed()
    else:
        logging.error('handle_8 is missing')
        raise MissingDocChunkError()


# 8.1 遗传毒性：体外-回复突变试验部分 - 11-细胞毒性作用 字段
@broker.task
@check_doc(GeneratedField.CYTOTOXIC_EFFECTS_8)
async def get_cytotoxic_effects_8(project_id: int, doc_id: int) -> str | None:
    async with get_session() as mysql:
        contents = await DocChunk.query_v2(mysql, doc_id, titles='摘%要', columns=col(DocChunk.paragraph))

    if contents:
        content = await BCERerank().most_relatived_content('细胞毒性', contents)

        temp_list = json.loads(content)

        content = '\n'.join(temp_list)

        return await CytotoxicEffects8.extract(content)
    else:
        logging.error('cytotoxic_effects_8 is missing')
        raise MissingDocChunkError()


# 8.1 遗传毒性：体外-回复突变试验部分 - 12-遗传毒性作用 字段
@broker.task
@check_doc(GeneratedField.GENOTOXIC_EFFECTS_8)
async def get_genotoxic_effects_8(project_id: int, doc_id: int) -> str | None:
    async with get_session() as mysql:
        contents = await DocChunk.query_v2(mysql, doc_id, titles='结论', columns=col(DocChunk.paragraph))

    if contents:
        content = await BCERerank().most_relatived_content('测试结果', contents)

        temp_list = json.loads(content)

        content = '\n'.join(temp_list)

        return await GenotoxicEffects8.extract(content)
    else:
        logging.error('genotoxic_effects_8 is missing')
        raise MissingDocChunkError()


@broker.task
@check_doc(GeneratedField.TABLE_8_1, field_type=list)
async def get_table_8_1(project_id: int, doc_id: int, test_product: str) -> Table | None:
    async with get_session() as mysql:
        table_chunks = await TableChunk.query_by_doc(
            mysql, doc_id, '%细菌回复突变%试验结果%', columns=(TableChunk.table_title, TableChunk.content_json)
        )

    if table_chunks:
        return generate_table_8_1(table_chunks, test_product)
    else:
        logging.error('table_8_1 chunk is missing')
        raise MissingTableChunkError()


@broker.task
@check_project('')
async def get_all_fields_of_report8_for_project(project_id: int) -> None:
    async with get_session() as mysql:
        docs = await Doc.get_by_project(
            mysql,
            project_id,
            trial_subtype=TrialSubType.AMES_TEST.value,
            columns=(Doc.id, Doc.test_product),
        )

    if docs:
        doc = docs[0]  # 只有一份回复突变报告
        doc_id = doc.id
        tasks: list[AsyncTaskiqTask] = [
            await get_number_of_parallel_cultures.kiq(project_id, doc_id),
            await get_positive_control_sample.kiq(project_id, doc_id),
            await get_metabolic_system.kiq(project_id, doc_id),
            await get_handle_8.kiq(project_id, doc_id),
            await get_cytotoxic_effects_8.kiq(project_id, doc_id),
            await get_genotoxic_effects_8.kiq(project_id, doc_id),
            await get_table_8_1.kiq(project_id, doc.id, doc.test_product),
        ]
        for task in tasks:
            await task.wait_result()
    else:
        logging.error('ames doc is missing')
