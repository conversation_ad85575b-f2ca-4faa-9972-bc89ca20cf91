import json
import logging
import re

from sqlmodel import col
from taskiq import AsyncTaskiqTask

from app.clients.mysql import get_session
from app.extractors.dosage_7 import (
    DateOfFirstDosage,
    InitialAge,
    NoAdverseReactionDosage,
    RecoveryPeriod,
)
from app.extractors.dosage_7_generate_default_table import GenerateDefaultTable
from app.extractors.table_7_3 import (
    HemagglutinationTable,
    generate_clinical_observation_from_table,
    generate_table_clinical_observation,
    generate_table_food_intake,
    generate_table_gross_pathology,
    generate_table_near_death_or_dying,
    generate_table_urinalysis,
)
from app.extractors.table_7_3_abnormal_body_temperature import (
    AbnormalBodyTemperatureTable,
)
from app.extractors.table_7_3_abnormal_electrocardiogram import (
    AbnormalElectrocardiogramTable,
)
from app.extractors.table_7_3_abnormal_weight import AbnormalWeightTable
from app.extractors.table_7_3_blood_biochemistry import BloodBiochemistryTable
from app.extractors.table_7_3_hematology import HematologyTable
from app.extractors.table_7_3_histopathology import HistopathologyTable
from app.extractors.table_7_3_organ_weight import OrganWeightTable
from app.models.doc import Doc
from app.models.doc_chunk import DocChunk
from app.models.generated_field import GeneratedField
from app.models.table_chunk import TableChunk
from app.schemas.doc_base_field import TrialSubType
from app.schemas.table import TableRow, TableRows
from app.task import broker
from app.tasks.extractor.report7_2 import generate_7_2_table
from app.utils.bce_rerank import BCERerank
from app.utils.exception import (
    MissingDocChunkError,
    MissingFieldError,
    MissingTableChunkError,
)

from ..check import check_doc, check_project


# 8.2 遗传毒性：重复给药毒性-关键试验 - 4-初始年龄 字段
@broker.task
@check_doc(GeneratedField.INITIAL_AGE)
async def get_initial_age(project_id: int, doc_id: int) -> str | None:
    async with get_session() as mysql:
        doc_chunk_ids = await DocChunk.query(mysql, doc_id, title='%动物%', columns=col(DocChunk.id))
        if not doc_chunk_ids:
            raise MissingDocChunkError()

        for doc_chunk_id in doc_chunk_ids:
            # 可能有多个相同片段，找包含表格名为 '%实验动物%' 的片段.
            content = await TableChunk.query_first(
                mysql, doc_chunk_id, '%实验动物%', columns=col(TableChunk.content_md)
            )
            if content:
                break

    if content:
        return await InitialAge.extract(content)
    else:
        logging.error('table chunk for get_initial_age is missing')
        raise MissingTableChunkError()


# 8.2 遗传毒性：重复给药毒性-关键试验 - 5-恢复期 字段
@broker.task
@check_doc(GeneratedField.RECOVERY_PERIOD)
async def get_recovery_period(project_id: int, doc_id: int) -> str | None:
    async with get_session() as mysql:
        trial_title = await Doc.get_by_id(mysql, doc_id, col(Doc.trial_title))

    if trial_title:
        return await RecoveryPeriod.extract(trial_title)
    else:
        logging.error('trial_title is missing')
        raise MissingFieldError()


# 8.2 遗传毒性：重复给药毒性-关键试验 - 7-首次给药日期 字段
@broker.task
@check_doc(GeneratedField.DATE_OF_FIRST_DOSAGE)
async def get_date_of_first_dosage(project_id: int, doc_id: int) -> str | None:
    async with get_session() as mysql:
        doc_chunk_ids = await DocChunk.query(mysql, doc_id, title='%试验日程%', columns=col(DocChunk.id))
        if not doc_chunk_ids:
            raise MissingDocChunkError()

        for doc_chunk_id in doc_chunk_ids:
            # 里边的表格是没有名字的
            content = await TableChunk.query_first(mysql, doc_chunk_id, columns=col(TableChunk.content_md))
            if content:
                break

    if content:
        return await DateOfFirstDosage.extract(content)
    else:
        logging.error('table chunk for date_of_first_dosage is missing')
        raise MissingTableChunkError()


# 8.2 遗传毒性：重复给药毒性-关键试验 - 11-未见不良反应剂量 字段
@broker.task
@check_doc(GeneratedField.NO_ADVERSE_REACTION_DOSAGE)
async def get_no_adverse_reaction_dosage(project_id: int, doc_id: int) -> str | None:
    async with get_session() as mysql:
        contents = await DocChunk.query_v2(mysql, doc_id, titles=('综合讨论', '结论'), columns=col(DocChunk.paragraph))
        filtered_contents = await DocChunk.filter_content(
            mysql,
            doc_id,
            contents,
            contents=('未见不良反应剂量', '无可见不良反应剂量', '无毒性反应剂量', 'NOAEL'),
            columns=col(DocChunk.paragraph),
            required=True,
        )
        if filtered_contents:
            contents = filtered_contents

    if contents:
        content = await BCERerank().most_relatived_paragraph('未见不良反应剂量', contents)
        return await NoAdverseReactionDosage.extract(content)
    else:
        logging.error('no_adverse_reaction_dosage is missing')
        raise MissingDocChunkError()


DOSE_PATTERN = re.compile(r'\d+(?:\.\d+)?')


# 267.7 续表 - 表头的生成
def generate_table_7_3_header(administration_dosage: str) -> TableRows:
    """
    administration_dosage：给药剂量，有两种形式：
        1. "2、5、7 mg/kg/day"
        2. "雄性：5、15 mg/kg/day\n雌性：2、7 mg/kg/day"
    return: 2行的表头，分别是日剂量和动物数量，例如：
        [
            ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2', '15', '7'],
            ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
        ]
    """
    # [1]:生成日剂量
    first_row: TableRow = ['日剂量（mg/kg）', '0（对照）', '0（对照）']
    administration_dosage_lines = administration_dosage.split('\n')
    if len(administration_dosage_lines) == 1:
        # 形式 1:雌雄剂量相同，添加两次
        doses = DOSE_PATTERN.findall(administration_dosage)
        for dose in doses:
            first_row.extend([dose, dose])
    else:
        # 形式 2
        male_doses = DOSE_PATTERN.findall(administration_dosage_lines[0])
        female_doses = DOSE_PATTERN.findall(administration_dosage_lines[1])
        for male_dose, female_dose in zip(male_doses, female_doses):
            first_row.extend([male_dose, female_dose])
    # [2]:生成动物数量
    second_row: TableRow = ['动物数量']
    second_row.extend(['M:', 'F:'] * ((len(first_row) - 1) // 2))
    return [first_row, second_row]


# 267.7 续表 - 表头的生成,markdown格式
def generate_table_7_3_header_md(header: TableRows) -> str:
    header_md = f"| {' | '.join(header[0])} |\n| {' | '.join(header[1])} |"
    return header_md


# 8.2 遗传毒性：重复给药毒性-关键试验 - 眼科检查 表格
@broker.task
@check_doc(GeneratedField.EYE_EXAMINATION_TABLE, field_type=tuple)
async def get_eye_examination_table(
    project_id: int, doc_id: int, parent_id: int, header: TableRows, header_md: str
) -> tuple[TableRows, TableRows]:
    async with get_session() as mysql:
        doc_chunk = await DocChunk.query_sub(
            mysql, doc_id, parent_id, title='%眼科检查%', columns=(DocChunk.id, DocChunk.parent_id, DocChunk.title)
        )
    administration_table: TableRows = []
    recovery_table: TableRows = []
    if doc_chunk:
        fill_column_cnt = len(header[0]) - 1
        administration_table.extend(header)
        administration_table_value = await GenerateDefaultTable.generate(fill_column_cnt, '眼科检查')
        administration_table.append(administration_table_value)
        return (administration_table, recovery_table)
    else:
        raise MissingDocChunkError()


# 8.2 遗传毒性：重复给药毒性-关键试验 - 血压 表格
@broker.task
@check_doc(GeneratedField.BLOOD_PRESSURE_TABLE, field_type=tuple)
async def get_blood_pressure_table(
    project_id: int, doc_id: int, parent_id: int, header: TableRows, header_md: str
) -> tuple[TableRows, TableRows]:
    async with get_session() as mysql:
        doc_chunk = await DocChunk.query_sub(
            mysql, doc_id, parent_id, title='%血压%', columns=(DocChunk.id, DocChunk.parent_id, DocChunk.title)
        )
    administration_table: TableRows = []
    recovery_table: TableRows = []
    if doc_chunk:
        fill_column_cnt = len(header[0]) - 1
        administration_table = []
        administration_table.extend(header)
        administration_table_value = await GenerateDefaultTable.generate(fill_column_cnt, '血压')
        administration_table.append(administration_table_value)

    return (administration_table, recovery_table)


# 8.2 遗传毒性：重复给药毒性-关键试验 - 体重异常 表格, 必选字段
@broker.task
@check_doc(GeneratedField.ABNORMAL_WEIGHT_TABLE, field_type=tuple)
async def get_abnormal_weight_table(
    project_id: int, doc_id: int, parent_id: int, header: TableRows, header_md: str
) -> tuple[TableRows, TableRows]:  # 给药期 / 恢复期
    tables = []
    async with get_session() as mysql:
        doc_chunk = await DocChunk.query_sub(
            mysql, doc_id, parent_id, title='%体重变化%', columns=(DocChunk.id, DocChunk.parent_id, DocChunk.title)
        )

        if doc_chunk:
            tables = await TableChunk.query(
                mysql, doc_chunk.id, table_title='%体重%', columns=(TableChunk.table_title, TableChunk.content_md)
            )
        else:
            table = []

    administration_table: TableRows = []
    recovery_table: TableRows = []
    if tables:
        for table in tables:
            if '恢复期' in table.table_title:
                if recovery_table:
                    logging.warning('get_abnormal_weight_table recovery table is duplicated')
                    continue
                recovery_table_text = await AbnormalWeightTable.extract(table.content_md, header_md)
                recovery_table = AbnormalWeightTable.parse_table(recovery_table_text, header)
            else:
                if administration_table:
                    logging.warning('get_abnormal_weight_table administration table is duplicated')
                    continue
                administration_table_text = await AbnormalWeightTable.extract(table.content_md, header_md)
                administration_table = AbnormalWeightTable.parse_table(administration_table_text, header)

    # 给药期如果没有表格，则返回默认值。恢复期如果没有表格，则返回空列表。
    fill_column_cnt = len(header[0]) - 1
    if not administration_table:
        administration_table.extend(header)
        administration_table_value = await GenerateDefaultTable.generate(fill_column_cnt, '体重和体重变化')
        administration_table.append(administration_table_value)

    return (administration_table, recovery_table)


# 8.2 遗传毒性：重复给药毒性-关键试验 - 血生化 恢复期 表格
@broker.task
@check_doc(GeneratedField.BLOOD_BIOCHEMISTRY_TABLE, field_type=tuple)
async def get_blood_biochemistry_table(
    project_id: int, doc_id: int, parent_id: int, header: TableRows, header_md: str
) -> tuple[TableRows, TableRows]:  # 给药期 / 恢复期
    tables = []
    async with get_session() as mysql:
        doc_chunk = await DocChunk.query_sub(
            mysql, doc_id, parent_id, title='%血%生化%', columns=(DocChunk.id, DocChunk.parent_id, DocChunk.title)
        )

        if doc_chunk:
            tables = await TableChunk.query(
                mysql, doc_chunk.id, columns=(TableChunk.table_title, TableChunk.content_md)
            )
        else:
            raise MissingDocChunkError()
    administration_table: TableRows = []
    recovery_table: TableRows = []
    if tables:
        administration_table: TableRows = []
        recovery_table: TableRows = []
        for table in tables:
            if '恢复期' in table.table_title:
                if recovery_table:
                    logging.warning('get_blood_biochemistry_table recovery table is duplicated')
                    continue
                recovery_table_text = await BloodBiochemistryTable.extract(table.content_md, header_md)
                recovery_table = BloodBiochemistryTable.parse_table(recovery_table_text, header)
            else:
                if administration_table:
                    logging.warning('get_blood_biochemistry_table administration table is duplicated')
                    continue
                administration_table_text = await BloodBiochemistryTable.extract(table.content_md, header_md)
                administration_table = BloodBiochemistryTable.parse_table(administration_table_text, header)

        return (administration_table, recovery_table)
    else:
        logging.error('table chunk for blood_biochemistry_table is missing')
        raise MissingTableChunkError()


# 8.2 遗传毒性：重复给药毒性-关键试验 - 血液学 表格
@broker.task
@check_doc(GeneratedField.HEMATOLOGY_TABLE, field_type=tuple)
async def get_hematology_table(
    project_id: int, doc_id: int, parent_id: int, header: TableRows, header_md: str
) -> tuple[TableRows, TableRows]:  # 给药期 / 恢复期
    tables = []
    async with get_session() as mysql:
        doc_chunk = await DocChunk.query_sub(
            mysql, doc_id, parent_id, title='%血液学%', columns=(DocChunk.id, DocChunk.parent_id, DocChunk.title)
        )

        if doc_chunk:
            tables = await TableChunk.query(
                mysql, doc_chunk.id, columns=(TableChunk.table_title, TableChunk.content_md)
            )
        else:
            raise MissingDocChunkError()

    if tables:
        administration_table: TableRows = []
        recovery_table: TableRows = []
        for table in tables:
            if '血液学' in table.table_title:
                if '恢复期' in table.table_title:
                    if recovery_table:
                        logging.warning('get_hematology_table recovery table is duplicated')
                        continue
                    recovery_table_text = await HematologyTable.extract(table.content_md, header_md)
                    recovery_table = HematologyTable.parse_table(recovery_table_text, header)
                elif '终末期' in table.table_title:
                    if administration_table:
                        logging.warning('get_hematology_table administration table is duplicated')
                        continue
                    administration_table_text = await HematologyTable.extract(table.content_md, header_md)
                    administration_table = HematologyTable.parse_table(administration_table_text, header)

        return (administration_table, recovery_table)
    else:
        # logging.error('table chunk for hematology_table is missing')
        # raise MissingTableChunkError()
        # 血液学如果没有也需要生成新的一行而不是直接抛异常
        target_table_none = ['血液学参数']
        add_num = len(header[0]) - 1
        target_table_none.extend(['-'] * add_num)
        administration_table = header + [target_table_none]
        recovery_table = []
        return (administration_table, recovery_table)


# 8.2 遗传毒性：重复给药毒性-关键试验 - 血清电解质 表格； 它与 血生化用同一套提示词
@broker.task
@check_doc(GeneratedField.SERUM_ELECTROLYTES_TABLE, field_type=tuple)
async def get_serum_electrolytes_table(
    project_id: int, doc_id: int, parent_id: int, header: TableRows, header_md: str
) -> tuple[TableRows, TableRows]:  # 给药期 / 恢复期
    tables = []
    async with get_session() as mysql:
        doc_chunk = await DocChunk.query_sub(
            mysql, doc_id, parent_id, title='%血清电解质%', columns=(DocChunk.id, DocChunk.parent_id, DocChunk.title)
        )

        if doc_chunk:
            tables = await TableChunk.query(
                mysql, doc_chunk.id, columns=(TableChunk.table_title, TableChunk.content_md)
            )
        else:
            raise MissingDocChunkError()

    if tables:
        administration_table: TableRows = []
        recovery_table: TableRows = []
        for table in tables:
            if '恢复期' in table.table_title:
                if recovery_table:
                    logging.warning('get_serum_electrolytes_table recovery table is duplicated')
                    continue
                recovery_table_text = await BloodBiochemistryTable.extract(table.content_md, header_md)
                recovery_table = BloodBiochemistryTable.parse_table(recovery_table_text, header)
            else:
                if administration_table:
                    logging.warning('get_serum_electrolytes_table administration table is duplicated')
                    continue
                administration_table_text = await BloodBiochemistryTable.extract(table.content_md, header_md)
                administration_table = BloodBiochemistryTable.parse_table(administration_table_text, header)

        return (administration_table, recovery_table)
    else:
        logging.error('table chunk for serum_electrolytes_table is missing')
        raise MissingTableChunkError()


# 8.2 遗传毒性：重复给药毒性-关键试验 - 器官重量 表格
@broker.task
@check_doc(GeneratedField.ORGAN_WEIGHT_TABLE, field_type=tuple)
async def get_organ_weight_table(
    project_id: int, doc_id: int, parent_id: int, header: TableRows, header_md: str
) -> tuple[TableRows, TableRows]:  # 给药期 / 恢复期
    tables = []
    async with get_session() as mysql:
        doc_chunk = await DocChunk.query_sub(
            mysql, doc_id, parent_id, title='%脏器重量%', columns=(DocChunk.id, DocChunk.parent_id, DocChunk.title)
        )

        if doc_chunk:
            tables = await TableChunk.query(
                mysql, doc_chunk.id, columns=(TableChunk.table_title, TableChunk.content_md)
            )

    administration_table: TableRows = []
    recovery_table: TableRows = []
    if tables:
        for table in tables:
            if '恢复期' in table.table_title:
                if recovery_table:
                    logging.warning('get_organ_weight_table recovery table is duplicated')
                    continue
                recovery_table_text = await OrganWeightTable.extract(table.content_md, header_md, table.table_title)
                recovery_table = OrganWeightTable.parse_table(recovery_table_text, header)
            else:
                if administration_table:
                    logging.warning('get_organ_weight_table administration table is duplicated')
                    continue
                administration_table_text = await OrganWeightTable.extract(
                    table.content_md, header_md, table.table_title
                )
                administration_table = OrganWeightTable.parse_table(administration_table_text, header)

    # 给药期如果没有表格，则返回默认值。 恢复期如果没有表格，则返回空列表
    fill_column_cnt = len(header[0]) - 1
    if not administration_table:
        administration_table.extend(header)
        administration_table_value = await GenerateDefaultTable.generate(fill_column_cnt, '器官重量')
        administration_table.append(administration_table_value)

    return (administration_table, recovery_table)


# 8.2 遗传毒性：重复给药毒性-关键试验 - 组织病理学 表格
@broker.task
@check_doc(GeneratedField.HISTOPATHOLOGY_TABLE, field_type=tuple)
async def get_histopathology_table(
    project_id: int, doc_id: int, parent_id: int, header: TableRows, header_md: str
) -> tuple[TableRows, TableRows]:  # 给药期 / 恢复期
    tables = []
    async with get_session() as mysql:
        doc_chunk = await DocChunk.query_sub(
            mysql, doc_id, parent_id, title='%组织病理学%', columns=(DocChunk.id, DocChunk.parent_id, DocChunk.title)
        )

        if doc_chunk:
            tables = await TableChunk.query(
                mysql, doc_chunk.id, columns=(TableChunk.table_title, TableChunk.content_md)
            )

    administration_table: TableRows = []
    recovery_table: TableRows = []
    if tables:
        for table in tables:
            if '恢复期' in table.table_title:
                if recovery_table:
                    logging.warning('get_histopathology_table recovery table is duplicated')
                    continue
                recovery_table_text = await HistopathologyTable.extract(table.content_md, header_md)
                recovery_table = HistopathologyTable.parse_table(recovery_table_text, header)
            else:
                if administration_table:
                    logging.warning('get_histopathology_table administration table is duplicated')
                    continue
                administration_table_text = await HistopathologyTable.extract(table.content_md, header_md)
                administration_table = HistopathologyTable.parse_table(administration_table_text, header)

    # 给药期如果没有表格，则返回默认值。恢复期如果没有表格，则返回空列表
    fill_column_cnt = len(header[0]) - 1
    if not administration_table:
        administration_table.extend(header)
        administration_table_value = await GenerateDefaultTable.generate(fill_column_cnt, '组织病理学')
        administration_table.append(administration_table_value)

    return (administration_table, recovery_table)


# 8.2 遗传毒性：重复给药毒性-关键试验 - 体温异常 表格
@broker.task
@check_doc(GeneratedField.ABNORMAL_BODY_TEMPERATURE_TABLE, field_type=tuple)
async def get_abnormal_body_temperature_table(
    project_id: int, doc_id: int, parent_id: int, header: TableRows, header_md: str
) -> tuple[TableRows, TableRows]:  # 给药期 / 恢复期
    tables = []
    async with get_session() as mysql:
        doc_chunk = await DocChunk.query_sub(
            mysql, doc_id, parent_id, title='%体温%', columns=(DocChunk.id, DocChunk.parent_id, DocChunk.title)
        )

        if doc_chunk:
            tables = await TableChunk.query(
                mysql, doc_chunk.id, columns=(TableChunk.table_title, TableChunk.content_md)
            )
        else:
            raise MissingDocChunkError()

    if tables:
        administration_table: TableRows = []
        recovery_table: TableRows = []
        for table in tables:
            if '恢复期' in table.table_title:
                if recovery_table:
                    logging.warning('get_abnormal_body_temperature_table recovery table is duplicated')
                    continue
                recovery_table_text = await AbnormalBodyTemperatureTable.extract(table.content_md, header_md)
                recovery_table = AbnormalBodyTemperatureTable.parse_table(recovery_table_text, header)
            else:
                if administration_table:
                    logging.warning('get_abnormal_body_temperature_table administration table is duplicated')
                    continue
                administration_table_text = await AbnormalBodyTemperatureTable.extract(table.content_md, header_md)
                administration_table = AbnormalBodyTemperatureTable.parse_table(administration_table_text, header)

        return (administration_table, recovery_table)
    else:
        logging.error('table chunk for abnormal_body_temperature_table is missing')
        raise MissingTableChunkError()


# 8.2 遗传毒性：重复给药毒性-关键试验 - 心电图异常 表格
@broker.task
@check_doc(GeneratedField.ABNORMAL_ELECTROCARDIOGRAM_TABLE, field_type=tuple)
async def get_abnormal_electrocardiogram_table(
    project_id: int, doc_id: int, parent_id: int, header: TableRows, header_md: str
) -> tuple[TableRows, TableRows]:  # 给药期 / 恢复期
    tables = []
    async with get_session() as mysql:
        # 心电图可能会叫ECG、EKG
        doc_chunk = await DocChunk.query_sub(
            mysql,
            doc_id,
            parent_id,
            title=('%心电图%', '%ECG%', '%EKG%'),
            columns=(DocChunk.id, DocChunk.parent_id, DocChunk.title),
        )

        if doc_chunk:
            tables = await TableChunk.query(
                mysql, doc_chunk.id, columns=(TableChunk.table_title, TableChunk.content_md)
            )
        else:
            raise MissingDocChunkError()
    if tables:
        administration_table: TableRows = []
        recovery_table: TableRows = []
        for table in tables:
            if '恢复期' in table.table_title:
                if recovery_table:
                    logging.warning('get_abnormal_electrocardiogram_table recovery table is duplicated')
                    continue
                recovery_table_text = await AbnormalElectrocardiogramTable.extract(table.content_md, header_md)
                recovery_table = AbnormalElectrocardiogramTable.parse_table(recovery_table_text, header)
            else:
                if administration_table:
                    logging.warning('get_abnormal_electrocardiogram_table administration table is duplicated')
                    continue
                administration_table_text = await AbnormalElectrocardiogramTable.extract(table.content_md, header_md)
                administration_table = AbnormalElectrocardiogramTable.parse_table(administration_table_text, header)

        return (administration_table, recovery_table)
    else:
        target_table_none = ['心电图']
        add_num = len(header[0]) - 1
        target_table_none.extend(['-'] * add_num)
        administration_table = header + [target_table_none]
        recovery_table = []
        return (administration_table, recovery_table)


# 267.7 续表 - 摄食量
@broker.task
@check_doc(GeneratedField.TABLE_7_3_FOOD_INTAKE, field_type=tuple)
async def generate_table_7_3_food_intake(
    project_id: int, doc_id: int, parent_id: int, header: TableRows, header_md: str
) -> tuple[TableRows, TableRows]:  # 给药期 / 恢复期
    async with get_session() as mysql:
        doc_chunks = await DocChunk.query_v2(
            mysql, doc_id, parent_id, titles=('摄食', '食量'), columns=(DocChunk.id, DocChunk.paragraph)
        )
        filtered_doc_chunks = await DocChunk.filter_content(
            mysql, doc_id, doc_chunks, parent_id, contents=('摄食', '食量'), columns=(DocChunk.id, DocChunk.paragraph)
        )
        if filtered_doc_chunks:
            doc_chunks = filtered_doc_chunks

    administration_table: TableRows = []
    recovery_table: TableRows = []
    if doc_chunks:
        contents = ['\n'.join(json.loads(doc_chunk.paragraph)) for doc_chunk in doc_chunks]
        input_text = await BCERerank().most_relatived_content('摄食量', contents)
        return await generate_table_food_intake(input_text, header, header_md)

    # 默认返回值处理
    logging.error('TABLE_7_3_FOOD_INTAKE chunk is missing')
    fill_column_cnt = len(header[0]) - 1
    administration_table.extend(header)
    result_table = ['摄食量']
    result_table.extend(['-'] * fill_column_cnt)
    administration_table.append(result_table)
    return (administration_table, recovery_table)


# 267.7 续表 - 临床观察
@broker.task
@check_doc(GeneratedField.TABLE_7_3_CLINICAL_OBSERVATION, field_type=tuple)
async def generate_table_7_3_clinical_observation(
    project_id: int, doc_id: int, parent_id: int, header: TableRows, header_md: str
) -> tuple[TableRows, TableRows]:  # 给药期 / 恢复期
    administration_table: TableRows = []
    recovery_table: TableRows = []

    async with get_session() as mysql:
        doc_chunk = await DocChunk.query_sub(
            mysql, doc_id, parent_id, title='临床观察', columns=(DocChunk.id, DocChunk.parent_id, DocChunk.paragraph)
        )

        if doc_chunk:
            # 拿到临床观察下的表格
            table_chunk = await TableChunk.query_first(
                mysql, doc_chunk.id, columns=(TableChunk.table_title, TableChunk.content_md)
            )

            if table_chunk:
                # 如果有表格，直接找表格里的数据去生成，走新的API提取，不过拿到后后续的处理逻辑是一样的
                return await generate_clinical_observation_from_table(table_chunk.content_md, header, header_md)

    if doc_chunk:
        temp_list = json.loads(doc_chunk.paragraph)
        content = '\n'.join(temp_list)
        return await generate_table_clinical_observation(content, header, header_md)
    else:
        logging.error('TABLE_7_3_CLINICAL_OBSERVATION chunk is missing')
        fill_column_cnt = len(header[0]) - 1
        administration_table.extend(header)
        result_table = ['临床观察']
        result_table.extend(['-'] * fill_column_cnt)
        administration_table.append(result_table)
        return (administration_table, recovery_table)


# 267.7 续表 - 大体病理学
@broker.task
@check_doc(GeneratedField.TABLE_7_3_GROSS_PATHOLOGY, field_type=tuple)
async def generate_table_7_3_gross_pathology(
    project_id: int, doc_id: int, parent_id: int, header: list[list[str]], header_md: str
) -> tuple[TableRows, TableRows]:  # 给药期 / 恢复期
    async with get_session() as mysql:
        doc_chunk = await DocChunk.query_sub(
            mysql,
            doc_id,
            parent_id,
            title=('大体病理学%', '大体观察', '大体解剖'),
            columns=(DocChunk.id, DocChunk.parent_id, DocChunk.paragraph),
        )

    administration_table: TableRows = []
    recovery_table: TableRows = []
    if doc_chunk:
        temp_list = json.loads(doc_chunk.paragraph)
        content = '\n'.join(temp_list)
        return await generate_table_gross_pathology(content, header, header_md)
    else:
        logging.error('TABLE_7_3_GROSS_PATHOLOGY chunk is missing')
        fill_column_cnt = len(header[0]) - 1
        administration_table.extend(header)
        result_table = ['大体病理学']
        result_table.extend(['-'] * fill_column_cnt)
        administration_table.append(result_table)
        return (administration_table, recovery_table)


# 267.7 续表 - 濒死/死亡
@broker.task
@check_doc(GeneratedField.TABLE_7_3_NEAR_DEATH_OR_DYING, field_type=tuple)
async def generate_table_7_3_near_death_or_dying(
    project_id: int, doc_id: int, parent_id: int, header: list[list[str]], header_md: str
) -> tuple[TableRows, TableRows]:  # 给药期 / 恢复期
    async with get_session() as mysql:
        doc_chunks = await DocChunk.query_v2(
            mysql, doc_id, parent_id, titles='死亡', columns=(DocChunk.id, DocChunk.paragraph)
        )
        if doc_chunks:
            assert isinstance(doc_chunks, list)

            # 添加子章节
            sub_doc_chunks = await DocChunk.get_subs(mysql, doc_id, [doc_chunk.id for doc_chunk in doc_chunks])
            if sub_doc_chunks:
                doc_chunks.extend(sub_doc_chunks)

            filtered_doc_chunks = await DocChunk.filter_content(
                mysql, doc_id, doc_chunks, parent_id, contents='死亡', columns=(DocChunk.id, DocChunk.paragraph)
            )
            if filtered_doc_chunks:
                doc_chunks = filtered_doc_chunks

            doc_chunk_ids = [doc_chunk.id for doc_chunk in doc_chunks]
        else:
            # 获取所有结果与讨论下的子章节
            doc_chunk_ids = await DocChunk.get_sub_ids(mysql, doc_id, parent_id)
            if doc_chunk_ids:
                # 添加两层
                doc_chunk_ids = await DocChunk.extend_with_sub_ids(mysql, doc_id, doc_chunk_ids)

        # 初始化
        table_chunks = []

        if doc_chunk_ids:
            table_chunks = await TableChunk.query_v2(
                mysql,
                doc_id,
                doc_chunk_ids,
                titles='死亡',
                contents='死亡',
                columns=(TableChunk.table_title, TableChunk.content_md),
                can_ignore_doc_chunk_ids=False,  # 有太多无关表格，不允许忽略文档切片
            )

    # 解析表头数据，得到表头输出结果
    if table_chunks:
        input_text = await BCERerank().most_relatived_table_chunk('死亡', table_chunks)
        return await generate_table_near_death_or_dying('', input_text, header, header_md)
    else:
        administration_table: TableRows = []
        recovery_table: TableRows = []
        fill_column_cnt = len(header[0]) - 1
        administration_table.extend(header)
        result_table = ['濒死/死亡']
        result_table.extend(['0'] * fill_column_cnt)
        administration_table.append(result_table)
        return (administration_table, recovery_table)


# 267.7 续表 - 尿液分析
@broker.task
@check_doc(GeneratedField.TABLE_7_3_URINALYSIS, field_type=tuple)
async def generate_table_7_3_urinalysis(
    project_id: int, doc_id: int, parent_id: int, header: list[list[str]], header_md: str
) -> tuple[TableRows, TableRows]:  # 给药期 / 恢复期
    async with get_session() as mysql:
        doc_chunks = await DocChunk.query_v2(
            mysql, doc_id, parent_id, titles='尿液', columns=(DocChunk.id, DocChunk.paragraph)
        )
        filtered_doc_chunks = await DocChunk.filter_content(
            mysql, doc_id, doc_chunks, parent_id, contents='尿液', columns=(DocChunk.id, DocChunk.paragraph)
        )
        if filtered_doc_chunks:
            doc_chunks = filtered_doc_chunks

    if doc_chunks:
        contents = ['\n'.join(json.loads(doc_chunk.paragraph)) for doc_chunk in doc_chunks]
        input_text = await BCERerank().most_relatived_content('尿液分析', contents)
        return await generate_table_urinalysis(input_text, header, header_md)
    else:
        logging.error('TABLE_7_3_URINALYSIS chunk is missing')
        raise MissingDocChunkError()


# 267.7 续表 - 血凝
@broker.task
@check_doc(GeneratedField.TABLE_7_3_HEMAGGLUTINATION, field_type=tuple)
async def generate_table_7_3_hemagglutination(
    project_id: int, doc_id: int, parent_id: int, header: TableRows, header_md: str
) -> tuple[TableRows | None, TableRows | None]:  # 给药期 / 恢复期
    async with get_session() as mysql:
        doc_chunks = await DocChunk.query_v2(
            mysql, doc_id, parent_id, titles=('血凝', '凝血'), columns=(DocChunk.id, DocChunk.paragraph)
        )
        if doc_chunks:
            assert isinstance(doc_chunks, list)

            # 添加子章节
            sub_doc_chunks = await DocChunk.get_subs(mysql, doc_id, [doc_chunk.id for doc_chunk in doc_chunks])
            if sub_doc_chunks:
                doc_chunks.extend(sub_doc_chunks)

            filtered_doc_chunks = await DocChunk.filter_content(
                mysql,
                doc_id,
                doc_chunks,
                parent_id,
                contents=('血凝', '凝血'),
                columns=(DocChunk.id, DocChunk.paragraph),
            )
            if filtered_doc_chunks:
                doc_chunks = filtered_doc_chunks

            doc_chunk_ids = [doc_chunk.id for doc_chunk in doc_chunks]
        else:
            # 获取所有结果与讨论下的子章节
            doc_chunk_ids = await DocChunk.get_sub_ids(mysql, doc_id, parent_id)
            if doc_chunk_ids:
                # 添加两层
                doc_chunk_ids = await DocChunk.extend_with_sub_ids(mysql, doc_id, doc_chunk_ids)

        if doc_chunk_ids:
            tables = await TableChunk.query_v2(
                mysql,
                doc_id,
                doc_chunk_ids,
                titles=('血凝', '凝血'),
                contents=('血凝', '凝血'),
                columns=(TableChunk.table_title, TableChunk.content_md),
                can_ignore_doc_chunk_ids=False,  # 有太多无关表格，不允许忽略文档切片
            )
        else:
            raise MissingDocChunkError()

    if tables:
        administration_table: TableRows | None = None
        recovery_table: TableRows | None = None
        for table in tables:
            if '恢复期' in table.table_title:
                if recovery_table:
                    logging.warning('get_hemagglutination_table recovery table is duplicated')
                    continue
                recovery_table_text = await HemagglutinationTable.extract(table.content_md, header_md)
                recovery_table = HemagglutinationTable.parse_table(recovery_table_text, header)
            else:
                if administration_table:
                    logging.warning('get_hemagglutination_table administration table is duplicated')
                    continue
                administration_table_text = await HemagglutinationTable.extract(table.content_md, header_md)
                administration_table = HemagglutinationTable.parse_table(administration_table_text, header)
        return (administration_table, recovery_table)
    else:
        target_table_none = ['凝血']
        add_num = len(header[0]) - 1
        target_table_none.extend(['-'] * add_num)
        administration_table = header + [target_table_none]
        recovery_table = []
        return (administration_table, recovery_table)


# 8.2 遗传毒性：重复给药毒性-关键试验 - 临床病理 表格，必选。 它是由 血凝 血液学 血生化  血清电解质  尿液分析  拼接而来
@broker.task
@check_doc(GeneratedField.CLINICAL_PATHOLOGY_TABLE, field_type=tuple)
async def get_clinical_pathology_table(
    project_id: int, doc_id: int, parent_id: int, header: TableRows, header_md: str
) -> tuple[TableRows, TableRows]:  # 给药期 / 恢复期
    tasks: list[AsyncTaskiqTask] = []
    tasks.extend(
        [
            await generate_table_7_3_hemagglutination.kiq(project_id, doc_id, parent_id, header, header_md),  # 血凝
            await get_blood_biochemistry_table.kiq(project_id, doc_id, parent_id, header, header_md),  # 血生化
            await get_hematology_table.kiq(project_id, doc_id, parent_id, header, header_md),  # 血液学
            await get_serum_electrolytes_table.kiq(project_id, doc_id, parent_id, header, header_md),  # 血清电解质
            await generate_table_7_3_urinalysis.kiq(project_id, doc_id, parent_id, header, header_md),  # 尿液分析
        ]
    )

    big_administration_table: TableRows = []
    big_recovery_table: TableRows = []
    big_administration_table.extend(header)
    big_recovery_table.extend(header)
    row = ['临床病理'] * len(header[0])
    big_administration_table.append(row)
    big_recovery_table.append(row)

    results = []
    for task in tasks:
        results.append(await task.wait_result())

    for result in results:
        return_value = result.return_value
        if return_value:
            administration_table, recovery_table = return_value
            if administration_table and len(administration_table) >= 2:
                big_administration_table.extend(administration_table[2:])  # 不需要表头，下同
            if recovery_table and len(recovery_table) >= 2:
                big_recovery_table.extend(recovery_table[2:])

    # 给药期如果表格长度为3，即只有表头，则返回默认值
    fill_column_cnt = len(header[0]) - 1
    if len(big_administration_table) == 3:
        big_administration_table = []
        big_administration_table.extend(header)
        big_administration_table_value = await GenerateDefaultTable.generate(fill_column_cnt, '临床病理')
        big_administration_table.append(big_administration_table_value)
    # 恢复期如果表格长度为3，即只有表头，则返回空
    if len(big_recovery_table) == 3:
        big_recovery_table = []

    return (big_administration_table, big_recovery_table)


# 8.2 遗传毒性：重复给药毒性-关键试验 - 续表
@broker.task
@check_doc(GeneratedField.TABLE_7_3, field_type=list)
async def get_table_7_3(project_id: int, doc_id: int, header: TableRows, header_md: str) -> TableRows | None:
    async with get_session() as mysql:
        parent_id = await DocChunk.query_first(mysql, doc_id, title=('%结果%讨论%', '%结果%'), columns=col(DocChunk.id))

    administration_big_table: TableRows = []
    administration_big_table.extend(header)
    row = ['值得注意的结果'] * len(header[0])
    administration_big_table.append(row)

    recovery_big_table: TableRows = []

    if parent_id:
        tasks: list[AsyncTaskiqTask] = []
        tasks.extend(
            [
                await generate_table_7_3_near_death_or_dying.kiq(
                    project_id, doc_id, parent_id, header, header_md
                ),  # 濒死/死亡
                await get_abnormal_weight_table.kiq(project_id, doc_id, parent_id, header, header_md),  # 体重异常
                await generate_table_7_3_food_intake.kiq(project_id, doc_id, parent_id, header, header_md),  # 摄食量
                await generate_table_7_3_clinical_observation.kiq(
                    project_id, doc_id, parent_id, header, header_md
                ),  # 临床观察异常项
                await get_abnormal_body_temperature_table.kiq(
                    project_id, doc_id, parent_id, header, header_md
                ),  # 体温异常周期
                await get_abnormal_electrocardiogram_table.kiq(
                    project_id, doc_id, parent_id, header, header_md
                ),  # 心电图异常项
                await get_blood_pressure_table.kiq(project_id, doc_id, parent_id, header, header_md),  # 血压
                await get_eye_examination_table.kiq(project_id, doc_id, parent_id, header, header_md),  # 眼科检查
                await get_clinical_pathology_table.kiq(
                    project_id, doc_id, parent_id, header, header_md
                ),  # 临床病理 包括 血凝 血液学 血生化 血清电解质 尿液分析
                await get_organ_weight_table.kiq(project_id, doc_id, parent_id, header, header_md),  # 异常器官重量
                await generate_table_7_3_gross_pathology.kiq(
                    project_id, doc_id, parent_id, header, header_md
                ),  # 大体病理学
                await get_histopathology_table.kiq(project_id, doc_id, parent_id, header, header_md),  # 组织病理学
            ]
        )
        results = []
        for task in tasks:
            results.append(await task.wait_result())

        for result in results:
            return_value = result.return_value
            if return_value:
                administration_table, recovery_table = return_value
                if administration_table and len(administration_table) >= 2:
                    administration_big_table.extend(administration_table[2:])  # 不需要表头，下同
                if recovery_table and len(recovery_table) >= 2:
                    recovery_big_table.extend(recovery_table[2:])

    result_table: TableRows = []
    result_table.extend(administration_big_table)

    # 附加检查需要加一行附加检查
    row = ['附加检查'] * len(header[0])
    result_table.append(row)

    # 给药后评价不管是否有，都需要一行显示
    row = ['给药后评价'] * len(header[0])
    result_table.append(row)

    if recovery_big_table:
        result_table.extend(recovery_big_table)

    return result_table


@broker.task
@check_project('')
async def get_all_fields_of_report7_for_project(project_id: int) -> None:
    async with get_session() as mysql:
        docs = await Doc.get_by_project(
            mysql,
            project_id,
            trial_subtype=TrialSubType.REPEATED_DOSE_SPECIFICITY.value,
            columns=(Doc.id, Doc.species, Doc.administration_dosage, Doc.dosing_regimen),
        )
    if docs:
        tasks: list[AsyncTaskiqTask] = []
        for doc in docs:
            if doc.administration_dosage:
                common_header = generate_table_7_3_header(doc.administration_dosage)
                common_header_md = generate_table_7_3_header_md(common_header)
                tasks.extend(
                    [
                        await get_initial_age.kiq(project_id, doc.id),
                        await get_recovery_period.kiq(project_id, doc.id),
                        await get_date_of_first_dosage.kiq(project_id, doc.id),
                        await get_no_adverse_reaction_dosage.kiq(project_id, doc.id),
                        await generate_7_2_table.kiq(project_id, doc.id, doc.dosing_regimen, doc.administration_dosage),
                        await get_table_7_3.kiq(project_id, doc.id, common_header, common_header_md),
                    ]
                )
        for task in tasks:
            await task.wait_result()
    else:
        logging.error('repeated specificity doc is missing')
