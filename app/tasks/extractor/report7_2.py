import logging

from sqlmodel import col
from taskiq import AsyncTaskiqTask

from app.clients.mysql import get_session
from app.extractors.table_7_2 import (
    generate_table_auc_last_c_max_data,
    generate_table_header,
)
from app.models.doc import Doc
from app.models.doc_chunk import DocChunk
from app.models.generated_field import GeneratedField
from app.models.table_chunk import TableChunk
from app.schemas.doc_base_field import TrialMainType, TrialSubType
from app.schemas.table import TableRows
from app.task import broker
from app.utils.bce_rerank import BCERerank
from app.utils.exception import MissingTableChunkError

from ..check import check_doc, check_project


# 267.7主表-下半：获取表头数据（包括“日剂量”、“动物数量”两行）
@broker.task
@check_doc(GeneratedField.TABLE_7_2_HEADER, field_type=list)
async def get_table_7_2_header(project_id: int, doc_id: int, administration_dosage: str) -> TableRows | None:
    async with get_session() as mysql:
        doc_chunk_ids = await DocChunk.query_v2(
            mysql, doc_id, titles=('组别设计', '动物分组'), columns=col(DocChunk.id)
        )
        if doc_chunk_ids:
            doc_chunk_ids = await DocChunk.extend_with_sub_ids(mysql, doc_id, doc_chunk_ids)

        table_chunks = await TableChunk.query_v2(
            mysql,
            doc_id,
            doc_chunk_ids,
            titles=('试验设计', '剂量设计'),
            contents='剂量',
            columns=(TableChunk.table_title, TableChunk.content_md),
        )

    # 解析表头数据，得到表头输出结果
    if table_chunks:
        input_text = await BCERerank().most_relatived_table_chunk('给药剂量和动物数量', table_chunks)
        return await generate_table_header(input_text, administration_dosage)
    else:
        logging.error('table_7_header chunk is missing')
        raise MissingTableChunkError()


# 267.7主表-下半：获取表内数据
@broker.task
@check_doc(GeneratedField.TABLE_7_2_DATA, field_type=list)
async def get_table_7_2_data(
    project_id: int, doc_id: int, dosing_regimen: str, table_header: TableRows
) -> TableRows | None:
    async with get_session() as mysql:
        parent_id = await DocChunk.query_first(mysql, doc_id, title='%结果%讨论%', columns=col(DocChunk.id))
        if not parent_id:
            parent_id = await DocChunk.query_first(mysql, doc_id, title='%结果%', columns=col(DocChunk.id))

        doc_chunk_ids = await DocChunk.query_v2(
            mysql,
            doc_id,
            parent_id,
            titles='毒代动力学%分析',
            columns=col(DocChunk.id),
        )
        if doc_chunk_ids:
            doc_chunk_ids = await DocChunk.extend_with_sub_ids(mysql, doc_id, doc_chunk_ids)

        # 找包含表格名为 '毒代动力学参数' 或 'TK参数' 的片段
        table_chunks = await TableChunk.query_v2(
            mysql,
            doc_id,
            doc_chunk_ids,
            ('毒代动力学参数', 'TK参数'),
            ('AUClast', 'Cmax'),
            (TableChunk.table_title, TableChunk.content_md),
        )

    if table_chunks:
        input_text = await BCERerank().most_relatived_table_chunk('包含第2组的毒代动力学参数', table_chunks)
        return await generate_table_auc_last_c_max_data(input_text, dosing_regimen, table_header)

    else:
        logging.error('get_table_7_2_data chunk is missing')
        raise MissingTableChunkError()


@broker.task
@check_doc(GeneratedField.TABLE_7_2, field_type=list)
async def generate_7_2_table(
    project_id: int, doc_id: int, dosing_regimen: str, administration_dosage: str
) -> TableRows | None:
    task = await get_table_7_2_header.kiq(project_id, doc_id, administration_dosage)
    result = await task.wait_result()
    table_header = result.return_value
    task = await get_table_7_2_data.kiq(project_id, doc_id, dosing_regimen, table_header)
    result = await task.wait_result()
    return result.return_value


@broker.task
@check_project('')
async def get_all_fields_of_report7_for_project(project_id: int) -> TableRows | None:
    async with get_session() as mysql:
        docs = await Doc.get_by_project(
            mysql,
            project_id,
            trial_main_type=TrialMainType.REPEATED_DOSE.value,
            trial_subtype=TrialSubType.REPEATED_DOSE_SPECIFICITY.value,
            columns=(Doc.id, Doc.dosing_regimen, Doc.administration_dosage),
        )

    # 有多份重复给药毒性：关键试验
    if docs:
        tasks: list[AsyncTaskiqTask] = []
        for doc in docs:
            tasks.append(
                await generate_7_2_table.kiq(project_id, doc.id, doc.dosing_regimen, doc.administration_dosage),
            )

        results: TableRows = []
        for task in tasks:
            result = await task.wait_result()
            if result.return_value:
                results.append(result.return_value)

        return results

    else:
        logging.error('repeated dose doc is missing')
