import logging

from sqlmodel import col
from taskiq import AsyncTaskiqTask

from app.clients.mysql import get_session
from app.extractors.tk import MediumHeader, SmallTKTable, TKTable
from app.models.doc import Doc
from app.models.doc_chunk import DocChunk
from app.models.generated_field import GeneratedField
from app.models.table_chunk import TableChunk
from app.schemas.doc_base_field import TrialMainType
from app.schemas.table import Table
from app.task import broker
from app.utils.bce_rerank import BCERerank
from app.utils.exception import (
    GenerateError,
    GenerateFailed,
    MissingDocError,
    MissingTableChunkError,
)

from ..check import check_doc, check_project


@broker.task
@check_doc(GeneratedField.PK_TABLE)
async def get_small_pk_table(project_id: int, doc_id: int) -> str | None:
    async with get_session() as mysql:
        parent_id = await DocChunk.query_first(mysql, doc_id, title='%结果%讨论%', columns=col(DocChunk.id))
        if not parent_id:
            parent_id = await DocChunk.query_first(mysql, doc_id, title='%结果%', columns=col(DocChunk.id))

        doc_chunk_ids = await DocChunk.query_v2(
            mysql,
            doc_id,
            parent_id,
            titles='毒代动力学%分析',
            columns=col(DocChunk.id),
        )
        if doc_chunk_ids:
            doc_chunk_ids = await DocChunk.extend_with_sub_ids(mysql, doc_id, doc_chunk_ids)

        # 找包含表格名为 '毒代动力学参数' 或 'TK参数' 的片段
        table_chunks = await TableChunk.query_v2(
            mysql,
            doc_id,
            doc_chunk_ids,
            ('毒代动力学参数', 'TK参数'),
            ('AUClast', 'Cmax'),
            (TableChunk.table_title, TableChunk.content_md),
        )

    if table_chunks:
        input_text = await BCERerank().most_relatived_table_chunk('包含第2组的毒代动力学参数', table_chunks)
        print(input_text)
        return await TKTable.extract(input_text)

    logging.error('pk table chunk is missing')
    raise MissingTableChunkError()


@broker.task
@check_project(GeneratedField.PK_TABLE, field_type=list)
async def generate_pk_table(project_id: int) -> Table | None:
    async with get_session() as mysql:
        docs = await Doc.get_by_project(
            mysql,
            project_id,
            trial_main_type=TrialMainType.REPEATED_DOSE.value,
            columns=(Doc.id, Doc.species, Doc.administration_method, Doc.dosing_regimen),
        )
    if docs:
        tasks: list[AsyncTaskiqTask] = []
        for doc in docs:
            task = await get_small_pk_table.kiq(project_id, doc.id)
            tasks.append(task)

        results: list[str] = []
        for task in tasks:
            result = await task.wait_result()
            if result.is_err:
                results.append('')
            else:
                results.append(result.return_value)

        medium_headers: list[MediumHeader] = []
        small_tables: list[SmallTKTable] = []
        all_last_days: set[str] = set()
        for small_table_text, doc in zip(results, docs):
            medium_headers.append((doc.species or '', doc.administration_method or '', doc.dosing_regimen or ''))
            small_table = ([], {}, {})  # 默认值，生成失败时，使用空表
            if small_table_text and not GenerateError.is_error(small_table_text):
                try:
                    small_table, last_days = TKTable.parse_small_table(small_table_text)
                except Exception:
                    logging.exception('parse_small_table failed: %d', doc.id)
                    await GeneratedField.set_for_doc_id(doc.id, GeneratedField.PK_TABLE, GenerateFailed.message())
                else:
                    all_last_days.update(last_days)
            small_tables.append(small_table)

        return TKTable.merge_tables(medium_headers, small_tables, all_last_days)

    logging.error('doc is missing')
    raise MissingDocError()


get_all_fields_of_report3_for_project = generate_pk_table
