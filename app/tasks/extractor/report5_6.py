import json
import logging
from calendar import c

from sqlmodel import col
from taskiq import AsyncTaskiqTask

from app.clients.mysql import get_session
from app.extractors.dosage_5_6 import DosageMaxAndNotableResult, GenderAndNumberPerGroup
from app.models.doc import Doc
from app.models.doc_chunk import DocChunk
from app.models.generated_field import GeneratedField
from app.models.table_chunk import TableChunk
from app.schemas.doc_base_field import Trial<PERSON>ainType, TrialSubType
from app.task import broker
from app.utils.bce_rerank import BCERerank
from app.utils.exception import (
    MissingDocChunkError,
    MissingDocError,
    MissingFieldError,
    MissingTableChunkError,
)

from ..check import check_doc, check_project


@broker.task
@check_doc(GeneratedField.ADMINISTRATION_METHOD_SOLVENT_AND_DOSAGE_FORM)
async def get_administration_method_solvent_and_dosage_form(project_id: int, doc_id: int) -> str | None:
    async with get_session() as mysql:
        doc = await Doc.get_by_id(mysql, doc_id, columns=(Doc.solvent_and_dosage_form, Doc.administration_method))
        if not doc:
            raise MissingDocError()

    if doc.solvent_and_dosage_form and doc.administration_method:
        return f'{doc.administration_method}\n({doc.solvent_and_dosage_form})'
    else:
        logging.error('solvent_and_dosage_form or administration_method are missing')
        raise MissingFieldError()


@broker.task
@check_doc(GeneratedField.GENDER_AND_NUMBER_PER_GROUP)
async def get_gender_and_number_per_group(project_id: int, doc_id: int) -> str | None:
    async with get_session() as mysql:
        doc_chunk_ids = await DocChunk.query_v2(
            mysql,
            doc_id,
            titles=('组别', '试验设计', '实验设计'),
            columns=col(DocChunk.id),
        )
        if doc_chunk_ids:
            doc_chunk_ids = await DocChunk.extend_with_sub_ids(mysql, doc_id, doc_chunk_ids)

        table_chunks = await TableChunk.query_v2(
            mysql,
            doc_id,
            doc_chunk_ids,
            titles=('试验', '实验'),
            contents=(('雄', '雌'), ('M', 'F')),
            columns=(TableChunk.content_md, TableChunk.table_title),
        )

    if table_chunks:
        input_text = await BCERerank().most_relatived_table_chunk('分组的雄性数量和雌性数量', table_chunks)
        return await GenderAndNumberPerGroup.extract(input_text)
    else:
        logging.error('table chunk for gender_and_number_per_group is missing')
        raise MissingTableChunkError()


@broker.task
@check_doc(GeneratedField.DOSAGE_MAX, GeneratedField.NOTABLE_RESULT)
async def get_dosage_max_and_notable_result(project_id: int, doc_id: int) -> tuple[str, str] | None:
    async with get_session() as mysql:
        contents = await DocChunk.query_v2(mysql, doc_id, titles='结论', columns=col(DocChunk.paragraph))
        filtered_contents = await DocChunk.filter_content(
            mysql, doc_id, contents, contents=('最大耐受', 'NOAEL', '不良耐受'), columns=col(DocChunk.paragraph)
        )
        if filtered_contents:
            contents = filtered_contents

    if contents:
        content = await BCERerank().most_relatived_content('包含第2组的毒代动力学参数', contents)
        temp_list = json.loads(content)
        content = '\n'.join(temp_list)
        return await DosageMaxAndNotableResult.extract(content)
    else:
        logging.error('doc_chunk for dosage_max is missing')
        raise MissingDocChunkError()


@broker.task
@check_project('')
async def get_all_fields_of_report5_for_project(project_id: int) -> None:
    async with get_session() as mysql:
        docs = await Doc.get_by_project(
            mysql,
            project_id,
            trial_main_type=TrialMainType.SINGLE_DOSE.value,
            columns=(Doc.id, Doc.species, Doc.administration_method, Doc.dosing_regimen),
        )
    if docs:
        tasks: list[AsyncTaskiqTask] = []
        for doc in docs:
            temp_tasks = [
                await get_administration_method_solvent_and_dosage_form.kiq(project_id, doc.id),
                await get_gender_and_number_per_group.kiq(project_id, doc.id),
                await get_dosage_max_and_notable_result.kiq(project_id, doc.id),
            ]
            tasks.extend(temp_tasks)
        for task in tasks:
            await task.wait_result()
    else:
        logging.error('single dose doc is missing')


@broker.task
@check_project('')
async def get_all_fields_of_report6_for_project(project_id: int) -> None:
    async with get_session() as mysql:
        docs = await Doc.get_by_project(
            mysql,
            project_id,
            trial_subtype=TrialSubType.REPEATED_DOSE.value,
            columns=(Doc.id, Doc.species, Doc.administration_method, Doc.dosing_regimen),
        )
    if docs:
        tasks: list[AsyncTaskiqTask] = []
        for doc in docs:
            tasks.extend(
                [
                    await get_administration_method_solvent_and_dosage_form.kiq(project_id, doc.id),
                    await get_gender_and_number_per_group.kiq(project_id, doc.id),
                    await get_dosage_max_and_notable_result.kiq(project_id, doc.id),
                ]
            )
        for task in tasks:
            await task.wait_result()
    else:
        logging.error('repeated dose doc is missing')
