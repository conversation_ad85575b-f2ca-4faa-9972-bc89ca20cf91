FROM purefda/ind267-backend


FROM debian:bookworm-slim

RUN apt-get update \
    && apt-get install -y --no-install-recommends ca-certificates \
    && apt-get autoclean \
    && rm -rf /var/lib/apt/lists/*

COPY --from=0 /usr/local/bin/python /usr/local/bin/
# 这个文件和 Python 版本相关
COPY --from=0 /usr/local/lib/libpython3.12.so.1.0 /usr/local/lib/
# 这些文件是 Python 的依赖
COPY --from=0 /usr/lib/aarch64-linux-gnu/libssl.so.3 /usr/lib/aarch64-linux-gnu/
COPY --from=0 /usr/lib/aarch64-linux-gnu/libcrypto.so.3 /usr/lib/aarch64-linux-gnu/
COPY --from=0 /usr/lib/aarch64-linux-gnu/libexpat.so.1 /usr/lib/aarch64-linux-gnu/
# 这是 uvicorn，可能会升级
COPY --from=0 /usr/local/bin/uvicorn /usr/local/bin/
# 这些是 Python 的标准库和第三方库，可能会升级或变化
COPY --from=0 /usr/local/lib/python3.12/ /usr/local/lib/python3.12/
COPY app /app

CMD ["uvicorn", "--host", "0.0.0.0", "app.main:app"]
