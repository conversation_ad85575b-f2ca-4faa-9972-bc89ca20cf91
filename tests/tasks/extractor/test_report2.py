import pytest
from fastapi.testclient import TestClient

from app.main import app
from app.schemas.doc_base_field import TrialSubType
from app.tasks.extractor.base_field import parse_cover
from app.tasks.extractor.report2 import get_experiment_type


@pytest.mark.asyncio(loop_scope='session')
async def test_get_experiment_type():
    with TestClient(app):  # 触发 lifespan
        # A-230611
        await parse_cover(405, 1867)
        assert (
            await get_experiment_type(405, 1867, TrialSubType.SINGLE_DOSE) == '急性毒理学'
        )  # TODO: 'Sprague Dawley大鼠单次经口灌胃给予XXX的急性毒理学试验'

        # A-230612
        await parse_cover(405, 1868)
        assert (
            await get_experiment_type(405, 1868, TrialSubType.SINGLE_DOSE) == '急性毒理学'
        )  # TODO: '比格犬单次经口灌胃给予XXX的急性毒理学试验'

        # A-231609
        await parse_cover(405, 1869)
        assert (
            await get_experiment_type(405, 1869, TrialSubType.SINGLE_DOSE) == '最大耐受量'
        )  # TODO: 'Sprague Dawley大鼠经口灌胃给予新药001的最大耐受量试验'

        # AB-231646
        await parse_cover(405, 1870)
        assert (
            await get_experiment_type(405, 1870, TrialSubType.REPEATED_DOSE_SPECIFICITY)
            == '最大耐受量和14天剂量范围探索'
        )

        # B-3D3211
        await parse_cover(405, 1871)
        assert await get_experiment_type(405, 1871, TrialSubType.REPEATED_DOSE) == '28天剂量范围确定'

        # B-3R3248
        await parse_cover(405, 1872)
        assert await get_experiment_type(405, 1872, TrialSubType.REPEATED_DOSE) == '28天剂量范围确定'

        # B-23238
        await parse_cover(405, 1873)
        assert await get_experiment_type(405, 1873, TrialSubType.REPEATED_DOSE) == '14天剂量范围确定'

        # C-230613
        await parse_cover(405, 1874)
        assert await get_experiment_type(405, 1874, TrialSubType.REPEATED_DOSE) == '连续28天及恢复28天'

        # C-232003
        await parse_cover(405, 1875)
        assert await get_experiment_type(405, 1875, TrialSubType.REPEATED_DOSE) == '连续28天及恢复28天'

        # C-230614
        await parse_cover(405, 1876)
        assert await get_experiment_type(405, 1876, TrialSubType.REPEATED_DOSE) == '连续28天及恢复28天'

        # C-231610
        await parse_cover(405, 1877)
        assert await get_experiment_type(405, 1877, TrialSubType.REPEATED_DOSE) == '连续28天及恢复28天'

        # D-231925
        await parse_cover(405, 1878)
        assert await get_experiment_type(405, 1878, TrialSubType.AMES_TEST) == '回复突变'

        # E-230783
        await parse_cover(405, 1879)
        assert await get_experiment_type(405, 1879, TrialSubType.CHROMOSOME_ABERRATION_TEST) == '染色体畸变'

        # F-230784
        await parse_cover(405, 1880)
        assert await get_experiment_type(405, 1880, TrialSubType.MICRONUCLEUS_TEST) == '微核'

        # E-231924
        await parse_cover(406, 1881)
        assert await get_experiment_type(406, 1881, TrialSubType.CHROMOSOME_ABERRATION_TEST) == '染色体畸变'

        # F-231926
        await parse_cover(406, 1882)
        assert (
            await get_experiment_type(406, 1882, TrialSubType.MICRONUCLEUS_TEST) == '微核'
        )  # TODO: 'Sprague Dawley大鼠经口灌胃给予新药001连续2天的微核试验'
