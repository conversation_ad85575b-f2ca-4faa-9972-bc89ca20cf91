import pytest
from fastapi.testclient import TestClient

from app.main import app
from app.tasks.extractor.report7_2 import (
    get_all_fields_of_report7_for_project,
    get_table_7_2_data,
    get_table_7_2_header,
)


# 267.7主表-下半：获取表头数据，主要包括“日剂量”、“动物数量”两行
@pytest.mark.asyncio(loop_scope='session')
async def test_get_table_7_2_header():
    with TestClient(app):
        assert await get_table_7_2_header(462, 2075, '雄性：5、15、75 mg/kg/day \n雌性：2.5、7.5、50 mg/kg/day') == [
            ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
            ['动物数量', 'M:4', 'F:4', 'M:4', 'F:4', 'M:4', 'F:4', 'M:4', 'F:4'],
        ]

        assert await get_table_7_2_header(463, 2076, '1.5、5、15 mg/kg/day') == [
            ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'],
            ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
        ]


# 267.7主表-下半：获取表内数据
@pytest.mark.asyncio(loop_scope='session')
async def test_get_table_7_2_data():
    with TestClient(app):
        assert await get_table_7_2_data(
            462,
            2075,
            '28天',
            [
                ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                ['动物数量', 'M:4', 'F:4', 'M:4', 'F:4', 'M:4', 'F:4', 'M:4', 'F:4'],
            ],
        ) == [
            ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
            ['动物数量', 'M:4', 'F:4', 'M:4', 'F:4', 'M:4', 'F:4', 'M:4', 'F:4'],
            [
                '毒代动力学AUClast(hr*ng/ml)',
                '毒代动力学AUClast(hr*ng/ml)',
                '毒代动力学AUClast(hr*ng/ml)',
                '毒代动力学AUClast(hr*ng/ml)',
                '毒代动力学AUClast(hr*ng/ml)',
                '毒代动力学AUClast(hr*ng/ml)',
                '毒代动力学AUClast(hr*ng/ml)',
                '毒代动力学AUClast(hr*ng/ml)',
                '毒代动力学AUClast(hr*ng/ml)',
            ],
            ['第1天', '-', '-', '517794', '416645', '1492946', '1379860', '2692723', '3926221'],
            ['第28天', '-', '-', '1291721', '4027396', '3264410', '6625633', '2884819', '6920757'],
            [
                '毒代动力学Cmax(ng/ml)',
                '毒代动力学Cmax(ng/ml)',
                '毒代动力学Cmax(ng/ml)',
                '毒代动力学Cmax(ng/ml)',
                '毒代动力学Cmax(ng/ml)',
                '毒代动力学Cmax(ng/ml)',
                '毒代动力学Cmax(ng/ml)',
                '毒代动力学Cmax(ng/ml)',
                '毒代动力学Cmax(ng/ml)',
            ],
            ['第1天', '-', '-', '28625', '19400', '127000', '112925', '146750', '189250'],
            ['第28天', '-', '-', '44850', '78500', '116075', '155500', '154750', '179000'],
        ]

        assert await get_table_7_2_data(
            463,
            2076,
            '28天',
            [
                ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'],
                ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
            ],
        ) == [
            ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'],
            ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
            [
                '毒代动力学AUClast(hr*ng/mL)',
                '毒代动力学AUClast(hr*ng/mL)',
                '毒代动力学AUClast(hr*ng/mL)',
                '毒代动力学AUClast(hr*ng/mL)',
                '毒代动力学AUClast(hr*ng/mL)',
                '毒代动力学AUClast(hr*ng/mL)',
                '毒代动力学AUClast(hr*ng/mL)',
                '毒代动力学AUClast(hr*ng/mL)',
                '毒代动力学AUClast(hr*ng/mL)',
            ],
            ['第1天', '-', '-', '7539', '11670', '38835', '37662', '190826', '163700'],
            ['第28天', '-', '-', '6854', '9386', '34451', '32532', '216157', '169215'],
            [
                '毒代动力学Cmax(ng/mL)',
                '毒代动力学Cmax(ng/mL)',
                '毒代动力学Cmax(ng/mL)',
                '毒代动力学Cmax(ng/mL)',
                '毒代动力学Cmax(ng/mL)',
                '毒代动力学Cmax(ng/mL)',
                '毒代动力学Cmax(ng/mL)',
                '毒代动力学Cmax(ng/mL)',
                '毒代动力学Cmax(ng/mL)',
            ],
            ['第1天', '-', '-', '1578', '2118', '6476', '7252', '22480', '18660'],
            ['第28天', '-', '-', '1618', '2038', '5112', '6264', '21500', '22040'],
        ]


@pytest.mark.asyncio(loop_scope='session')
async def test_get_all_fields_of_report7_for_project():
    with TestClient(app):
        assert await get_all_fields_of_report7_for_project(462) == [
            [
                ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                ['动物数量', 'M:4', 'F:4', 'M:4', 'F:4', 'M:4', 'F:4', 'M:4', 'F:4'],
                [
                    '毒代动力学AUClast(hr*ng/ml)',
                    '毒代动力学AUClast(hr*ng/ml)',
                    '毒代动力学AUClast(hr*ng/ml)',
                    '毒代动力学AUClast(hr*ng/ml)',
                    '毒代动力学AUClast(hr*ng/ml)',
                    '毒代动力学AUClast(hr*ng/ml)',
                    '毒代动力学AUClast(hr*ng/ml)',
                    '毒代动力学AUClast(hr*ng/ml)',
                    '毒代动力学AUClast(hr*ng/ml)',
                ],
                ['第1天', '-', '-', '517794', '416645', '1492946', '1379860', '2692723', '3926221'],
                ['第28天', '-', '-', '1291721', '4027396', '3264410', '6625633', '2884819', '6920757'],
                [
                    '毒代动力学Cmax(ng/ml)',
                    '毒代动力学Cmax(ng/ml)',
                    '毒代动力学Cmax(ng/ml)',
                    '毒代动力学Cmax(ng/ml)',
                    '毒代动力学Cmax(ng/ml)',
                    '毒代动力学Cmax(ng/ml)',
                    '毒代动力学Cmax(ng/ml)',
                    '毒代动力学Cmax(ng/ml)',
                    '毒代动力学Cmax(ng/ml)',
                ],
                ['第1天', '-', '-', '28625', '19400', '127000', '112925', '146750', '189250'],
                ['第28天', '-', '-', '44850', '78500', '116075', '155500', '154750', '179000'],
            ],
        ]
