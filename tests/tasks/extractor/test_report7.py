import pytest
from fastapi.testclient import TestClient

from app.main import app
from app.tasks.extractor.report7 import (
    generate_table_7_3_header,
    generate_table_7_3_header_md,
    get_abnormal_body_temperature_table,
    get_abnormal_electrocardiogram_table,
    get_abnormal_weight_table,
    get_blood_biochemistry_table,
    get_blood_pressure_table,
    get_clinical_pathology_table,
    get_date_of_first_dosage,
    get_eye_examination_table,
    get_hematology_table,
    get_histopathology_table,
    get_initial_age,
    get_no_adverse_reaction_dosage,
    get_organ_weight_table,
    get_recovery_period,
    get_serum_electrolytes_table,
    get_table_7_3,
)


@pytest.mark.asyncio(loop_scope='session')
async def test_get_initial_age():
    with TestClient(app):  # 触发 lifespan 以关闭 redis
        # 重复给药关键: 4232-************, project_id: 462 , doc_id: 2075
        assert await get_initial_age(462, 2075) == '7-8周'

        # 重复给药关键: 4232-************, project_id: 172 , doc_id: 903
        assert await get_initial_age(463, 2076) == '6.9-7.5个月'


@pytest.mark.asyncio(loop_scope='session')
async def test_get_recovery_period():
    with TestClient(app):  # 触发 lifespan 以关闭 redis
        # 重复给药关键: 4232-************, project_id: 172 , doc_id: 902
        assert await get_recovery_period(902, 2075) == '28天'

        # 重复给药关键: 4232-************, project_id: 172 , doc_id: 903
        assert await get_recovery_period(903, 2076) == '28天'


@pytest.mark.asyncio(loop_scope='session')
async def test_get_date_of_first_dosage():
    with TestClient(app):  # 触发 lifespan 以关闭 redis
        # 重复给药关键: 4232-************, project_id: 172 , doc_id: 902
        assert (
            await get_date_of_first_dosage(905, 2075)
            == '2023年12月13日（主试验雄性和所有TK动物）2023年12月14日（主试验雌性）'
        )

        # 重复给药关键: 4232-************, project_id: 172 , doc_id: 903
        assert await get_date_of_first_dosage(906, 2076) == '2023年12月21日（雄性动物）\n2023年12月22日（雌性动物）'


@pytest.mark.asyncio(loop_scope='session')
async def test_get_no_adverse_reaction_dosage():
    with TestClient(app):  # 触发 lifespan 以关闭 redis
        # 重复给药关键: 4232-************, project_id: 172 , doc_id: 902
        assert await get_no_adverse_reaction_dosage(905, 2075) == '75 mg/kg/day（雄性）和50 mg/kg/day（雌性）'

        # 重复给药关键: 4232-************, project_id: 172 , doc_id: 903
        assert await get_no_adverse_reaction_dosage(906, 2076) == '15 mg/kg/day'


@pytest.mark.asyncio(loop_scope='session')
async def test_get_abnormal_body_temperature_table():
    with TestClient(app):  # 触发 lifespan 以关闭 redis
        # 重复给药关键: 4232-************, project_id: 172 , doc_id: 903, 这个是没有任何数据的
        assert (
                await get_abnormal_body_temperature_table(
                    172,
                    903,
                    39027,
                    [
                        ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'],
                        ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ],
                    '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 1.5 | 1.5 | 5 | 5 | 15 | 15 |
    | 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
                ) is None
        )


@pytest.mark.asyncio(loop_scope='session')
async def test_get_eye_examination_table():
    with TestClient(app):  # 触发 lifespan 以关闭 redis
        # 重复给药关键: 4232-************, project_id: 462 , doc_id: 2075
        assert (
            await get_eye_examination_table(
                462,
                2075,
                58986,
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
        | 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ['眼科检查', '-', '-', '-', '-', '-', '-', '-', '-'],
                ],
                [],
            )
        )

        # 重复给药关键: 4232-************, project_id: 463 , doc_id: 2076
        assert (
            await get_eye_examination_table(
                463,
                2076,
                59058,
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 1.5 | 1.5 | 5 | 5 | 15 | 15 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ['眼科检查', '-', '-', '-', '-', '-', '-', '-', '-'],
                ],
                [],
            )
        )


@pytest.mark.asyncio(loop_scope='session')
async def test_get_blood_pressure_table():
    with TestClient(app):  # 触发 lifespan 以关闭 redis
        # 重复给药关键: 4232-************, project_id: 462 , doc_id: 2075无血压数据

        # 重复给药关键: 4232-************, project_id: 172 , doc_id: 903
        assert (
            await get_blood_pressure_table(
                463,
                2076,
                59058,
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 1.5 | 1.5 | 5 | 5 | 15 | 15 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    [
                        '血压',
                        '-',
                        '-',
                        '-',
                        '-',
                        '-',
                        '-',
                        '-',
                        '-',
                    ],
                ],
                [],
            )
        )


@pytest.mark.asyncio(loop_scope='session')
async def test_generate_table_7_3_header():
    with TestClient(app):  # 触发 lifespan 以关闭 redis
        # 重复给药关键: 4232-************, project_id: 462 , doc_id: 2075
        assert (
            generate_table_7_3_header(
                '''雄性：5、15、75 mg/kg/day
雌性：2.5、7.5、50 mg/kg/day'''
            )
            == [
                ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
            ]
        )

        header2 = [
            ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'],
            ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
        ]
        # 重复给药关键: 4232-************, project_id: 463 , doc_id: 2076

        assert generate_table_7_3_header('''1.5、5、15 mg/kg/day''') == [
            ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'],
            ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
        ]


@pytest.mark.asyncio(loop_scope='session')
async def test_generate_table_7_3_header_md():
    with TestClient(app):  # 触发 lifespan 以关闭 redis
        # 重复给药关键: 4232-************, project_id: 462 , doc_id: 2075
        assert (
            generate_table_7_3_header_md(
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ]
            )
            == '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |'''
        )

        # 重复给药关键: 4232-************, project_id: 463 , doc_id: 2076
        assert (
            generate_table_7_3_header_md(
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ]
            )
            == '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 1.5 | 1.5 | 5 | 5 | 15 | 15 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |'''
        )


@pytest.mark.asyncio(loop_scope='session')
async def test_get_abnormal_weight_table():
    with TestClient(app):  # 触发 lifespan 以关闭 redis
        # 重复给药关键: ************, project_id: 351 , doc_id: 1725
        assert (
            await get_abnormal_weight_table(
                351,
                1725,
                37626,
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '2', '2', '3', '3', '5', '5'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 2 | 2 | 3 | 3 | 5 | 5 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '2', '2', '3', '3', '5', '5'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    [
                        '体重和体重变化',
                        '体重和体重变化',
                        '体重和体重变化',
                        '体重和体重变化',
                        '体重和体重变化',
                        '体重和体重变化',
                        '体重和体重变化',
                        '体重和体重变化',
                        '体重和体重变化',
                    ],
                    [
                        'Days 1-8 (g)\n（%体重变化百分比）',
                        '60',
                        '13.17',
                        '50.28**\n16.2%↓',
                        '19.91\n51.11%↑',
                        '47.87***\n20.22%↓',
                        '17.5\n32.84%↑',
                        '45.43***\n24.29%↓',
                        '11.47\n12.9%↓',
                    ],
                ],
                [],
            )
        )

        # 重复给药关键: 4232-************, project_id: 280 , doc_id: 1583
        assert (
            await get_abnormal_weight_table(
                280,
                1583,
                29609,
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
        | 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    [
                        '体重和体重变化',
                        '体重和体重变化',
                        '体重和体重变化',
                        '体重和体重变化',
                        '体重和体重变化',
                        '体重和体重变化',
                        '体重和体重变化',
                        '体重和体重变化',
                        '体重和体重变化',
                    ],
                    [
                        'Days 1-8 (g)\n（%体重变化百分比）',
                        '53.14',
                        '16.41',
                        '55.69\n4.80%↑',
                        '21.77\n32.67%↑',
                        '50.97\n4.08%↓',
                        '14.29\n12.92%↓',
                        '49.73\n6.41%↓',
                        '13.35\n18.65%↓',
                    ],
                    [
                        'Days 8-15 (g)\n（%体重变化百分比）',
                        '50.45',
                        '17.85',
                        '47.67\n5.5%↓',
                        '15.28\n14.4%↓',
                        '46.87\n7.2%↓',
                        '22.68\n27.06%↑',
                        '49.35\n2.2%↓',
                        '20.60\n15.41%↑',
                    ],
                ],
                [],
            )
        )

        # 重复给药关键: 4232-************, project_id: 280 , doc_id: 1577

        assert (
            await get_abnormal_weight_table(
                280,
                1577,
                29226,
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 1.5 | 1.5 | 5 | 5 | 15 | 15 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    [
                        '体重和体重变化',
                        '体重和体重变化',
                        '体重和体重变化',
                        '体重和体重变化',
                        '体重和体重变化',
                        '体重和体重变化',
                        '体重和体重变化',
                        '体重和体重变化',
                        '体重和体重变化',
                    ],
                    [
                        'Day28\n（%体重变化百分比）',
                        '9.228',
                        '8.132',
                        '9.426\n7.99%',
                        '7.636\n1.71%',
                        '9.586\n11.42%',
                        '8.140\n5.38%',
                        '8.818\n1.78%',
                        '8.170\n4.56%',
                    ],
                ],
                [],
            )
        )


@pytest.mark.asyncio(loop_scope='session')
async def test_get_blood_biochemistry_table():
    with TestClient(app):  # 触发 lifespan 以关闭 redis
        # 重复给药关键: 4232-************, project_id: 462 , doc_id: 2075
        assert (
            await get_blood_biochemistry_table(
                462,
                2075,
                58995,
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    [
                        '血生化参数',
                        '血生化参数',
                        '血生化参数',
                        '血生化参数',
                        '血生化参数',
                        '血生化参数',
                        '血生化参数',
                        '血生化参数',
                        '血生化参数',
                    ],
                    [
                        'ALB（g/L）\n（%变化百分比）',
                        '46.81',
                        '52.2',
                        '46.82\n（0%）',
                        '50.72\n（3%↓）',
                        '46.53\n（1%↓）',
                        '49.13*\n（6%↓）',
                        '46.93\n（0%）',
                        '45.94***\n（12%↓）',
                    ],
                    [
                        'GLO （g/L）\n（%变化百分比）',
                        '17.04',
                        '14.4',
                        '16.24\n（5%↓）',
                        '14.54\n（1%↑）',
                        '16.75\n（2%↓）',
                        '14.87\n（3%↑）',
                        '18.84**\n（11%↑）',
                        '17.14***\n（19%↑）',
                    ],
                    [
                        'A/G （Ratio）\n（%变化百分比）',
                        '2.764',
                        '3.645',
                        '2.896\n（5%↑）',
                        '3.51\n（4%↓）',
                        '2.789\n（1%↑）',
                        '3.329\n（9%↓）',
                        '2.503*\n（9%↓）',
                        '2.696***\n（26%↓）',
                    ],
                ],
                [],
            )
        )

        # 重复给药关键: 4232-************, project_id: 463 , doc_id: 2076
        assert (
            await get_blood_biochemistry_table(
                463,
                2076,
                59070,
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 1.5 | 1.5 | 5 | 5 | 15 | 15 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    [
                        '血生化参数',
                        '血生化参数',
                        '血生化参数',
                        '血生化参数',
                        '血生化参数',
                        '血生化参数',
                        '血生化参数',
                        '血生化参数',
                        '血生化参数',
                    ],
                    [
                        'Na（mmol/L）\n（%变化百分比）',
                        '147.22',
                        '146.14',
                        '146.46\n（1%↓）',
                        '147.04\n（1%↑）',
                        '145.84\n（1%↓）',
                        '145.86\n（0%）',
                        '141.84\n***\n（4%↓）',
                        '144.66\n（1%↓）',
                    ],
                    [
                        'Cl (mmol/L)\n（%变化百分比）',
                        '108.2',
                        '107.86',
                        '108.68\n（0%）',
                        '109.12\n（1%↑）',
                        '108.4\n（0%）',
                        '108.16\n（0%）',
                        '103.72\n**\n（4%↓）',
                        '106.1\n（2%↓）',
                    ],
                ],
                [],
            )
        )


@pytest.mark.asyncio(loop_scope='session')
async def test_get_hematology_table():
    with TestClient(app):  # 触发 lifespan 以关闭 redis
        # 重复给药关键: 4232-************, project_id: 462 , doc_id: 2075
        assert (
            await get_hematology_table(
                462,
                2075,
                58995,
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    [
                        '血液学参数',
                        '血液学参数',
                        '血液学参数',
                        '血液学参数',
                        '血液学参数',
                        '血液学参数',
                        '血液学参数',
                        '血液学参数',
                        '血液学参数',
                    ],
                    ['RBC（10^12/L）',
                     '8.25',
                     '7.927',
                     '8.3',
                     '7.705',
                     '8.219',
                     '7.609',
                     '7.97',
                     '7.116***'],
                    ['(% 差值)', '-', '-', '+1', '-3', '0', '-4', '-3', '-10'],
                    ['HGB (g/dL)',
                     '16.48',
                     '15.19',
                     '16.31',
                     '15.16',
                     '16.07',
                     '14.97',
                     '15.73',
                     '14.3**'],
                    ['(% 差值)', '-', '-', '-1', '0', '-2', '-1', '-5', '-6']
                ],
                [],
            )
        )

        assert (
                await get_hematology_table(
                    482,
                    2138,
                    62825,
                    [
                        ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                        ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ],
                    '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
    | 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
                )
                == (
                    [
                        ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                        ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                        ['血液学参数', '血液学参数', '血液学参数', '血液学参数', '血液学参数', '血液学参数', '血液学参数', '血液学参数', '血液学参数'],
                        ['RBC（10^12/L）', 'RBC（10^12/L）', 'RBC（10^12/L）', 'RBC（10^12/L）', 'RBC（10^12/L）', 'RBC（10^12/L）', 'RBC（10^12/L）', 'RBC（10^12/L）', 'RBC（10^12/L）'],
                        ['Day -10a/-11', '5.602', '5.898', '5.948', '5.362', '5.708', '5.374', '5.468', '5.398'],
                        ['Day -3a', '5.326', '5.520', '5.726', '4.998', '5.332', '5.060', '5.356', '5.164'],
                        ['Day 29', '5.160', '5.392', '5.092', '4.568**', '4.756', '4.378**', '4.728', '4.344**'],
                        ['（% 差值）', '–', '–', '-1', '-15', '-8', '-19', '-8', '-19'],
                        ['HCT<br>（%）', 'HCT<br>（%）', 'HCT<br>（%）', 'HCT<br>（%）', 'HCT<br>（%）', 'HCT<br>（%）', 'HCT<br>（%）', 'HCT<br>（%）', 'HCT<br>（%）'],
                        ['Day -10a/-11', '42.72', '44.42', '44.78', '41.50', '43.64', '41.40', '41.90', '41.36'],
                        ['Day -3a', '40.24', '41.70', '43.08', '38.72', '40.94', '38.78', '40.84', '39.18'],
                        ['Day 29', '39.26', '41.08', '38.78', '35.68**', '36.82', '34.22***', '36.60', '33.94***'],
                        ['（% 差值）', '–', '–', '-1', '-13', '-6', '-17', '-7', '-17'],
                        ['HGB（g/dL）', 'HGB（g/dL）', 'HGB（g/dL）', 'HGB（g/dL）', 'HGB（g/dL）', 'HGB（g/dL）', 'HGB（g/dL）', 'HGB（g/dL）', 'HGB（g/dL）'],
                        ['Day -10a/-11', '13.34', '13.74', '14.02', '12.80', '13.80', '12.82', '13.06', '12.80'],
                        ['Day -3a', '12.66', '12.94', '13.34', '12.00', '12.86', '12.06', '12.86', '12.34'],
                        ['Day 29', '12.30', '12.80', '12.14', '11.14*', '11.58', '10.62**', '11.54', '10.60**'],
                        ['（% 差值）', '–', '–', '-1', '-13', '-6', '-17', '-6', '-17']
                    ],
                    [],
                )
        )

        # 重复给药关键: 4232-************, project_id: 463 , doc_id: 2076
        assert (
                await get_serum_electrolytes_table(
                    463,
                    2076,
                    59070,
                    [
                        ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'],
                        ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ],
                    '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 1.5 | 1.5 | 5 | 5 | 15 | 15 |
    | 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
                ) is None
        )


@pytest.mark.asyncio(loop_scope='session')
async def test_get_serum_electrolytes_table():
    with TestClient(app):  # 触发 lifespan 以关闭 redis
        # 重复给药关键: 4232-************, project_id: 462 , doc_id: 2075
        assert (
                await get_serum_electrolytes_table(
                    462,
                    2075,
                    39143,
                    [
                        ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                        ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ],
                    '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
    | 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
                ) is None
        )

        # 重复给药关键: 4232-************, project_id: 463 , doc_id: 2076
        assert (
                await get_serum_electrolytes_table(
                    172,
                    903,
                    39027,
                    [
                        ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'],
                        ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ],
                    '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 1.5 | 1.5 | 5 | 5 | 15 | 15 |
    | 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
                ) is None
        )


@pytest.mark.asyncio(loop_scope='session')
async def test_get_organ_weight_table():
    with TestClient(app):  # 触发 lifespan 以关闭 redis
        # 重复给药关键: 4232-************, project_id: 462 , doc_id: 2075
        assert (
            await get_organ_weight_table(
                462,
                2075,
                58986,
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    [
                        '器官重量',
                        '器官重量',
                        '器官重量',
                        '器官重量',
                        '器官重量',
                        '器官重量',
                        '器官重量',
                        '器官重量',
                        '器官重量',
                    ],
                    [' 肝脏 ', ' 肝脏 ', ' 肝脏 ', ' 肝脏 ', ' 肝脏 ', ' 肝脏 ', ' 肝脏 ', ' 肝脏 ', ' 肝脏 '],
                    [
                        '脏器重量（g）\n（%变化百分比）',
                        '12.6931',
                        '7.2643',
                        '12.6014\n（1%↓）',
                        '7.9534\n（9%↑）',
                        '12.4500\n（2%↓）',
                        '8.3677*\n（15%↑）',
                        '14.6611**\n（16%↑）',
                        '9.4006***\n（29%↑）',
                    ],
                    [
                        '脏体比（%）\n（%变化百分比）',
                        '3.0781',
                        '3.0248',
                        '3.0022\n（2%↓）',
                        '3.1041\n（3%↑）',
                        '2.9971\n（3%↓）',
                        '3.3305**\n（10%↑）',
                        '3.5400***\n（15%↑）',
                        '3.8423***\n（27%↑）',
                    ],
                    [
                        '脏脑比（%）\n（%变化百分比）',
                        '600.75',
                        '369.603',
                        '598.457\n（0%）',
                        '407.107\n（10%↑）',
                        '585.512\n（3%↓）',
                        '422.406*\n（14%↑）',
                        '701.401**\n（17%↑）',
                        '486.713***\n（32%↑）',
                    ],
                ],
                [],
            )
        )

        # 重复给药关键: 4232-************, project_id: 463 , doc_id: 2076
        assert (
            await get_organ_weight_table(
                463,
                2076,
                59058,
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 1.5 | 1.5 | 5 | 5 | 15 | 15 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ['器官重量', '-', '-', '-', '-', '-', '-', '-', '-'],
                ],
                [],
            )
        )


@pytest.mark.asyncio(loop_scope='session')
async def test_get_histopathology_table():
    with TestClient(app):  # 触发 lifespan 以关闭 redis
        # 重复给药关键: 4232-************-10, project_id: 462 , doc_id: 1075
        assert (
            await get_histopathology_table(
                462,
                2075,
                58986,
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
        | 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    [
                        '组织病理学',
                        '组织病理学',
                        '组织病理学',
                        '组织病理学',
                        '组织病理学',
                        '组织病理学',
                        '组织病理学',
                        '组织病理学',
                        '组织病理学',
                    ],
                    ['肾上腺', '肾上腺', '肾上腺', '肾上腺', '肾上腺', '肾上腺', '肾上腺', '肾上腺', '肾上腺'],
                    [
                        '双侧皮质肥大',
                        '双侧皮质肥大',
                        '双侧皮质肥大',
                        '双侧皮质肥大',
                        '双侧皮质肥大',
                        '双侧皮质肥大',
                        '双侧皮质肥大',
                        '双侧皮质肥大',
                        '双侧皮质肥大',
                    ],
                    ['轻微', '-', '-', '-', '-', '-', '-', '10/10', '10/10'],
                    ['肝', '肝', '肝', '肝', '肝', '肝', '肝', '肝', '肝'],
                    [
                        '小叶中央肝细胞肥大',
                        '小叶中央肝细胞肥大',
                        '小叶中央肝细胞肥大',
                        '小叶中央肝细胞肥大',
                        '小叶中央肝细胞肥大',
                        '小叶中央肝细胞肥大',
                        '小叶中央肝细胞肥大',
                        '小叶中央肝细胞肥大',
                        '小叶中央肝细胞肥大',
                    ],
                    ['轻微', '-', '-', '-', '-', '-', '-', '3/10', '4/10'],
                ],
                [],
            )
        )

        # 重复给药关键: 4232-************, project_id: 463 , doc_id: 2076
        assert (
            await get_histopathology_table(
                463,
                2076,
                59058,
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 1.5 | 1.5 | 5 | 5 | 15 | 15 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    [
                        '组织病理学',
                        '组织病理学',
                        '组织病理学',
                        '组织病理学',
                        '组织病理学',
                        '组织病理学',
                        '组织病理学',
                        '组织病理学',
                        '组织病理学',
                    ],
                    ['肾上腺', '肾上腺', '肾上腺', '肾上腺', '肾上腺', '肾上腺', '肾上腺', '肾上腺', '肾上腺'],
                    [
                        '双侧皮质球状带肥大',
                        '双侧皮质球状带肥大',
                        '双侧皮质球状带肥大',
                        '双侧皮质球状带肥大',
                        '双侧皮质球状带肥大',
                        '双侧皮质球状带肥大',
                        '双侧皮质球状带肥大',
                        '双侧皮质球状带肥大',
                        '双侧皮质球状带肥大',
                    ],
                    ['轻微', '-', '-', '-', '-', '1/3', '-', '2/3', '3/3'],
                    ['轻度', '-', '-', '-', '-', '-', '-', '1/3', '-'],
                    ['胃', '胃', '胃', '胃', '胃', '胃', '胃', '胃', '胃'],
                    [
                        '胃底腺黏液细胞肥大',
                        '胃底腺黏液细胞肥大',
                        '胃底腺黏液细胞肥大',
                        '胃底腺黏液细胞肥大',
                        '胃底腺黏液细胞肥大',
                        '胃底腺黏液细胞肥大',
                        '胃底腺黏液细胞肥大',
                        '胃底腺黏液细胞肥大',
                        '胃底腺黏液细胞肥大',
                    ],
                    ['轻微', '-', '-', '-', '-', '2/3', '-', '3/3', '2/3'],
                    ['盲肠', '盲肠', '盲肠', '盲肠', '盲肠', '盲肠', '盲肠', '盲肠', '盲肠'],
                    [
                        '黏膜萎缩',
                        '黏膜萎缩',
                        '黏膜萎缩',
                        '黏膜萎缩',
                        '黏膜萎缩',
                        '黏膜萎缩',
                        '黏膜萎缩',
                        '黏膜萎缩',
                        '黏膜萎缩',
                    ],
                    ['轻微', '-', '-', '-', '-', '1/3', '-', '2/3', '1/3'],
                    ['结肠', '结肠', '结肠', '结肠', '结肠', '结肠', '结肠', '结肠', '结肠'],
                    [
                        '黏膜萎缩',
                        '黏膜萎缩',
                        '黏膜萎缩',
                        '黏膜萎缩',
                        '黏膜萎缩',
                        '黏膜萎缩',
                        '黏膜萎缩',
                        '黏膜萎缩',
                        '黏膜萎缩',
                    ],
                    ['轻微', '-', '-', '-', '-', '1/3', '-', '2/3', '-'],
                    ['直肠', '直肠', '直肠', '直肠', '直肠', '直肠', '直肠', '直肠', '直肠'],
                    [
                        '黏膜萎缩',
                        '黏膜萎缩',
                        '黏膜萎缩',
                        '黏膜萎缩',
                        '黏膜萎缩',
                        '黏膜萎缩',
                        '黏膜萎缩',
                        '黏膜萎缩',
                        '黏膜萎缩',
                    ],
                    ['轻微', '-', '-', '-', '-', '1/3', '-', '2/3', '2/3'],
                ],
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    [
                        '组织病理学',
                        '组织病理学',
                        '组织病理学',
                        '组织病理学',
                        '组织病理学',
                        '组织病理学',
                        '组织病理学',
                        '组织病理学',
                        '组织病理学',
                    ],
                    ['肾上腺', '肾上腺', '肾上腺', '肾上腺', '肾上腺', '肾上腺', '肾上腺', '肾上腺', '肾上腺'],
                    [
                        '双侧皮质球状带肥大',
                        '双侧皮质球状带肥大',
                        '双侧皮质球状带肥大',
                        '双侧皮质球状带肥大',
                        '双侧皮质球状带肥大',
                        '双侧皮质球状带肥大',
                        '双侧皮质球状带肥大',
                        '双侧皮质球状带肥大',
                        '双侧皮质球状带肥大',
                    ],
                    ['轻微', '-', '-', '-', '-', '1/2', '-', '1/2', '2/2'],
                    ['轻度', '-', '-', '-', '-', '-', '-', '1/2', '-'],
                ],
            )
        )


@pytest.mark.asyncio(loop_scope='session')
async def test_get_abnormal_electrocardiogram_table():
    with TestClient(app):  # 触发 lifespan 以关闭 redis
        # 重复给药关键: 4232-************-10, project_id: 462, doc_id: 2075, parent_id: 58986
        assert (
            await get_abnormal_electrocardiogram_table(
                462,
                2075,
                58986,
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ['心电图', '心电图', '心电图', '心电图', '心电图', '心电图', '心电图', '心电图', '心电图'],
                    [
                        '心率（HR）',
                        '心率（HR）',
                        '心率（HR）',
                        '心率（HR）',
                        '心率（HR）',
                        '心率（HR）',
                        '心率（HR）',
                        '心率（HR）',
                        '心率（HR）',
                    ],
                    ['D1_1h\n（%变化幅度）', '-', '-', '-', '-', '3%↓', '15%↓', '9%↓', '17%↓'],
                    [
                        'QRS波群时限（QRS）',
                        'QRS波群时限（QRS）',
                        'QRS波群时限（QRS）',
                        'QRS波群时限（QRS）',
                        'QRS波群时限（QRS）',
                        'QRS波群时限（QRS）',
                        'QRS波群时限（QRS）',
                        'QRS波群时限（QRS）',
                        'QRS波群时限（QRS）',
                    ],
                    ['D28_1h\n（%变化幅度）', '-', '-', '-', '24%↓', '-', '-', '-', '-'],
                    ['D8_1h\n（%变化幅度）', '-', '-', '-', '-', '-', '-', '-', '22%↓'],
                    [
                        'QT期间（QT）',
                        'QT期间（QT）',
                        'QT期间（QT）',
                        'QT期间（QT）',
                        'QT期间（QT）',
                        'QT期间（QT）',
                        'QT期间（QT）',
                        'QT期间（QT）',
                        'QT期间（QT）',
                    ],
                    ['D1_1h\n（%变化幅度）', '-', '-', '-', '-', '-', '27%↑', '17%↑', '38%↑'],
                    ['D8_1h\n（%变化幅度）', '-', '-', '-', '-', '-', '18%↑', '-', '-'],
                ],
                [],
            )
        )

        # 重复给药关键: 4232-************, project_id: 463 , doc_id: 2076, 这个是没有任何数据的
        assert (
                await get_abnormal_electrocardiogram_table(
                    463,
                    2076,
                    59058,
                    [
                        ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'],
                        ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ],
                    '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 1.5 | 1.5 | 5 | 5 | 15 | 15 |
    | 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
                ) is None
        )


@pytest.mark.asyncio(loop_scope='session')
async def test_get_table_7_3():
    with TestClient(app):  # 触发 lifespan 以关闭 redis
        # 重复给药关键: 4232-************-10, project_id: 462, doc_id: 2075, 有所有异常项
        assert (
            await get_table_7_3(
                462,
                2075,
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
        | 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == [
                ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                [
                    '值得注意的结果',
                    '值得注意的结果',
                    '值得注意的结果',
                    '值得注意的结果',
                    '值得注意的结果',
                    '值得注意的结果',
                    '值得注意的结果',
                    '值得注意的结果',
                    '值得注意的结果',
                ],
                ['濒死/死亡', '0', '0', '0', '1', '1', '0', '0', '2'],
                [
                    '体重和体重变化',
                    '体重和体重变化',
                    '体重和体重变化',
                    '体重和体重变化',
                    '体重和体重变化',
                    '体重和体重变化',
                    '体重和体重变化',
                    '体重和体重变化',
                    '体重和体重变化',
                ],
                [
                    'Days 1-8 (g)\n（%体重变化百分比）',
                    '53.14',
                    '16.41',
                    '55.69\n4.80%↑',
                    '21.77\n32.67%↑',
                    '50.97\n4.08%↓',
                    '14.29\n12.92%↓',
                    '49.73\n6.41%↓',
                    '13.35\n18.65%↓',
                ],
                [
                    'Days 8-15 (g)\n（%体重变化百分比）',
                    '50.45',
                    '17.85',
                    '47.67\n5.5%↓',
                    '15.28\n14.4%↓',
                    '46.87\n7.2%↓',
                    '22.68\n27.06%↑',
                    '49.35\n2.2%↓',
                    '20.60\n15.41%↑',
                ],
                ['摄食量', '-', '-', '-', '-', '-', '-', '-', '-'],
                ['临床观察', '-', '-', '-', '-', '-', '-', '-', '-'],
                ['体温', '体温', '体温', '体温', '体温', '体温', '体温', '体温', '体温'],
                ['D1_1h\n（%变化幅度）', '-', '-', '-', '-', '-', '4%↓', '3%↓', '4%↓'],
                ['D1_24h\n（%变化幅度）', '-', '-', '1%↑', '-', '-', '-', '-', '-'],
                ['D28_1h\n（%变化幅度）', '-', '-', '-', '-', '-', '-', '3%↑', '-'],
                ['心电图', '心电图', '心电图', '心电图', '心电图', '心电图', '心电图', '心电图', '心电图'],
                [
                    '心率（HR）',
                    '心率（HR）',
                    '心率（HR）',
                    '心率（HR）',
                    '心率（HR）',
                    '心率（HR）',
                    '心率（HR）',
                    '心率（HR）',
                    '心率（HR）',
                ],
                ['D1_1h\n（%变化幅度）', '-', '-', '-', '-', '3%↓', '15%↓', '9%↓', '17%↓'],
                [
                    'QRS波群时限（QRS）',
                    'QRS波群时限（QRS）',
                    'QRS波群时限（QRS）',
                    'QRS波群时限（QRS）',
                    'QRS波群时限（QRS）',
                    'QRS波群时限（QRS）',
                    'QRS波群时限（QRS）',
                    'QRS波群时限（QRS）',
                    'QRS波群时限（QRS）',
                ],
                ['D28_1h\n（%变化幅度）', '-', '-', '-', '24%↓', '-', '-', '-', '-'],
                ['D8_1h\n（%变化幅度）', '-', '-', '-', '-', '-', '-', '-', '22%↓'],
                [
                    'QT期间（QT）',
                    'QT期间（QT）',
                    'QT期间（QT）',
                    'QT期间（QT）',
                    'QT期间（QT）',
                    'QT期间（QT）',
                    'QT期间（QT）',
                    'QT期间（QT）',
                    'QT期间（QT）',
                ],
                ['D1_1h\n（%变化幅度）', '-', '-', '-', '-', '-', '27%↑', '17%↑', '38%↑'],
                ['D8_1h\n（%变化幅度）', '-', '-', '-', '-', '-', '18%↑', '-', '-'],
                ['眼科检查', '-', '-', '-', '-', '-', '-', '-', '-'],
                [
                    '临床病理',
                    '临床病理',
                    '临床病理',
                    '临床病理',
                    '临床病理',
                    '临床病理',
                    '临床病理',
                    '临床病理',
                    '临床病理',
                ],
                ['血凝', '血凝', '血凝', '血凝', '血凝', '血凝', '血凝', '血凝', '血凝'],
                [
                    'APTT（seconds）\n（%变化百分比）',
                    '23.93',
                    '20.16',
                    '23.90\n（0.13%↓）',
                    '18.41\n（8.67%↓）',
                    '22.09\n（7.69%↓）',
                    '19.38\n（3.87%↓）',
                    '20.52\n（14.25%↓）',
                    '17.65\n（12.46%↓）',
                ],
                [
                    'PT（seconds）\n（%变化百分比）',
                    '22.95',
                    '20.88',
                    '21.30\n（7.19%↓）',
                    '18.78\n（10.06%↓）',
                    '20.55\n（10.46%↓）',
                    '19.43\n（6.94%↓）',
                    '19.22\n（16.25%↓）',
                    '18.56\n（11.11%↓）',
                ],
                [
                    '血生化参数',
                    '血生化参数',
                    '血生化参数',
                    '血生化参数',
                    '血生化参数',
                    '血生化参数',
                    '血生化参数',
                    '血生化参数',
                    '血生化参数',
                ],
                [
                    'ALB（g/L）\n（%变化百分比）',
                    '46.81',
                    '52.2',
                    '46.82\n（0%）',
                    '50.72\n（3%↓）',
                    '46.53\n（1%↓）',
                    '49.13*\n（6%↓）',
                    '46.93\n（0%）',
                    '45.94***\n（12%↓）',
                ],
                [
                    'GLO （g/L）\n（%变化百分比）',
                    '17.04',
                    '14.4',
                    '16.24\n（5%↓）',
                    '14.54\n（1%↑）',
                    '16.75\n（2%↓）',
                    '14.87\n（3%↑）',
                    '18.84**\n（11%↑）',
                    '17.14***\n（19%↑）',
                ],
                [
                    'A/G （Ratio）\n（%变化百分比）',
                    '2.764',
                    '3.645',
                    '2.896\n（5%↑）',
                    '3.51\n（4%↓）',
                    '2.789\n（1%↑）',
                    '3.329\n（9%↓）',
                    '2.503*\n（9%↓）',
                    '2.696***\n（26%↓）',
                ],
                [
                    '血液学参数',
                    '血液学参数',
                    '血液学参数',
                    '血液学参数',
                    '血液学参数',
                    '血液学参数',
                    '血液学参数',
                    '血液学参数',
                    '血液学参数',
                ],
                [
                    'RBC（10^12/L）\n（%变化百分比）',
                    '8.25',
                    '7.927',
                    '8.3\n（1%↑）',
                    '7.705\n（3%↓）',
                    '8.219\n（0%）',
                    '7.609\n（4%↓）',
                    '7.97\n（3%↓）',
                    '7.116***\n（10%↓）',
                ],
                [
                    'HGB (g/dL)\n（%变化百分比）',
                    '16.48',
                    '15.19',
                    '16.31\n（1%↓）',
                    '15.16\n（0%）',
                    '16.07\n（2%↓）',
                    '14.97\n（1%↓）',
                    '15.73\n（5%↓）',
                    '14.3**\n（6%↓）',
                ],
                [
                    '尿液分析（第29天）',
                    '尿液分析（第29天）',
                    '尿液分析（第29天）',
                    '尿液分析（第29天）',
                    '尿液分析（第29天）',
                    '尿液分析（第29天）',
                    '尿液分析（第29天）',
                    '尿液分析（第29天）',
                    '尿液分析（第29天）',
                ],
                ['PRO', '-', '-', '↑', '-', '↑', '-', '↑', '↑'],
                ['LEU', '-', '-', '↑', '-', '↑', '-', '↑', '↑'],
                ['URO', '-', '-', '-', '-', '↑', '↑', '↑', '↑'],
                [
                    '器官重量',
                    '器官重量',
                    '器官重量',
                    '器官重量',
                    '器官重量',
                    '器官重量',
                    '器官重量',
                    '器官重量',
                    '器官重量',
                ],
                [' 肝脏 ', ' 肝脏 ', ' 肝脏 ', ' 肝脏 ', ' 肝脏 ', ' 肝脏 ', ' 肝脏 ', ' 肝脏 ', ' 肝脏 '],
                [
                    '脏器重量（g）\n（%变化百分比）',
                    '12.6931',
                    '7.2643',
                    '12.6014\n（1%↓）',
                    '7.9534\n（9%↑）',
                    '12.4500\n（2%↓）',
                    '8.3677*\n（15%↑）',
                    '14.6611**\n（16%↑）',
                    '9.4006***\n（29%↑）',
                ],
                [
                    '脏体比（%）\n（%变化百分比）',
                    '3.0781',
                    '3.0248',
                    '3.0022\n（2%↓）',
                    '3.1041\n（3%↑）',
                    '2.9971\n（3%↓）',
                    '3.3305**\n（10%↑）',
                    '3.5400***\n（15%↑）',
                    '3.8423***\n（27%↑）',
                ],
                [
                    '脏脑比（%）\n（%变化百分比）',
                    '600.75',
                    '369.603',
                    '598.457\n（0%）',
                    '407.107\n（10%↑）',
                    '585.512\n（3%↓）',
                    '422.406*\n（14%↑）',
                    '701.401**\n（17%↑）',
                    '486.713***\n（32%↑）',
                ],
                [
                    '大体病理学（第29天）',
                    '大体病理学（第29天）',
                    '大体病理学（第29天）',
                    '大体病理学（第29天）',
                    '大体病理学（第29天）',
                    '大体病理学（第29天）',
                    '大体病理学（第29天）',
                    '大体病理学（第29天）',
                    '大体病理学（第29天）',
                ],
                ['肝脏', '肝脏', '肝脏', '肝脏', '肝脏', '肝脏', '肝脏', '肝脏', '肝脏'],
                ['体积增大', '-', '-', '-', '2', '-', '2', '5', '7'],
                [
                    '组织病理学',
                    '组织病理学',
                    '组织病理学',
                    '组织病理学',
                    '组织病理学',
                    '组织病理学',
                    '组织病理学',
                    '组织病理学',
                    '组织病理学',
                ],
                ['肾上腺', '肾上腺', '肾上腺', '肾上腺', '肾上腺', '肾上腺', '肾上腺', '肾上腺', '肾上腺'],
                [
                    '双侧皮质肥大',
                    '双侧皮质肥大',
                    '双侧皮质肥大',
                    '双侧皮质肥大',
                    '双侧皮质肥大',
                    '双侧皮质肥大',
                    '双侧皮质肥大',
                    '双侧皮质肥大',
                    '双侧皮质肥大',
                ],
                ['轻微', '-', '-', '-', '-', '-', '-', '10/10', '10/10'],
                ['肝', '肝', '肝', '肝', '肝', '肝', '肝', '肝', '肝'],
                [
                    '小叶中央肝细胞肥大',
                    '小叶中央肝细胞肥大',
                    '小叶中央肝细胞肥大',
                    '小叶中央肝细胞肥大',
                    '小叶中央肝细胞肥大',
                    '小叶中央肝细胞肥大',
                    '小叶中央肝细胞肥大',
                    '小叶中央肝细胞肥大',
                    '小叶中央肝细胞肥大',
                ],
                ['轻微', '-', '-', '-', '-', '-', '-', '3/10', '4/10'],
                ['附加检查', '附加检查', '附加检查', '附加检查', '附加检查', '附加检查', '附加检查', '附加检查', '附加检查'],
                [
                    '给药后评价',
                    '给药后评价',
                    '给药后评价',
                    '给药后评价',
                    '给药后评价',
                    '给药后评价',
                    '给药后评价',
                    '给药后评价',
                    '给药后评价',
                ],
                [
                    '临床病理',
                    '临床病理',
                    '临床病理',
                    '临床病理',
                    '临床病理',
                    '临床病理',
                    '临床病理',
                    '临床病理',
                    '临床病理',
                ],
                ['血凝', '血凝', '血凝', '血凝', '血凝', '血凝', '血凝', '血凝', '血凝'],
                [
                    'APTT（seconds）\n（%变化百分比）',
                    '21.88',
                    '19.24',
                    '22.68\n（3.66%↑）',
                    '18.62\n（3.22%↓）',
                    '19.96\n（8.78%↓）',
                    '19.14\n（0.52%↓）',
                    '20.58\n（5.94%↓）',
                    '17.82\n（7.38%↓）',
                ],
                [
                    'PT（seconds）\n（%变化百分比）',
                    '23.32',
                    '19.16',
                    '22.12\n（5.15%↓）',
                    '19.14\n（0.10%↓）',
                    '20.46\n（12.26%↓）',
                    '19.92\n（3.97%↑）',
                    '20.08\n（13.89%↓）',
                    '18.82\n（1.77%↓）',
                ],
            ]
        )

        # 重复给药关键: 4232-************, project_id: 463 , doc_id: 2076
        assert (
            await get_table_7_3(
                463,
                2076,
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 1.5 | 1.5 | 5 | 5 | 15 | 15 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == [
                ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'],
                ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                [
                    '值得注意的结果',
                    '值得注意的结果',
                    '值得注意的结果',
                    '值得注意的结果',
                    '值得注意的结果',
                    '值得注意的结果',
                    '值得注意的结果',
                    '值得注意的结果',
                    '值得注意的结果',
                ],
                ['濒死/死亡', '0', '0', '0', '0', '0', '0', '0', '0'],
                [
                    '体重和体重变化',
                    '体重和体重变化',
                    '体重和体重变化',
                    '体重和体重变化',
                    '体重和体重变化',
                    '体重和体重变化',
                    '体重和体重变化',
                    '体重和体重变化',
                    '体重和体重变化',
                ],
                [
                    'Day28\n（%体重变化百分比）',
                    '9.228',
                    '8.132',
                    '9.426\n7.99%',
                    '7.636\n1.71%',
                    '9.586\n11.42%',
                    '8.140\n5.38%',
                    '8.818\n1.78%',
                    '8.170\n4.56%',
                ],
                ['摄食量', '-', '-', '-', '-', '-', '-', '一般/不良', '-'],
                ['临床观察', '-', '-', '-', '-', '-', '-', '-', '-'],
                ['血压', '-', '-', '-', '-', '-', '-', '-', '-'],
                ['眼科检查', '-', '-', '-', '-', '-', '-', '-', '-'],
                [
                    '临床病理',
                    '临床病理',
                    '临床病理',
                    '临床病理',
                    '临床病理',
                    '临床病理',
                    '临床病理',
                    '临床病理',
                    '临床病理',
                ],
                [
                    '血生化参数',
                    '血生化参数',
                    '血生化参数',
                    '血生化参数',
                    '血生化参数',
                    '血生化参数',
                    '血生化参数',
                    '血生化参数',
                    '血生化参数',
                ],
                [
                    'Na（mmol/L）\n（%变化百分比）',
                    '147.22',
                    '146.14',
                    '146.46\n（1%↓）',
                    '147.04\n（1%↑）',
                    '145.84\n（1%↓）',
                    '145.86\n（0%）',
                    '141.84\n***\n（4%↓）',
                    '144.66\n（1%↓）',
                ],
                [
                    'Cl (mmol/L)\n（%变化百分比）',
                    '108.2',
                    '107.86',
                    '108.68\n（0%）',
                    '109.12\n（1%↑）',
                    '108.4\n（0%）',
                    '108.16\n（0%）',
                    '103.72\n**\n（4%↓）',
                    '106.1\n（2%↓）',
                ],
                ['器官重量', '-', '-', '-', '-', '-', '-', '-', '-'],
                ['大体病理学', '-', '-', '-', '-', '-', '-', '-', '-'],
                [
                    '组织病理学',
                    '组织病理学',
                    '组织病理学',
                    '组织病理学',
                    '组织病理学',
                    '组织病理学',
                    '组织病理学',
                    '组织病理学',
                    '组织病理学',
                ],
                ['肾上腺', '肾上腺', '肾上腺', '肾上腺', '肾上腺', '肾上腺', '肾上腺', '肾上腺', '肾上腺'],
                [
                    '双侧皮质球状带肥大',
                    '双侧皮质球状带肥大',
                    '双侧皮质球状带肥大',
                    '双侧皮质球状带肥大',
                    '双侧皮质球状带肥大',
                    '双侧皮质球状带肥大',
                    '双侧皮质球状带肥大',
                    '双侧皮质球状带肥大',
                    '双侧皮质球状带肥大',
                ],
                ['轻微', '-', '-', '-', '-', '1', '-', '2', '3'],
                ['轻度', '-', '-', '-', '-', '-', '-', '1', '-'],
                ['胃', '胃', '胃', '胃', '胃', '胃', '胃', '胃', '胃'],
                [
                    '胃底腺黏液细胞肥大',
                    '胃底腺黏液细胞肥大',
                    '胃底腺黏液细胞肥大',
                    '胃底腺黏液细胞肥大',
                    '胃底腺黏液细胞肥大',
                    '胃底腺黏液细胞肥大',
                    '胃底腺黏液细胞肥大',
                    '胃底腺黏液细胞肥大',
                    '胃底腺黏液细胞肥大',
                ],
                ['轻微', '-', '-', '-', '-', '2', '-', '3', '2'],
                ['盲肠', '盲肠', '盲肠', '盲肠', '盲肠', '盲肠', '盲肠', '盲肠', '盲肠'],
                [
                    '黏膜萎缩',
                    '黏膜萎缩',
                    '黏膜萎缩',
                    '黏膜萎缩',
                    '黏膜萎缩',
                    '黏膜萎缩',
                    '黏膜萎缩',
                    '黏膜萎缩',
                    '黏膜萎缩',
                ],
                ['轻微', '-', '-', '-', '-', '1', '-', '2', '1'],
                ['结肠', '结肠', '结肠', '结肠', '结肠', '结肠', '结肠', '结肠', '结肠'],
                [
                    '黏膜萎缩',
                    '黏膜萎缩',
                    '黏膜萎缩',
                    '黏膜萎缩',
                    '黏膜萎缩',
                    '黏膜萎缩',
                    '黏膜萎缩',
                    '黏膜萎缩',
                    '黏膜萎缩',
                ],
                ['轻微', '-', '-', '-', '-', '1', '-', '2', '-'],
                ['直肠', '直肠', '直肠', '直肠', '直肠', '直肠', '直肠', '直肠', '直肠'],
                [
                    '黏膜萎缩',
                    '黏膜萎缩',
                    '黏膜萎缩',
                    '黏膜萎缩',
                    '黏膜萎缩',
                    '黏膜萎缩',
                    '黏膜萎缩',
                    '黏膜萎缩',
                    '黏膜萎缩',
                ],
                ['轻微', '-', '-', '-', '-', '1', '-', '2', '2'],
                [
                    '给药后评价',
                    '给药后评价',
                    '给药后评价',
                    '给药后评价',
                    '给药后评价',
                    '给药后评价',
                    '给药后评价',
                    '给药后评价',
                    '给药后评价',
                ],
                [
                    '组织病理学',
                    '组织病理学',
                    '组织病理学',
                    '组织病理学',
                    '组织病理学',
                    '组织病理学',
                    '组织病理学',
                    '组织病理学',
                    '组织病理学',
                ],
                ['肾上腺', '肾上腺', '肾上腺', '肾上腺', '肾上腺', '肾上腺', '肾上腺', '肾上腺', '肾上腺'],
                [
                    '双侧皮质球状带肥大',
                    '双侧皮质球状带肥大',
                    '双侧皮质球状带肥大',
                    '双侧皮质球状带肥大',
                    '双侧皮质球状带肥大',
                    '双侧皮质球状带肥大',
                    '双侧皮质球状带肥大',
                    '双侧皮质球状带肥大',
                    '双侧皮质球状带肥大',
                ],
                ['轻微', '-', '-', '-', '-', '1', '-', '1', '2'],
                ['轻度', '-', '-', '-', '-', '-', '-', '1', '-'],
            ]
        )
