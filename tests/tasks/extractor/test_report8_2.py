import pytest
from fastapi.testclient import TestClient

from app.main import app
from app.tasks.extractor.report8_2 import (
    get_administration_date_chromosome,
    get_analyze_cell_number_chromosome,
    get_genotoxic_effects_chromosome,
    get_handle_chromosome,
    get_merged_ch_table,
    get_metabolic_system_chromosome,
    get_number_of_parallel_cultures_chromosome,
    get_positive_control_sample_chromosome,
    get_small_ch_table_text,
    get_toxic_effects_chromosome,
)


@pytest.mark.asyncio(loop_scope='session')
async def test_get_number_of_parallel_cultures_chromosome():
    with TestClient(app):  # 触发 lifespan 以关闭 redis
        # 遗传毒性-染色体畸变: E-230783, project_id: 351, doc_id: 1728
        assert await get_number_of_parallel_cultures_chromosome(351, 1728) == '2'

        # 遗传毒性-染色体畸变: E-231924, project_id: 348 , doc_id: 1698
        assert await get_number_of_parallel_cultures_chromosome(348, 1698) == '2'

        # 遗传毒性-染色体畸变: 09806-210867, project_id: 471 , doc_id: 2103. 这是3月26号客户的内测3
        assert await get_number_of_parallel_cultures_chromosome(471, 2103) == '2'


@pytest.mark.asyncio(loop_scope='session')
async def test_get_metabolic_system_chromosome():
    with TestClient(app):  # 触发 lifespan 以关闭 redis
        # 遗传毒性-染色体畸变: E-230783, project_id: 351, doc_id: 1728
        assert await get_metabolic_system_chromosome(351, 1728) == 'β-萘黄酮和苯巴比妥诱导的大鼠肝脏S9'

        # 遗传毒性-染色体畸变: E-231924, project_id: 348 , doc_id: 1698
        assert await get_metabolic_system_chromosome(348, 1698) == 'β-萘黄酮和苯巴比妥诱导的大鼠肝脏S9'


@pytest.mark.asyncio(loop_scope='session')
async def test_get_analyze_cell_number_chromosome():
    with TestClient(app):  # 触发 lifespan 以关闭 redis
        # 遗传毒性-染色体畸变: E-230783, project_id: 351, doc_id: 1728
        assert await get_analyze_cell_number_chromosome(351, 1728) == '300个'

        # 遗传毒性-染色体畸变: E-231924, project_id: 348 , doc_id: 1698
        assert await get_analyze_cell_number_chromosome(348, 1698) == '300个'


@pytest.mark.asyncio(loop_scope='session')
async def test_get_administration_date_chromosome():
    with TestClient(app):  # 触发 lifespan 以关闭 redis
        # 遗传毒性-染色体畸变: E-230783, project_id: 351, doc_id: 1728
        assert await get_administration_date_chromosome(351, 1728) == '2023年06月15日'

        # 遗传毒性-染色体畸变: E-231924, project_id: 348 , doc_id: 1698  doc_chunk_id: 21433
        assert await get_administration_date_chromosome(348, 1698) == '2023年12月26日'


@pytest.mark.asyncio(loop_scope='session')
async def test_get_positive_control_sample_chromosome():
    with TestClient(app):  # 触发 lifespan 以关闭 redis
        # 遗传毒性-染色体畸变: E-230783, project_id: 351, doc_id: 1728
        assert await get_positive_control_sample_chromosome(351, 1728) == '环磷酰胺一水合物（CP）、丝裂霉素C（MMC）'

        # 遗传毒性-染色体畸变: E-231924, project_id: 348 , doc_id: 1698  doc_chunk_id: 21380
        assert await get_positive_control_sample_chromosome(348, 1698) == '环磷酰胺一水合物（CP）、丝裂霉素C（MMC）'


@pytest.mark.asyncio(loop_scope='session')
async def test_get_handle_chromosome():
    with TestClient(app):  # 触发 lifespan 以关闭 redis
        # 遗传毒性-染色体畸变: E-230783, project_id: 351, doc_id: 1728
        assert (
            await get_handle_chromosome(351, 1728)
            == 'S9代谢活化给药3小时系列、非代谢活化给药3小时系列和非代谢活化给药20小时系列。在给药20至24小时后收获所有细胞。'
        )
        # 遗传毒性-染色体畸变: E-231924, project_id: 348 , doc_id: 1698
        assert (
            await get_handle_chromosome(348, 1698)
            == 'S9代谢活化给药3小时系列、非代谢活化给药3小时系列和非代谢活化给药20小时系列。在给药20至24小时后收获所有细胞。'
        )


@pytest.mark.asyncio(loop_scope='session')
async def test_get_toxic_effects_chromosome():
    with TestClient(app):  # 触发 lifespan 以关闭 redis
        # 遗传毒性-染色体畸变: E-230783, project_id: 436, doc_id: 1988
        assert (
            await get_toxic_effects_chromosome(436, 1988)
            == '''S9活化给药3小时系列，50 µg/mL浓度下细胞毒性为3%
非代谢活化给药3小时系列，50 µg/mL浓度下细胞毒性为-1%
非代谢活化给药20小时系列，50 µg/mL浓度下细胞毒性为18%'''
        )

        # 遗传毒性-染色体畸变: E-231924, project_id: 348 , doc_id: 1698
        assert (
            await get_toxic_effects_chromosome(348, 1698)
            == '''S9活化给药3小时系列，240 µg/mL浓度下细胞毒性为47%
非代谢活化给药3小时系列，240 µg/mL浓度下细胞毒性为54%
非代谢活化给药20小时系列，200 µg/mL浓度下细胞毒性为51%'''
        )


@pytest.mark.asyncio(loop_scope='session')
async def test_get_genotoxic_effects_chromosome():
    with TestClient(app):  # 触发 lifespan 以关闭 redis
        # 遗传毒性-染色体畸变: E-230783, project_id: 351, doc_id: 1728
        assert await get_genotoxic_effects_chromosome(351, 1728) == '阴性'

        # 遗传毒性-染色体畸变: E-231924, project_id: 348 , doc_id: 1698
        assert await get_genotoxic_effects_chromosome(348, 1698) == '阴性'


@pytest.mark.asyncio(loop_scope='session')
async def test_get_small_ch_table_text():
    with TestClient(app):  # 触发 lifespan 以关闭 redis
        # 遗传毒性-染色体畸变: E-230783, project_id: 351, doc_id: 1728. 由于表格格式不规范，该用例通过率不稳定
        test_product = 'NS-041'
        assert (
            await get_small_ch_table_text(351, 1728, test_product)
            == '''| 受试物 | 给药（μg/mL） | 细胞毒性（%） | 计数细胞总数 | 含有多倍体细胞（%） | 含有核内复制细胞（%） | 裂隙（%） | 畸变染色体（%） | 畸变细胞（%） |
| S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 |
| DMSO (1%) | DMSO (1%) | 0 | 300 | 0.0 | 0.0 | 0.3 | 0.0 | 0.0 |
| NS-041 | 10 | -1 | 300 | 0.0 | 3.0 | 1.0 | 0.0 | 0.0 |
| NS-041 | 20 | 4 | 300 | 0.0 | 5.3 | 0.3 | 0.3 | 0.3 |
| NS-041 | 50P | 3 | 300 | 0.0 | 4.7 | 1 | 0.0 | 0.0 |
| CP 5 | CP 5 | ND | 300 | 0.0 | 1.7 | 0.7 | 25.7 | 24.3** |
| 非代谢活化给药3小时系列 | 非代谢活化给药3小时系列 | 非代谢活化给药3小时系列 | 非代谢活化给药3小时系列 | 非代谢活化给药3小时系列 | 非代谢活化给药3小时系列 | 非代谢活化给药3小时系列 | 非代谢活化给药3小时系列 | 非代谢活化给药3小时系列 | 非代谢活化给药3小时系列 |
| DMSO (1%) | 0 | 0 | 300 | 0.0 | 0.0 | 0.0 | 0.3 | 0.3 |
| NS-041 | 10 | -3 | 300 | 0.0 | 0.0 | 0.0 | 0.3 | 0.0 |
| NS-041 | 20 | 1 | 300 | 0.0 | 0.0 | 0.0 | 0.0 | 0.0 |
| NS-041 | 50P | -1 | 300 | 0.0 | 0.0 | 0.3 | 1.0 | 0.0 |
| MMC 0.5 | MMC 0.5 | ND | 300 | 0.0 | 0.0 | 0.0 | 1.3 | 27.3 | 26.7** |
| 非代谢活化给药20小时系列 | 非代谢活化给药20小时系列 | 非代谢活化给药20小时系列 | 非代谢活化给药20小时系列 | 非代谢活化给药20小时系列 | 非代谢活化给药20小时系列 | 非代谢活化给药20小时系列 | 非代谢活化给药20小时系列 | 非代谢活化给药20小时系列 | 非代谢活化给药20小时系列 |
| DMSO (1%) | 0 | 0 | 300 | 0.0 | 0.0 | 0.0 | 0.7 | 0.0 |
| NS-041 | 10 | 3 | 300 | 0.3 | 0.3 | 0.0 | 0.3 | 0.0 |
| NS-041 | 20 | 0 | 300 | 0.0 | 0.0 | 0.0 | 0.3 | 0.0 |
| NS-041 | 50P | 18 | 300 | 0.0 | 0.0 | 0.0 | 1.3 | 0.3 |
| MMC 0.15 | MMC 0.15 | ND | 300 | 0.0 | 0.0 | 0.0 | 0.7 | 26.0 | 25.3** |'''
        )

        # 遗传毒性-染色体畸变: E-231924, project_id: 348 , doc_id: 1698
        test_product = '新药001'
        assert (
            await get_small_ch_table_text(348, 1698, test_product)
            == '''| 受试物 | 给药（μg/mL） | 细胞毒性（%） | 计数细胞总数 | 含有多倍体细胞（%） | 含有核内复制细胞（%） | 裂隙（%） | 畸变染色体（%） | 畸变细胞（%） |
| S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 |
| DMSO (1%) | DMSO (1%) | 0 | 300 | 0.0 | 0.0 | 0.0 | 0.7 | 0.7 |
| 新药001 | 40 | 3 | 300 | 0.0 | 0.0 | 0.0 | 0.3 | 0.3 |
| 新药001 | 120 | 11 | 300 | 0.0 | 0.0 | 0.3 | 0.0 | 0.0 |
| 新药001 | 200 | 31 | 300 | 0.0 | 0.0 | 0.0 | 0.0 | 0.0 |
| 新药001 | 240 | 47 | 300 | 0.0 | 1.0 | 0.7 | 1.7 | 1.7 |
| CP 5 | CP 5 | ND | 300 | 0.0 | 0.7 | 0.3 | 13.7 | 13.0** |
| 非代谢活化给药3小时系列 | 非代谢活化给药3小时系列 | 非代谢活化给药3小时系列 | 非代谢活化给药3小时系列 | 非代谢活化给药3小时系列 | 非代谢活化给药3小时系列 | 非代谢活化给药3小时系列 | 非代谢活化给药3小时系列 | 非代谢活化给药3小时系列 |
| DMSO (1%) | DMSO (1%) | 0 | 300 | 0.0 | 0.0 | 0.3 | 0.3 | 0.3 |
| 新药001 | 40 | 8 | 300 | 0.0 | 0.0 | 0.3 | 0.0 | 0.0 |
| 新药001 | 120 | 5 | 300 | 0.0 | 0.0 | 0.0 | 0.7 | 0.7 |
| 新药001 | 200 | 29 | 300 | 0.0 | 0.0 | 0.7 | 0.3 | 0.3 |
| 新药001 | 240 | 54 | 300 | 0.3 | 1.0 | 0.0 | 1.7 | 1.3 |
| MMC 0.5 | MMC 0.5 | ND | 300 | 0.0 | 0.3 | 0.3 | 18.0 | 16.7** |
| 非代谢活化给药20小时系列 | 非代谢活化给药20小时系列 | 非代谢活化给药20小时系列 | 非代谢活化给药20小时系列 | 非代谢活化给药20小时系列 | 非代谢活化给药20小时系列 | 非代谢活化给药20小时系列 | 非代谢活化给药20小时系列 | 非代谢活化给药20小时系列 |
| DMSO (1%) | DMSO (1%) | 0 | 300 | 0.0 | 0.0 | 0.3 | 0.3 | 0.3 |
| 新药001 | 40 | 2 | 300 | 0.0 | 0.0 | 0.0 | 0.0 | 0.0 |
| 新药001 | 100 | 17 | 300 | 0.0 | 0.0 | 0.0 | 0.3 | 0.3 |
| 新药001 | 170 | 27 | 300 | 0.3 | 0.0 | 0.3 | 0.3 | 0.3 |
| 新药001 | 200 | 51 | 300 | 0.0 | 0.0 | 0.3 | 0.3 | 0.3 |
| MMC 0.15 | MMC 0.15 | ND | 300 | 0.0 | 0.0 | 0.3 | 18.3 | 18.0** |'''
        )


@pytest.mark.asyncio(loop_scope='session')
async def test_get_merged_ch_table():
    with TestClient(app):  # 触发 lifespan 以关闭 redis
        # 遗传毒性-染色体畸变: E-230783, project_id: 351, doc_id: 1728. 由于表格格式不规范，该用例通过率不稳定
        test_product = 'NS-041'
        assert await get_merged_ch_table(351, 1728, test_product) == [
            (
                '给药（μg/mL）',
                '给药（μg/mL）',
                '细胞毒性（%）',
                '计数细胞总数',
                '含有多倍体细胞（%）',
                '含有核内复制细胞（%）',
                '裂隙（%）',
                '畸变染色体（%）',
                '畸变细胞（%）',
            ),
            (
                'S9代谢活化给药3小时系列',
                'S9代谢活化给药3小时系列',
                'S9代谢活化给药3小时系列',
                'S9代谢活化给药3小时系列',
                'S9代谢活化给药3小时系列',
                'S9代谢活化给药3小时系列',
                'S9代谢活化给药3小时系列',
                'S9代谢活化给药3小时系列',
                'S9代谢活化给药3小时系列',
                'S9代谢活化给药3小时系列',
            ),
            ('DMSO (1%)', 'DMSO (1%)', '0', '300', '0.0', '0.0', '0.3', '0.0', '0.0'),
            ('NS-041', '10', '-1', '300', '0.0', '3.0', '1.0', '0.0', '0.0'),
            ('NS-041', '20', '4', '300', '0.0', '5.3', '0.3', '0.3', '0.3'),
            ('NS-041', '50P', '3', '300', '0.0', '4.7', '1', '0.0', '0.0'),
            ('CP 5', 'CP 5', 'ND', '300', '0.0', '1.7', '0.7', '25.7', '24.3**'),
            (
                '非代谢活化给药3小时系列',
                '非代谢活化给药3小时系列',
                '非代谢活化给药3小时系列',
                '非代谢活化给药3小时系列',
                '非代谢活化给药3小时系列',
                '非代谢活化给药3小时系列',
                '非代谢活化给药3小时系列',
                '非代谢活化给药3小时系列',
                '非代谢活化给药3小时系列',
                '非代谢活化给药3小时系列',
            ),
            ('DMSO (1%)', 'DMSO (1%)', '0', '300', '0.0', '0.0', '0.0', '0.3', '0.3'),
            ('NS-041', '10', '-3', '300', '0.0', '0.0', '0.0', '0.3', '0.0'),
            ('NS-041', '20', '1', '300', '0.0', '0.0', '0.0', '0.0', '0.0'),
            ('NS-041', '50P', '-1', '300', '0.0', '0.0', '0.3', '1.0', '0.0'),
            ('MMC 0.5', 'MMC 0.5', 'ND', '300', '0.0', '0.0', '0.0', '1.3', '27.3', '26.7**'),
            (
                '非代谢活化给药20小时系列',
                '非代谢活化给药20小时系列',
                '非代谢活化给药20小时系列',
                '非代谢活化给药20小时系列',
                '非代谢活化给药20小时系列',
                '非代谢活化给药20小时系列',
                '非代谢活化给药20小时系列',
                '非代谢活化给药20小时系列',
                '非代谢活化给药20小时系列',
                '非代谢活化给药20小时系列',
            ),
            ('DMSO (1%)', 'DMSO (1%)', '0', '300', '0.0', '0.0', '0.0', '0.7', '0.0'),
            ('NS-041', '10', '3', '300', '0.3', '0.3', '0.0', '0.3', '0.0'),
            ('NS-041', '20', '0', '300', '0.0', '0.0', '0.0', '0.3', '0.0'),
            ('NS-041', '50P', '18', '300', '0.0', '0.0', '0.0', '1.3', '0.3'),
            ('MMC 0.15', 'MMC 0.15', 'ND', '300', '0.0', '0.0', '0.0', '0.7', '26.0', '25.3**'),
        ]

        # 遗传毒性-染色体畸变: E-231924, project_id: 348 , doc_id: 1698  这份文档刚好对 动态表格的原始表格进行了正确格式
        test_product = '新药001'
        assert await get_merged_ch_table(348, 1698, test_product) == [
            (
                '给药（μg/mL）',
                '给药（μg/mL）',
                '细胞毒性（%）',
                '计数细胞总数',
                '含有多倍体细胞（%）',
                '含有核内复制细胞（%）',
                '裂隙（%）',
                '畸变染色体（%）',
                '畸变细胞（%）',
            ),
            (
                'S9代谢活化给药3小时系列',
                'S9代谢活化给药3小时系列',
                'S9代谢活化给药3小时系列',
                'S9代谢活化给药3小时系列',
                'S9代谢活化给药3小时系列',
                'S9代谢活化给药3小时系列',
                'S9代谢活化给药3小时系列',
                'S9代谢活化给药3小时系列',
                'S9代谢活化给药3小时系列',
            ),
            ('DMSO (1%)', 'DMSO (1%)', '0', '300', '0.0', '0.0', '0.0', '0.7', '0.7'),
            ('新药001', '40', '3', '300', '0.0', '0.0', '0.0', '0.3', '0.3'),
            ('新药001', '120', '11', '300', '0.0', '0.0', '0.3', '0.0', '0.0'),
            ('新药001', '200', '31', '300', '0.0', '0.0', '0.0', '0.0', '0.0'),
            ('新药001', '240', '47', '300', '0.0', '1.0', '0.7', '1.7', '1.7'),
            ('CP 5', 'CP 5', 'ND', '300', '0.0', '0.7', '0.3', '13.7', '13.0**'),
            (
                '非代谢活化给药3小时系列',
                '非代谢活化给药3小时系列',
                '非代谢活化给药3小时系列',
                '非代谢活化给药3小时系列',
                '非代谢活化给药3小时系列',
                '非代谢活化给药3小时系列',
                '非代谢活化给药3小时系列',
                '非代谢活化给药3小时系列',
                '非代谢活化给药3小时系列',
            ),
            ('DMSO (1%)', 'DMSO (1%)', '0', '300', '0.0', '0.0', '0.3', '0.3', '0.3'),
            ('新药001', '40', '8', '300', '0.0', '0.0', '0.3', '0.0', '0.0'),
            ('新药001', '120', '5', '300', '0.0', '0.0', '0.0', '0.7', '0.7'),
            ('新药001', '200', '29', '300', '0.0', '0.0', '0.7', '0.3', '0.3'),
            ('新药001', '240', '54', '300', '0.3', '1.0', '0.0', '1.7', '1.3'),
            ('MMC 0.5', 'MMC 0.5', 'ND', '300', '0.0', '0.3', '0.3', '18.0', '16.7**'),
            (
                '非代谢活化给药20小时系列',
                '非代谢活化给药20小时系列',
                '非代谢活化给药20小时系列',
                '非代谢活化给药20小时系列',
                '非代谢活化给药20小时系列',
                '非代谢活化给药20小时系列',
                '非代谢活化给药20小时系列',
                '非代谢活化给药20小时系列',
                '非代谢活化给药20小时系列',
            ),
            ('DMSO (1%)', 'DMSO (1%)', '0', '300', '0.0', '0.0', '0.3', '0.3', '0.3'),
            ('新药001', '40', '2', '300', '0.0', '0.0', '0.0', '0.0', '0.0'),
            ('新药001', '100', '17', '300', '0.0', '0.0', '0.0', '0.3', '0.3'),
            ('新药001', '170', '27', '300', '0.3', '0.0', '0.3', '0.3', '0.3'),
            ('新药001', '200', '51', '300', '0.0', '0.0', '0.3', '0.3', '0.3'),
            ('MMC 0.15', 'MMC 0.15', 'ND', '300', '0.0', '0.0', '0.3', '18.3', '18.0**'),
        ]
