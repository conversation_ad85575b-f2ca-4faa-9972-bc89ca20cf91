import pytest
from fastapi.testclient import TestClient

from app.main import app
from app.tasks.extractor.report9 import (
    get_administration_date,
    get_administration_method_9,
    get_age,
    get_cell_number,
    get_evaluate_cells,
    get_genotoxic_effects_9,
    get_merged_mc_table,
    get_sampling_time,
    get_small_mc_table,
    get_toxic_effects,
)


# 9.1 遗传毒性：体内-微核试验部分 - 5-采样时间 字段
@pytest.mark.asyncio(loop_scope='session')
async def test_get_sampling_time():
    with TestClient(app):
        # 微核试验: F-231926, project_id: 406 , doc_id: 1882, stag环境的
        assert await get_sampling_time(project_id=406, doc_id=1882) == '最后一次给药后18-24 小时'

        # 微核试验: F-230784, project_id: 405 , doc_id: 1880, stag环境的
        assert await get_sampling_time(project_id=405, doc_id=1880) == '最后一次给药后18-24小时'

        # 微核试验: 09806-210868, project_id: 471 , doc_id: 2104, 客户内测3
        assert await get_sampling_time(project_id=471, doc_id=2104) == '最后一次给药后 18-24 小时'


# 9.1 遗传毒性：体内-微核试验部分 - 7-年龄 字段
@pytest.mark.asyncio(loop_scope='session')
async def test_get_age():
    with TestClient(app):
        # 微核试验: F-231926, project_id: 406 , doc_id: 1882, stag环境的
        assert await get_age(project_id=406, doc_id=1882) == '6至8周'

        # 微核试验: F-230784, project_id: 405 , doc_id: 1880, stag环境的
        assert await get_age(project_id=405, doc_id=1880) == '6至8周'


# 9.1 遗传毒性：体内-微核试验部分 - 8-给药方法 字段
@pytest.mark.asyncio(loop_scope='session')
async def test_get_administration_method_9():
    with TestClient(app):
        # 微核试验: F-231926, project_id: 406 , doc_id: 1882, stag环境的
        assert await get_administration_method_9(project_id=406, doc_id=1882) == '经口灌胃'

        # 微核试验: F-230784, project_id: 405 , doc_id: 1880, stag环境的
        assert await get_administration_method_9(project_id=405, doc_id=1880) == '经口灌胃'


# 9.1 遗传毒性：体内-微核试验部分 - 9-给药日期 字段
@pytest.mark.asyncio(loop_scope='session')
async def test_get_administration_date():
    with TestClient(app):
        # 微核试验: F-231926, project_id: 406 , doc_id: 1882, stag环境的
        assert await get_administration_date(project_id=406, doc_id=1882) == '2023年12月27日'

        # 微核试验: F-230784, project_id: 405 , doc_id: 1880, stag环境的
        assert await get_administration_date(project_id=405, doc_id=1880) == '2023年06月19日'


# 9.1 遗传毒性：体内-微核试验部分 - 10-评价细胞 字段
@pytest.mark.asyncio(loop_scope='session')
async def test_get_evaluate_cells():
    with TestClient(app):
        # 微核试验: F-231926, project_id: 406 , doc_id: 1882, stag环境的
        assert await get_evaluate_cells(project_id=406, doc_id=1882) == '嗜多染红细胞'

        # 微核试验: F-230784, project_id: 405 , doc_id: 1880, stag环境的
        assert await get_evaluate_cells(project_id=405, doc_id=1880) == '嗜多染红细胞'


# 9.1 遗传毒性：体内-微核试验部分 12-分析细胞数量/每动物 字段
@pytest.mark.asyncio(loop_scope='session')
async def test_get_cell_number():
    with TestClient(app):
        # 微核试验: F-231926, project_id: 406 , doc_id: 1882, stag环境的
        assert await get_cell_number(project_id=406, doc_id=1882) == '至少4000个'

        # 微核试验: F-230784, project_id: 405 , doc_id: 1880, stag环境的
        assert await get_cell_number(project_id=405, doc_id=1880) == '至少4000个'


# 9.1 遗传毒性：体内-微核试验部分 - 13-毒性/细胞毒性作用 字段
@pytest.mark.asyncio(loop_scope='session')
async def test_get_toxic_effects():
    with TestClient(app):
        # 微核试验: F-231926, project_id: 406 , doc_id: 1882, stag环境的
        assert await get_toxic_effects(project_id=406, doc_id=1882) == '无'

        # 微核试验: F-230784, project_id: 405 , doc_id: 1880, stag环境的
        assert await get_toxic_effects(project_id=405, doc_id=1880) == '无'


# 9.1 遗传毒性：体内-微核试验部分 - 14-遗传毒性作用 字段
@pytest.mark.asyncio(loop_scope='session')
async def test_get_genotoxic_effects_9():
    with TestClient(app):
        # 微核试验: F-231926, project_id: 406 , doc_id: 1882, stag环境的
        assert await get_genotoxic_effects_9(project_id=406, doc_id=1882) == '无'

        # 微核试验: F-230784, project_id: 405 , doc_id: 1880, stag环境的
        assert await get_genotoxic_effects_9(project_id=405, doc_id=1880) == '无'


@pytest.mark.asyncio(loop_scope='session')
async def test_get_small_mc_table():
    with TestClient(app):  # 触发 lifespan 以关闭 redis
        # 微核试验: F-230784, project_id: 405 , doc_id: 1880, stag环境的
        assert await get_small_mc_table(405, 1880) == (
            (
                '| 受试物 | 剂量（mg/kg） |\n| 溶媒对照 | |\n| NS-041 | 15 |\n| NS-041 | 30 |\n| NS-041 | 60 |\n| 环磷酰胺一水合物 | 20 |',
                '[["给药组", "采样时间点 (小时)", "PCE 百分比 (均数 ± 标准误, %)", "计数 PCE 总数", "观察到 MN-PCE 总数", "MN-PCE 频率 (均数 ± 标准误, %)"], ["组 1 (溶媒对照)", "组 1 (溶媒对照)", "组 1 (溶媒对照)", "组 1 (溶媒对照)", "组 1 (溶媒对照)", "组 1 (溶媒对照)"], ["", "24", "63.29±1.57", "20000", "27", "0.14± 0.02"], ["组 2 (NS-041, 15 mg/kg/day)", "组 2 (NS-041, 15 mg/kg/day)", "组 2 (NS-041, 15 mg/kg/day)", "组 2 (NS-041, 15 mg/kg/day)", "组 2 (NS-041, 15 mg/kg/day)", "组 2 (NS-041, 15 mg/kg/day)"], ["", "24", "65.10±0.81", "20000", "26", "0.13± 0.02"], ["组 3 (NS-041, 30 mg/kg/day)", "组 3 (NS-041, 30 mg/kg/day)", "组 3 (NS-041, 30 mg/kg/day)", "组 3 (NS-041, 30 mg/kg/day)", "组 3 (NS-041, 30 mg/kg/day)", "组 3 (NS-041, 30 mg/kg/day)"], ["", "24", "63.51±1.46", "20000", "23", "0.12±0.01"], ["组 4 (NS-041, 60 mg/kg/day)", "组 4 (NS-041, 60 mg/kg/day)", "组 4 (NS-041, 60 mg/kg/day)", "组 4 (NS-041, 60 mg/kg/day)", "组 4 (NS-041, 60 mg/kg/day)", "组 4 (NS-041, 60 mg/kg/day)"], ["", "24", "63.24±1.15", "20000", "25", "0.13± 0.02"], ["组 5 (环磷酰胺一水合物, 20 mg/kg)", "组 5 (环磷酰胺一水合物, 20 mg/kg)", "组 5 (环磷酰胺一水合物, 20 mg/kg)", "组 5 (环磷酰胺一水合物, 20 mg/kg)", "组 5 (环磷酰胺一水合物, 20 mg/kg)", "组 5 (环磷酰胺一水合物, 20 mg/kg)"], ["", "24", "53.83±1.63**", "20000", "831", "4.16± 0.13***"], ["**与平行溶媒对照比有显著差异, 方差分析(ANOVA), P<0.01***与平行溶媒对照比有显著差异, 方差分析(ANOVA), P<0.001", "**与平行溶媒对照比有显著差异, 方差分析(ANOVA), P<0.01***与平行溶媒对照比有显著差异, 方差分析(ANOVA), P<0.001", "**与平行溶媒对照比有显著差异, 方差分析(ANOVA), P<0.01***与平行溶媒对照比有显著差异, 方差分析(ANOVA), P<0.001", "**与平行溶媒对照比有显著差异, 方差分析(ANOVA), P<0.01***与平行溶媒对照比有显著差异, 方差分析(ANOVA), P<0.001", "**与平行溶媒对照比有显著差异, 方差分析(ANOVA), P<0.01***与平行溶媒对照比有显著差异, 方差分析(ANOVA), P<0.001", "**与平行溶媒对照比有显著差异, 方差分析(ANOVA), P<0.01***与平行溶媒对照比有显著差异, 方差分析(ANOVA), P<0.001"]]',
                '["表4 微核总结数据", "性别: 雄性"]',
            ),
            (
                '| 受试物 | 剂量（mg/kg） |\n| 溶媒对照 | |\n| NS-041 | 15 |\n| NS-041 | 30 |\n| NS-041 | 60 |\n| 环磷酰胺一水合物 | 20 |',
                '[["给药组", "采样时间点 (小时)", "PCE 百分比 (均数 ± 标准误, %)", "计数 PCE 总数", "观察到 MN-PCE 总数", "MN-PCE 频率 (均数 ± 标准误, %)"], ["组 1 (溶媒对照)", "组 1 (溶媒对照)", "组 1 (溶媒对照)", "组 1 (溶媒对照)", "组 1 (溶媒对照)", "组 1 (溶媒对照)"], ["", "24", "58.97±1.59", "20000", "24", "0.12±0.01"], ["组 2 (NS-041, 15 mg/kg/day)", "组 2 (NS-041, 15 mg/kg/day)", "组 2 (NS-041, 15 mg/kg/day)", "组 2 (NS-041, 15 mg/kg/day)", "组 2 (NS-041, 15 mg/kg/day)", "组 2 (NS-041, 15 mg/kg/day)"], ["", "24", "58.97±2.58", "20000", "22", "0.11±0.01"], ["组 3 (NS-041, 30 mg/kg/day)", "组 3 (NS-041, 30 mg/kg/day)", "组 3 (NS-041, 30 mg/kg/day)", "组 3 (NS-041, 30 mg/kg/day)", "组 3 (NS-041, 30 mg/kg/day)", "组 3 (NS-041, 30 mg/kg/day)"], ["", "24", "62.97±1.63", "20000", "25", "0.13±0.01"], ["组 4 (NS-041, 60 mg/kg/day)", "组 4 (NS-041, 60 mg/kg/day)", "组 4 (NS-041, 60 mg/kg/day)", "组 4 (NS-041, 60 mg/kg/day)", "组 4 (NS-041, 60 mg/kg/day)", "组 4 (NS-041, 60 mg/kg/day)"], ["", "24", "52.31±5.51", "20000", "28", "0.14±0.02"], ["组 5 (环磷酰胺一水合物, 20 mg/kg)", "组 5 (环磷酰胺一水合物, 20 mg/kg)", "组 5 (环磷酰胺一水合物, 20 mg/kg)", "组 5 (环磷酰胺一水合物, 20 mg/kg)", "组 5 (环磷酰胺一水合物, 20 mg/kg)", "组 5 (环磷酰胺一水合物, 20 mg/kg)"], ["", "24", "54.64±2.31", "20000", "482", "2.41±0.25***"], ["***与平行溶媒对照比有显著差异, 方差分析(ANOVA), P<0.001", "***与平行溶媒对照比有显著差异, 方差分析(ANOVA), P<0.001", "***与平行溶媒对照比有显著差异, 方差分析(ANOVA), P<0.001", "***与平行溶媒对照比有显著差异, 方差分析(ANOVA), P<0.001", "***与平行溶媒对照比有显著差异, 方差分析(ANOVA), P<0.001", "***与平行溶媒对照比有显著差异, 方差分析(ANOVA), P<0.001"]]',
                '["表4 微核总结数据(续)", "性别:  雌性"]',
            ),
        )

        # 微核试验: F-231926, project_id: 406 , doc_id: 1882, stag环境的
        assert await get_small_mc_table(406, 1882) == (
            (
                '''| 受试物 | 剂量（mg/kg） |
| 含5%DMSO+10%Solutol HS15的20% HP-β-CD去离子水溶液 | |
| 新药001 | 100 |
| 新药001 | 200 |
| 新药001 | 400 |
| 环磷酰胺一水合物 | 20 |''',
                '''[["给药组", "采样时间点 (小时)", "PCE 百分比 (均数 ± 标准误, %)", "计数 PCE 总数", "观察到 MN-PCE 总数", "MN-PCE 频率 (均数 ± 标准误, %)"], ["组 1含5%DMSO+10%Solutol HS15的20% HP-β-CD去离子水溶液", "组 1含5%DMSO+10%Solutol HS15的20% HP-β-CD去离子水溶液", "组 1含5%DMSO+10%Solutol HS15的20% HP-β-CD去离子水溶液", "组 1含5%DMSO+10%Solutol HS15的20% HP-β-CD去离子水溶液", "组 1含5%DMSO+10%Solutol HS15的20% HP-β-CD去离子水溶液", "组 1含5%DMSO+10%Solutol HS15的20% HP-β-CD去离子水溶液"], ["", "18-24", "  58.21±0.40 ", "20000", "23", "0.12±0.01 "], ["组 2 (新药001, 100 mg/kg/day)", "组 2 (新药001, 100 mg/kg/day)", "组 2 (新药001, 100 mg/kg/day)", "组 2 (新药001, 100 mg/kg/day)", "组 2 (新药001, 100 mg/kg/day)", "组 2 (新药001, 100 mg/kg/day)"], ["", "18-24", "57.85 ±0.85", "20000", "21", "0.11±0.01 "], ["组 3 (新药001, 200 mg/kg/day)", "组 3 (新药001, 200 mg/kg/day)", "组 3 (新药001, 200 mg/kg/day)", "组 3 (新药001, 200 mg/kg/day)", "组 3 (新药001, 200 mg/kg/day)", "组 3 (新药001, 200 mg/kg/day)"], ["", "18-24", "58.58 ±0.52", "20000", "22", "0.11±0.01 "], ["组 4 (新药001, 400 mg/kg/day)", "组 4 (新药001, 400 mg/kg/day)", "组 4 (新药001, 400 mg/kg/day)", "组 4 (新药001, 400 mg/kg/day)", "组 4 (新药001, 400 mg/kg/day)", "组 4 (新药001, 400 mg/kg/day)"], ["", "18-24", "58.56 ±0.94", "20000", "24", "0.12±0.01"], ["组 6 (环磷酰胺一水合物, 20 mg/kg)", "组 6 (环磷酰胺一水合物, 20 mg/kg)", "组 6 (环磷酰胺一水合物, 20 mg/kg)", "组 6 (环磷酰胺一水合物, 20 mg/kg)", "组 6 (环磷酰胺一水合物, 20 mg/kg)", "组 6 (环磷酰胺一水合物, 20 mg/kg)"], ["", "24±1", "50.31±0.56***", "20000", "655", "3.28±0.14***"], ["***与平行溶媒对照比有显著差异, 方差分析(ANOVA), P<0.001", "***与平行溶媒对照比有显著差异, 方差分析(ANOVA), P<0.001", "***与平行溶媒对照比有显著差异, 方差分析(ANOVA), P<0.001", "***与平行溶媒对照比有显著差异, 方差分析(ANOVA), P<0.001", "***与平行溶媒对照比有显著差异, 方差分析(ANOVA), P<0.001", "***与平行溶媒对照比有显著差异, 方差分析(ANOVA), P<0.001"]]''',
                '''["表5 微核总结数据", "性别: 雄性"]''',
            ),
            (
                '''| 受试物 | 剂量（mg/kg） |
| 含5%DMSO+10%Solutol HS15的20% HP-β-CD去离子水溶液 | |
| 新药001 | 100 |
| 新药001 | 200 |
| 新药001 | 400 |
| 环磷酰胺一水合物 | 20 |''',
                '''[["给药组", "采样时间点 (小时)", "PCE 百分比 (均数 ± 标准误, %)", "计数 PCE 总数", "观察到 MN-PCE 总数", "MN-PCE 频率 (均数 ± 标准误, %)"], ["组 1含5%DMSO+10%Solutol HS15的20% HP-β-CD去离子水溶液", "组 1含5%DMSO+10%Solutol HS15的20% HP-β-CD去离子水溶液", "组 1含5%DMSO+10%Solutol HS15的20% HP-β-CD去离子水溶液", "组 1含5%DMSO+10%Solutol HS15的20% HP-β-CD去离子水溶液", "组 1含5%DMSO+10%Solutol HS15的20% HP-β-CD去离子水溶液", "组 1含5%DMSO+10%Solutol HS15的20% HP-β-CD去离子水溶液"], ["", "18-24", "  57.96 ±0.28 ", "20000", "22", "0.11±0.01 "], ["组 2 (新药001, 100 mg/kg/day)", "组 2 (新药001, 100 mg/kg/day)", "组 2 (新药001, 100 mg/kg/day)", "组 2 (新药001, 100 mg/kg/day)", "组 2 (新药001, 100 mg/kg/day)", "组 2 (新药001, 100 mg/kg/day)"], ["", "18-24", "59.22 ±0.69", "20000", "22", "0.11±0.01"], ["组 3 (新药001, 200 mg/kg/day)", "组 3 (新药001, 200 mg/kg/day)", "组 3 (新药001, 200 mg/kg/day)", "组 3 (新药001, 200 mg/kg/day)", "组 3 (新药001, 200 mg/kg/day)", "组 3 (新药001, 200 mg/kg/day)"], ["", "18-24", "58.19 ±0.71", "20000", "21", "0.11±0.01"], ["组 4 (新药001, 400 mg/kg/day)", "组 4 (新药001, 400 mg/kg/day)", "组 4 (新药001, 400 mg/kg/day)", "组 4 (新药001, 400 mg/kg/day)", "组 4 (新药001, 400 mg/kg/day)", "组 4 (新药001, 400 mg/kg/day)"], ["", "18-24", "58.70 ±0.61", "20000", "22", "0.11±0.01"], ["组 5 (环磷酰胺一水合物, 20 mg/kg)", "组 5 (环磷酰胺一水合物, 20 mg/kg)", "组 5 (环磷酰胺一水合物, 20 mg/kg)", "组 5 (环磷酰胺一水合物, 20 mg/kg)", "组 5 (环磷酰胺一水合物, 20 mg/kg)", "组 5 (环磷酰胺一水合物, 20 mg/kg)"], ["", "24±1", "49.28 ±0.33 ***", "20000", "655", "3.28±0.22***"], ["***与平行溶媒对照比有显著差异, 方差分析(ANOVA), P<0.001", "***与平行溶媒对照比有显著差异, 方差分析(ANOVA), P<0.001", "***与平行溶媒对照比有显著差异, 方差分析(ANOVA), P<0.001", "***与平行溶媒对照比有显著差异, 方差分析(ANOVA), P<0.001", "***与平行溶媒对照比有显著差异, 方差分析(ANOVA), P<0.001", "***与平行溶媒对照比有显著差异, 方差分析(ANOVA), P<0.001"]]''',
                '''["表5 微核总结数据(雌性重复主试验)", "性别:  雌性"]''',
            ),
        )


@pytest.mark.asyncio(loop_scope='session')
async def test_get_merged_mc_table():
    with TestClient(app):  # 触发 lifespan 以关闭 redis
        # 微核试验: F-230784, project_id: 405 , doc_id: 1880, stag环境的
        solvent_and_dosage_form = (
            '含5% 二甲基亚砜（Dimethyl sulfoxide，DMSO）和10% Kolliphor® HS 15 (Solutol)的去离子水溶液'
        )
        assert await get_merged_mc_table(405, 1880, solvent_and_dosage_form) == [
            (
                '性别',
                '受试物',
                '剂量（mg/kg）',
                'PCE百分率（平均值 ± 标准误，%）',
                'PCE计数',
                '观察到的MN-PCE总数',
                'MN-PCE频率 (平均值 ± 标准误，%)',
            ),
            ('雄性', '溶媒对照', '', '63.29 ± 1.57', '20000', '27', '0.14 ± 0.02'),
            ('雄性', 'NS-041', '15', '65.10 ± 0.81', '20000', '26', '0.13 ± 0.02'),
            ('雄性', 'NS-041', '30', '63.51 ± 1.46', '20000', '23', '0.12 ± 0.01'),
            ('雄性', 'NS-041', '60', '63.24 ± 1.15', '20000', '25', '0.13 ± 0.02'),
            ('雄性', '环磷酰胺一水合物', '20', '53.83 ± 1.63**', '20000', '831', '4.16 ± 0.13***'),
            ('雌性', '溶媒对照', '', '58.97 ± 1.59', '20000', '24', '0.12 ± 0.01'),
            ('雌性', 'NS-041', '15', '58.97 ± 2.58', '20000', '22', '0.11 ± 0.01'),
            ('雌性', 'NS-041', '30', '62.97 ± 1.63', '20000', '25', '0.13 ± 0.01'),
            ('雌性', 'NS-041', '60', '52.31 ± 5.51', '20000', '28', '0.14 ± 0.02'),
            ('雌性', '环磷酰胺一水合物', '20', '54.64 ± 2.31', '20000', '482', '2.41 ± 0.25***'),
        ]

        # 微核试验: F-231926, project_id: 406 , doc_id: 1882, stag环境的
        solvent_and_dosage_form = '含5%DMSO+10%Solutol HS15的20% HP-β-CD去离子水溶液'
        assert await get_merged_mc_table(406, 1882, solvent_and_dosage_form) == [
            (
                '性别',
                '受试物',
                '剂量（mg/kg）',
                'PCE百分率（平均值 ± 标准误，%）',
                'PCE计数',
                '观察到的MN-PCE总数',
                'MN-PCE频率 (平均值 ± 标准误，%)',
            ),
            ('雄性', '溶媒', '', '58.21 ± 0.40', '20000', '23', '0.12 ± 0.01'),
            ('雄性', '新药001', '100', '57.85 ± 0.85', '20000', '21', '0.11 ± 0.01'),
            ('雄性', '新药001', '200', '58.58 ± 0.52', '20000', '22', '0.11 ± 0.01'),
            ('雄性', '新药001', '400', '58.56 ± 0.94', '20000', '24', '0.12 ± 0.01'),
            ('雄性', '环磷酰胺一水合物', '20', '50.31 ± 0.56***', '20000', '655', '3.28 ± 0.14***'),
            ('雌性', '溶媒', '', '57.96 ± 0.28', '20000', '22', '0.11 ± 0.01'),
            ('雌性', '新药001', '100', '59.22 ± 0.69', '20000', '22', '0.11 ± 0.01'),
            ('雌性', '新药001', '200', '58.19 ± 0.71', '20000', '21', '0.11 ± 0.01'),
            ('雌性', '新药001', '400', '58.70 ± 0.61', '20000', '22', '0.11 ± 0.01'),
            ('雌性', '环磷酰胺一水合物', '20', '49.28 ± 0.33 ***', '20000', '655', '3.28 ± 0.22***'),
        ]
