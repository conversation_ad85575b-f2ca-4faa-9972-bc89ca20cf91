import pytest
from fastapi.testclient import TestClient

from app.main import app
from app.tasks.extractor.report8 import (
    get_cytotoxic_effects_8,
    get_genotoxic_effects_8,
    get_handle_8,
    get_metabolic_system,
    get_number_of_parallel_cultures,
    get_positive_control_sample,
    get_table_8_1,
)


# 8.1 遗传毒性：体外-回复突变试验部分 - 3-平行培养物数量 字段
@pytest.mark.asyncio(loop_scope='session')
async def test_get_number_of_parallel_cultures():
    with TestClient(app):
        assert await get_number_of_parallel_cultures(project_id=422, doc_id=1928) == '3倍以上'


# 8.1 遗传毒性：体外-回复突变试验部分 - 8-阳性对照品 字段
@pytest.mark.asyncio(loop_scope='session')
async def test_get_positive_control_sample():
    with TestClient(app):
        assert (
            await get_positive_control_sample(project_id=422, doc_id=1928)
            == '2-氨基蒽（2-AA）、2-硝基芴（2-NF）、ICR-191、N-甲基-N-硝基-N亚硝基胍（MNNG）'
        )


# 8.1 遗传毒性：体外-回复突变试验部分 - 9-代谢系统 字段
@pytest.mark.asyncio(loop_scope='session')
async def test_get_metabolic_system():
    with TestClient(app):
        assert (
            await get_metabolic_system(project_id=422, doc_id=1928)
            == '代谢活化系统（β-萘黄酮和苯巴比妥诱导的大鼠肝脏S9）'
        )


# 8.1 遗传毒性：体外-回复突变试验部分 - 10-处理 字段
@pytest.mark.asyncio(loop_scope='session')
async def test_get_handle_8():
    with TestClient(app):
        assert (
            await get_handle_8(project_id=422, doc_id=1928)
            == 'S9混合物现用现配，将100 μL溶媒对照物或受试物制剂或100 μL阳性对照物、500 μL S9混合物或PBS缓冲液以及100 μL测试菌株隔夜培养液分别加入到2 mL融化的补充顶层琼脂中（维持在45±2°C）。立即混匀，然后浇入到最低葡萄糖琼脂平皿中，迅速旋转平皿使琼脂分布均匀，然后放置在水平台面上直至凝固。将凝固后的平皿倒扣在37±2°C的培养箱中孵育48至72小时，孵育结束后立即计数。所有受试物给药均在室温黄光灯下进行。'
        )


# 8.1 遗传毒性：体外-回复突变试验部分 - 11-细胞毒性作用 字段
@pytest.mark.asyncio(loop_scope='session')
async def test_get_cytotoxic_effects_8():
    with TestClient(app):
        assert await get_cytotoxic_effects_8(project_id=422, doc_id=1928) == '无'


# 8.1 遗传毒性：体外-回复突变试验部分 - 12-遗传毒性作用 字段
@pytest.mark.asyncio(loop_scope='session')
async def test_get_genotoxic_effects_8():
    with TestClient(app):
        assert await get_genotoxic_effects_8(project_id=422, doc_id=1928) == '无'


# 8.1 遗传毒性-回复突变试验 42331-31006-231925.docx
# D-231925，一样的数据
@pytest.mark.asyncio(loop_scope='session')
async def test_get_table_8_1():
    with TestClient(app):
        assert await get_table_8_1(367, 1776, '新药001') == [
            [
                '代谢活化',
                '供试品',
                '剂量水平(μg/皿)',
                '回复突变菌落数（平均菌落数±标准差）',
                '回复突变菌落数（平均菌落数±标准差）',
                '回复突变菌落数（平均菌落数±标准差）',
                '回复突变菌落数（平均菌落数±标准差）',
            ],
            ['代谢活化', '供试品', '剂量水平(μg/皿)', 'TA98', 'TA100', 'TA1535', 'WP2 uvrA'],
            ['有代谢活化', '新药001', '50', '34.33±2.52', '78.67±10.41', '10.33±4.04', '25.33±4.73'],
            ['有代谢活化', '新药001', '128', '30.67±2.89', '69.33±4.51', '12.33±3.51', '18.00±3.00'],
            ['有代谢活化', '新药001', '320', '27.00±3.61', '65.33±9.07', '15.00±2.65', '16.67±2.08'],
            ['有代谢活化', '新药001', '800', '28.00±7.94', '68.00±4.00', '10.00±5.29', '14.67±3.51'],
            ['有代谢活化', '新药001', '2000', '24.00±3.46', '64.00±6.08', '14.67±3.06', '14.00±2.65'],
            ['有代谢活化', '溶媒', '溶媒', '30.33±3.51', '65.67±2.08', '15.33±3.06', '14.00±0.00'],
            ['有代谢活化', '阳性对照', '阳性对照', '1162.00±64.09', '1054.67±116.90', '155.33±42.36', '225.33±29.14'],
            ['无代谢活化', '新药001', '50', '28.33±3.51', '85.33±19.14', '15.67±1.53', '14.00±14.00'],
            ['无代谢活化', '新药001', '128', '23.67±5.03', '78.67±6.66', '17.00±6.56', '15.00±15.00'],
            ['无代谢活化', '新药001', '320', '27.00±4.36', '94.00±8.72', '10.67±4.16', '16.00±16.00'],
            ['无代谢活化', '新药001', '800', '25.67±5.51', '79.33±20.03', '7.67±1.53', '20.33±20.33'],
            ['无代谢活化', '新药001', '2000', '23.33±2.08', '69.67±9.61', '10.00±2.00', '17.67±17.67'],
            ['无代谢活化', '溶媒', '溶媒', '28.33±6.11', '89.33±12.66', '15.67±2.52', '15.33±15.33'],
        ]

        assert await get_table_8_1(440, 1994, '新药001') == [
            ['代谢活化', '供试品', '剂量水平(μg/皿)', '回复突变菌落数（平均菌落数±标准差）', '回复突变菌落数（平均菌落数±标准差）', '回复突变菌落数（平均菌落数±标准差）', '回复突变菌落数（平均菌落数±标准差）', '回复突变菌落数（平均菌落数±标准差）',],
            ['代谢活化', '供试品', '剂量水平(μg/皿)', 'TA98', 'TA100', 'TA1535', 'TA1537', 'WP2 uvrA'],
            ['有代谢活化', '新药001', '200', '29.67±2.52', '144.00±13.23', '5.67±3.06', '8.00±4.00', '21.33±2.52'],
            ['有代谢活化', '新药001', '500', '29.33±8.33', '146.33±13.28', '5.67±3.51', '6.33±1.53', '22.67±3.06'],
            ['有代谢活化', '新药001', '1000', '33.67±6.81', '155.33±1.53', '6.33±3.51', '6.00±3.61', '22.33±6.43'],
            ['有代谢活化', '新药001', '2000', '29.33±8.02', '160.00±13.11', '5.67±1.15', '6.67±1.15', '21.67±4.16'],
            ['有代谢活化', '新药001', '5000', '24.33±2.08', '118.67±21.55', '5.33±2.52', '3.33±0.58', '23.33±3.06'],
            ['有代谢活化', '溶媒', '溶媒', '24.00±4.36', '139.00±20.66', '7.00±3.00', '5.33±2.08', '21.67±3.51'],
            ['有代谢活化', '阳性对照', '阳性对照', '1510.67±113.16', '1302.67±351.23', '140.00±41.58', '187.33±17.62', '266.67±10.02'],
            ['无代谢活化', '新药001', '200', '23.00±7.94', '133.00±11.79', '9.67±2.08', '6.33±1.15', '17.67±3.06'],
            ['无代谢活化', '新药001', '500', '22.33±1.15', '157.33±13.20', '11.33±3.06', '10.33±1.15', '19.33±3.06'],
            ['无代谢活化', '新药001', '1000', '21.33±7.57', '147.33±12.34', '8.00±1.73', '5.00±1.73', '21.00±1.00'],
            ['无代谢活化', '新药001', '2000', '21.00±2.65', '135.33±23.09', '7.67±3.06', '5.00±1.73', '13.67±3.06'],
            ['无代谢活化', '新药001', '5000', '13.33±3.06', '135.33±27.39', '7.67±3.06', '3.00±1.00', '16.00±1.73'],
            ['无代谢活化', '溶媒', '溶媒', '18.67±1.53', '130.33±14.01', '11.00±2.65', '5.33±0.58', '21.00±4.58'],
            ['无代谢活化', '阳性对照', '阳性对照', '557.33±85.33', '664.00±45.08', '177.67±12.66', '234.00±19.00', '161.67±19.43'],
        ]
