import pytest
from fastapi.testclient import TestClient

from app.main import app
from app.tasks.extractor.report3 import generate_pk_table, get_small_pk_table


@pytest.mark.asyncio(loop_scope='session')
async def test_get_small_pk_table():
    with TestClient(app):  # 触发 lifespan
        # AB-231646
        assert (
            await get_small_pk_table(405, 1870)
            == '''| 性别（雄/雌） | 性别（雄/雌） | 雄 | 雌 |
| 时间 | 剂量(mg/kg) | AUClast (hr*ng/ml) | AUClast (hr*ng/ml) |
| 1 | 5 | 65107 | 17292 |
| 1 | 15 | 336811 | 188949 |
| 1 | 50 | 459393 | 1468814 |
| 1 | 100 | 1263210 | 2489036 |
| 14 | 5 | 59296 | 24826 |
| 14 | 15 | 302480 | 99587 |
| 14 | 50 | 331361 | 498004 |
| 14 | 100 | 404698 | - |'''
        )

        # B-3D3211
        assert (
            await get_small_pk_table(405, 1871)
            == '''| 性别（雄/雌） | 性别（雄/雌） | 雄 | 雌 |
| 时间 | 剂量(mg/kg) | AUClast (hr*ng/ml) | AUClast (hr*ng/ml) |
| 1 | 0.3 | 28.1 | 56.2 |
| 1 | 1.0 | 91.3 | 141 |
| 1 | 3.0 | 257 | 226 |
| 28 | 0.3 | 21.9 | 23.4 |
| 28 | 1.0 | 140 | 196 |
| 28 | 3.0 | 465 | 149 |'''
        )

        # B-3R3248
        assert (
            await get_small_pk_table(405, 1872)
            == '''| 性别（雄/雌） | 性别（雄/雌） | 雄 | 雌 |
| 时间 | 剂量(mg/kg) | AUClast (hr*ng/ml) | AUClast (hr*ng/ml) |
| 1 | 3 | 975 | 2482 |
| 1 | 6 | 1929 | 3837 |
| 1 | 10 | 1921 | 5198 |
| 28 | 3 | 1443 | 3680 |
| 28 | 6 | 2220 | 4637 |
| 28 | 10 | 1920 | 7980 |'''
        )

        # B-23238
        assert (
            await get_small_pk_table(405, 1873)
            == '''| 性别（雄/雌） | 性别（雄/雌） | 雄 | 雌 |
| 时间 | 剂量(mg/kg) | AUClast (hr*ng/ml) | AUClast (hr*ng/ml) |
| 1 | 10 | 1051074 | 1248594 |
| 1 | 25 | 2020304 | 2838366 |
| 1 | 75 | 2681692 | 3417484 |
| 1 | 150 | 3203287 | 3990086 |
| 14 | 10 | 1999763 | 5888665 |
| 14 | 25 | 2321910 | 6223126 |
| 14 | 75 | 2820237 | 4889237 |
| 14 | 150 | 2542402 | 4970898 |'''
        )

        # C-230613
        assert (
            await get_small_pk_table(405, 1874)
            == '''| 性别（雄/雌） | 性别（雄/雌） | 雄 | 雌 |
| 时间 | 剂量(mg/kg) | AUClast (hr*ng/ml) | AUClast (hr*ng/ml) |
| 1 | 2 | 1047 | 2060 |
| 1 | 3 | 1197 | 3962 |
| 1 | 5 | 1587 | 2674 |
| 28 | 2 | 583 | 2945 |
| 28 | 3 | 952 | 4374 |
| 28 | 5 | 1220 | 3344 |'''
        )

        # C-232003
        assert (
            await get_small_pk_table(405, 1875)
            == '''| 性别（雄/雌） | 性别（雄/雌） | 雄 | 雌 |
| 时间 | 剂量(mg/kg) | AUClast (hr*ng/ml) | AUClast (hr*ng/ml) |
| 1 | 1.5 | 7539 | 11670 |
| 1 | 5 | 38835 | 37662 |
| 1 | 15 | 190826 | 163700 |
| 28 | 1.5 | 6854 | 9386 |
| 28 | 5 | 34451 | 32532 |
| 28 | 15 | 216157 | 169215 |'''
        )

        # C-230614
        assert (
            await get_small_pk_table(405, 1876)
            == '''| 性别（雄/雌） | 性别（雄/雌） | 雄 | 雌 |
| 时间 | 剂量(mg/kg) | AUClast (hr*ng/ml) | AUClast (hr*ng/ml) |
| 1 | 1 | 116 | 71.6 |
| 1 | 2 | 261 | 135 |
| 1 | 4 | 477 | 289 |
| 28 | 1 | 166 | 107 |
| 28 | 2 | 299 | 276 |
| 28 | 4 | 455 | 450 |'''
        )

        # C-231610
        assert (
            await get_small_pk_table(405, 1877)
            == '''| 性别（雄/雌） | 性别（雄/雌） | 雄 | 雌 |
| 时间 | 剂量(mg/kg) | AUClast (hr*ng/ml) | AUClast (hr*ng/ml) |
| 1 | 2.5 | - | 416645 |
| 1 | 5 | 517794 | - |
| 1 | 7.5 | - | 1379860 |
| 1 | 15 | 1492946 | - |
| 1 | 50 | - | 3926221 |
| 1 | 75 | 2692723 | - |
| 28 | 2.5 | - | 4027396 |
| 28 | 5 | 1291721 | - |
| 28 | 7.5 | - | 6625633 |
| 28 | 15 | 3264410 | - |
| 28 | 50 | - | 6920757 |
| 28 | 75 | 2884819 | - |'''
        )


@pytest.mark.asyncio(loop_scope='session')
async def test_generate_pk_table():
    with TestClient(app):  # 触发 lifespan
        assert await generate_pk_table(434) == [
            [
                '种属',
                '种属',
                'Sprague-Dawley大鼠 [Crl: CD (SD)] (SPF/VAF)',
                'Sprague-Dawley大鼠 [Crl: CD (SD)] (SPF/VAF)',
                'Sprague-Dawley大鼠[Crl:CD(SD)IGS](SPF)',
                'Sprague-Dawley大鼠[Crl:CD(SD)IGS](SPF)',
                'Sprague-Dawley大鼠[Crl:CD(SD)IGS](SPF/VAF)',
                'Sprague-Dawley大鼠[Crl:CD(SD)IGS](SPF/VAF)',
                'Sprague-Dawley大鼠[Crl:CD(SD)IGS](SPF/VAF)',
                'Sprague-Dawley大鼠[Crl:CD(SD)IGS](SPF/VAF)',
                '比格犬',
                '比格犬',
                '比格犬',
                '比格犬',
                '比格犬（Beagle Dogs，无给药史，普通级）',
                '比格犬（Beagle Dogs，无给药史，普通级）',
                '比格犬（Beagle Dogs，无给药史，普通级）',
                '比格犬（Beagle Dogs，无给药史，普通级）',
            ],
            [
                '给药途径',
                '给药途径',
                '灌胃',
                '灌胃',
                '经口灌胃',
                '经口灌胃',
                '经口灌胃',
                '经口灌胃',
                '经口灌胃',
                '经口灌胃',
                '灌胃',
                '灌胃',
                '经口灌胃',
                '经口灌胃',
                '经口灌胃',
                '经口灌胃',
                '经口灌胃',
                '经口灌胃',
            ],
            [
                '试验类型',
                '试验类型',
                '28天',
                '28天',
                '14天',
                '14天',
                '28天',
                '28天',
                '28天',
                '28天',
                '28天',
                '28天',
                '14天',
                '14天',
                '28天',
                '28天',
                '28天',
                '28天',
            ],
            [
                '性别（雄/雌）',
                '性别（雄/雌）',
                '雄',
                '雌',
                '雄',
                '雌',
                '雄',
                '雌',
                '雄',
                '雌',
                '雄',
                '雌',
                '雄',
                '雌',
                '雄',
                '雌',
                '雄',
                '雌',
            ],
            [
                '时间',
                '剂量(mg/kg)',
                'AUClast (hr*ng/ml)',
                'AUClast (hr*ng/ml)',
                'AUClast (hr*ng/ml)',
                'AUClast (hr*ng/ml)',
                'AUClast (hr*ng/ml)',
                'AUClast (hr*ng/ml)',
                'AUClast (hr*ng/ml)',
                'AUClast (hr*ng/ml)',
                'AUClast (hr*ng/ml)',
                'AUClast (hr*ng/ml)',
                'AUClast (hr*ng/ml)',
                'AUClast (hr*ng/ml)',
                'AUClast (hr*ng/ml)',
                'AUClast (hr*ng/ml)',
                'AUClast (hr*ng/ml)',
                'AUClast (hr*ng/ml)',
            ],
            ['第1天', '0.3', '-', '-', '-', '-', '-', '-', '-', '-', '28.1', '56.2', '-', '-', '-', '-', '-', '-'],
            ['第1天', '1', '-', '-', '-', '-', '-', '-', '-', '-', '91.3', '141', '-', '-', '-', '-', '116', '71.6'],
            ['第1天', '1.5', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-', '7539', '11670', '-', '-'],
            ['第1天', '2', '-', '-', '-', '-', '-', '-', '1047', '2060', '-', '-', '-', '-', '-', '-', '261', '135'],
            ['第1天', '2.5', '-', '-', '-', '-', '-', '416645', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-'],
            [
                '第1天',
                '3',
                '975',
                '2482',
                '-',
                '-',
                '-',
                '-',
                '1197',
                '3962',
                '257',
                '226',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
            ],
            ['第1天', '4', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-', '477', '289'],
            [
                '第1天',
                '5',
                '-',
                '-',
                '-',
                '-',
                '517794',
                '-',
                '1587',
                '2674',
                '-',
                '-',
                '65107',
                '17292',
                '38835',
                '37662',
                '-',
                '-',
            ],
            ['第1天', '6', '1929', '3837', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-'],
            ['第1天', '7.5', '-', '-', '-', '-', '-', '1379860', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-'],
            [
                '第1天',
                '10',
                '1921',
                '5198',
                '1051074',
                '1248594',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
            ],
            [
                '第1天',
                '15',
                '-',
                '-',
                '-',
                '-',
                '1492946',
                '-',
                '-',
                '-',
                '-',
                '-',
                '336811',
                '188949',
                '190826',
                '163700',
                '-',
                '-',
            ],
            ['第1天', '25', '-', '-', '2020304', '2838366', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-'],
            [
                '第1天',
                '50',
                '-',
                '-',
                '-',
                '-',
                '-',
                '3926221',
                '-',
                '-',
                '-',
                '-',
                '459393',
                '1468814',
                '-',
                '-',
                '-',
                '-',
            ],
            [
                '第1天',
                '75',
                '-',
                '-',
                '2681692',
                '3417484',
                '2692723',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
            ],
            [
                '第1天',
                '100',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '1263210',
                '2489036',
                '-',
                '-',
                '-',
                '-',
            ],
            [
                '第1天',
                '150',
                '-',
                '-',
                '3203287',
                '3990086',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
            ],
            ['第14/28天', '0.3', '-', '-', '-', '-', '-', '-', '-', '-', '21.9', '23.4', '-', '-', '-', '-', '-', '-'],
            ['第14/28天', '1', '-', '-', '-', '-', '-', '-', '-', '-', '140', '196', '-', '-', '-', '-', '166', '107'],
            ['第14/28天', '1.5', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-', '6854', '9386', '-', '-'],
            ['第14/28天', '2', '-', '-', '-', '-', '-', '-', '583', '2945', '-', '-', '-', '-', '-', '-', '299', '276'],
            ['第14/28天', '2.5', '-', '-', '-', '-', '-', '4027396', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-'],
            [
                '第14/28天',
                '3',
                '1443',
                '3680',
                '-',
                '-',
                '-',
                '-',
                '952',
                '4374',
                '465',
                '149',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
            ],
            ['第14/28天', '4', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-', '455', '450'],
            [
                '第14/28天',
                '5',
                '-',
                '-',
                '-',
                '-',
                '1291721',
                '-',
                '1220',
                '3344',
                '-',
                '-',
                '59296',
                '24826',
                '34451',
                '32532',
                '-',
                '-',
            ],
            ['第14/28天', '6', '2220', '4637', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-'],
            ['第14/28天', '7.5', '-', '-', '-', '-', '-', '6625633', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-'],
            [
                '第14/28天',
                '10',
                '1920',
                '7980',
                '1999763',
                '5888665',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
            ],
            [
                '第14/28天',
                '15',
                '-',
                '-',
                '-',
                '-',
                '3264410',
                '-',
                '-',
                '-',
                '-',
                '-',
                '302480',
                '99587',
                '216157',
                '169215',
                '-',
                '-',
            ],
            [
                '第14/28天',
                '25',
                '-',
                '-',
                '2321910',
                '6223126',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
            ],
            [
                '第14/28天',
                '50',
                '-',
                '-',
                '-',
                '-',
                '-',
                '6920757',
                '-',
                '-',
                '-',
                '-',
                '331361',
                '498004',
                '-',
                '-',
                '-',
                '-',
            ],
            [
                '第14/28天',
                '75',
                '-',
                '-',
                '2820237',
                '4889237',
                '2884819',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
            ],
            ['第14/28天', '100', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-', '404698', '-', '-', '-', '-', '-'],
            [
                '第14/28天',
                '150',
                '-',
                '-',
                '2542402',
                '4970898',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
                '-',
            ],
        ]
