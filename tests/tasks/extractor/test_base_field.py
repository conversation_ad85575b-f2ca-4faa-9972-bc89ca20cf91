import pytest
from fastapi.testclient import TestClient

from app.main import app
from app.schemas.doc_base_field import TrialSubType
from app.tasks.extractor.base_field import (
    get_administration_dosage,
    get_administration_method,
    get_cover,
    get_dosing_regimen,
    get_solvent_and_dosage_form,
    get_species,
    parse_cover,
)


@pytest.mark.asyncio(loop_scope='session')
async def test_get_cover():
    with TestClient(app):  # 触发 lifespan
        assert await get_cover(1886)


@pytest.mark.asyncio(loop_scope='session')
async def test_parse_cover():
    with TestClient(app):  # 触发 lifespan
        # A-230611
        assert await parse_cover(405, 1867) in [
            (
                'XXX',
                '',  # TODO: 这里丢失了换行
                'Sprague Dawley大鼠单次经口灌胃给予XXX的急性毒理学试验',
                '康龙化成（北京）生物技术有限公司',
                '33416-230611',
            ),
            (
                'XXX',
                'XXX中华人民共和国XXXXXX',  # TODO: 这里丢失了换行
                'Sprague Dawley大鼠单次经口灌胃给予XXX的急性毒理学试验',
                '康龙化成（北京）生物技术有限公司',
                '33416-230611',
            ),
        ]

        # A-230612
        assert await parse_cover(405, 1868) in [
            (
                'XXX',
                '',  # TODO: 这里丢失了换行
                '比格犬单次经口灌胃给予XXX的急性毒理学试验',
                '康龙化成（北京）生物技术有限公司',
                '33416-230612',
            ),
            (
                'XXX',
                'XXX中华人民共和国XXXXXX',  # TODO: 这里丢失了换行
                '比格犬单次经口灌胃给予XXX的急性毒理学试验',
                '康龙化成（北京）生物技术有限公司',
                '33416-230612',
            ),
        ]

        # A-231609
        assert await parse_cover(405, 1869) == (
            '新药001',
            '',
            'Sprague Dawley大鼠经口灌胃给予新药001的最大耐受量试验',
            '康龙化成（北京）生物技术有限公司',
            '31003-231609',
        )

        # AB-231646
        assert await parse_cover(405, 1870) == (
            '新药001',
            '',
            '比格犬经口灌胃给予新药001的最大耐受量和14天剂量范围探索毒理学及毒代动力学试验',
            '康龙化成（北京）生物技术有限公司',
            '31003-231646',
        )

        # B-3D3211
        assert await parse_cover(405, 1871) == (
            'XXX',
            '',
            '比格犬灌胃给予XXX的28天剂量范围确定的毒理学和毒代动力学试验',
            '康龙化成（宁波）药物开发有限公司',
            '3D3211',
        )

        # B-3R3248
        assert await parse_cover(405, 1872) == (
            'XXX',
            '',
            'Sprague-Dawley大鼠灌胃给予XXX的28天剂量范围确定的毒理学和毒代动力学试验',
            '康龙化成（宁波）药物开发有限公司',
            '3R3248',
        )

        # B-23238
        assert await parse_cover(405, 1873) == (
            '新药001',
            '',
            'Sprague Dawley大鼠经口灌胃给予新药001的14天剂量范围确定的毒理学和毒代动力学试验',
            '康龙化成（宁波）药物开发有限公司',
            '31053-23238',
        )

        # C-230613
        assert await parse_cover(405, 1874) == (
            'NS-041',
            '纽欧申医药（上海）有限公司',
            'Sprague Dawley大鼠经口灌胃给予NS-041连续28天及恢复28天的毒理学及毒代动力学试验',
            '康龙化成（北京）生物技术有限公司',
            '33403-230613',
        )

        # C-232003
        assert await parse_cover(405, 1875) == (
            '新药001',
            '',
            '比格犬经口灌胃重复给予新药001连续28天及恢复28天的毒理学及毒代动力学试验',
            '康龙化成（北京）生物技术有限公司',
            '31003-232003',
        )

        # C-230614
        assert await parse_cover(405, 1876) == (
            'NS-041',
            '纽欧申医药（上海）有限公司',
            '比格犬经口灌胃给予NS-041连续28天及恢复28天的毒理学及毒代动力学试验',
            '康龙化成（北京）生物技术有限公司',
            '33403-230614',
        )

        # C-231610
        assert await parse_cover(405, 1877) == (
            '新药001',
            '',
            'Sprague Dawley大鼠经口灌胃给予新药001连续28天及恢复28天的毒理学及毒代动力学试验',
            '康龙化成（北京）生物技术有限公司',
            '31003-231610',
        )

        # D-231925
        assert await parse_cover(405, 1878) in [
            (
                '新药001',
                '',
                '新药001：鼠伤寒沙门氏菌和大肠埃希杆菌回复突变试验',
                '康龙化成（北京）生物技术有限公司',
                '31006-231925',
            ),
            (
                '新药001',
                '',
                '新药001：鼠伤寒沙门氏菌和大肠埃希杆菌回复突变试验总结报告',
                '康龙化成（北京）生物技术有限公司',
                '31006-231925',
            ),
        ]
        return

        # E-230783
        assert await parse_cover(405, 1879) == (
            'NS-041',
            '纽欧申医药（上海）有限公司',
            'NS-041：中国仓鼠卵巢细胞体外染色体畸变试验',
            '康龙化成（北京）生物技术有限公司',
            '33406-230783',
        )

        # F-230784
        assert await parse_cover(405, 1880) == (
            'NS-041',
            '纽欧申医药（上海）有限公司',
            'NS-041：大鼠体内微核试验',
            '康龙化成（北京）生物技术有限公司',
            '33406-230784',
        )

        # E-231924
        assert await parse_cover(406, 1881) == (
            '新药001',
            '',
            '新药001：中国仓鼠卵巢细胞体外染色体畸变试验',
            '康龙化成（北京）生物技术有限公司',
            '31006-231924',
        )

        # F-231926
        assert await parse_cover(406, 1882) == (
            '新药001',
            '',
            'Sprague Dawley大鼠经口灌胃给予新药001连续2天的微核试验',
            '康龙化成（北京）生物技术有限公司',
            '31006-231926',
        )


@pytest.mark.asyncio(loop_scope='session')
async def test_get_species():
    with TestClient(app):  # 触发 lifespan
        # A-230611
        assert await get_species(405, 1867, TrialSubType.SINGLE_DOSE) == 'Sprague-Dawley大鼠[Crl:CD(SD)IGS](SPF/VAF)'

        # A-230612
        assert await get_species(405, 1868, TrialSubType.SINGLE_DOSE) == '比格犬'

        # A-231609
        assert await get_species(405, 1869, TrialSubType.SINGLE_DOSE) == 'Sprague-Dawley大鼠[Crl:CD(SD)IGS](SPF)'

        # AB-231646
        assert await get_species(405, 1870, TrialSubType.REPEATED_DOSE_SPECIFICITY) == '比格犬'

        # B-3D3211
        assert await get_species(405, 1871, TrialSubType.REPEATED_DOSE_SPECIFICITY) == '比格犬'

        # B-3R3248
        assert (
            await get_species(405, 1872, TrialSubType.REPEATED_DOSE_SPECIFICITY)
            == 'Sprague-Dawley大鼠 [Crl: CD (SD)] (SPF/VAF)'
        )

        # B-23238
        assert (
            await get_species(405, 1873, TrialSubType.REPEATED_DOSE_SPECIFICITY)
            == 'Sprague-Dawley大鼠[Crl:CD(SD)IGS](SPF)'
        )

        # C-230613
        assert await get_species(405, 1874, TrialSubType.REPEATED_DOSE) == 'Sprague-Dawley大鼠[Crl:CD(SD)IGS](SPF/VAF)'

        # C-232003
        assert await get_species(405, 1875, TrialSubType.REPEATED_DOSE) == '比格犬（Beagle Dogs，无给药史，普通级）'

        # C-230614
        assert await get_species(405, 1876, TrialSubType.REPEATED_DOSE) == '比格犬（Beagle Dogs，无给药史，普通级）'

        # C-231610
        assert await get_species(405, 1877, TrialSubType.REPEATED_DOSE) == 'Sprague-Dawley大鼠[Crl:CD(SD)IGS](SPF/VAF)'

        # D-231925
        assert (
            await get_species(405, 1878, TrialSubType.AMES_TEST)
            == '鼠伤寒沙门氏菌 TA98，鼠伤寒沙门氏菌 TA100，鼠伤寒沙门氏菌 TA1535，鼠伤寒沙门氏菌 TA1537，大肠埃希杆菌 WP2 uvrA'
        )

        # E-230783
        await parse_cover(405, 1879)  # 先生成 cover 的缓存
        assert await get_species(405, 1879, TrialSubType.CHROMOSOME_ABERRATION_TEST) == '中国仓鼠卵巢细胞'

        # F-230784
        assert await get_species(405, 1880, TrialSubType.MICRONUCLEUS_TEST) == '大鼠'

        # E-231924
        await parse_cover(406, 1881)  # 先生成 cover 的缓存
        assert await get_species(406, 1881, TrialSubType.CHROMOSOME_ABERRATION_TEST) == '中国仓鼠卵巢细胞'

        # F-231926
        assert await get_species(406, 1882, TrialSubType.MICRONUCLEUS_TEST) == '大鼠'


@pytest.mark.asyncio(loop_scope='session')
async def test_get_solvent_and_dosage_form():
    with TestClient(app):  # 触发 lifespan
        # A-230611
        assert (
            await get_solvent_and_dosage_form(405, 1867, TrialSubType.SINGLE_DOSE)
            == '含5% DMSO和10% Solutol HS 15的去离子水溶液'
        )

        # A-230612
        assert (
            await get_solvent_and_dosage_form(405, 1868, TrialSubType.SINGLE_DOSE)
            == '含5% DMSO 和10% Solutol HS 15 的去离子水溶液'
        )

        # A-231609
        assert (
            await get_solvent_and_dosage_form(405, 1869, TrialSubType.SINGLE_DOSE)
            == '含10%乙醇和40% PEG 400的去离子水溶液'
        )

        # AB-231646
        assert (
            await get_solvent_and_dosage_form(405, 1870, TrialSubType.REPEATED_DOSE_SPECIFICITY)
            == '含5% DMSO+10% Solutol的20%HP-β-CD去离子水溶液'
        )

        # B-3D3211
        assert (
            await get_solvent_and_dosage_form(405, 1871, TrialSubType.REPEATED_DOSE_SPECIFICITY)
            == '含5% DMSO和10% Solutol的去离子水溶液'
        )

        # B-3R3248
        assert (
            await get_solvent_and_dosage_form(405, 1872, TrialSubType.REPEATED_DOSE)
            == '含5% DMSO和10% Solutol的去离子水溶液'
        )

        # B-23238
        assert (
            await get_solvent_and_dosage_form(405, 1873, TrialSubType.REPEATED_DOSE_SPECIFICITY)
            == '5%DMSO+10%Solutol的20%HP-β-CD去离子水'
        )

        # C-230613
        assert (
            await get_solvent_and_dosage_form(405, 1874, TrialSubType.REPEATED_DOSE)
            == '5% DMSO和10% Solutol HS 15的去离子水溶液'
        )

        # C-232003
        assert (
            await get_solvent_and_dosage_form(405, 1875, TrialSubType.REPEATED_DOSE)
            == '含5%DMSO+10%Solutol HS15 的20% HP-β-CD 去离子水溶液'
        )

        # C-230614
        assert (
            await get_solvent_and_dosage_form(405, 1876, TrialSubType.REPEATED_DOSE)
            == '含5% DMSO 和10% Solutol HS 15 的去离子水溶液'
        )

        # C-231610
        assert (
            await get_solvent_and_dosage_form(405, 1877, TrialSubType.REPEATED_DOSE)
            == '含5%DMSO+10%Solutol HS15的20% HP-β-CD去离子水溶液'
        )

        # D-231925
        assert await get_solvent_and_dosage_form(405, 1878, TrialSubType.AMES_TEST) == '二甲基亚砜（DMSO）'

        # E-230783
        assert (
            await get_solvent_and_dosage_form(405, 1879, TrialSubType.CHROMOSOME_ABERRATION_TEST)
            == '二甲基亚砜（DMSO）'
        )

        # F-230784
        assert (
            await get_solvent_and_dosage_form(405, 1880, TrialSubType.MICRONUCLEUS_TEST)
            == '含5% 二甲基亚砜（Dimethyl sulfoxide，DMSO）和10% Kolliphor® HS 15 (Solutol)的去离子水溶液'
        )

        # E-231924
        assert (
            await get_solvent_and_dosage_form(406, 1881, TrialSubType.CHROMOSOME_ABERRATION_TEST)
            == '二甲基亚砜（DMSO）'
        )

        # F-231926
        assert (
            await get_solvent_and_dosage_form(406, 1882, TrialSubType.MICRONUCLEUS_TEST)
            == '含5%DMSO+10%Solutol HS15的20% HP-β-CD去离子水溶液'
        )


@pytest.mark.asyncio(loop_scope='session')
async def test_get_administration_method():
    with TestClient(app):  # 触发 lifespan
        # A-230611
        assert (
            await get_administration_method(405, 1867, TrialSubType.SINGLE_DOSE)
            == '经口灌胃给药。给药前整夜禁食，给药后约2小时喂食'  # TODO: 经口灌胃
        )

        # A-230612
        assert (
            await get_administration_method(405, 1868, TrialSubType.SINGLE_DOSE)
            == '经口灌胃，给药前整晚禁食'  # TODO: 经口灌胃
        )

        # A-231609
        assert await get_administration_method(405, 1869, TrialSubType.SINGLE_DOSE) == '经口灌胃'

        # AB-231646
        await parse_cover(405, 1870)
        assert await get_administration_method(405, 1870, TrialSubType.REPEATED_DOSE_SPECIFICITY) == '经口灌胃'

        # B-3D3211
        await parse_cover(405, 1871)
        assert await get_administration_method(405, 1871, TrialSubType.REPEATED_DOSE_SPECIFICITY) == '灌胃'

        # B-3R3248
        await parse_cover(405, 1872)
        assert await get_administration_method(405, 1872, TrialSubType.REPEATED_DOSE) == '灌胃'

        # B-23238
        await parse_cover(405, 1873)
        assert await get_administration_method(405, 1873, TrialSubType.REPEATED_DOSE_SPECIFICITY) == '经口灌胃'

        # C-230613
        await parse_cover(405, 1874)
        assert await get_administration_method(405, 1874, TrialSubType.REPEATED_DOSE) == '经口灌胃'

        # C-232003
        await parse_cover(405, 1875)
        assert await get_administration_method(405, 1875, TrialSubType.REPEATED_DOSE) == '经口灌胃'

        # C-230614
        await parse_cover(405, 1876)
        assert await get_administration_method(405, 1876, TrialSubType.REPEATED_DOSE) == '经口灌胃'

        # C-231610
        await parse_cover(405, 1877)
        assert await get_administration_method(405, 1877, TrialSubType.REPEATED_DOSE) == '经口灌胃'

        # D-231925
        assert await get_administration_method(405, 1878, TrialSubType.AMES_TEST) == '体外'

        # E-230783
        assert await get_administration_method(405, 1879, TrialSubType.CHROMOSOME_ABERRATION_TEST) == '体外'

        # F-230784
        assert await get_administration_method(405, 1880, TrialSubType.MICRONUCLEUS_TEST) == '体内'

        # E-231924
        assert await get_administration_method(406, 1881, TrialSubType.CHROMOSOME_ABERRATION_TEST) == '体外'

        # F-231926
        assert await get_administration_method(406, 1882, TrialSubType.MICRONUCLEUS_TEST) == '体内'


@pytest.mark.asyncio(loop_scope='session')
async def test_get_dosing_regimen():
    with TestClient(app):  # 触发 lifespan
        # A-230611
        assert await get_dosing_regimen(405, 1867, TrialSubType.SINGLE_DOSE) == '-'

        # A-230612
        assert await get_dosing_regimen(405, 1868, TrialSubType.SINGLE_DOSE) == '-'

        # A-231609
        assert await get_dosing_regimen(405, 1869, TrialSubType.SINGLE_DOSE) == '-'

        # AB-231646
        await parse_cover(405, 1870)
        assert await get_dosing_regimen(405, 1870, TrialSubType.REPEATED_DOSE_SPECIFICITY) == '14天'

        # B-3D3211
        await parse_cover(405, 1871)
        assert await get_dosing_regimen(405, 1871, TrialSubType.REPEATED_DOSE_SPECIFICITY) == '28天'

        # B-3R3248
        await parse_cover(405, 1872)
        assert await get_dosing_regimen(405, 1872, TrialSubType.REPEATED_DOSE) == '28天'

        # B-23238
        await parse_cover(405, 1873)
        assert await get_dosing_regimen(405, 1873, TrialSubType.REPEATED_DOSE_SPECIFICITY) == '14天'

        # C-230613
        await parse_cover(405, 1874)
        assert await get_dosing_regimen(405, 1874, TrialSubType.REPEATED_DOSE) == '28天'

        # C-232003
        await parse_cover(405, 1875)
        assert await get_dosing_regimen(405, 1875, TrialSubType.REPEATED_DOSE) == '28天'

        # C-230614
        await parse_cover(405, 1876)
        assert await get_dosing_regimen(405, 1876, TrialSubType.REPEATED_DOSE) == '28天'

        # C-231610
        await parse_cover(405, 1877)
        assert await get_dosing_regimen(405, 1877, TrialSubType.REPEATED_DOSE) == '28天'

        # D-231925
        assert await get_dosing_regimen(405, 1878, TrialSubType.AMES_TEST) == '-'

        # E-230783
        assert (
            await get_dosing_regimen(405, 1879, TrialSubType.CHROMOSOME_ABERRATION_TEST)
            == '+S9, 3h | -S9, 3h | -S9, 20h'
        )

        # F-230784
        assert await get_dosing_regimen(405, 1880, TrialSubType.MICRONUCLEUS_TEST) == '间隔24h给予2次'

        # E-231924
        assert (
            await get_dosing_regimen(406, 1881, TrialSubType.CHROMOSOME_ABERRATION_TEST)
            == '+S9, 3h | -S9, 3h | -S9, 20h'
        )

        # F-231926
        assert await get_dosing_regimen(406, 1882, TrialSubType.MICRONUCLEUS_TEST) == '间隔24h给予2次'


@pytest.mark.asyncio(loop_scope='session')
async def test_get_administration_dosage():
    with TestClient(app):  # 触发 lifespan
        # A-230611
        assert await get_administration_dosage(405, 1867, TrialSubType.SINGLE_DOSE) == '15、30、60 mg/kg'

        # A-230612
        assert await get_administration_dosage(405, 1868, TrialSubType.SINGLE_DOSE) == '6、8、10 mg/kg'

        # A-231609
        assert await get_administration_dosage(405, 1869, TrialSubType.SINGLE_DOSE) == '400、600、800、1000 mg/kg'

        # AB-231646
        assert (
            await get_administration_dosage(405, 1870, TrialSubType.REPEATED_DOSE_SPECIFICITY)
            == '10、50、250、500、1000 mg/kg/day'
        )

        # B-3D3211
        assert (
            await get_administration_dosage(405, 1871, TrialSubType.REPEATED_DOSE_SPECIFICITY)
            == '0.3、1.0、3.0 mg/kg/day'
        )

        # B-3R3248
        assert await get_administration_dosage(405, 1872, TrialSubType.REPEATED_DOSE) == '3、6、10 mg/kg/day'

        # B-23238
        assert (
            await get_administration_dosage(405, 1873, TrialSubType.REPEATED_DOSE_SPECIFICITY)
            == '10、25、75、150 mg/kg/day'
        )

        # C-230613
        assert await get_administration_dosage(405, 1874, TrialSubType.REPEATED_DOSE) == '2、3、5 mg/kg/day'

        # C-232003
        assert await get_administration_dosage(405, 1875, TrialSubType.REPEATED_DOSE) == '1.5、5、15 mg/kg/day'

        # C-230614
        assert await get_administration_dosage(405, 1876, TrialSubType.REPEATED_DOSE) == '1、2、4 mg/kg/day'

        # C-231610
        assert (
            await get_administration_dosage(405, 1877, TrialSubType.REPEATED_DOSE)
            == '雄性：5、15、75 mg/kg/day\n雌性：2.5、7.5、50 mg/kg/day'
        )

        # D-231925
        assert await get_administration_dosage(405, 1878, TrialSubType.AMES_TEST) == '200、500、1000、2000、5000 µg/皿'

        # E-230783
        assert (
            await get_administration_dosage(405, 1879, TrialSubType.CHROMOSOME_ABERRATION_TEST)
            == '+S9, 3 h：10、20、50 μg/mL\n-S9, 3 h：10、20、50 μg/mL\n-S9, 20 h：10、20、50 μg/mL'
        )

        # F-230784
        assert await get_administration_dosage(405, 1880, TrialSubType.MICRONUCLEUS_TEST) == '15、30、60 mg/kg'

        # E-231924
        assert (
            await get_administration_dosage(406, 1881, TrialSubType.CHROMOSOME_ABERRATION_TEST)
            == '+S9, 3 h：40、120、200、240 μg/mL\n-S9, 3 h：40、120、200、240 μg/mL\n-S9, 20 h：40、100、170、200 μg/mL'
        )

        # F-231926
        assert await get_administration_dosage(406, 1882, TrialSubType.MICRONUCLEUS_TEST) == '100、200、400 mg/kg'
