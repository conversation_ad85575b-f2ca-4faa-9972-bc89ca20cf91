import pytest
from fastapi.testclient import TestClient

from app.main import app
from app.tasks.extractor.report7 import (
    generate_table_7_3_clinical_observation,
    generate_table_7_3_food_intake,
    generate_table_7_3_gross_pathology,
    generate_table_7_3_hemagglutination,
    generate_table_7_3_near_death_or_dying,
    generate_table_7_3_urinalysis,
)

'''
267.7 续表
'''


# 267.7 续表 - 摄食量
@pytest.mark.asyncio(loop_scope='session')
async def test_generate_table_7_3_food_intake():
    with TestClient(app):
        # Sprague Dawley大鼠经口灌胃给予新药001连续28天及恢复28天的毒理学及毒代动力学试验
        assert (
            await generate_table_7_3_food_intake(
                462,
                2075,
                58986,
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ['摄食量', '-', '-', '-', '-', '-', '-', '-', '-'],
                ],
                [],
            )
        )

        # 比格犬经口灌胃重复给予新药001连续28天及恢复28天的毒理学及毒代动力学试验
        assert (
            await generate_table_7_3_food_intake(
                463,
                2076,
                59058,
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 1.5 | 1.5 | 5 | 5 | 15 | 15 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ['摄食量', '-', '-', '-', '-', '-', '-', '一般/不良', '-'],
                ],
                [],
            )
        )


# # 267.7 续表 - 临床观察
@pytest.mark.asyncio(loop_scope='session')
async def test_generate_table_7_3_clinical_observation():
    with TestClient(app):
        # # Sprague Dawley大鼠经口灌胃给予新药001连续28天及恢复28天的毒理学及毒代动力学试验
        assert (
            await generate_table_7_3_clinical_observation(
                462,
                2075,
                58988,
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ['临床观察', '-', '-', '-', '-', '-', '-', '-', '-'],
                ],
                [],
            )
        )

        # # 比格犬经口灌胃重复给予新药001连续28天及恢复28天的毒理学及毒代动力学试验
        assert (
            await generate_table_7_3_clinical_observation(
                463,
                2076,
                59060,
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 1.5 | 1.5 | 5 | 5 | 15 | 15 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ['临床观察', '-', '-', '-', '-', '-', '-', '-', '-'],
                ],
                [],
            )
        )


# 267.7 续表 - 大体病理学
@pytest.mark.asyncio(loop_scope='session')
async def test_generate_table_7_3_gross_pathology():
    with TestClient(app):
        # Sprague Dawley大鼠经口灌胃给予新药001连续28天及恢复28天的毒理学及毒代动力学试验
        assert (
            await generate_table_7_3_gross_pathology(
                462,
                2075,
                58986,
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ['大体病理学（第29天）',
                     '大体病理学（第29天）',
                     '大体病理学（第29天）',
                     '大体病理学（第29天）',
                     '大体病理学（第29天）',
                     '大体病理学（第29天）',
                     '大体病理学（第29天）',
                     '大体病理学（第29天）',
                     '大体病理学（第29天）'],
                    ['肝脏', '肝脏', '肝脏', '肝脏', '肝脏', '肝脏', '肝脏', '肝脏', '肝脏'],
                    ['体积增大', '-', '-', '-', '2', '-', '2', '5', '7']],
                [],
            )
        )

        # 比格犬经口灌胃重复给予新药001连续28天及恢复28天的毒理学及毒代动力学试验
        assert (
            await generate_table_7_3_gross_pathology(
                463,
                2076,
                59058,
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 1.5 | 1.5 | 5 | 5 | 15 | 15 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ['大体病理学', '-', '-', '-', '-', '-', '-', '-', '-'],
                ],
                [],
            )
        )


# 267.7 续表 - 濒死/死亡
@pytest.mark.asyncio(loop_scope='session')
async def test_generate_table_7_3_near_death_or_dying():
    with TestClient(app):
        # 比格犬经口灌胃重复给予新药001连续28天及恢复28天的毒理学及毒代动力学试验
        assert (
            await generate_table_7_3_near_death_or_dying(
                293,
                1279,
                57831,
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 1.5 | 1.5 | 5 | 5 | 15 | 15 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ['濒死/死亡', '0', '0', '0', '0', '0', '0', '0', '0'],
                ],
                [],
            )
        )

        # Sprague Dawley大鼠经口灌胃给予新药001连续28天及恢复28天的毒理学及毒代动力学试验
        # doc_id = 1280 And parent_id = 57921无该doc chunk
        assert (
            await generate_table_7_3_near_death_or_dying(
                293,
                1280,
                57921,
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ['濒死/死亡', '0', '0', '0', '0', '0', '0', '0', '0'],
                ],
                [],
            )
        )

        # Sprague Dawley大鼠经口灌胃给予新药001连续28天及恢复28天的毒理学及毒代动力学试验
        assert (
            await generate_table_7_3_near_death_or_dying(
                293,
                1281,
                58094,
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ['濒死/死亡', '0', '0', '0', '0', '0', '0', '0', '0'],
                ],
                [],
            )
        )


# 267.7 续表 - 尿液分析
@pytest.mark.asyncio(loop_scope='session')
async def test_generate_table_7_3_urinalysis():
    with TestClient(app):
        assert (
            await generate_table_7_3_urinalysis(
                462,
                2075,
                58995,
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    [
                        '尿液分析（第29天）',
                        '尿液分析（第29天）',
                        '尿液分析（第29天）',
                        '尿液分析（第29天）',
                        '尿液分析（第29天）',
                        '尿液分析（第29天）',
                        '尿液分析（第29天）',
                        '尿液分析（第29天）',
                        '尿液分析（第29天）',
                    ],
                    ['PRO', '-', '-', '↑', '-', '↑', '-', '↑', '↑'],
                    ['LEU', '-', '-', '↑', '-', '↑', '-', '↑', '↑'],
                    ['URO', '-', '-', '-', '-', '↑', '↑', '-', '↑'],
                ],
                [],
            )
        )

        assert (
            await generate_table_7_3_urinalysis(
                463,
                2076,
                59070,
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 1.5 | 1.5 | 5 | 5 | 15 | 15 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ['尿液分析', '-', '-', '-', '-', '-', '-', '-', '-'],
                ],
                [],
            )
        )


# 267.7 续表 - 血凝 待补充

