import pytest

from app.clients.mysql import get_session
from app.models.doc import Doc
from app.schemas.doc import DocChunkStatus
from app.tasks.chunk import doc_chunk_task


@pytest.mark.asyncio(loop_scope='session')
async def test_chunk_task():
    async with get_session() as session:
        # 固定一个doc_id进行测试
        await Doc.update_by_id(session, 320, {Doc.chunk_status.name: DocChunkStatus.NOT_STARTED.value})
        await session.commit()
        # 微核文档
        assert await doc_chunk_task(320, 'https://assets.pureglobal.cn/builder/ind267/%E5%BE%AE%E6%A0%B8%E8%AF%95%E9%AA%8C_%E5%88%87%E7%89%87%E6%A0%87%E6%B3%A8_20241029v1.docx') == True