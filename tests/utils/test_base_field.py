from app.schemas.doc_base_field import TrialMainType, TrialSubType
from app.utils.doc.doc_base_field import (
    get_trial_number,
    get_trial_title,
    get_trial_type,
)


def test_get_trial_type():
    # 单次给药: 4231-31003-231609.docx
    trial_title = 'SpragueDawley大鼠经口灌胃给予新药001的最大耐受量试验'
    trial_main_type, trial_subtype = get_trial_type(trial_title)
    assert trial_main_type == TrialMainType.SINGLE_DOSE
    assert trial_subtype == TrialSubType.SINGLE_DOSE

    # 单次给药: 33416-230611-总结报告.docx
    trial_title = 'Sprague Dawley大鼠单次经口灌胃给予XXX的急性毒理学试验'
    trial_main_type, trial_subtype = get_trial_type(trial_title)
    assert trial_main_type == TrialMainType.SINGLE_DOSE
    assert trial_subtype == TrialSubType.SINGLE_DOSE

    # 单次给药： 4. 33416-230612 data CHN.docx
    trial_title = '比格犬单次经口灌胃给予XXX的急性毒理学试验'
    trial_main_type, trial_subtype = get_trial_type(trial_title)
    assert trial_main_type == TrialMainType.SINGLE_DOSE
    assert trial_subtype == TrialSubType.SINGLE_DOSE

    # 重复给药-非关键试验：4232-31053-23238.docx
    trial_title = 'Sprague Dawley大鼠经口灌胃给予新药001的14天剂量范围确定的毒理学和毒代动力学试验'
    trial_main_type, trial_subtype = get_trial_type(trial_title)
    assert trial_main_type == TrialMainType.REPEATED_DOSE
    assert trial_subtype == TrialSubType.REPEATED_DOSE

    # 重复给药-非关键试验：3. 3D3211 Dog DRF.docx
    trial_title = '比格犬灌胃给予XXX的28天剂量范围确定的毒理学和毒代动力学试验'
    trial_main_type, trial_subtype = get_trial_type(trial_title)
    assert trial_main_type == TrialMainType.REPEATED_DOSE
    assert trial_subtype == TrialSubType.REPEATED_DOSE

    # 重复给药-非关键试验：3.  3R3248 Rat DRF.docx
    trial_title = 'Sprague-Dawley大鼠灌胃给予XXX的28天剂量范围确定的毒理学和毒代动力学试验'
    trial_main_type, trial_subtype = get_trial_type(trial_title)
    assert trial_main_type == TrialMainType.REPEATED_DOSE
    assert trial_subtype == TrialSubType.REPEATED_DOSE

    # 重复给药-关键试验：4232-31003-231610.docx
    trial_title = 'Sprague Dawley大鼠经口灌胃给予新药001连续28天及恢复28天的毒理学及毒代动力学试验'
    trial_main_type, trial_subtype = get_trial_type(trial_title)
    assert trial_main_type == TrialMainType.REPEATED_DOSE
    assert trial_subtype == TrialSubType.REPEATED_DOSE  # 这里不会区分关键

    # 重复给药-关键试验：4232-31003-232003.docx
    trial_title = '比格犬经口灌胃重复给予新药001连续28天及恢复28天的毒理学及毒代动力学试验'
    trial_main_type, trial_subtype = get_trial_type(trial_title)
    assert trial_main_type == TrialMainType.REPEATED_DOSE
    assert trial_subtype == TrialSubType.REPEATED_DOSE  # 这里不会区分关键

    # 遗传毒性-回复突变：42331-31006-231925.docx
    trial_title = '新药001：鼠伤寒沙门氏菌和大肠埃希杆菌回复突变试验'
    trial_main_type, trial_subtype = get_trial_type(trial_title)
    assert trial_main_type == TrialMainType.GENOTOXICITY
    assert trial_subtype == TrialSubType.AMES_TEST

    # 遗传毒性-染色体畸变：42331-31006-231924.docx
    trial_title = '新药001：中国仓鼠卵巢细胞体外染色体畸变试验'
    trial_main_type, trial_subtype = get_trial_type(trial_title)
    assert trial_main_type == TrialMainType.GENOTOXICITY
    assert trial_subtype == TrialSubType.CHROMOSOME_ABERRATION_TEST

    # 遗传毒性-微核：42332-31006-231926.docx
    trial_title = 'Sprague Dawley大鼠经口灌胃给予新药001连续2天的微核试验'
    trial_main_type, trial_subtype = get_trial_type(trial_title)
    assert trial_main_type == TrialMainType.GENOTOXICITY
    assert trial_subtype == TrialSubType.MICRONUCLEUS_TEST


# 1 - 3 基础字段 - 试验标题
def test_get_trial_title():
    # 单次给药：33416-230611-总结报告.docx
    assert (
        get_trial_title(
            [
                '总结报告',
                'Sprague Dawley大鼠单次经口灌胃给予XXX的急性毒理学试验',
                '受试物：XXX',
                '康龙化成TSP专题代号：33416-230611',
                '研究完成日期：2023年11月08日',
                '委托方XXX中华人民共和国XXXXXX',
                '研究机构康龙化成（北京）生物技术有限公司中华人民共和国北京市昌平区中关村生命科学园科学园路32号，102206',
            ]
        )
        == 'Sprague Dawley大鼠单次经口灌胃给予XXX的急性毒理学试验'
    )

    # 单次给药：4231-31003-231609.docx
    assert (
        get_trial_title(
            [
                '总结报告',
                'Sprague Dawley大鼠经口灌胃给予新药001的最大耐受量试验',
                '受试物：新药001',
                '康龙化成TSP专题代号：31003-231609 ',
                '委托方',
                '研究单位康龙化成（北京）生物技术有限公司中华人民共和国北京市昌平区中关村生命科学园科学园路32号，102206',
            ]
        )
        == 'Sprague Dawley大鼠经口灌胃给予新药001的最大耐受量试验'
    )

    # 单次给药：4. 33416-230612 data CHN.docx
    assert (
        get_trial_title(
            [
                '总结报告',
                '比格犬单次经口灌胃给予XXX的急性毒理学试验',
                '受试物: XXX',
                '康龙化成TSP专题代号: 33416-230612',
                '研究完成日期：2023年xx月xx日',
                '委托方XXX中华人民共和国XXXXXX',
                '研究机构康龙化成（北京）生物技术有限公司中华人民共和国北京市昌平区中关村生命科学园科学园路32号，102206',
            ]
        )
        == '比格犬单次经口灌胃给予XXX的急性毒理学试验'
    )

    # 重复给药-非关键试验 4232-31053-23238.docx
    assert (
        get_trial_title(
            [
                '试验报告',
                'Sprague Dawley大鼠经口灌胃给予新药001的14天剂量范围确定的毒理学和毒代动力学试验',
                '受试物：新药001',
                '康龙化成TSP专题代号：31053-23238',
                '研究完成日期：2024年07月09日',
                '委托方',
                '研究单位康龙化成（宁波）药物开发有限公司中华人民共和国浙江省慈溪市杭州湾生命科技园启源路39号，315336',
            ]
        )
        == 'Sprague Dawley大鼠经口灌胃给予新药001的14天剂量范围确定的毒理学和毒代动力学试验'
    )

    # 重复给药-非关键试验 3. 3D3211 Dog DRF.docx
    assert (
        get_trial_title(
            [
                '总结报告',
                '比格犬灌胃给予XXX的28天剂量范围',
                '确定的毒理学和毒代动力学试验',
                '受试物：XXX',
                '康龙化成TSP专题代号：3D3211',
                '研究完成日期：2023年07月14日',
                '委托方',
                'XXX',
                '中华人民共和国',
                'XXX',
                'XXX',
                '研究机构',
                '康龙化成（宁波）药物开发有限公司',
                '中华人民共和国',
                '浙江省慈溪市杭州湾生命科技园',
                '启源路39号，315336',
            ]
        )
        == '比格犬灌胃给予XXX的28天剂量范围确定的毒理学和毒代动力学试验'
    )

    # 重复给药-非关键试验：3.  3R3248 Rat DRF.docx
    assert (
        get_trial_title(
            [
                '总结报告',
                'Sprague-Dawley大鼠灌胃给予XXX的28天剂量范围确定的毒理学和毒代动力学试验',
                '受试物：XXX',
                '康龙化成TSP专题代号：3R3248',
                '研究完成日期：2023年07月14日',
                '委托方',
                'XXX',
                '中华人民共和国',
                'XXX',
                'XXX',
                '研究机构',
                '康龙化成（宁波）药物开发有限公司',
                '中华人民共和国',
                '浙江省慈溪市杭州湾生命科技园',
                '启源路39号，315336',
            ]
        )
        == 'Sprague-Dawley大鼠灌胃给予XXX的28天剂量范围确定的毒理学和毒代动力学试验'
    )

    # 重复给药-关键试验：4232-31003-231610.docx
    assert (
        get_trial_title(
            [
                '总结报告',
                'Sprague Dawley大鼠经口灌胃给予新药001连续28天及恢复28天的毒理学及毒代动力学试验',
                '受试物：新药001',
                '康龙化成TSP专题代号：31003-231610',
                '专题完成日期：2024年07月22日',
                '委托方',
                '研究机构',
                '康龙化成（北京）生物技术有限公司',
                '中华人民共和国',
                '北京市昌平区中关村生命科学园',
                '科学园路32号，102206',
            ]
        )
        == 'Sprague Dawley大鼠经口灌胃给予新药001连续28天及恢复28天的毒理学及毒代动力学试验'
    )

    # 重复给药-关键试验：4232-31003-232003.docx
    assert (
        get_trial_title(
            [
                '总结报告',
                '比格犬经口灌胃重复给予新药001连续28天及恢复28天的毒理学及毒代动力学试验',
                '受试物: 新药001',
                '康龙化成TSP专题代号: 31003-232003',
                '研究完成日期：2024年07月22日',
                '委托方',
                '研究机构康龙化成（北京）生物技术有限公司中华人民共和国北京市昌平区中关村生命科学园科学园路32号，102206',
            ]
        )
        == '比格犬经口灌胃重复给予新药001连续28天及恢复28天的毒理学及毒代动力学试验'
    )

    # 遗传毒性-回复突变：42331-31006-231925.docx
    assert (
        get_trial_title(
            [
                '新药001：鼠伤寒沙门氏菌和大肠埃希杆菌',
                '回复突变试验',
                '总结报告',
                '受试物:新药001',
                '委托方：',
                '研究机构：康龙化成（北京）生物技术有限公司',
                '北京市昌平区中关村生命科学园科学园路32号',
                '102206',
                '专题代号：31006-231925',
                '研究完成日期：',
                '2024年07月05日',
            ]
        )
        == '新药001：鼠伤寒沙门氏菌和大肠埃希杆菌回复突变试验'
    )

    # 遗传毒性-染色体畸变：42331-31006-231924.docx
    assert (
        get_trial_title(
            [
                '总结报告',
                '新药001：中国仓鼠卵巢细胞体外染色体畸变试验',
                '受试物: 新药001',
                '委托方',
                '研究机构:康龙化成（北京）生物技术有限公司中华人民共和国',
                '北京市昌平区中关村生命科学园科学园路32 号',
                '102206',
                '专题代号: 31006-231924',
                '研究完成日期:07/19/2024',
            ]
        )
        == '新药001：中国仓鼠卵巢细胞体外染色体畸变试验'
    )

    # 遗传毒性-微核：42332-31006-231926.docx
    assert (
        get_trial_title(
            [
                '总结报告',
                'Sprague Dawley大鼠经口灌胃给予新药001连续2天的微核试验',
                '受试物:新药001',
                '委托方：',
                '研究机构：康龙化成（北京）生物技术有限公司',
                '北京市昌平区中关村生命科学园科学园路32号',
                '102206',
                '专题代号：31006-231926',
                '研究完成日期：2024年07月16日',
            ]
        )
        == 'Sprague Dawley大鼠经口灌胃给予新药001连续2天的微核试验'
    )


# 1 - 5 基础字段 - 试验编号
def test_get_trial_number():
    # 单次给药：33416-230611-总结报告.docx
    assert (
        get_trial_number(
            [
                '总结报告',
                'Sprague Dawley大鼠单次经口灌胃给予XXX的急性毒理学试验',
                '受试物：XXX',
                '康龙化成TSP专题代号：33416-230611',
                '研究完成日期：2023年11月08日',
                '委托方XXX中华人民共和国XXXXXX',
                '研究机构康龙化成（北京）生物技术有限公司中华人民共和国北京市昌平区中关村生命科学园科学园路32号，102206',
            ]
        )
        == '33416-230611'
    )

    # 单次给药：4231-31003-231609.docx
    assert (
        get_trial_number(
            [
                '总结报告',
                'Sprague Dawley大鼠经口灌胃给予新药001的最大耐受量试验',
                '受试物：新药001',
                '康龙化成TSP专题代号：31003-231609 ',
                '委托方',
                '研究单位康龙化成（北京）生物技术有限公司中华人民共和国北京市昌平区中关村生命科学园科学园路32号，102206',
            ]
        )
        == '31003-231609'
    )

    # 单次给药：4. 33416-230612 data CHN.docx
    assert (
        get_trial_number(
            [
                '总结报告',
                '比格犬单次经口灌胃给予XXX的急性毒理学试验',
                '受试物: XXX',
                '康龙化成TSP专题代号: 33416-230612',
                '研究完成日期：2023年xx月xx日',
                '委托方XXX中华人民共和国XXXXXX',
                '研究机构康龙化成（北京）生物技术有限公司中华人民共和国北京市昌平区中关村生命科学园科学园路32号，102206',
            ]
        )
        == '33416-230612'
    )

    # 重复给药-非关键试验：4232-31053-23238.docx
    assert (
        get_trial_number(
            [
                '试验报告',
                'Sprague Dawley大鼠经口灌胃给予新药001的14天剂量范围确定的毒理学和毒代动力学试验',
                '受试物：新药001',
                '康龙化成TSP专题代号：31053-23238',
                '研究完成日期：2024年07月09日',
                '委托方',
                '研究单位康龙化成（宁波）药物开发有限公司中华人民共和国浙江省慈溪市杭州湾生命科技园启源路39号，315336',
            ]
        )
        == '31053-23238'
    )

    # 重复给药-非关键试验：3. 3D3211 Dog DRF.docx
    assert (
        get_trial_number(
            [
                '总结报告',
                '比格犬灌胃给予XXX的28天剂量范围',
                '确定的毒理学和毒代动力学试验',
                '受试物：XXX',
                '康龙化成TSP专题代号：3D3211',
                '研究完成日期：2023年07月14日',
                '委托方',
                'XXX',
                '中华人民共和国',
                'XXX',
                'XXX',
                '研究机构',
                '康龙化成（宁波）药物开发有限公司',
                '中华人民共和国',
                '浙江省慈溪市杭州湾生命科技园',
                '启源路39号，315336',
            ]
        )
        == '3D3211'
    )

    # 重复给药-非关键试验：3.  3R3248 Rat DRF.docx
    assert (
        get_trial_number(
            [
                '总结报告',
                'Sprague-Dawley大鼠灌胃给予XXX的28天剂量范围确定的毒理学和毒代动力学试验',
                '受试物：XXX',
                '康龙化成TSP专题代号：3R3248',
                '研究完成日期：2023年07月14日',
                '委托方',
                'XXX',
                '中华人民共和国',
                'XXX',
                'XXX',
                '研究机构',
                '康龙化成（宁波）药物开发有限公司',
                '中华人民共和国',
                '浙江省慈溪市杭州湾生命科技园',
                '启源路39号，315336',
            ]
        )
        == '3R3248'
    )

    # 重复给药-关键试验：4232-31003-231610.docx
    assert (
        get_trial_number(
            [
                '总结报告',
                'Sprague Dawley大鼠经口灌胃给予新药001连续28天及恢复28天的毒理学及毒代动力学试验',
                '受试物：新药001',
                '康龙化成TSP专题代号：31003-231610',
                '专题完成日期：2024年07月22日',
                '委托方',
                '研究机构',
                '康龙化成（北京）生物技术有限公司',
                '中华人民共和国',
                '北京市昌平区中关村生命科学园',
                '科学园路32号，102206',
            ]
        )
        == '31003-231610'
    )

    # 重复给药-关键试验：4232-31003-232003.docx
    assert (
        get_trial_number(
            [
                '总结报告',
                '比格犬经口灌胃重复给予新药001连续28天及恢复28天的毒理学及毒代动力学试验',
                '受试物: 新药001',
                '康龙化成TSP专题代号: 31003-232003',
                '研究完成日期：2024年07月22日',
                '委托方',
                '研究机构康龙化成（北京）生物技术有限公司中华人民共和国北京市昌平区中关村生命科学园科学园路32号，102206',
            ]
        )
        == '31003-232003'
    )

    # 遗传毒性-回复突变：42331-31006-231925.docx
    assert (
        get_trial_number(
            [
                '总结报告',
                '新药001：中国仓鼠卵巢细胞体外染色体畸变试验',
                '受试物: 新药001',
                '委托方',
                '研究机构:康龙化成（北京）生物技术有限公司中华人民共和国',
                '北京市昌平区中关村生命科学园科学园路32 号',
                '102206',
                '专题代号: 31006-231924',
                '研究完成日期:07/19/2024',
            ]
        )
        == '31006-231924'
    )

    # 遗传毒性-染色体畸变：42331-31006-231924.docx
    assert (
        get_trial_number(
            [
                '总结报告',
                '新药001：中国仓鼠卵巢细胞体外染色体畸变试验',
                '受试物: 新药001',
                '委托方',
                '研究机构:康龙化成（北京）生物技术有限公司中华人民共和国',
                '北京市昌平区中关村生命科学园科学园路32 号',
                '102206',
                '专题代号: 31006-231924',
                '研究完成日期:07/19/2024',
            ]
        )
        == '31006-231924'
    )

    # 遗传毒性-微核：42332-31006-231926.docx
    assert (
        get_trial_number(
            [
                '总结报告',
                'Sprague Dawley大鼠经口灌胃给予新药001连续2天的微核试验',
                '受试物:新药001',
                '委托方：',
                '研究机构：康龙化成（北京）生物技术有限公司',
                '北京市昌平区中关村生命科学园科学园路32号',
                '102206',
                '专题代号：31006-231926',
                '研究完成日期：2024年07月16日',
            ]
        )
        == '31006-231926'
    )
