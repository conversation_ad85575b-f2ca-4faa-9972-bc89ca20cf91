import pytest

from app.utils.volcengine_deepseek import VolcEngineDeepSeekR1, VolcEngineDeepSeekV3


@pytest.mark.asyncio(loop_scope='session')
class TestVolcEngineDeepSeekV3:
    async def test_chat(self):
        result = await VolcEngineDeepSeekV3().chat(user_message='hello', max_tokens=1)
        assert 'hello' in result.content.lower()
        assert not result.reasoning_content


@pytest.mark.asyncio(loop_scope='session')
class TestVolcEngineDeepSeekR1:
    async def test_chat(self):
        result = await VolcEngineDeepSeekR1().chat(user_message='hello', max_tokens=1)
        assert result.reasoning_content
