import pytest

from fastapi.testclient import TestClient
from app.main import app
from app.schemas.builder import BuilderSave<PERSON>etail, BuilderTable, TableKey, BuilderTableRow, BuilderCol
from app.schemas.doc_generate import CellHorizontallyAlign, CellVerticalAlign
from app.utils.error_code import <PERSON><PERSON>rCode
from dotenv import load_dotenv

TEST_BUILDER_PROJECT_CACHE_ID = 172
TEST_BUILDER_PROJECT_ID = 173
TEST_DELETE_BUILDER_PROJECT_ID = 1
TEST_SAVE_BUILDER_PROJECT_ID = 2

load_dotenv()


@pytest.mark.asyncio(loop_scope='session')
async def test_get_project_api():
    with TestClient(app) as client:
        # 成功 - 实时生成
        response_success = client.get(f'/api/v1/builder?project_id={TEST_BUILDER_PROJECT_ID}')
        assert response_success.status_code == 200

        response_success_dict: dict = response_success.json()
        builder_success: dict = response_success_dict['data']

        assert response_success_dict['code'] == 0
        assert builder_success['id'] == TEST_BUILDER_PROJECT_ID
        assert 'test_product_list' in builder_success
        assert 'applicant_list' in builder_success
        assert 'table_list' in builder_success
        assert 'chapter_list' in builder_success
        assert len(builder_success['table_list']) > 0

        # 成功 - 读取数据库缓存
        response_success_cache = client.get(f'/api/v1/builder?project_id={TEST_BUILDER_PROJECT_CACHE_ID}')
        assert response_success_cache.status_code == 200

        response_success_cache_dict: dict = response_success_cache.json()
        builder_success_cache: dict = response_success_cache_dict['data']

        assert response_success_cache_dict['code'] == 0
        assert builder_success_cache['id'] == TEST_BUILDER_PROJECT_CACHE_ID
        assert 'test_product_list' in builder_success_cache
        assert 'applicant_list' in builder_success_cache
        assert 'table_list' in builder_success_cache
        assert 'chapter_list' in builder_success_cache
        assert len(builder_success_cache['table_list']) > 0

        # 异常 - 项目不存在
        response_not_exit = client.get('/api/v1/builder?project_id=0')
        assert response_not_exit.status_code == 400

        response_not_exit_dict: dict = response_not_exit.json()

        assert response_not_exit_dict['code'] == ErrorCode.BAD_REQUEST
        assert response_not_exit_dict['msg'] == '项目不存在'

        # 异常 - 项目已删除
        response_deleted = client.get(f'/api/v1/builder?project_id={TEST_DELETE_BUILDER_PROJECT_ID}')
        assert response_deleted.status_code == 400

        response_deleted_dict: dict = response_deleted.json()

        assert response_deleted_dict['code'] == ErrorCode.BAD_REQUEST
        assert response_deleted_dict['msg'] == '项目已删除'


@pytest.mark.asyncio(loop_scope='session')
async def test_save_builder_api():
    with TestClient(app) as client:
        save_data: BuilderSaveDetail = BuilderSaveDetail(
            id=TEST_SAVE_BUILDER_PROJECT_ID,
            applicant_list=['test applicant'],
            table_list=[
                BuilderTable(
                    table_key=TableKey.TABLE_2671,
                    pass_before_row=1,
                    footer='test footer',
                    header_title='test header title',
                    left_fixed_header_title='test left fixed header title',
                    subheader_title='subheader title',
                    left_fixed_subheader_title='test left fixed subheader title',
                    test_product='test product',
                    trial_number='test trial number',
                    rows=[BuilderTableRow(
                        is_bold=True,
                        cols=[BuilderCol(
                            value='test value',
                            title='test title',
                            row_span=True,
                            row_span_count=1,
                            row_span_first_row=True,
                            col_span=True,
                            col_span_count=1,
                            align_horizontally=CellHorizontallyAlign.CENTER,
                            align_vertical=CellVerticalAlign.CENTER,
                            font_color='000000'
                        )]
                    )]
                )
            ],
            test_product_list=['test product']
        )

        # 枚举转字符串
        data_dict = save_data.model_dump()
        for table in data_dict['table_list']:
            table['table_key'] = table['table_key'].value
            for table_row in table['rows']:
                for table_col in table_row['cols']:
                    table_col['align_vertical'] = table_col['align_vertical'].value
                    table_col['align_horizontally'] = table_col['align_horizontally'].value

        # 成功
        response_success = client.post('/api/v1/builder/save', json=data_dict)

        assert response_success.status_code == 200

        response_success_dict: dict = response_success.json()
        assert response_success_dict['code'] == 0


@pytest.mark.asyncio(loop_scope='session')
async def test_output_api():
    with TestClient(app) as client:
        # 成功 - 读取数据库缓存
        response_success = client.get(f'/api/v1/builder/export?project_id={TEST_BUILDER_PROJECT_CACHE_ID}')
        assert response_success.status_code == 200

        # 异常 - 项目不存在
        response_not_exit = client.get('/api/v1/builder/export?project_id=0')
        assert response_not_exit.status_code == 400

        response_not_exit_dict: dict = response_not_exit.json()

        assert response_not_exit_dict['code'] == ErrorCode.BAD_REQUEST
        assert response_not_exit_dict['msg'] == '项目不存在'

        # 异常 - 项目已删除
        response_deleted = client.get(f'/api/v1/builder/export?project_id={TEST_DELETE_BUILDER_PROJECT_ID}')
        assert response_deleted.status_code == 400

        response_deleted_dict: dict = response_deleted.json()

        assert response_deleted_dict['code'] == ErrorCode.BAD_REQUEST
        assert response_deleted_dict['msg'] == '项目已删除'


