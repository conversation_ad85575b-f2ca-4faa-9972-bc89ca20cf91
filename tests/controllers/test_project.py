from datetime import datetime

import pytest
from dotenv import load_dotenv
from fastapi.testclient import TestClient

from app.main import app

load_dotenv()


STANDARD_TEST_PROJECT_ID = 172

SIMPLE_TEST_PROJECT_ID = 260


@pytest.mark.asyncio(loop_scope='session')
async def test_get_project_list_api():
    """
    测试项目列表
    """
    with TestClient(app) as client:
        response_success = client.get('/api/v1/projects', headers={'token': 'test'})
        assert response_success.status_code == 200

        response_success_dict: dict = response_success.json()
        project_list: list = response_success_dict['data']

        # 成功 - 默认获取第一页成功
        assert response_success_dict['code'] == 0
        assert project_list['total'] >= 0

        # 成功 - 如果数量大于10，获取第二页成功
        if project_list['total'] > 10:
            response_success_page_2 = client.get('/api/v1/projects?page=2', headers={'token': 'test'})
            assert response_success_page_2.status_code == 200

            response_success_page_2_dict: dict = response_success_page_2.json()
            project_list_page_2: list = response_success_page_2_dict['data']

            assert response_success_page_2_dict['code'] == 0
            assert len(project_list_page_2['projects']) >= 0

        # 异常 - 页码小于1
        response_page_error = client.get('/api/v1/projects?page=0', headers={'token': 'test'})
        assert response_page_error.status_code == 400

        response_page_error_dict: dict = response_page_error.json()
        assert response_page_error_dict['code'] == 2


@pytest.mark.asyncio(loop_scope='session')
async def test_get_project_detail():
    """
    测试项目详情
    """
    with TestClient(app) as client:
        # 成功 - 获取项目详情
        response_success = client.get(
            '/api/v1/project', params={'project_id': SIMPLE_TEST_PROJECT_ID}, headers={'token': 'test'}
        )
        assert response_success.status_code == 200

        response_success_dict: dict = response_success.json()
        project_detail: dict = response_success_dict['data']

        assert response_success_dict['code'] == 0
        assert project_detail['id'] == SIMPLE_TEST_PROJECT_ID

        # 异常 - 项目不存在
        response_not_exit = client.get('/api/v1/project', params={'project_id': 0}, headers={'token': 'test'})
        assert response_not_exit.status_code == 400

        response_not_exit_dict: dict = response_not_exit.json()

        assert response_not_exit_dict['code'] == 2


@pytest.mark.asyncio(loop_scope='session')
async def test_save_project():
    """
    测试保存项目 & 删除项目
    """
    with TestClient(app) as client:
        # 临时保存项目（通过上传文件触发）
        temp_params = {
            'project_name': 'temp_test_project',
            'template': 'temp_test-template',
            'version': 'v1.0.0',
            'is_temp': True,
            'files': {
                'singleDose': [],
                'repeatedDose': [],
                'repeatedDoseNonKey': [],
                'mutagenicity': [],
                'chromosome': [],
                'micronucleus': [],
            },
        }

        temp_response_success = client.post('/api/v1/project/save', json=temp_params, headers={'token': 'test'})
        assert temp_response_success.status_code == 200

        # 成功 - 保存项目，返回项目ID
        temp_response_success_dict: dict = temp_response_success.json()
        assert temp_response_success_dict['code'] == 0
        assert temp_response_success_dict['data']['id'] is not None

        # 成功 - 获取项目详情
        temp_project_success = client.get(
            '/api/v1/project',
            params={'project_id': temp_response_success_dict['data']['id']},
            headers={'token': 'test'},
        )
        assert temp_project_success.status_code == 200

        temp_project_success_dict: dict = temp_project_success.json()
        assert temp_project_success_dict['code'] == 0

        # 项目详情与保存的项目ID一致
        temp_project_detail: dict = temp_project_success_dict['data']
        assert temp_project_detail['id'] == temp_response_success_dict['data']['id']

        # 状态为未生成
        assert temp_project_detail['status'] == 0

        # 标准保存项目
        params = {
            'project_name': 'test_project',
            'template': 'test-template',
            'version': 'v1.0.0',
            'files': {
                'singleDose': [],
                'repeatedDose': [],
                'repeatedDoseNonKey': [],
                'mutagenicity': [],
                'chromosome': [],
                'micronucleus': [],
            },
        }
        response_success = client.post('/api/v1/project/save', json=params, headers={'token': 'test'})
        assert response_success.status_code == 200

        # 成功 - 保存项目，返回项目ID
        response_success_dict: dict = response_success.json()
        assert response_success_dict['code'] == 0
        assert response_success_dict['data']['id'] is not None

        # 成功 - 获取项目详情
        project_success = client.get(
            '/api/v1/project', params={'project_id': response_success_dict['data']['id']}, headers={'token': 'test'}
        )
        assert project_success.status_code == 200

        project_success_dict: dict = project_success.json()
        assert project_success_dict['code'] == 0

        # 项目详情与保存的项目ID一致
        project_detail: dict = project_success_dict['data']
        assert project_detail['id'] == response_success_dict['data']['id']

        # 状态为准备
        assert project_detail['status'] == 1

        # 正常 - 保存相同项目正常
        params['project_id'] = response_success_dict['data']['id']
        response_fail = client.post('/api/v1/project/save', json=params, headers={'token': 'test'})
        assert response_fail.status_code == 200

        # 异常 - 保存项目失败 - ID不存在
        params['project_id'] = 0
        response_fail = client.post('/api/v1/project/save', json=params, headers={'token': 'test'})
        assert response_fail.status_code == 400

        response_fail_dict: dict = response_fail.json()
        assert response_fail_dict['code'] == 2

        # 成功 删除项目 - 临时项目
        delete_temp_project_response = client.delete(
            '/api/v1/project/' + str(temp_response_success_dict['data']['id']), headers={'token': 'test'}
        )
        assert delete_temp_project_response.status_code == 200
        assert delete_temp_project_response.json()['code'] == 0

        # 成功 删除项目 - 标准项目
        delete_project_response = client.delete(
            '/api/v1/project/' + str(response_success_dict['data']['id']), headers={'token': 'test'}
        )
        assert delete_project_response.status_code == 200
        assert delete_project_response.json()['code'] == 0

        # 异常 - 删除项目失败 - ID不存在
        delete_fail_response = client.delete('/api/v1/project/0', headers={'token': 'test'})
        assert delete_fail_response.status_code == 400
        assert delete_fail_response.json()['code'] == 2


@pytest.mark.asyncio(loop_scope='session')
async def test_get_generate_status():
    """
    测试获取生成状态
    """
    with TestClient(app) as client:
        # 成功 - 生成完成
        response_success = client.get(
            '/api/v1/project/generate/status',
            params={'project_id': STANDARD_TEST_PROJECT_ID},
            headers={'token': 'test'},
        )
        assert response_success.status_code == 200

        response_success_dict: dict = response_success.json()
        assert response_success_dict['code'] == 0
        assert response_success_dict['data']['status'] == 3

        # 异常 - 无项目切片
        response_fail = client.get(
            '/api/v1/project/generate/status', params={'project_id': 1}, headers={'token': 'test'}
        )
        assert response_fail.status_code == 400

        response_fail_dict: dict = response_fail.json()
        assert response_fail_dict['code'] == 2
        assert response_fail_dict['msg'] == '项目没有文档切片'

        # 异常 - 项目不存在
        response_fail = client.get(
            '/api/v1/project/generate/status', params={'project_id': 0}, headers={'token': 'test'}
        )
        assert response_fail.status_code == 400

        response_fail_dict: dict = response_fail.json()
        assert response_fail_dict['code'] == 2


@pytest.mark.asyncio(loop_scope='session')
async def test_start_stop_generate_and_upload_file():
    """
    测试生成项目 & 上传文件 &  停止生成项目
    """
    with TestClient(app) as client:
        # 保存项目（通过上传文件触发）
        current_datetime = datetime.now()
        formatted_time = current_datetime.strftime('%Y-%m-%d %H:%M:%S')
        print(f'formatted_time: {formatted_time}')
        params = {
            'project_name': f'test_project_{formatted_time}',
            'template': 'test_template',
            'version': 'v1.0.0',
            'files': {
                'singleDose': [],
                'repeatedDose': [],
                'repeatedDoseNonKey': [],
                'mutagenicity': [],
                'chromosome': [],
                'micronucleus': [],
            },
        }

        response_success = client.post('/api/v1/project/save', json=params, headers={'token': 'test'})
        assert response_success.status_code == 200

        # 成功 - 保存项目，返回项目ID
        response_success_dict: dict = response_success.json()
        assert response_success_dict['code'] == 0
        assert response_success_dict['data']['id'] is not None

        gen_project_id = response_success_dict['data']['id']
        params['project_id'] = gen_project_id

        # 单次给药 - 上传文件
        with open('uploads/单次给药/比格犬单次经口灌胃给予XXX.docx', 'rb') as file:
            single_dose_upload_response = client.post(
                '/api/v1/project/upload',
                files={
                    'file': (
                        '比格犬单次经口灌胃给予XXX.docx',
                        file,
                        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    )
                },
                data={'project_id': gen_project_id, 'trial_type': 1},
                headers={'token': 'test'},
            )
        # 成功 - 上传文件
        assert single_dose_upload_response.status_code == 200
        single_dose_upload_response_dict: dict = single_dose_upload_response.json()
        assert single_dose_upload_response_dict['code'] == 0

        params['files']['singleDose'].append(
            {
                'doc_id': single_dose_upload_response_dict['data']['doc_id'],
                'file_name': '比格犬单次经口灌胃给予XXX.docx',
                'id': '1',
                'type': 1,
                'isLoading': False,
                'isValid': True,
            }
        )

        # 重复给药 - 上传文件
        # 非关键重复
        with open('uploads/重复给药/大鼠经口灌胃给予新药001的14天剂量范围确定.docx', 'rb') as file:
            repeated_dose_upload_response = client.post(
                '/api/v1/project/upload',
                files={
                    'file': (
                        '大鼠经口灌胃给予新药001的14天剂量范围确定.docx',
                        file,
                        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    )
                },
                data={'project_id': gen_project_id, 'trial_type': 2},
                headers={'token': 'test'},
            )
        # 成功 - 上传文件
        assert repeated_dose_upload_response.status_code == 200
        repeated_dose_upload_response_dict: dict = repeated_dose_upload_response.json()
        assert repeated_dose_upload_response_dict['code'] == 0

        params['files']['repeatedDose'].append(
            {
                'doc_id': repeated_dose_upload_response_dict['data']['doc_id'],
                'file_name': '大鼠经口灌胃给予新药001的14天剂量范围确定.docx',
                'id': '2',
                'type': 2,
                'isLoading': False,
                'isValid': True,
            }
        )

        # 关键重复
        with open('uploads/重复给药/大鼠经口灌胃给予新药001连续28天及恢复28天.docx', 'rb') as file:
            key_repeated_dose_upload_response = client.post(
                '/api/v1/project/upload',
                files={
                    'file': (
                        '大鼠经口灌胃给予新药001连续28天及恢复28天.docx',
                        file,
                        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    )
                },
                data={'project_id': gen_project_id, 'trial_type': 2},
                headers={'token': 'test'},
            )
        # 成功 - 上传文件
        assert key_repeated_dose_upload_response.status_code == 200
        key_repeated_dose_upload_response_dict: dict = key_repeated_dose_upload_response.json()
        assert key_repeated_dose_upload_response_dict['code'] == 0

        params['files']['repeatedDose'].append(
            {
                'doc_id': key_repeated_dose_upload_response_dict['data']['doc_id'],
                'file_name': '大鼠经口灌胃给予新药001连续28天及恢复28天.docx',
                'id': '3',
                'type': 2,
                'isLoading': False,
                'isValid': True,
            }
        )

        # 遗传毒性 - 回复突变
        with open('uploads/遗传毒性/回复突变.docx', 'rb') as file:
            mutagenicity_upload_response = client.post(
                '/api/v1/project/upload',
                files={
                    'file': (
                        '回复突变.docx',
                        file,
                        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    )
                },
                data={'project_id': gen_project_id, 'trial_type': 4},
                headers={'token': 'test'},
            )
        # 成功 - 上传文件
        assert mutagenicity_upload_response.status_code == 200
        mutagenicity_upload_response_dict: dict = mutagenicity_upload_response.json()
        assert mutagenicity_upload_response_dict['code'] == 0

        params['files']['mutagenicity'].append(
            {
                'doc_id': mutagenicity_upload_response_dict['data']['doc_id'],
                'file_name': '回复突变.docx',
                'id': '4',
                'type': 3,
                'isLoading': False,
                'isValid': True,
            }
        )

        # 染色体 - 染色体畸变
        with open('uploads/遗传毒性/染色体畸变.docx', 'rb') as file:
            chromosome_upload_response = client.post(
                '/api/v1/project/upload',
                files={
                    'file': (
                        '染色体畸变.docx',
                        file,
                        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    )
                },
                data={'project_id': gen_project_id, 'trial_type': 5},
                headers={'token': 'test'},
            )
        # 成功 - 上传文件
        assert chromosome_upload_response.status_code == 200
        chromosome_upload_response_dict: dict = chromosome_upload_response.json()
        assert chromosome_upload_response_dict['code'] == 0

        params['files']['chromosome'].append(
            {
                'doc_id': chromosome_upload_response_dict['data']['doc_id'],
                'file_name': '染色体畸变.docx',
                'id': '5',
                'type': 4,
                'isLoading': False,
                'isValid': True,
            }
        )

        # 遗传毒性 - 微核
        with open('uploads/遗传毒性/微核.docx', 'rb') as file:
            micronucleus_upload_response = client.post(
                '/api/v1/project/upload',
                files={
                    'file': (
                        '微核.docx',
                        file,
                        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    )
                },
                data={'project_id': gen_project_id, 'trial_type': 6},
                headers={'token': 'test'},
            )
        # 成功 - 上传文件
        assert micronucleus_upload_response.status_code == 200
        micronucleus_upload_response_dict: dict = micronucleus_upload_response.json()
        assert micronucleus_upload_response_dict['code'] == 0

        params['files']['micronucleus'].append(
            {
                'doc_id': micronucleus_upload_response_dict['data']['doc_id'],
                'file_name': '微核.docx',
                'id': '6',
                'type': 5,
                'isLoading': False,
                'isValid': True,
            }
        )

        # 异常 - 传入不合规文件
        with open('uploads/遗传毒性/微核.docx', 'rb') as file:
            micronucleus_upload_error_response = client.post(
                '/api/v1/project/upload',
                files={
                    'file': (
                        '微核.docx',
                        file,
                        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    )
                },
                data={'project_id': gen_project_id, 'trial_type': 1},
                headers={'token': 'test'},
            )
        # 异常 - 上传文件
        assert micronucleus_upload_error_response.status_code == 400
        micronucleus_upload_error_response_dict: dict = micronucleus_upload_error_response.json()
        assert micronucleus_upload_error_response_dict['code'] == 2

        # 保存一下结果
        response_success = client.post('/api/v1/project/save', json=params, headers={'token': 'test'})
        assert response_success.status_code == 200

        # 生成项目
        print(f'params: {params}')
        generate_response = client.post('/api/v1/project/generate', json=params, headers={'token': 'test'})
        assert generate_response.status_code == 200

        # 成功 - 生成项目
        generate_response_dict: dict = generate_response.json()
        assert generate_response_dict['code'] == 0
        assert generate_response_dict['data']['id'] == gen_project_id

        # 停止项目生成
        stop_generate_response = client.post(
            '/api/v1/project/generate/stop', json={'project_id': gen_project_id}, headers={'token': 'test'}
        )
        assert stop_generate_response.status_code == 200

        # 成功 - 停止项目生成
        stop_generate_response_dict: dict = stop_generate_response.json()
        assert stop_generate_response_dict['code'] == 0
