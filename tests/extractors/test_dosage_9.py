import pytest

from app.extractors.dosage_9 import (
    AdministrationDate,
    AdministrationMethod9,
    CellNumber,
    EvaluateCells,
    GenotoxicEffects9,
    GetAge,
    SamplingTime,
    ToxicEffects,
)


@pytest.mark.asyncio(loop_scope='session')
class TestSamplingTime:
    async def test_extract(self):

        # 遗传毒性-微核: 42332-31006-231926.docx
        assert (
            await SamplingTime.extract(
                '第一次微核主试验中的雄性大鼠和第二次微核主试验中的雌性大鼠，溶媒对照和受试物组动物在最后一次给药后18-24 小时，按计划实施安乐死以采集骨髓。阳性药组动物在最后一次给药后24±1 小时，按计划实施安乐死以采集骨髓。'
            )
            == '最后一次给药后18-24 小时'
        )

        # GPT生成的案例
        assert (
            await SamplingTime.extract(
                '第一次微核主试验中，受试物组和溶媒对照组动物在末次给药后 24±1 小时实施安乐死，以采集骨髓样本。阳性对照组动物则在末次给药后 18-24 小时实施安乐死以采集骨髓。'
            )
            == '末次给药后 24±1 小时'
        )

        # GPT生成的案例
        assert (
            await SamplingTime.extract(
                '试验设计中规定，受试物组的动物在末次给药后 18-24 小时完成安乐死，以采集骨髓。溶媒对照组的动物也按照同样的时间点采样。'
            )
            == '末次给药后 18-24 小时'
        )

        # GPT生成的案例
        assert (
            await SamplingTime.extract(
                '所有受试物组大鼠在最后一次给药后 24±1 小时实施安乐死，并采集骨髓样本用于分析。阳性药物组按照给药后 18-24 小时采样。'
            )
            == '最后一次给药后 24±1 小时'
        )


@pytest.mark.asyncio(loop_scope='session')
class TestGetAge:
    async def test_extract(self):

        # 遗传毒性-微核: 42332-31006-231926.docx
        assert (
            await GetAge.extract(
                '''剂量探索试验给药第一天，动物周龄为6至9周，体重为雄性266.7克至339.7克，雌性为175.0克至227.9克。
                微核主试验给药第一天，动物周龄为6至8周，体重为雄性248.5克至287.1克，雌性为191.3克至217.3克。
                雌性重复微核主试验给药第一天，动物周龄为6至8周，体重为雌性178.8克至197.7克。'''
            )
            == '6至8周'
        )

        # GPT生成的案例
        assert (
            await GetAge.extract(
                '''剂量探索试验开始时，动物周龄为5至7周，体重为雄性220.4克至310.6克，雌性为180.2克至225.7克。
                微核主试验开始时，动物周龄为7至9周，体重为雄性255.3克至300.5克，雌性为200.1克至240.2克。
                重复微核主试验开始时，动物周龄为6至8周，体重为雌性188.9克至207.3克。'''
            )
            == '7至9周'
        )

        # GPT生成的案例
        assert (
            await GetAge.extract(
                '''剂量探索试验给药第一天，动物周龄为4至6周，体重为雄性215.3克至295.7克，雌性为160.5克至200.3克。
                微核主试验给药第一天，动物周龄为6至8周，体重为雄性250.1克至310.2克，雌性为190.3克至230.7克。'''
            )
            == '6至8周'
        )

        # GPT生成的案例
        assert (
            await GetAge.extract(
                '''微核主试验给药第一天，动物周龄为5至7周，体重为雄性220.6克至280.4克，雌性为180.9克至210.7克。
                剂量探索试验开始时，动物周龄为6至8周，体重为雄性230.2克至305.8克，雌性为190.1克至220.3克。'''
            )
            == '5至7周'
        )


@pytest.mark.asyncio(loop_scope='session')
class TestAdministrationMethod9:
    async def test_extract(self):

        # 遗传毒性-微核: 42332-31006-231926.docx
        assert (
            await AdministrationMethod9.extract(
                '''受试物通过经口灌胃给药。本试验选用口服给药因其与临床拟定的给药方式相同。'''
            )
            == '经口灌胃'
        )

        # GPT生成的案例
        assert (
            await AdministrationMethod9.extract(
                '''本试验中，受试物以皮下注射方式给药。选择皮下注射是为了确保药物能够稳定进入血液循环，并与其他研究保持一致。'''
            )
            == '皮下注射'
        )

        # GPT生成的案例
        assert (
            await AdministrationMethod9.extract(
                '''本次试验的给药方式为静脉注射，原因是静脉注射可快速将药物输送至全身，并能较好地控制药物剂量。'''
            )
            == '静脉注射'
        )

        # GPT生成的案例
        assert (
            await AdministrationMethod9.extract(
                '''受试物通过腹腔注射方式进行给药。这种方式被选择是因为它能够较快地达到系统循环，适用于本次实验设计的需要。'''
            )
            == '腹腔注射'
        )


@pytest.mark.asyncio(loop_scope='session')
class TestAdministrationDate:
    async def test_extract(self):

        # 遗传毒性-微核: 42332-31006-231926.docx
        assert (
            await AdministrationDate.extract(
                '''研究开始日期：	2023年12月18日
                试验开始日期：	2023年12月27日
                试验完成日期：	2024年02月15日'''
            )
            == '2023年12月27日'
        )

        # GPT生成的案例
        assert (
            await AdministrationDate.extract(
                '''研究开始日期：2024年01月05日
                试验开始日期：2024年01月12日
                试验完成日期：2024年03月20日'''
            )
            == '2024年01月12日'
        )

        # GPT生成的案例
        assert (
            await AdministrationDate.extract(
                '''研究开始日期：2022年11月10日
                试验开始日期：2022年11月15日
                试验完成日期：2023年01月30日'''
            )
            == '2022年11月15日'
        )

        # GPT生成的案例
        assert (
            await AdministrationDate.extract(
                '''研究开始日期：2023年06月01日
                试验开始日期：2023年06月10日
                试验完成日期：2023年08月25日'''
            )
            == '2023年06月10日'
        )


@pytest.mark.asyncio(loop_scope='session')
class TestEvaluateCells:
    async def test_extract(self):

        # 遗传毒性-微核: 42332-31006-231926.docx
        assert (
            await EvaluateCells.extract(
                '本试验目的是通过对成年SD(Sprague-Dawley)大鼠经口灌胃给予新药001，检测其骨髓中嗜多染红细胞（Polychromatic erythrocyte, PCE）的微核率，以评价它在体内是否对染色体有断裂损伤作用和/或对有丝分裂有破坏作用。'
            )
            == '嗜多染红细胞'
        )

        # GPT生成的案例
        assert (
            await EvaluateCells.extract(
                '本研究旨在通过检测小鼠骨髓中成熟红细胞（Normochromatic erythrocyte, NCE）的微核率，评估试验物质是否具有致突变性。'
            )
            == '成熟红细胞'
        )

        # GPT生成的案例
        assert (
            await EvaluateCells.extract(
                '该试验通过检测大鼠外周血中嗜多染红细胞（Polychromatic erythrocyte, PCE）的微核频率，评价药物对体内染色体的损伤效应。'
            )
            == '嗜多染红细胞'
        )

        # GPT生成的案例
        assert (
            await EvaluateCells.extract(
                '试验的主要目标是通过检测大鼠骨髓中红细胞祖细胞（Erythroid progenitor cells）的形态变化，判断试验药物是否具有造血毒性。'
            )
            == '红细胞祖细胞'
        )


@pytest.mark.asyncio(loop_scope='session')
class TestCellNumber:
    async def test_extract(self):

        # 遗传毒性-微核: 42332-31006-231926.docx
        assert (
            await CellNumber.extract(
                '在荧光显微镜下放大400至600倍分析染色后的骨髓涂片。每张涂片计数至少2000个嗜多染红细胞（PCE），每只动物计数至少4000个PCE，以评估微核率。同时每张片子计数至少500个红细胞，每只动物计数至少1000个红细胞，以评估每只动物PCE占红细胞的比例。'
            )
            == '至少4000个'
        )

        # GPT生成的案例
        assert (
            await CellNumber.extract(
                '染色后的外周血涂片在光学显微镜下观察，放大600至1000倍，计数每只动物至少1000个成熟红细胞（NCE），以计算微核率。同时计数至少2000个红细胞，以计算NCE占红细胞总数的比例。'
            )
            == '至少1000个'
        )

        # GPT生成的案例
        assert (
            await CellNumber.extract(
                '通过荧光显微镜观察骨髓涂片，每张涂片计数至少3000个嗜多染红细胞（PCE），每只动物计数至少6000个PCE，以评估微核形成情况。同时计数每张涂片上的所有红细胞，以评估PCE与红细胞的比例。'
            )
            == '至少6000个'
        )

        # GPT生成的案例
        assert (
            await CellNumber.extract(
                '荧光显微镜下放大500倍观察涂片，计数每张涂片上的细胞，总计数至少1500个细胞（包括PCE与NCE），每只动物计数至少3000个细胞，以计算PCE占比和微核率。'
            )
            == '至少3000个'
        )


@pytest.mark.asyncio(loop_scope='session')
class TestToxicEffects:
    async def test_extract(self):

        # 遗传毒性-微核: 42332-31006-231926.docx
        assert (
            await ToxicEffects.extract(
                '和平行的溶媒对照相比，雌雄大鼠在所有给药剂量水平上均未观察到PCE占红细胞总数百分比的显著降低，这提示新药001在所测剂量对雌雄大鼠骨髓均无细胞毒性。'
            )
            == '无'
        )

        # DIY
        assert (
            await ToxicEffects.extract(
                '和平行的溶媒对照相比，雌雄大鼠在50g给药剂量水平上观察到PCE占红细胞总数百分比的显著降低，这提示新药001在所测剂量对雌雄大鼠骨髓有细胞毒性。'
            )
            == '雌雄大鼠在50g给药剂量水平上观察到PCE占红细胞总数百分比的显著降低'
        )

        # GPT生成的案例
        assert (
            await ToxicEffects.extract(
                '在所有剂量组中，PCE占红细胞总数的百分比与溶媒对照相比均无显著差异。这表明，新药002对大鼠骨髓无明显细胞毒性。'
            )
            == '无'
        )

        # GPT生成的案例
        assert (
            await ToxicEffects.extract(
                '实验数据显示，在100 mg/kg剂量组，观察到PCE占红细胞总数百分比显著低于溶媒对照组，这表明新药003在该剂量对大鼠骨髓具有细胞毒性。'
            )
            == '在100 mg/kg剂量组，观察到PCE占红细胞总数百分比显著低于溶媒对照组'
        )


@pytest.mark.asyncio(loop_scope='session')
class TestGenotoxicEffects9:
    async def test_extract(self):

        # 遗传毒性-微核: 42332-31006-231926.docx
        assert (
            await GenotoxicEffects9.extract(
                '本微核试验是有效的，试验结果符合阴性的判断标准。因此，受试物新药001在本次大鼠骨髓细胞微核试验中被判为阴性。'
            )
            == '无'
        )

        # GPT生成的案例
        assert (
            await GenotoxicEffects9.extract(
                '在本试验中，阳性对照组表现出显著的微核率增加，表明试验系统可靠。受试物新药002未导致微核率显著增加，符合阴性判断标准，因此被判定为阴性。'
            )
            == '无'
        )

        # GPT生成的案例
        assert (
            await GenotoxicEffects9.extract(
                '实验结果显示，受试物新药003在高剂量组引起了微核率显著增加，超出了阴性对照的正常范围，符合阳性判断标准。因此，受试物新药003被判定为阳性。'
            )
            == '受试物新药003在高剂量组引起了微核率显著增加，超出了阴性对照的正常范围'
        )

        # GPT生成的案例
        assert (
            await GenotoxicEffects9.extract(
                '试验数据显示，所有受试物剂量组的微核率均未超过历史对照范围，且未见显著性差异，符合阴性判断标准。受试物新药004在本次试验中被判定为阴性。'
            )
            == '无'
        )
