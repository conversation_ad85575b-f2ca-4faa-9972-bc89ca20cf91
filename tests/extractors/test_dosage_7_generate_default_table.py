import pytest

from app.extractors.dosage_7_generate_default_table import GenerateDefaultTable


@pytest.mark.asyncio(loop_scope='session')
class TestGenerateDefaultTable:
    async def test_generate(self):
        header_1 = [
            ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
            ['动物数量', 'M:4', 'F:4', 'M:4', 'F:4', 'M:4', 'F:4', 'M:4', 'F:4'],
        ]
        assert await GenerateDefaultTable.generate(
            len(header_1[0]) - 1,
            '濒死/死亡',
            '0',
        ) == ['濒死/死亡', '0', '0', '0', '0', '0', '0', '0', '0']

        header_2 = [
            ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'],
            ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
        ]
        assert await GenerateDefaultTable.generate(
            len(header_2[0]) - 1,
            '眼科检查',
            '-',
        ) == ['眼科检查', '-', '-', '-', '-', '-', '-', '-', '-']
