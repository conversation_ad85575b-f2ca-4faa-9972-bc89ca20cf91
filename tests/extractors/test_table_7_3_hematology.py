import pytest

from app.extractors.table_7_3_hematology import HematologyTable


class TestHematologyTable:
    @pytest.mark.asyncio(loop_scope='session')
    async def test_extract(self):
        # 重复给药关键: 4232-************, project_id: 172 , doc_id: 902
        #  C-231610，7.7.2血液学，与以下表格一致
        assert (
            await HematologyTable.extract(
                '''| 性别 | 雄性 | 雄性 | 雄性 | 雄性 | 雌性 | 雌性 | 雌性 | 雌性 |
| 组别 | 1 | 2 | 3 | 4 | 1 | 2 | 3 | 4 |
| 受试物 | 溶媒 | 新药001 | 新药001 | 新药001 | 溶媒 | 新药001 | 新药001 | 新药001 |
| 剂量（mg/kg/day） | 0 | 5 | 15 | 75 | 0 | 2.5 | 7.5 | 50 |
| 检查动物数 | 10 | 10 | 10 | 10 | 10 | 10 | 10 | 10 |
| RBC（10^12/L） | 8.25 | 8.3 | 8.219 | 7.97 | 7.927 | 7.705 | 7.609 | 7.116*** |
| (% 差值) | - | +1 | 0 | -3 | - |  -3 | -4 | -10 |
| HGB (g/dL) | 16.48 | 16.31 | 16.07 | 15.73 | 15.19 | 15.16 | 14.97 | 14.3** |
| (% 差值) | - | -1 | -2 | -5 | - | 0 | -1 | -6 |
| 注：表中所示结果为组均值，粗体数值考虑与受试物相关。 “–”= 不适用；“+/-” = 升高/降低。（%差值）= 给药组均值与对照组均值之差相对对照组均值的百分比，四舍五入到整数。统计分析：Dunnett Test：** - 与对照组有显著差异（P < 0.01）；*** - 与对照组有显著差异（P < 0.001）。 | 注：表中所示结果为组均值，粗体数值考虑与受试 物相关。 “–”= 不适用；“+/-” = 升高/降低。（%差值）= 给药组均值与对照组均值之差相对对照组均值的百分比，四舍五入到整数。统计分析：Dunnett Test：** - 与对照组 有显著差异（P < 0.01）；*** - 与对照组有显著差异（P < 0.001）。 | 注：表中所示结果为组均值，粗体数值考虑与受试物相关。 “–”= 不适用；“+/-” = 升高/降低。（%差值）= 给药组均值与对照组均值之差相对对照组均值的百分比，四舍五入到整数。统计分析：Dunnett Test：** - 与对照组有显著差异（P < 0.01）；*** - 与对照组有显著差 异（P < 0.001）。 | 注：表中所示结果为组均值，粗体数值考虑与受试物相关。 “–”= 不适用；“+/-” = 升高/降低。（%差值）= 给药组均值与对照组均值之差相对对照组均值的百分比，四舍五入到整数。统计分析：Dunnett Test：** - 与对照组有显著差异（P < 0.01）；*** - 与对照组有显著差异（P < 0.001）。 | 注：表中所示结果为组均值， 粗体数值考虑与受试物相关。 “–”= 不适用；“+/-” = 升高/降低。（%差值）= 给药组均值与对照组均值之差相对对照组均值的百分比，四舍五入到整数。统计分析：Dunnett Test：** - 与对照组有显著差异（P < 0.01）；*** - 与对照组有显著差异（P < 0.001）。 | 注：表中所示结果为组均值，粗体数值考虑与受试物相关。 “–”= 不适用；“+/-” = 升高/降低。（%差值）= 给药组均值与对照组均值之差相对对照组均值的百分比，四舍五入到整数。统计分析：Dunnett Test：** - 与对照组有显著差异（P < 0.01）；*** - 与对照组有显著差异（P < 0.001）。 | 注：表中所示结果为组均值，粗体数值考虑与受试物相关。 “–”= 不适用；“+/-” = 升高/降低。（%差值）= 给药组均值与对照组均值之差相对对照组均值的百分比，四舍五入到整数。统计分析：Dunnett Test：** - 与对照组有显著差异（P < 0.01）；*** - 与对照组有显著差异（P < 0.001）。 | 注：表中所 示结果为组均值，粗体数值考虑与受试物相关。 “–”= 不适用；“+/-” = 升高/降低。（%差值）= 给药组均值与对照组均值之差相对对照组均值的百分比，四舍五入到整数。统计分析：Dunnett Test：** - 与对照组有显著差异（P < 0.01）；*** - 与对照组有显著差异（P < 0.001）。 | 注：表中所示结果为组均值，粗体数值考虑与受试物相关。 “–”= 不适用；“+/-” = 升高/降低。（%差值）= 给药组均值与对照组均值之差相对对照组均值的百分比，四舍五入到整数。统计分析：Dunnett Test：** - 与对照组有显著差异（P < 0.01）；*** - 与对照组有显著差异（P < 0.001）。 |''',
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M:10 | F:10 | M:10 | F:10 | M:10 | F:10 | M:10 | F:10 |
| RBC（10^12/L） | 8.25 | 7.927 | 8.3 | 7.705 | 8.219 | 7.609 | 7.97 | 7.116*** |
| (% 差值) | - | - | +1 | -3 | 0 | -4 | -3 | -10 |
| HGB (g/dL) | 16.48 | 15.19 | 16.31 | 15.16 | 16.07 | 14.97 | 15.73 | 14.3** |
| (% 差值) | - | - | -1 | 0 | -2 | -1 | -5 | -6 |'''
        )

        # 重复给药关键: 4232-31003-232003, project_id: 172 , doc_id: 903 没表格的
        # C-232003，7.10.2血液学，无表格
        # C-230613，7.10.2血液学，无表格

        #  C-230614，7.10.2血液学
        assert (
                await HematologyTable.extract(
                    '''| 性别 | 雄性 | 雄性 | 雄性 | 雄性 | 雌性 | 雌性 | 雌性 | 雌性 |
| 组别 | 1 | 2 | 3 | 4 | 1 | 2 | 3 | 4 |
| 受试物 | 溶媒 | 新药001 | 新药001 | 新药001 | 溶媒 | 新药001 | 新药001 | 新药001 |
| 剂量（mg/kg/day） | 0 | 1 | 2 | 4 | 0 | 1 | 2 | 4 |
| 检查动物数 | 5 | 5 | 5 | 5 | 5 | 5 | 5 | 5 |
| ANRETIC(10^9/L) | 35.20 | 33.20 | 28.16 | 25.06 | 52.96 | 48.34 | 22.88 | 26.08 |
| (%差值) | - | -6 | -20 | -29 | - | -9 | -57 | -51 |
| WBC(10^9/L) | 9.390 | 7.976 | 7.892 | 7.204 | 8.506 | 9.024 | 8.542 | 6.940 |
| (%差值) | -15 | -16 | -23 | - | +6 | - | -18 | - |
| ABNEUT(10^9/L) | 5.590  | 4.988  | 4.904  | 4.356  | 4.780  | 5.690  | 5.054  | 3.970  |
| (%差值) | -11 | -12 | -22 | - | +19 | +6 | -17 | - |
| ABLYMP(10^9/L) | 2.798  | 1.966**| 2.100* | 1.958**| 2.856  | 2.448  | 2.624  | 2.218  |
| (%差值) | -30 | -25 | -30 | - | -14 | -8 | -22 | - |
| ABBASO(10^9/L) | 0.050  | 0.020  | 0.040a | 0.026  | 0.040  | 0.030  | 0.034  | 0.044  |
| (%差值) | - | -60 | -20 | -48 | - | -25 | -15 | -10 |
| ABEOS(10^9/L) | 0.176 | 0.204 | 0.154 | 0.160 | 0.276 | 0.154 | 0.138 | 0.122 |
| (%差值) | - | +16 | -13 | -9 | - | -44 | -50 | -56 |''',
                    '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 1 | 1 | 2 | 2 | 4 | 4 |
    | 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',)
                == '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 1 | 1 | 2 | 2 | 4 | 4 |
| 动物数量 | M:5 | F:5 | M:5 | F:5 | M:5 | F:5 | M:5 | F:5 |
| ANRETIC(10^9/L) | 35.20 | 52.96 | 33.20 | 48.34 | 28.16 | 22.88 | 25.06 | 26.08 |
| (%差值) | - | - | -6 | -9 | -20 | -57 | -29 | -51 |
| WBC(10^9/L) | 9.390 | 8.506 | 7.976 | 9.024 | 7.892 | 8.542 | 7.204 | 6.940 |
| (%差值) | -15 | +6 | -16 | - | -23 | -18 | - | - |
| ABNEUT(10^9/L) | 5.590 | 4.780 | 4.988 | 5.690 | 4.904 | 5.054 | 4.356 | 3.970 |
| (%差值) | -11 | +19 | -12 | +6 | -22 | -17 | - | - |
| ABLYMP(10^9/L) | 2.798 | 2.856 | 1.966** | 2.448 | 2.100* | 2.624 | 1.958** | 2.218 |
| (%差值) | -30 | -14 | -25 | -8 | -30 | -22 | - | - |
| ABBASO(10^9/L) | 0.050 | 0.040 | 0.020 | 0.030 | 0.040a | 0.034 | 0.026 | 0.044 |
| (%差值) | - | - | -60 | -25 | -20 | -15 | -48 | -10 |
| ABEOS(10^9/L) | 0.176 | 0.276 | 0.204 | 0.154 | 0.154 | 0.138 | 0.160 | 0.122 |
| (%差值) | - | - | +16 | -44 | -13 | -50 | -9 | -56 |''')

    def test_parse_table(self):
        # 重复给药关键: 4232-************, project_id: 172 , doc_id: 902
        header1 = [
            ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
            ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
        ]
        assert (
            HematologyTable.parse_table(
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M:10 | F:10 | M:10 | F:10 | M:10 | F:10 | M:10 | F:10 |
| RBC（10^12/L） | 8.25 | 7.927 | 8.3 | 7.705 | 8.219 | 7.609 | 7.97 | 7.116*** |
| (% 差值) | - | - | +1 | -3 | 0 | -4 | -3 | -10 |
| HGB (g/dL) | 16.48 | 15.19 | 16.31 | 15.16 | 16.07 | 14.97 | 15.73 | 14.3** |
| (% 差值) | - | - | -1 | 0 | -2 | -1 | -5 | -6 |''',
                header1,
            )
            == [
                ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                [
                    '血液学参数',
                    '血液学参数',
                    '血液学参数',
                    '血液学参数',
                    '血液学参数',
                    '血液学参数',
                    '血液学参数',
                    '血液学参数',
                    '血液学参数',
                ],
                [
                    'RBC（10^12/L）\n（%变化百分比）',
                    '8.25',
                    '7.927',
                    '8.3\n（1%↑）',
                    '7.705\n（3%↓）',
                    '8.219\n（0%）',
                    '7.609\n（4%↓）',
                    '7.97\n（3%↓）',
                    '7.116***\n（10%↓）',
                ],
                [
                    'HGB (g/dL)\n（%变化百分比）',
                    '16.48',
                    '15.19',
                    '16.31\n（1%↓）',
                    '15.16\n（0%）',
                    '16.07\n（2%↓）',
                    '14.97\n（1%↓）',
                    '15.73\n（5%↓）',
                    '14.3**\n（6%↓）',
                ],
            ]
        )

        # 重复给药关键: 4232-31003-232003, project_id: 172 , doc_id: 903  没数据的
