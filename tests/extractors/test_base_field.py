import pytest

from app.extractors.base_field import (
    AdministrationMethod,
    AmesSpecies,
    ChromosomeAberrationAndAmesSolvent,
    ChromosomeAberrationRegimen,
    ChromosomeAberrationSpecies,
    Cover,
    DosageAmes,
    DosageChromosome,
    DosageDoseSpecies,
    DosageMicronucleus,
    DosageSingle,
    MicronucleusRegimen,
    MicronucleusSolvent,
    MicronucleusSpecies,
    RepeatedDoseRegimen,
    RepeatedDoseSpecies,
    SingleAdministrationMethod,
    SingleAndRepeatedDoseSolvent,
    SingleDoseSpecies,
)
from app.schemas.doc_base_field import TrialMainType, TrialSubType


# 2.1.2 - 1 毒理学概述 - 单次给药毒性部分 - 种属与品类
@pytest.mark.asyncio(loop_scope='session')
class TestCover:
    async def test_extract(self):
        assert (
            await Cover.extract(
                '''总结报告

试验名称：SD大鼠静脉注射给予XXX单次给药毒性试验

试验编号：S19030AD1

专题负责人：陆小松，M.S.

试验机构：XXX生物技术南通有限公司

委托方：XXX

报告签发日期：

总页数：<xx>页，包含本页

GLP遵从性声明和签字页

GLP COMPLIANCE STATEMENT AND SIGNATURE

试验编号：S19030AD1

试验名称：SD大鼠静脉注射给予XXX单次给药毒性试验（A Single Dose Toxicity Study of SHPL-49 Injection in SD Rats via Intravenous Administration）

除供试品质量检测外，本试验所有阶段均在本试验机构完成，遵从下述GLP管理规范，试验期间未见影响试验质量和完整性的偏离。

NMPA (CFDA).《药物非临床研究质量管理规范》。

US FDA 21 CFR Part 58, Good Laboratory Practice for Nonclinical Laboratory Studies.

OECD Series on Principles of Good Laboratory Practice and Compliance Monitoring No. 1: OECD Principles of Good Laboratory Practice.

供试品由委托方提供，供试品质量检测在非GLP条件下完成。

Except for the certificates of analysis (COA) of the test article, this study was conducted by the test facility according to the following these GLP regulations. No deviations markedly affect the quality and integrity of the study.

NMPA (CFDA). Good Laboratory Practice for Non-clinical Laboratory Studies.

US FDA 21 CFR Part 58, Good Laboratory Practice for Nonclinical Laboratory Studies.

OECD Series on Principles of Good Laboratory Practice and Compliance Monitoring No. 1: OECD Principles of Good Laboratory Practice.

The certificates of analysis (COA) of the test article was provided by the sponsor, and conducted under non-GLP conditions.

签名（Signature）：                        日期（Date）：       年    月    日

，M.S.                             YYYY /MM/DD

专题负责人（Study Director）

质量保证声明

QUALITY ASSURANCE STATEMENT

本机构QAU对该试验进行了如下检查（供试品质量检测除外）。本试验报告如实描述试验材料和方法，准确完整地反映试验过程中产生的原始数据。

The conduct of the study was inspected by the QAU of the test facility as follows (except certificate of analysis (COA) of the test article).The final report accurately and completely describes the materials and methods, and the reported results accurately and completely reflect the raw data of the study.

签名（Signature）：                        日期（Date）：       年    月    日

高嘉政（Jiazheng Gao）, B.S.                              YYYY /  MM/  DD

质量保证人员（Quality Assurance）'''
            )
            == ('SD大鼠静脉注射给予XXX单次给药毒性试验', 'XXX生物技术南通有限公司', 'S19030AD1')
        )


# 2.1.2 - 1 毒理学概述 - 单次给药毒性部分 - 种属与品类
@pytest.mark.asyncio(loop_scope='session')
class TestSingleDoseSpecies:
    async def test_extract(self):
        # 单次给药：33416-230611-总结报告.docx
        assert (
            await SingleDoseSpecies.extract('Sprague-Dawley大鼠[Crl:CD(SD)IGS](SPF/VAF)，无给药史')
            == 'Sprague-Dawley大鼠[Crl:CD(SD)IGS](SPF/VAF)'
        )

        # 单次给药：4231-31003-231609.docx
        assert (
            await SingleDoseSpecies.extract('Sprague-Dawley大鼠[Crl:CD(SD)IGS](SPF)，无给药史')
            == 'Sprague-Dawley大鼠[Crl:CD(SD)IGS](SPF)'
        )

        # 单次给药：4. 33416-230612 data CHN.docx
        assert await SingleDoseSpecies.extract('比格犬（普通级、无给药史）') == '比格犬'


# 2.2.2 - 1 毒理学概述 - 重复给药毒性-关键/非关键试验 - 种属与品类
@pytest.mark.asyncio(loop_scope='session')
class TestRepeatedDoseSpecies:
    async def test_extract(self):
        # 重复给药-非关键：4232-31053-23238.docx
        assert (
            await RepeatedDoseSpecies.extract('Sprague-Dawley大鼠[Crl:CD(SD)IGS](SPF)，无给药史')
            == 'Sprague-Dawley大鼠[Crl:CD(SD)IGS](SPF)'
        )

        # 重复给药-非关键：3. 3D3211 Dog DRF.docx
        assert await RepeatedDoseSpecies.extract('比格犬（无给药史，普通级）') == '比格犬'

        # 重复给药-非关键：3. 3R3248 Rat DRF.docx
        assert (
            await RepeatedDoseSpecies.extract('Sprague-Dawley大鼠 [Crl: CD (SD)] (SPF/VAF)')
            == 'Sprague-Dawley大鼠 [Crl: CD (SD)] (SPF/VAF)'
        )

        # 重复给药-关键：4232-31003-231610.docx
        assert (
            await RepeatedDoseSpecies.extract('Sprague-Dawley大鼠[Crl:CD(SD)IGS](SPF/VAF)，无给药史')
            == 'Sprague-Dawley大鼠[Crl:CD(SD)IGS](SPF/VAF)'
        )

        # 重复给药-关键：4232-31003-232003.docx
        assert await RepeatedDoseSpecies.extract('比格犬（Beagle Dogs，无给药史，普通级）') == '比格犬'


# 2.4.2 - 1 毒理学概述 - 遗传毒性-回复突变试验部分 - 种属与品类
@pytest.mark.asyncio(loop_scope='session')
class TestAmesSpecies:
    async def test_extract(self):
        assert (
            await AmesSpecies.extract(
                '本试验使用了组氨酸缺陷型鼠伤寒沙门氏菌TA98、TA100、TA1535、TA1537（Ames et al., 1975）和大肠埃希杆菌WP2 uvrA（Green and Muriel, 1976）。'
            )
            == '鼠伤寒沙门氏菌 TA98，鼠伤寒沙门氏菌 TA100，鼠伤寒沙门氏菌 TA1535，鼠伤寒沙门氏菌 TA1537，大肠埃希杆菌 WP2 uvrA'
        )

        # DIY
        assert (
            await AmesSpecies.extract(
                '这项研究涉及了腺病毒AD5、AD2（Smith et al., 1990）以及肺炎链球菌Pneumococcus 23F（Johnson, 1985）'
            )
            == '腺病毒 AD5，腺病毒 AD2，肺炎链球菌 Pneumococcus 23F'
        )


# 2.5.2 - 1 毒理学概述 - 遗传毒性-染色体试验部分 - 种属与品类
@pytest.mark.asyncio(loop_scope='session')
class TestChromosomeAberrationSpecies:
    async def test_extract(self):
        assert (
            await ChromosomeAberrationSpecies.extract('新药001：中国仓鼠卵巢细胞体外染色体畸变试验')
            == '中国仓鼠卵巢细胞'
        )

        # DIY
        assert (
            await ChromosomeAberrationSpecies.extract(
                '573试品：鲨鱼细胞shark07染色体畸变试验，蓝鲸细胞染色体畸变试验。'
            )
            == '鲨鱼细胞shark07,蓝鲸细胞'
        )


# 2.6.2 - 1 毒理学概述 - 遗传毒性-微核试验部分 - 种属与品类
@pytest.mark.asyncio(loop_scope='session')
class TestMicronucleusSpecies:
    async def test_extract(self):
        assert (
            await MicronucleusSpecies.extract('Sprague-Dawley (SD) 大鼠 [Crl:CD (SD)]，SPF级')
            == 'Sprague-Dawley (SD) 大鼠 [Crl:CD (SD)]'
        )

        assert await MicronucleusSpecies.extract('SD大鼠（成年），SPF级') == 'SD大鼠'

        # DIY
        assert (
            await MicronucleusSpecies.extract('Sprague-Dawley (SD) 大鼠 [Crl(SD)]（幼年期）')
            == 'Sprague-Dawley (SD) 大鼠 [Crl(SD)]'
        )


# 6.2 - 2 单次给药毒性 - 给药方法（溶媒/剂型）
# 7.2 - 2 重复给药毒性：非关键实验 - 给药方法（溶媒/剂型）
@pytest.mark.asyncio(loop_scope='session')
class TestSingleAndRepeatedDoseSolvent:
    async def test_extract(self):
        # 单词给药
        # 4231-31003-231609
        assert (
            await SingleAndRepeatedDoseSolvent.extract(
                '''a：含10%乙醇和40% PEG 400的去离子水溶液。
试验中的毒理学评价指标包括笼旁观察、详细临床观察、给药后详细临床观察、体重及体重变化。'''
            )
            == '含10%乙醇和40% PEG 400的去离子水溶液'
        )

        # 33416-230611-总结报告
        assert (
            await SingleAndRepeatedDoseSolvent.extract(
                '''a：含5% DMSO和10% Solutol HS 15的去离子水溶液。
给药信息
对于任一指定动物，给药日即为试验第一天（Day 1）。给药信息见正文表6。'''
            )
            == '含5% DMSO和10% Solutol HS 15的去离子水溶液'
        )

        # 33416-230612 data CHN
        assert (
            await SingleAndRepeatedDoseSolvent.extract(
                '''a: 含5% DMSO 和10% Solutol HS 15 的去离子水溶液。
给药信息
给药信息见正文表6。
正文表 6: 给药信息
给药途径经口灌胃，给药前整晚禁食给药频率单次给药给药体积5 mL/kg，每只动物的绝对给药体积以mL为单位，保留整位数，由Provantis系统根据动物最近记录的体重计算而得。实际给药量将遵循SOP 40.035。给药条件给药前制剂于室温下搅拌至少10分钟，并在给药过程中持续搅拌
评价指标
临床观察、体重和摄食量
在体期观察指标见正文表7。
正文表 7: 观察指标 '''
            )
            == '含5% DMSO 和10% Solutol HS 15 的去离子水溶液'
        )

        # 重复给药非关键
        # 4232-31053-23238
        assert (
            await SingleAndRepeatedDoseSolvent.extract(
                '''a：5%DMSO+10%Solutol的20%HP-β-CD去离子水。
试验中的毒理学评价指标包括笼旁观察、详细临床观察、体重及体重变化、摄食量、临床病理学检测（血生化，血液学和凝血）、绝对和相对脏器重量、大体病理学检查及毒代动力学分析（TK）。毒代动力学采血时间点见正文表5。'''
            )
            == '5%DMSO+10%Solutol的20%HP-β-CD去离子水'
        )

        # 3. 3D3211 Dog DRF
        assert (
            await SingleAndRepeatedDoseSolvent.extract(
                '''a: 含5% DMSO和10% Solutol的去离子水溶液
b: 终期处死（TS）动物，在第29 天安乐死
c: 恢复期处死（RS）动物，在第28 天后停止给药并在第57 天安乐死

试验中的毒理学评价指标包括临床观察（笼旁观察、详细临床观察和非计划临床观察）、体重及体重变化、摄食量（定性）、血压（每组每性别第2只动物）、心电图（每组每性别第1只动物）、临床病理学检测（血生化、血液学和凝血）、绝对和相对脏器重量、大体病理学检查、组织病理学评价及毒代动力学分析（TK）。
随机分组前、试验第29天（终末期）和第57天（恢复期）剖检前，从所有实验动物中采集血样，用于临床病理学检测。剩余的血清和血浆样本在专题结束前按康龙化成（宁波）TSP的SOPs进行处理。
在第1天和第28天，按正文表6所示时间点采集血液样品，用于评价XXX或XXX在犬血浆中的暴露量。备用样品及剩余的第一份样品在专题结束前按康龙化成（宁波）TSP的SOPs进行处理。
正文表6：毒代动力学采血时间点'''
            )
            == '含5% DMSO和10% Solutol的去离子水溶液'
        )

        # 3.  3R3248 Rat DRF
        assert (
            await SingleAndRepeatedDoseSolvent.extract(
                '''a: 含5% DMSO和10% Solutol的去离子水溶液
试验中的毒理学评价指标包括临床观察（笼旁观察、详细临床观察和非计划临床观察）、体重及体重变化、摄食量、临床病理学检测（血生化、血液学和凝血）、绝对和相对脏器重量、大体病理学检查、组织病理学评价及毒代动力学分析（TK）。
试验第29天剖检前，从所有存活的第1-5组主试验动物采集血样，用于临床病理学检测。剩余的血清和血浆样本在专题结束前按康龙化成（宁波）TSP的SOPs进行处理。
在第1天和第28天，按正文表6所示时间点采集血液样品，用于评价XXX 或XXX在大鼠血浆中的暴露量。备用样品及剩余的第一份样品在专题结束前按康龙化成（宁波）TSP的SOPs进行处理。
正文表6：毒代动力学采血时间点'''
            )
            == '含5% DMSO和10% Solutol的去离子水溶液'
        )

        # 重复给药关键
        # 4232-31003-231610
        assert (
            await SingleAndRepeatedDoseSolvent.extract(
                '''a: 含5%DMSO+10%Solutol HS15的20% HP-β-CD去离子水溶液。
b:终末期安乐死（TS）动物，在第29天安乐死。
c:恢复期安乐死（RS）动物，在第28天后停止给药并在第57天安乐死。
给药信息
主试验雄性和所有TK动物、主试验雌性动物分2天进行给药。对于任一指定动物，首次给药日即为试验第一天（Study Day 1）。给药信息见正文表6。'''
            )
            == '含5%DMSO+10%Solutol HS15的20% HP-β-CD去离子水溶液'
        )

        # 4232-31003-232003
        assert (
            await SingleAndRepeatedDoseSolvent.extract(
                '''M = 雄性; F = 雌性
a:终末期安乐死（TS）动物，在第29天安乐死。
b:恢复期安乐死（RS）动物，在第28天后停止给药并在第57天安乐死。
c:含5%DMSO+10%Solutol HS15 的20% HP-β-CD 去离子水溶液。'''
            )
            == '含5%DMSO+10%Solutol HS15 的20% HP-β-CD 去离子水溶液'
        )


# 26781 26782 遗传毒性：回复突变 以及 染色体试验 - 溶媒/剂型
@pytest.mark.asyncio(loop_scope='session')
class TestChromosomeAberrationAndAmesSolvent:
    async def test_extract(self):
        # 遗传毒性-回复突变
        # D-231925
        assert (
            await ChromosomeAberrationAndAmesSolvent.extract(
                '''| 名称： | 二甲基亚砜（DMSO） | 
| 批号： | SHBP4381 | 
| 复检日期： | 2024年03月31日 | 
| 保存条件： | 室温 | 
| 性状： | 无色液体 | 
| 供应商： | Sigma-Aldrich |'''
            )
            == '二甲基亚砜（DMSO）'
        )


# 11.1.2 - 11 遗传毒性：体内-微核试验部分 - 溶媒/剂型
@pytest.mark.asyncio(loop_scope='session')
class TestMicronucleusSolvent:
    async def test_extract(self):
        # 遗传毒性-微核
        # 42332-31006-231926
        assert (
            await MicronucleusSolvent.extract(
                '受试物溶媒对照为含5%DMSO+10%Solutol HS15的20% HP-β-CD去离子水溶液。去离子水由内部水系统新鲜制备。溶媒对照品的组分信息如下：'
            )
            == '含5%DMSO+10%Solutol HS15的20% HP-β-CD去离子水溶液'
        )


# 2671 单次给药 - 给药方法
@pytest.mark.asyncio(loop_scope='session')
class TestSingleAdministrationMethod:
    async def test_extract(self):

        # A-230612
        assert (
            await SingleAdministrationMethod.extract(
                '''| 给药途径 | 经口灌胃，给药前整晚禁食 |
| 给药频率 | 单次给药 |
| 给药体积 | 5 mL/kg，每只动物的绝对给药体积以mL为单位，保留整位数，由Provantis系统根据动物最近记录的体重计算而得。实际给药量将遵循SOP 40.035。 |
| 给药条件 | 给药前制剂于室温下搅拌至少10分钟，并在给药过程中持续搅拌 |'''
            )
            == '经口灌胃，给药前整晚禁食'
        )


# 2.2.2 - 2 重复给药毒性-非关键试验部分 - 给药方法
@pytest.mark.asyncio(loop_scope='session')
class TestAdministrationMethod:
    async def test_extract(self):

        # 重复给药非关键 4232-31053-23238
        assert (
            await AdministrationMethod.extract(
                'Sprague Dawley大鼠经口灌胃给予新药001的14天剂量范围确定的毒理学和毒代动力学试验'
            )
            == '经口灌胃'
        )

        # 3. 3D3211 Dog DRF
        assert (
            await AdministrationMethod.extract('比格犬灌胃给予XXX的28天剂量范围确定的毒理学和毒代动力学试验') == '灌胃'
        )

        # 3.  3R3248 Rat DRF
        assert (
            await AdministrationMethod.extract(
                'Sprague-Dawley大鼠灌胃给予XXX的28天剂量范围确定的毒理学和毒代动力学试验'
            )
            == '灌胃'
        )

        # 重复给药关键 4232-31003-231610
        assert (
            await AdministrationMethod.extract(
                'Sprague Dawley大鼠经口灌胃给予新药001连续28天及恢复28天的毒理学及毒代动力学试验'
            )
            == '经口灌胃'
        )

        # 4232-31003-232003
        assert (
            await AdministrationMethod.extract(
                '比格犬经口灌胃重复给予新药001连续28天及恢复28天的毒理学及毒代动力学试验'
            )
            == '经口灌胃'
        )

        # 大鼠经口重复给药28天毒性试验
        assert await AdministrationMethod.extract('XXX大鼠经口重复给药28天毒性试验') == '经口'

        # 比格犬经口重复给药4周毒性试验
        assert await AdministrationMethod.extract('''XXX比格犬经口重复给药4周毒性试验''') == '经口'


# 2.2.2 - 3 重复给药毒性-关键/非关键试验部分 - 给药周期
@pytest.mark.asyncio(loop_scope='session')
class TestRepeatedDoseRegimen:
    async def test_extract(self):
        # 重复给药毒性-非关键
        # 4232-31053-23238
        assert (
            await RepeatedDoseRegimen.extract(
                'Sprague Dawley大鼠经口灌胃给予新药001的14天剂量范围确定的毒理学和毒代动力学试验'
            )
            == '14天'
        )

        # 3. 3D3211 Dog DRF
        assert (
            await RepeatedDoseRegimen.extract('比格犬灌胃给予XXX的28天剂量范围确定的毒理学和毒代动力学试验') == '28天'
        )

        # 3.  3R3248 Rat DRF
        assert (
            await RepeatedDoseRegimen.extract('Sprague-Dawley大鼠灌胃给予XXX的28天剂量范围确定的毒理学和毒代动力学试验')
            == '28天'
        )

        # 重复给药毒性-关键性
        # 4232-31003-231610
        assert (
            await RepeatedDoseRegimen.extract(
                'Sprague Dawley大鼠经口灌胃给予新药001连续28天及恢复28天的毒理学及毒代动力学试验'
            )
            == '28天'
        )

        # 4232-31003-232003
        assert (
            await RepeatedDoseRegimen.extract('比格犬经口灌胃重复给予新药001连续28天及恢复28天的毒理学及毒代动力学试验')
            == '28天'
        )

        # 大鼠经口重复给药28天毒性试验

        # 比格犬经口重复给药4周毒性试验


# 2.5.2- 3 遗传毒性-染色体试验部分 - 给药周期
@pytest.mark.asyncio(loop_scope='session')
class TestChromosomeAberrationRegimen:
    async def test_extract(self):
        # 遗传毒性-染色体畸变
        # 42331-31006-231924
        assert (
            await ChromosomeAberrationRegimen.extract(
                '''| 给药处理系列 | 测试新药001浓度(μg/mL) |
| S9代谢活化给药3小时系列 | 40、120、200和240 |
| 非代谢活化给药3小时系列 | 40、120、200和240 |
| 非代谢活化给药20小时系列 | 40、100、170和200 |'''
            )
            == '+S9, 3h | -S9, 3h | -S9, 20h'
        )


# 2.6.2- 3 遗传毒性-微核试验部分 - 给药周期
@pytest.mark.asyncio(loop_scope='session')
class TestMicronucleusRegimen:
    async def test_extract(self):
        # 遗传毒性-微核
        # 42332-31006-231926
        assert (
            await MicronucleusRegimen.extract(
                '在剂量探索试验中，受试物通过经口灌胃给药2天，每天给药1次，每天给药时间间隔24小时（±30分钟）。'
            )
            == '间隔24h给予2次'
        )


# 2.1.2 - 4 毒理学概述 - 单次给药毒性部分 - 剂量
@pytest.mark.asyncio(loop_scope='session')
class TestDosageSingle:
    async def test_extract(self):
        # 单次给药
        # 4231-31003-231609
        assert (
            await DosageSingle.extract(
                '''|  组别a | 受试物 | 给药剂量 (mg/kg/day) | 浓度(mg/mL) | 动物数（雄性/雌性） |
|  1 | 新药001 | 400 | 40 | 3/3 |
|  2 | 新药001 | 1000 | 100 | 3/3 |
|  3 | 新药001 | 600 | 60 | 3/3 |
|  4 | 新药001 | 800 | 80 | 3/3 |'''
            )
            == '400、600、800、1000 mg/kg'
        )

        # 33416-230611-总结报告
        assert (
            await DosageSingle.extract(
                '''|  组别 | 受试物 | 给药剂量(mg/kg/day) | 浓度(mg/mL) | 动物数（雄/雌） |
|  1 | 溶媒a | 0 | 0 | 5/5 |
|  2 | XXX | 15 | 1.5 | 5/5 |
|  3 | XXX | 30 | 3 | 5/5 |
|  4 | XXX | 60 | 6 | 5/5 |'''
            )
            == '15、30、60 mg/kg'
        )

        # 单次给药_4. 33416-230612 data CHN
        assert (
            await DosageSingle.extract(
                '''|  组别 | 受试物 | 给药剂量(mg/kg) | 浓度(mg/mL) | 动物数(雄性/雌性) |
|  1 | 溶媒a | 0 | 0 | 1 /1 |
|  2 | XXX | 6 | 1.2 | 1 /1 |
|  3 | XXX | 8 | 1.6 | 1 /1 |
|  4 | XXX | 10 | 2 | 1 /1 |'''
            )
            == '6、8、10 mg/kg'
        )


# 2.3.2 - 4 毒理学概述 - 复给药毒性-关键试验部分 - 剂量
@pytest.mark.asyncio(loop_scope='session')
class TestDosageDoseSpecies:
    async def test_extract(self):
        # 重复给药非关键
        # 4232-31053-23238
        assert (
            await DosageDoseSpecies.extract(
                '''|  组别 | 受试物 | 给药剂量(mg/kg/day) | 浓度(mg/mL) | 动物数（雄性/雌性） | 动物数（雄性/雌性） |
|  组别 | 受试物 | 给药剂量(mg/kg/day) | 浓度(mg/mL) | 主试验 | TK |
|  1 | 溶媒a | 0 | 0 | 5/5 | 3/3 |
|  2 | 新药001 | 10 | 1 | 5/5 | 3/3 |
|  3 | 新药001 | 25 | 2.5 | 5/5 | 3/3 |
|  4 | 新药001 | 75 | 7.5 | 5/5 | 3/3 |
|  5 | 新药001 | 150 | 15 | 5/5 | 3/3 |'''
            )
            == '10、25、75、150 mg/kg/day'
        )

        # 3. 3D3211 Dog DRF
        assert (
            await DosageDoseSpecies.extract(
                '''|  组别 | 受试物 | 给药剂量(mg/kg/day) | 浓度(mg/mL) | 动物数(雄性/雌性) | 动物数(雄性/雌性) | 动物号(雄性/雌性) | 动物号(雄性/雌性) |
|  组别 | 受试物 | 给药剂量(mg/kg/day) | 浓度<br>(mg/mL) | TSb | RSC | TSb | RSC |
|  1 | 溶媒a | 0 | 0 | 1/1 | 1/1 | YD20627/YD20452 | YD20545/YD20492 |
|  2 | XXX | 0.3 | 0.06 | 2/2 | - | YD20621, YD20625/YD20508, YD20220 | - |
|  3 | XXX | 1.0 | 0.2 | 1/1 | 1/1 | YD20559/YD20480 | YD20569/YD20616 |
|  4 | XXX | 3.0 | 0.6 | 1/1 | 1/1 | YD20635/YD20520 | YD20557/YD20364 |
|  5 | XXX | 1.0 | 0.2 | 2/2 | - | YD20641, YD20563/YD20434, YD20592 | - |'''
            )
            == '0.3、1.0、3.0 mg/kg/day'
        )

        # 3.  3R3248 Rat DRF
        assert (
            await DosageDoseSpecies.extract(
                '''|  组别 | 受试物 | 给药剂量(mg/kg/day) | 浓度(mg/mL) | 动物数(雄性/雌性) | 动物数(雄性/雌性) | 动物号(雄性/雌性) | 动物号(雄性/雌性) |
|  组别 | 受试物 | 给药剂量(mg/kg/day) | 浓度<br>(mg/mL) | TSb | RSC | TSb | RSC |
|  1 | 溶媒a | 0 | 0 | 1/1 | 1/1 | YD20627/YD20452 | YD20545/YD20492 |
|  2 | XXX | 0.3 | 0.06 | 2/2 | - | YD20621, YD20625/YD20508, YD20220 | - |
|  3 | XXX | 1.0 | 0.2 | 1/1 | 1/1 | YD20559/YD20480 | YD20569/YD20616 |
|  4 | XXX | 3.0 | 0.6 | 1/1 | 1/1 | YD20635/YD20520 | YD20557/YD20364 |
|  5 | XXX | 1.0 | 0.2 | 2/2 | - | YD20641, YD20563/YD20434, YD20592 | - |'''
            )
            == '0.3、1.0、3.0 mg/kg/day'
        )

        # 重复给药关键
        # 4232-31003-231610
        assert (
            await DosageDoseSpecies.extract(
                '''|  组别 | 受试物 | 给药剂量(雄/雌)(mg/kg/day) | 浓度(雄/雌)(mg/mL) | 动物数 (雄/雌) | 动物数 (雄/雌) | 动物数 (雄/雌) |
|  组别 | 受试物 | 给药剂量(雄/雌)(mg/kg/day) | 浓度(雄/雌)(mg/mL) | 主试验动物 | 主试验动物 | TK |
|  组别 | 受试物 | 给药剂量(雄/雌)(mg/kg/day) | 浓度(雄/雌)(mg/mL) | TSb | RSc |  |
|  1 | 溶媒a | 0/0 | 0/0 | 10/10 | 5/5 | 4/4 |
|  2 | 新药001 | 5/2.5 | 0.5/0.25 | 10/10 | 5/5 | 4/4 |
|  3 | 新药001 | 15/7.5 | 1.5/0.75 | 10/10 | 5/5 | 4/4 |
|  4 | 新药001 | 75/50 | 7.5/5.0 | 10/10 | 5/5 | 4/4 |'''
            )
            == '''雄性：5、15、75 mg/kg/day
雌性：2.5、7.5、50 mg/kg/day'''
        )

        # 4232-31003-232003
        assert (
            await DosageDoseSpecies.extract(
                '''|  组别 | 受试物 | 给药剂量(mg/kg/day) | 浓度(mg/mL) | 动物数 (M/F) | 动物数 (M/F) |
|  组别 | 受试物 | 给药剂量(mg/kg/day) | 浓度(mg/mL) | TSa | RSb |
|  1 | 溶媒c | 0 | 0 | 3/3 | 2/2 |
|  2 | 新药001 | 1.5 | 0.3 | 3/3 | 2/2 |
|  3 | 新药001 | 5 | 1 | 3/3 | 2/2 |
|  4 | 新药001 | 15 | 3 | 3/3 | 2/2 |'''
            )
            == '1.5、5、15 mg/kg/day'
        )
        # 大鼠经口重复给药28天毒性试验

        # 比格犬经口重复给药4周毒性试验


# 2.4.2 - 4 毒理学概述 - 遗传毒性-回复突变试验部分 - 剂量
@pytest.mark.asyncio(loop_scope='session')
class TestDosageAmes:
    async def test_extract(self):
        # 遗传毒性-回复突变
        # 42331-31006-231925
        assert (
            await DosageAmes.extract(
                '''|  表IV. 细菌回复突变主试验的剂量水平 | 表IV. 细菌回复突变主试验的剂量水平 | 表IV. 细菌回复突变主试验的剂量水平 | 表IV. 细菌回复突变主试验的剂量水平 |
|  测试菌株 | ±S9 | 新药001剂量（µg/皿） | 新药001制剂浓度(mg/mL)  |
|  TA98、TA100、TA1535、TA1537和WP2 uvrA | ± | 200、500、1000、2000和5000 | 2、5、10、20和50 |'''
            )
            == '200、500、1000、2000、5000 µg/皿'
        )


# 2.5.2 - 4 毒理学概述 - 遗传毒性-染色体试验部分 - 剂量
@pytest.mark.asyncio(loop_scope='session')
class TestDosageChromosome:
    async def test_extract(self):
        # 遗传毒性-染色体
        # 42331-31006-231924
        assert (
            await DosageChromosome.extract(
                '''|  给药处理系列 | 测试新药001浓度(μg/mL) |
|  S9代谢活化给药3小时系列 | 40、120、200和240 |
|  非代谢活化给药3小时系列 | 40、120、200和240 |
|  非代谢活化给药20小时系列 | 40、100、170和200 |'''
            )
            == '''+S9, 3 h：40、120、200、240 μg/mL
-S9, 3 h：40、120、200、240 μg/mL
-S9, 20 h：40、100、170、200 μg/mL'''
        )


@pytest.mark.asyncio(loop_scope='session')
class TestDosageMicronucleus:
    async def test_extract(self):
        # 遗传毒性-微核
        # 42332-31006-231926
        assert (
            await DosageMicronucleus.extract(
                '''以下是正文内容：

在微核主试验中，每个性别27只大鼠随机分成5组，每组5只（其中高剂量组为每组7只），分别给予溶媒对照，受试物或者阳性对照。溶媒对照组和受试物组的动物在最后一次给药后18-24 小时的时间点处死动物取骨髓。阳性对照组的所有动物在最后一次给药后24小时 (±1小时)的时间点处死动物取骨髓。每只动物骨髓涂片在显微镜下观察计数，评价微核率以及PCE占红细胞的比例。
 首次给药日定义为微核主试验的第一天（Day 1）。
 微核主试验设计包括每组动物数目和给药方案如下表所示：以下是表格内容：

表头：
表6.4.2-1: 微核主试验设计表格：
|  组别 | 颜色编码 | 给药方案 | 制剂浓度(mg/mL) | 给药体积（mL/kg） | 动物数目 |
 |  1 | 白色 | 溶媒 | 0 | 10 | 5雄/5雌 |
 |  2 | 蓝色 | 新药001, 100 mg/kg/day | 10 | 10 | 5雄/5雌 |
 |  3 | 红色 | 新药001, 200 mg/kg/day | 20 | 10 | 5雄/5雌 |
 |  4 | 黄色 | 新药001, 400 mg/kg/day | 40 | 10 | 7雄* |
 |  5 | 黑色 | 新药001, 600 mg/kg/day | 60 | 10 | 7雌* |
 |  6 | 绿色 | CP, 20 mg/kg | 2 | 10 | 5雄/5雌 |
 |  备注: CP=环磷酰胺一水合物（阳性对照）*每个性别的7只动物包含了允许存在受试物诱导致死的可能。用于分析的动物数量为5只（高剂量组前5只存活的动物）。 | 备注: CP=环磷酰胺一水合物（阳性对照）*每个性别的7只动物包含了允许存在受试物诱导致死的可能。用于分析的动物数量为5只（高剂量组前5只存活的动物）。 | 备注: CP=环磷酰胺一水合物（阳性对照）*每个性别的7只动物包含了允许存在受试物诱导致死的可能。用于分析的动物数量为5只（高剂量组前5只存活的动物）。 | 备注: CP=环磷酰胺一水合物（阳性对照）*每个性别的7只动物包含了允许存在受试物诱导致死的可能。用于分析的动物数量为5只（高剂量组前5只存活的动物）。 | 备注: CP=环磷酰胺一水合物（阳性对照）*每个性别的7只动物包含了允许存在受试物诱导致死的可能。用于分析的动物数量为5只（高剂量组前5只存活的动物）。 | 备注: CP=环磷酰胺一水合物（阳性对照）*每个性别的7只动物包含了允许存在受试物诱导致死的可能。用于分析的动物数量为5只（高剂量组前5只存活的动物）。 |微核主试验中600 mg/kg/day的雌性大鼠在Day 3 有3只死亡，表明雌性大鼠在600 mg/kg/day的剂量水平不耐受，并且可分析的动物不足5只，因此，雌性大鼠以高剂量为400 mg/kg/day重新进行主试验。使用ProvantisTM 10.5系统将27只雌性大鼠随机分成5组，每组5只（其中高剂量组为每组7只），分别给予溶媒对照、受试物或者阳性对照。溶媒对照组和受试物组的动物将在最后一次给药后18-24小时处死动物取骨髓。阳性对照组的动物将在最后一次给药后24小时(±1小时)安乐死并取骨髓。每只动物骨髓涂片在显微镜下观察计数，评价微核率以及PCE占红细胞的比例。
 第一次给药日定义为雌性大鼠微核主试验的第一天（Day 1）。
 试验设计包括每组动物数目和给药方案如下表所示：表头：
表6.4.2-1-2: 试验设计表格：
|  组别 | 颜色编码 | 给药方案 | 制剂浓度(mg/mL) | 给药体积（mL/kg） | 动物数目 |
 |  1 | 白色 | 溶媒 | 0 | 10 | 5雌 |
 |  2 | 蓝色 | 新药001, 100 mg/kg/day | 10 | 10 | 5雌 |
 |  3 | 红色 | 新药001, 200 mg/kg/day | 20 | 10 | 5雌 |
 |  4 | 黄色 | 新药001, 400 mg/kg/day | 40 | 10 | 7雌* |
 |  5 | 绿色 | CP, 20 mg/kg | 2 | 10 | 5雌 |
 |  备注: CP=环磷酰胺一水合物（阳性对照）* 7只动物包含了允许存在受试物诱导致死的可能。用于分析的动物数量为5只（高剂量组前5只存活的动物）。 | 备注: CP=环磷酰胺一水合物（阳性对照）* 7只动物包含了允许存在受试物诱导致死的可能。用于分析的动物数量为5只（高剂量组前5只存活的动物）。 | 备注: CP=环磷酰胺一水合物（阳性对照）* 7只动物包含了允许存在受试物诱导致死的可能。用于分析的动物数量为5只（高剂量组前5只存活的动物）。 | 备注: CP=环磷酰胺一水合物（阳性对照）* 7只动物包含了允许存在受试物诱导致死的可能。用于分析的动物数量为5只（高剂量组前5只存活的动物）。 | 备注: CP=环磷酰胺一水合物（阳性对照）* 7只动物包含了允许存在受试物诱导致死的可能。用于分析的动物数量为5只（高剂量组前5只存活的动物）。 | 备注: CP=环磷酰胺一水合物（阳性对照）* 7只动物包含了允许存在受试物诱导致死的可能。用于分析的动物数量为5只（高剂量组前5只存活的动物）。 |'''
            )
            == '''100、200、400 mg/kg'''
        )


# # 集成测试 第一批文档
# @pytest.mark.asyncio(loop_scope='session')
# async def test_get_base_field_batch_1():
#     result = await chunk_process_document(
#         'uploads/1单次给药毒性_SD大鼠经口灌胃给予新药001_31003-231609.docx',
#         TrialMainType.SINGLE_DOSE.value,
#         TrialSubType.SINGLE_DOSE.value,
#     )
#     assert result.trial_main_type == TrialMainType.SINGLE_DOSE.value
#     assert result.trial_subtype == TrialSubType.SINGLE_DOSE.value
#     assert result.trial_title == 'Sprague Dawley大鼠经口灌胃给予新药001的最大耐受量试验'
#     assert result.trial_institution == '康龙化成（北京）生物技术有限公司'
#     assert result.trial_number == '31003-231609'
#     assert result.species == 'Sprague-Dawley大鼠[Crl:CD(SD)IGS](SPF)'
#     assert result.solvent_and_dosage_form == '含10%乙醇和40% PEG 400的去离子水溶液'
#     assert result.glp_compliance == False
#     assert result.administration_method == '经口灌胃'
#     assert result.administration_unit == 'mg/kg'
#     assert result.dosing_regimen == '-'
#     assert result.administration_dosage == '400、600、800、1000 mg/kg'

#     result = await chunk_process_document(
#         'uploads/3重复给药毒性非关键试验_SD大鼠经口灌胃给予新药001的14天_31053-23238.docx',
#         TrialMainType.REPEATED_DOSE.value,
#         TrialSubType.REPEATED_DOSE_SPECIFICITY.value,
#     )
#     assert result.trial_main_type == TrialMainType.REPEATED_DOSE.value
#     assert result.trial_subtype == TrialSubType.REPEATED_DOSE.value
#     assert result.trial_title == 'Sprague Dawley大鼠经口灌胃给予新药001的14天剂量范围确定的毒理学和毒代动力学试验'
#     assert result.trial_institution == '康龙化成（宁波）药物开发有限公司'
#     assert result.trial_number == '31053-23238'
#     assert result.species == 'Sprague-Dawley大鼠[Crl:CD(SD)IGS](SPF)'
#     assert result.solvent_and_dosage_form == '5%DMSO+10%Solutol的20%HP-β-CD去离子水'
#     assert result.glp_compliance == False
#     assert result.administration_method == '经口灌胃'
#     assert result.administration_unit == 'mg/kg/day'
#     assert result.dosing_regimen == '14天'
#     assert result.administration_dosage == '10、25、75、150 mg/kg/day'

#     result = await chunk_process_document(
#         'uploads/2重复给药毒性关键试验_SD大鼠经口灌胃给予新药001连续28天及恢复28天_4232-31003-231610.docx',
#         TrialMainType.REPEATED_DOSE.value,
#         TrialSubType.REPEATED_DOSE_SPECIFICITY.value,
#     )
#     assert result.trial_main_type == TrialMainType.REPEATED_DOSE.value
#     assert result.trial_subtype == TrialSubType.REPEATED_DOSE_SPECIFICITY.value
#     assert result.trial_title == 'Sprague Dawley大鼠经口灌胃给予新药001连续28天及恢复28天的毒理学及毒代动力学试验'
#     assert result.trial_institution == '康龙化成（北京）生物技术有限公司'
#     assert result.trial_number == '31003-231610'
#     assert result.species == 'Sprague-Dawley大鼠[Crl:CD(SD)IGS](SPF/VAF)'
#     assert result.solvent_and_dosage_form == '含5%DMSO+10%Solutol HS15的20% HP-β-CD去离子水溶液'
#     assert result.glp_compliance == True
#     assert result.administration_method == '经口灌胃'
#     assert result.administration_unit == 'mg/kg/day'
#     assert result.dosing_regimen == '28天'
#     assert (
#         result.administration_dosage
#         == '''雄性：5、15、75 mg/kg/day
# 雌性：2.5、7.5、50 mg/kg/day'''
#     )

#     result = await chunk_process_document(
#         'uploads/2重复给药毒性关键试验_比格犬经口灌胃重复给予新药001连续28天及恢复28天_4232-31003-232003.docx',
#         TrialMainType.REPEATED_DOSE.value,
#         TrialSubType.REPEATED_DOSE_SPECIFICITY.value,
#     )
#     assert result.trial_main_type == TrialMainType.REPEATED_DOSE.value
#     assert result.trial_subtype == TrialSubType.REPEATED_DOSE_SPECIFICITY.value
#     assert result.trial_title == '比格犬经口灌胃重复给予新药001连续28天及恢复28天的毒理学及毒代动力学试验'
#     assert result.trial_institution == '康龙化成（北京）生物技术有限公司'
#     assert result.trial_number == '31003-232003'
#     assert result.species == '比格犬'
#     assert result.solvent_and_dosage_form == '含5%DMSO+10%Solutol HS15 的20% HP-β-CD 去离子水溶液'
#     assert result.glp_compliance == True
#     assert result.administration_method == '经口灌胃'
#     assert result.administration_unit == 'mg/kg/day'
#     assert result.dosing_regimen == '28天'
#     assert result.administration_dosage == '''1.5、5、15 mg/kg/day'''

#     result = await chunk_process_document(
#         'uploads/4回复突变试验_鼠伤寒沙门氏菌和大肠埃希杆菌_31006-231925.docx',
#         TrialMainType.GENOTOXICITY.value,
#         TrialSubType.AMES_TEST.value,
#     )
#     assert result.trial_main_type == TrialMainType.GENOTOXICITY.value
#     assert result.trial_subtype == TrialSubType.AMES_TEST.value
#     assert result.trial_title == '新药001：鼠伤寒沙门氏菌和大肠埃希杆菌回复突变试验'
#     assert result.trial_institution == '康龙化成（北京）生物技术有限公司'
#     assert result.trial_number == '31006-231925'
#     assert (
#         result.species
#         == '鼠伤寒沙门氏菌 TA98，鼠伤寒沙门氏菌 TA100，鼠伤寒沙门氏菌 TA1535，鼠伤寒沙门氏菌 TA1537，大肠埃希杆菌 WP2 uvrA'
#     )
#     assert result.solvent_and_dosage_form == '二甲基亚砜（DMSO）'
#     assert result.glp_compliance == True
#     assert result.administration_method == '体外'
#     assert result.administration_unit == 'µg/皿'
#     assert result.dosing_regimen == '-'
#     assert result.administration_dosage == '200、500、1000、2000、5000 µg/皿'

#     result = await chunk_process_document(
#         'uploads/5染色体图表试验_中国仓鼠卵巢细胞体外染色体畸变_42331-31006-231924.docx',
#         TrialMainType.GENOTOXICITY.value,
#         TrialSubType.CHROMOSOME_ABERRATION_TEST.value,
#     )
#     assert result.trial_main_type == TrialMainType.GENOTOXICITY.value
#     assert result.trial_subtype == TrialSubType.CHROMOSOME_ABERRATION_TEST.value
#     assert result.trial_title == '新药001：中国仓鼠卵巢细胞体外染色体畸变试验'
#     assert result.trial_institution == '康龙化成（北京）生物技术有限公司'
#     assert result.trial_number == '31006-231924'
#     assert result.species == '中国仓鼠卵巢细胞'
#     assert result.solvent_and_dosage_form == '二甲基亚砜（DMSO）'
#     assert result.glp_compliance == True
#     assert result.administration_method == '体外'
#     assert result.administration_unit == 'μg/mL'
#     assert result.dosing_regimen == '+S9, 3h | -S9, 3h | -S9, 20h'
#     assert (
#         result.administration_dosage
#         == '''+S9, 3 h：40、120、200、240 μg/mL
# -S9, 3 h：40、120、200、240 μg/mL
# -S9, 20 h：40、100、170、200 μg/mL'''
#     )

#     result = await chunk_process_document(
#         'uploads/6微核试验_SD大鼠经口灌胃给予新药001连续2天_31006-231926.docx',
#         TrialMainType.GENOTOXICITY.value,
#         TrialSubType.MICRONUCLEUS_TEST.value,
#     )
#     assert result.trial_main_type == TrialMainType.GENOTOXICITY.value
#     assert result.trial_subtype == TrialSubType.MICRONUCLEUS_TEST.value
#     assert result.trial_title == 'Sprague Dawley大鼠经口灌胃给予新药001连续2天的微核试验'
#     assert result.trial_institution == '康龙化成（北京）生物技术有限公司'
#     assert result.trial_number == '31006-231926'
#     assert result.species == 'Sprague-Dawley (SD) 大鼠 [Crl:CD (SD)]'
#     assert result.solvent_and_dosage_form == '含5%DMSO+10%Solutol HS15的20% HP-β-CD去离子水溶液'
#     assert result.glp_compliance == True
#     assert result.administration_method == '体内'
#     assert result.administration_unit == 'mg/kg'
#     assert result.dosing_regimen == '间隔24h给予2次'
#     assert result.administration_dosage == '100、200、400 mg/kg'


# # 集成测试 - 2nd - 前6个字段
# @pytest.mark.asyncio(loop_scope='session')
# async def test_get_base_field_batch_2():
#     result = await chunk_process_document(
#         'uploads/1单次给药毒性_SD大鼠单次经口灌胃给予_33416-230611.docx',
#         TrialMainType.SINGLE_DOSE.value,
#         TrialSubType.SINGLE_DOSE.value,
#     )
#     assert result.trial_main_type == TrialMainType.SINGLE_DOSE.value
#     assert result.trial_subtype == TrialSubType.SINGLE_DOSE.value
#     assert result.trial_title == 'Sprague Dawley大鼠单次经口灌胃给予XXX的急性毒理学试验'
#     assert result.trial_institution == '康龙化成（北京）生物技术有限公司'
#     assert result.trial_number == '33416-230611'
#     assert result.species == 'Sprague-Dawley大鼠[Crl:CD(SD)IGS](SPF/VAF)'

#     result = await chunk_process_document(
#         'uploads/1单次给药毒性_比格犬单次经口灌胃给予XXX_33416-230612.docx',
#         TrialMainType.SINGLE_DOSE.value,
#         TrialSubType.SINGLE_DOSE.value,
#     )
#     assert result.trial_main_type == TrialMainType.SINGLE_DOSE.value
#     assert result.trial_subtype == TrialSubType.SINGLE_DOSE.value
#     assert result.trial_title == '比格犬单次经口灌胃给予XXX的急性毒理学试验'
#     assert result.trial_institution == '康龙化成（北京）生物技术有限公司'
#     assert result.trial_number == '33416-230612'
#     assert result.species == '比格犬'

#     result = await chunk_process_document(
#         'uploads/3重复给药毒性非关键试验_比格犬灌胃给予XXX的28天_3D3211.docx',
#         TrialMainType.REPEATED_DOSE.value,
#         TrialSubType.REPEATED_DOSE_SPECIFICITY.value,
#     )
#     assert result.trial_main_type == TrialMainType.REPEATED_DOSE.value
#     assert result.trial_subtype == TrialSubType.REPEATED_DOSE.value
#     assert result.trial_title == '比格犬灌胃给予XXX的28天剂量范围确定的毒理学和毒代动力学试验'
#     assert result.trial_institution == '康龙化成（宁波）药物开发有限公司'
#     assert result.trial_number == '3D3211'
#     assert result.species == '比格犬'

#     result = await chunk_process_document(
#         'uploads/3重复给药毒性非关键试验_SD大鼠灌胃给予XXX的28天_3R3248.docx',
#         TrialMainType.REPEATED_DOSE.value,
#         TrialSubType.REPEATED_DOSE_SPECIFICITY.value,
#     )
#     assert result.trial_main_type == TrialMainType.REPEATED_DOSE.value
#     assert result.trial_subtype == TrialSubType.REPEATED_DOSE.value
#     assert result.trial_title == 'Sprague-Dawley大鼠灌胃给予XXX的28天剂量范围确定的毒理学和毒代动力学试验'
#     assert result.trial_institution == '康龙化成（宁波）药物开发有限公司'
#     assert result.trial_number == '3R3248'
#     assert result.species == 'Sprague-Dawley大鼠 [Crl: CD (SD)] (SPF/VAF)'
