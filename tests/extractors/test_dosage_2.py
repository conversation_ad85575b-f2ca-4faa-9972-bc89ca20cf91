import pytest

from app.extractors.dosage_2 import ExperimentType

@pytest.mark.asyncio(loop_scope='session')
class TestExperimentType:
    async def test_extract(self):
        assert (
            await ExperimentType.extract(
                '''SD大鼠注射给予新药001的28天剂量范围待定的毒理学和毒代动力学试验 '''
            ) 
            == '28天剂量范围待定'
        )

        # 重复给药-非关键试验：4232-31053-23238.docx
        assert (
            await ExperimentType.extract(
                "Sprague Dawley大鼠经口灌胃给予新药001的14天剂量范围确定的毒理学和毒代动力学试验"
            )
            == '14天剂量范围确定'
        )

        # 重复给药-非关键试验：3. 3D3211 Dog DRF.docx
        assert (
            await ExperimentType.extract(
                "比格犬灌胃给予XXX的28天剂量范围确定的毒理学和毒代动力学试验"
            )
            == '28天剂量范围确定'
        )
        
        # 重复给药-非关键试验：3.  3R3248 Rat DRF.docx
        assert (
            await ExperimentType.extract(
                "Sprague-Dawley大鼠灌胃给予XXX的28天剂量范围确定的毒理学和毒代动力学试验"
            )
            == '28天剂量范围确定'
        ) 

        # 重复给药-关键试验：4232-31003-231610.docx
        assert (
            await ExperimentType.extract(
                "Sprague Dawley大鼠经口灌胃给予新药001连续28天及恢复28天的毒理学及毒代动力学试验"
            )
            == '连续28天及恢复28天'
        )
    
        # 重复给药-关键试验：4232-31003-232003.docx
        assert (
            await ExperimentType.extract(
                "比格犬经口灌胃重复给予新药001连续28天及恢复28天的毒理学及毒代动力学试验"
            )
            == '连续28天及恢复28天'
        ) 
