import pytest

from app.extractors.table_7_3_abnormal_weight import AbnormalWeightTable


class TestAbnormalWeightTable:
    @pytest.mark.asyncio(loop_scope='session')
    async def test_extract(self):
        # 重复给药关键: 4232-************, project_id: 172 , doc_id: 902
        # C-231610，7.4体重和体重变化，与下列表格一致
        assert (
            await AbnormalWeightTable.extract(
                '''| 性别 | 雄性 | 雄性 | 雄性 | 雄性 | 雌性 | 雌性 | 雌性 | 雌性 |
| 剂量（mg/kg/day） | 0 | 5 | 15 | 75 | 0 | 2.5 | 7.5 | 50 |
| Days 1-8 (g) | 53.14 | 55.69 | 50.97 | 49.73 | 16.41 | 21.77 | 14.29 | 13.35 |
| （%差值） | - | +4.80 | -4.08 | -6.41 | - | +32.67 | -12.92 | -18.65 |
| Days 8-15 (g) | 50.45 | 47.67 | 46.87 | 49.35 | 17.85 | 15.28 | 22.68 | 20.60 |
| （%差值） | - | -5.5 | -7.2 | -2.2 | - | -14.4 | +27.06 | +15.41 |''',
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |
| Days 1-8 (g) | 53.14 | 16.41 | 55.69 | 21.77 | 50.97 | 14.29 | 49.73 | 13.35 |
| （%差值） | - | - | +4.80 | +32.67 | -4.08 | -12.92 | -6.41 | -18.65 |
| Days 8-15 (g) | 50.45 | 17.85 | 47.67 | 15.28 | 46.87 | 22.68 | 49.35 | 20.60 |
| （%差值） | - | - | -5.5 | -14.4 | -7.2 | +27.06 | -2.2 | +15.41 |'''
        )

        # 重复给药关键: 4232-************, project_id: 172 , doc_id: 903
        # C-232003，7.4体重和体重变化，与下列表格一致
        assert (
            await AbnormalWeightTable.extract(
                '''| 给药剂量<br>（mg/kg/day） | 体重(Kg, Day1) | 体重(Kg, Day1) | 体重(Kg, Day28) | 体重(Kg, Day28) | 体重变化(Kg, Days 1-28) | 体重变化(Kg, Days 1-28) | 体重变化百分比(%, Days 1-28) | 体重变化百分比(%, Days 1-28) |
|  | M | F | M | F | M | F | M | F |
| 0 | 8.650 | 7.512 | 9.228 | 8.132 | 0.578 | 0.620 | 6.98 | 8.13 |
| 1.5 | 8.754 | 7.494 | 9.426 | 7.636 | 0.672 | 0.142 | 7.99 | 1.71 |
| 5 | 8.606 | 7.734 | 9.586 | 8.140 | 0.980 | 0.406 | 11.42 | 5.38 |
| 15 | 8.680 | 7.796 | 8.818 | 8.170 | 0.138 | 0.374 | 1.78 | 4.56 |
| 注释：15 mg/kg/day 剂量组仅1只雄性动物（#23819）Days 1-28体重降低，该动物体重降低幅度约11.4%。 | 注释：15 mg/kg/day 剂量组仅1只雄性动物（#23819）Days 1-28体重降低，该动物体重降低幅度约11.4%。 | 注释：15 mg/kg/day 剂量组仅1只雄性动物（#23819）Days 1-28体重降低，该动物体重降低幅度约11.4%。 | 注释：15 mg/kg/day 剂量组仅1只雄性动物（#23819）Days 1-28体重降低，该动物体重降低幅度约11.4%。 | 注释：15 mg/kg/day 剂量组仅1只雄性动物（#23819）Days 1-28体重降低，该动物体重 降低幅度约11.4%。 | 注释：15 mg/kg/day 剂量组仅1只雄性动物（#23819）Days 1-28体重降低，该动物体重降低幅度约11.4%。 | 注释：15 mg/kg/day 剂量组仅1只雄性动物 （#23819）Days 1-28体重降低，该动物体重降低幅度约11.4%。 | 注释：15 mg/kg/day 剂量组仅1只雄性动物（#23819）Days 1-28体重降低，该动物体重降低幅度约11.4%。 | 注释：15 mg/kg/day 剂量组仅1只雄性动物（#23819）Days 1-28体重降低，该动物体重降低幅度约11.4%。 |''',
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 1.5 | 1.5 | 5 | 5 | 15 | 15 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 1.5 | 1.5 | 5 | 5 | 15 | 15 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |
| 体重(Kg, Day28) | 9.228 | 8.132 | 9.426 | 7.636 | 9.586 | 8.140 | 8.818 | 8.170 |
| 体重变化百分比(%, Days 1-28) | 6.98 | 8.13 | 7.99 | 1.71 | 11.42 | 5.38 | 1.78 | 4.56 |'''
        )

        # C-230614，7.4体重和体重变化
        assert (
                await AbnormalWeightTable.extract(
                    '''| 给药剂量<br>（mg/kg/day） | 体重(Kg, Day1) | 体重(Kg, Day1) | 体重(Kg, Day28) | 体重(Kg, Day28) | 体重变化(Kg, Days 1-28) | 体重变化(Kg, Days 1-28) | 体重变化百分比(%, Days 1-28) | 体重变化百分比(%, Days 1-28) |
|  | M | F | M | F | M | F | M | F |
| 0 | 7.630 | 6.498 | 8.734 | 7.270 | 1.104 | 0.772 | 14.57 | 11.78 |
| 1 | 7.648 | 6.674 | 8.726 | 7.076 | 1.078 | 0.402 | 14.59 | 6.61 |
| 2 | 7.536 | 6.530 | 8.272 | 6.908 | 0.736 | 0.378 | 10.01 | 4.76 |
| 4 | 8.104 | 6.530 | 8.468 | 6.542 | 0.364* | 0.012 | 4.98* | -0.38 |
| 注释：15 mg/kg/day 剂量组仅1只雄性动物（#23819）Days 1-28体重降低，该动物体重降低幅度约11.4%。 | 注释：15 mg/kg/day 剂量组仅1只雄性动物（#23819）Days 1-28体重降低，该动物体重降低幅度约11.4%。 | 注释：15 mg/kg/day 剂量组仅1只雄性动物（#23819）Days 1-28体重降低，该动物体重降低幅度约11.4%。 | 注释：15 mg/kg/day 剂量组仅1只雄性动物（#23819）Days 1-28体重降低，该动物体重降低幅度约11.4%。 | 注释：15 mg/kg/day 剂量组仅1只雄性动物（#23819）Days 1-28体重降低，该动物体重 降低幅度约11.4%。 | 注释：15 mg/kg/day 剂量组仅1只雄性动物（#23819）Days 1-28体重降低，该动物体重降低幅度约11.4%。 | 注释：15 mg/kg/day 剂量组仅1只雄性动物 （#23819）Days 1-28体重降低，该动物体重降低幅度约11.4%。 | 注释：15 mg/kg/day 剂量组仅1只雄性动物（#23819）Days 1-28体重降低，该动物体重降低幅度约11.4%。 | 注释：15 mg/kg/day 剂量组仅1只雄性动物（#23819）Days 1-28体重降低，该动物体重降低幅度约11.4%。 |''',
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 1 | 1 | 2 | 2 | 4 | 4 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',)
                == '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 1 | 1 | 2 | 2 | 4 | 4 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |
| 体重(Kg, Day28) | 8.734 | 7.270 | 8.726 | 7.076 | 8.272 | 6.908 | 8.468 | 6.542 |
| 体重变化百分比(%, Days 1-28) | 14.57 | 11.78 | 14.59 | 6.61 | 10.01 | 4.76 | 4.98* | -0.38 |''')

        # C-230613，7.4体重和体重变化
        assert (
                await AbnormalWeightTable.extract(
                    '''| 性别 | 雄性 | 雄性 | 雄性 | 雄性 | 雌性 | 雌性 | 雌性 | 雌性 | 
| 剂量（mg/kg/day） | 0 | 2 | 3 | 5 | 0 | 2 | 3 | 5 | 
| Days1-8(g) | 60 | 50.28** | 47.87*** | 45.43*** | 13.17 | 19.91 | 17.5 | 11.47 |
| （%差值） | - | -16.2 | -20.22 | -24.29 | - | +51.11 | +32.84 | -12.9 |
| 注释：15 mg/kg/day 剂量组仅1只雄性动物（#23819）Days 1-28体重降低，该动物体重降低幅度约11.4%。 | 注释：15 mg/kg/day 剂量组仅1只雄性动物（#23819）Days 1-28体重降低，该动物体重降低幅度约11.4%。 | 注释：15 mg/kg/day 剂量组仅1只雄性动物（#23819）Days 1-28体重降低，该动物体重降低幅度约11.4%。 | 注释：15 mg/kg/day 剂量组仅1只雄性动物（#23819）Days 1-28体重降低，该动物体重降低幅度约11.4%。 | 注释：15 mg/kg/day 剂量组仅1只雄性动物（#23819）Days 1-28体重降低，该动物体重 降低幅度约11.4%。 | 注释：15 mg/kg/day 剂量组仅1只雄性动物（#23819）Days 1-28体重降低，该动物体重降低幅度约11.4%。 | 注释：15 mg/kg/day 剂量组仅1只雄性动物 （#23819）Days 1-28体重降低，该动物体重降低幅度约11.4%。 | 注释：15 mg/kg/day 剂量组仅1只雄性动物（#23819）Days 1-28体重降低，该动物体重降低幅度约11.4%。 | 注释：15 mg/kg/day 剂量组仅1只雄性动物（#23819）Days 1-28体重降低，该动物体重降低幅度约11.4%。 |''',
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 2 | 2 | 3 | 3 | 5 | 5 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',)
                == '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 2 | 2 | 3 | 3 | 5 | 5 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |
| Days1-8(g) | 60 | 13.17 | 50.28** | 19.91 | 47.87*** | 17.5 | 45.43*** | 11.47 |
| （%差值） | - | - | -16.2 | +51.11 | -20.22 | +32.84 | -24.29 | -12.9 |''')

    def test_parse_table(self):
        # 重复给药关键: 4232-************, project_id: 172 , doc_id: 902
        header1 = [
            ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
            ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
        ]
        assert (
            AbnormalWeightTable.parse_table(
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |
| Days 1-8 (g) | 53.14 | 16.41 | 55.69 | 21.77 | 50.97 | 14.29 | 49.73 | 13.35 |
| （%变化百分比） | - | - | +4.80 | +32.67 | -4.08 | -12.92 | -6.41 | -18.65 |
| Days 8-15 (g) | 50.45 | 17.85 | 47.67 | 15.28 | 46.87 | 22.68 | 49.35 | 20.60 |
| （%变化百分比） | - | - | -5.5 | -14.4 | -7.2 | +27.06 | -2.2 | +15.41 |''',
                header1,
            )
            == [
                ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                [
                    '体重和体重变化',
                    '体重和体重变化',
                    '体重和体重变化',
                    '体重和体重变化',
                    '体重和体重变化',
                    '体重和体重变化',
                    '体重和体重变化',
                    '体重和体重变化',
                    '体重和体重变化',
                ],
                [
                    'Days 1-8 (g)\n（%体重变化百分比）',
                    '53.14',
                    '16.41',
                    '55.69\n4.80%↑',
                    '21.77\n32.67%↑',
                    '50.97\n4.08%↓',
                    '14.29\n12.92%↓',
                    '49.73\n6.41%↓',
                    '13.35\n18.65%↓',
                ],
                [
                    'Days 8-15 (g)\n（%体重变化百分比）',
                    '50.45',
                    '17.85',
                    '47.67\n5.5%↓',
                    '15.28\n14.4%↓',
                    '46.87\n7.2%↓',
                    '22.68\n27.06%↑',
                    '49.35\n2.2%↓',
                    '20.60\n15.41%↑',
                ],
            ]
        )

        # 重复给药关键: 4232-************, project_id: 172 , doc_id: 903

        header2 = [
            ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'],
            ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
        ]
        assert (
            AbnormalWeightTable.parse_table(
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 1.5 | 1.5 | 5 | 5 | 15 | 15 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |
| 体重(Kg, Day28) | 9.228 | 8.132 | 9.426 | 7.636 | 9.586 | 8.140 | 8.818 | 8.170 |
| 体重变化百分比(%, Days 1-28) | 6.98 | 8.13 | 7.99 | 1.71 | 11.42 | 5.38 | 1.78 | 4.56 |''',
                header2,
            )
            == [
                ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'],
                ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                [
                    '体重和体重变化',
                    '体重和体重变化',
                    '体重和体重变化',
                    '体重和体重变化',
                    '体重和体重变化',
                    '体重和体重变化',
                    '体重和体重变化',
                    '体重和体重变化',
                    '体重和体重变化',
                ],
                [
                    'Day28\n（%体重变化百分比）',
                    '9.228',
                    '8.132',
                    '9.426\n7.99%',
                    '7.636\n1.71%',
                    '9.586\n11.42%',
                    '8.140\n5.38%',
                    '8.818\n1.78%',
                    '8.170\n4.56%',
                ],
            ]
        )
