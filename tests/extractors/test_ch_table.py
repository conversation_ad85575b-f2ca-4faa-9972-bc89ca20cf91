import pytest

from app.extractors.ch_table import CHTable


class TestCHTable:
    @pytest.mark.asyncio(loop_scope='session')
    async def test_extract(self):
        # 遗传毒性-染色体畸变: 42331-31006-231924 project_id: 155  doc_id 754 这是我修改原始文档表格，再去切数据的
        assert (
            await CHTable.extract(
                '''| 给药        (µg/mL) | 细胞毒性   (%) | 计数细胞总数 | 含有多倍体细胞 (%) | 含有核内复制细胞  (%) | % 裂隙 | %畸变染色体 | %畸变细胞 |
| S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 |
| DMSO (1%) | 0 | 300 | 0.0  | 0.0  | 0.0 | 0.7  | 0.7  |
|  |  |  |  |  |  |  |  |
| 新药001(µg/mL) | 新药001(µg/mL) | 新药001(µg/mL) | 新药001(µg/mL) | 新药001(µg/mL) | 新药001(µg/mL) | 新药001(µg/mL) | 新药001(µg/mL) |
| 40 | 3 | 300 | 0.0  | 0.0  | 0.0 | 0.3 | 0.3 |
| 120 | 11 | 300 | 0.0  | 0.0  | 0.3  | 0.0  | 0.0  |
| 200 | 31 | 300 | 0.0  | 0.0  | 0.0 | 0.0  | 0.0  |
| 240 | 47 | 300 | 0.0  | 1.0  | 0.7 | 1.7  | 1.7  |
|  |  |  |  |  |  |  |  |
| CP 5 | ND | 300 | 0.0  | 0.7  | 0.3  | 13.7  | 13.0** |
| 非代谢活化给药3小时系列 | 非代谢活化给药3小时系列 | 非代谢活化给药3小时系列 | 非代谢活化给药3小时系列 | 非代谢活化给药3小时系列 | 非代谢活化给药3小时系列 | 非代谢活化给药3小时系列 | 非代谢活化给药3小时系列 |
| DMSO (1%) | 0 | 300 | 0.0  | 0.0  | 0.3  | 0.3  | 0.3  |
|  |  |  |  |  |  |  |  |
| 新药001(µg/mL) | 新药001(µg/mL) | 新药001(µg/mL) | 新药001(µg/mL) | 新药001(µg/mL) | 新药001(µg/mL) | 新药001(µg/mL) | 新药001(µg/mL) |
| 40 | 8 | 300 | 0.0  | 0.0  | 0.3  | 0.0  | 0.0  |
| 120 | 5 | 300 | 0.0  | 0.0  | 0.0  | 0.7  | 0.7  |
| 200 | 29 | 300 | 0.0  | 0.0  | 0.7  | 0.3  | 0.3  |
| 240 | 54 | 300 | 0.3  | 1.0  | 0.0  | 1.7  | 1.3  |
|  |  |  |  |  |  |  |  |
| MMC 0.5 | ND | 300 | 0.0  | 0.3  | 0.3  | 18.0  | 16.7** |
| 非代谢活化给药20小时系列 | 非代谢活化给药20小时系列 | 非代谢活化给药20小时系列 | 非代谢活化给药20小时系列 | 非代谢活化给药20小时系列 | 非代谢活化给药20小 时系列 | 非代谢活化给药20小时系列 | 非代谢活化给药20小时系列 |
| DMSO (1%) | 0 | 300 | 0.0  | 0.0  | 0.3  | 0.3  | 0.3  |
|  |  |  |  |  |  |  |  |
| 新药001(µg/mL) | 新药001(µg/mL) | 新药001(µg/mL) | 新药001(µg/mL) | 新药001(µg/mL) | 新药001(µg/mL) | 新药001(µg/mL) | 新药001(µg/mL) |
| 40 | 2 | 300 | 0.0  | 0.0  | 0.0  | 0.0  | 0.0  |
| 100 | 17 | 300 | 0.0  | 0.0  | 0.0  | 0.3  | 0.3  |
| 170 | 27 | 300 | 0.3  | 0.0  | 0.3  | 0.3  | 0.3  |
| 200 | 51 | 300 | 0.0  | 0.0  | 0.3  | 0.3  | 0.3  |
|  |  |  |  |  |  |  |  |
| MMC<br>0.15 | ND | 300 | 0.0  | 0.0  | 0.3  | 18.3  | 18.0** |''',
                ''' ["% 不含裂隙畸变细胞：**, p≤0.01, 使用Fisher's Exact检验", "CP=环磷酰胺一水合物; MMC=丝裂霉素C", "ND: 阳性对照组的细胞毒性未测定。"]''',
                '新药001',
            )
            == '''| 受试物 | 给药（μg/mL） | 细胞毒性（%） | 计数细胞总数 | 含有多倍体细胞（%） | 含有核内复制细胞（%） | 裂隙（%） | 畸变染色体（%） | 畸变细胞（%） |
| S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 |
| DMSO (1%) | DMSO (1%) | 0 | 300 | 0.0 | 0.0 | 0.0 | 0.7 | 0.7 |
| 新药001 | 40 | 3 | 300 | 0.0 | 0.0 | 0.0 | 0.3 | 0.3 |
| 新药001 | 120 | 11 | 300 | 0.0 | 0.0 | 0.3 | 0.0 | 0.0 |
| 新药001 | 200 | 31 | 300 | 0.0 | 0.0 | 0.0 | 0.0 | 0.0 |
| 新药001 | 240 | 47 | 300 | 0.0 | 1.0 | 0.7 | 1.7 | 1.7 |
| CP 5 | CP 5 | ND | 300 | 0.0 | 0.7 | 0.3 | 13.7 | 13.0** |
| 非代谢活化给药3小时系列 | 非代谢活化给药3小时系列 | 非代谢活化给药3小时系列 | 非代谢活化给药3小时系列 | 非代谢活化给药3小时系列 | 非代谢活化给药3小时系列 | 非代谢活化给药3小时系列 | 非代谢活化给药3小时系列 | 非代谢活化给药3小时系列 |
| DMSO (1%) | DMSO (1%) | 0 | 300 | 0.0 | 0.0 | 0.3 | 0.3 | 0.3 |
| 新药001 | 40 | 8 | 300 | 0.0 | 0.0 | 0.3 | 0.0 | 0.0 |
| 新药001 | 120 | 5 | 300 | 0.0 | 0.0 | 0.0 | 0.7 | 0.7 |
| 新药001 | 200 | 29 | 300 | 0.0 | 0.0 | 0.7 | 0.3 | 0.3 |
| 新药001 | 240 | 54 | 300 | 0.3 | 1.0 | 0.0 | 1.7 | 1.3 |
| MMC 0.5 | MMC 0.5 | ND | 300 | 0.0 | 0.3 | 0.3 | 18.0 | 16.7** |
| 非代谢活化给药20小时系列 | 非代谢活化给药20小时系列 | 非代谢活化给药20小时系列 | 非代谢活化给药20小时系列 | 非代谢活化给药20小时系列 | 非代谢活化给药20小时系列 | 非代谢活化给药20小时系列 | 非代谢活化给药20小时系列 | 非代谢活化给药20小时系列 |
| DMSO (1%) | DMSO (1%) | 0 | 300 | 0.0 | 0.0 | 0.3 | 0.3 | 0.3 |
| 新药001 | 40 | 2 | 300 | 0.0 | 0.0 | 0.0 | 0.0 | 0.0 |
| 新药001 | 100 | 17 | 300 | 0.0 | 0.0 | 0.0 | 0.3 | 0.3 |
| 新药001 | 170 | 27 | 300 | 0.3 | 0.0 | 0.3 | 0.3 | 0.3 |
| 新药001 | 200 | 51 | 300 | 0.0 | 0.0 | 0.3 | 0.3 | 0.3 |
| MMC 0.15 | MMC 0.15 | ND | 300 | 0.0 | 0.0 | 0.3 | 18.3 | 18.0** |'''
        )

        # GPT生成
        assert (
            await CHTable.extract(
                '''| 给药        (µg/mL) | 细胞毒性   (%) | 计数细胞总数 | 含有多倍体细胞 (%) | 含有核内复制细胞  (%) | % 裂隙 | %畸变染色体 | %畸变细胞 |
| 代谢活化给药5年系列 | 代谢活化给药5年系列 | 代谢活化给药5年系列 | 代谢活化给药5年系列 | 代谢活化给药5年系列 | 代谢活化给药5年系列 | 代谢活化给药5年系列 | 代谢活化给药5年系列 |
| 含5% DMSO和10% Solutol的去离子水溶液 | 0 | 300 | 0.0  | 0.0  | 0.0 | 0.7  | 0.7  |
|  |  |  |  |  |  |  |  |
| cancer新药005@!=(g/L) | cancer新药005@!=(g/L) | cancer新药005@!=(g/L) | cancer新药005@!=(g/L) | cancer新药005@!=(g/L) | cancer新药005@!=(g/L) | cancer新药005@!=(g/L) | cancer新药005@!=(g/L) |
| 10 | 3 | 300 | 0.0  | 0.0  | 0.0 | 0.3 | 0.3 |
| 50 | 11 | 300 | 0.0  | 0.0  | 0.3  | 0.0  | 0.0  |
|  |  |  |  |  |  |  |  |
| CP 5 | NPAA | 300 | 0.0  | 0.7  | 0.3  | 13.7  | 13.0** |''',
                '''["% 不含裂隙畸变细胞：**, p≤0.01, 使用Fisher's Exact检验","CP=环磷酰胺一水合物; MMC=丝裂霉素C","NPAA: 阳性对照组的毒素没有测。"]''',
                'cancer新药005@!=',
            )
            == '''| 受试物 | 给药（μg/mL） | 细胞毒性（%） | 计数细胞总数 | 含有多倍体细胞（%） | 含有核内复制细胞（%） | 裂隙（%） | 畸变染色体（%） | 畸变细胞（%） |
| 代谢活化给药5年系列 | 代谢活化给药5年系列 | 代谢活化给药5年系列 | 代谢活化给药5年系列 | 代谢活化给药5年系列 | 代谢活化给药5年系列 | 代谢活化给药5年系列 | 代谢活化给药5年系列 | 代谢活化给药5年系列 |
| 含5% DMSO和10% Solutol的去离子水溶液 | 含5% DMSO和10% Solutol的去离子水溶液 | 0 | 300 | 0.0 | 0.0 | 0.0 | 0.7 | 0.7 |
| cancer新药005@!= | 10 | 3 | 300 | 0.0 | 0.0 | 0.0 | 0.3 | 0.3 |
| cancer新药005@!= | 50 | 11 | 300 | 0.0 | 0.0 | 0.3 | 0.0 | 0.0 |
| CP 5 | CP 5 | NPAA | 300 | 0.0 | 0.7 | 0.3 | 13.7 | 13.0** |'''
        )

    def test_parse_small_table(self):
        # 遗传毒性-染色体畸变: 42331-31006-231924 project_id: 155  doc_id 754 这是我修改原始文档表格，再去切数据的
        assert (
            CHTable.parse_small_table(
                '''| 受试物 | 给药（μg/mL） | 细胞毒性（%） | 计数细胞总数 | 含有多倍体细胞（%） | 含有核内复制细胞（%） | 裂隙（%） | 畸变染色体（%） | 畸变细胞（%） |
| S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 | S9代谢活化给药3小时系列 |
| DMSO (1%) | DMSO (1%) | 0 | 300 | 0.0 | 0.0 | 0.0 | 0.7 | 0.7 |
| 新药001| 40 | 3 | 300 | 0.0 | 0.0 | 0.0 | 0.3 | 0.3 |
| 新药001| 120 | 11 | 300 | 0.0 | 0.0 | 0.3 | 0.0 | 0.0 |
| 新药001| 200 | 31 | 300 | 0.0 | 0.0 | 0.0 | 0.0 | 0.0 |
| 新药001| 240 | 47 | 300 | 0.0 | 1.0 | 0.7 | 1.7 | 1.7 |
| CP 5 | CP 5 | ND | 300 | 0.0 | 0.7 | 0.3 | 13.7 | 13.0** |
| 非代谢活化给药3小时系列 | 非代谢活化给药3小时系列 | 非代谢活化给药3小时系列 | 非代谢活化给药3小时系列 | 非代谢活化给药3小时系列 | 非代谢活化给药3小时系列 | 非代谢活化给药3小时系列 | 非代谢活化给药3小时系列 | 非代谢活化给药3小时系列 |
| DMSO (1%) | DMSO (1%) | 0 | 300 | 0.0 | 0.0 | 0.3 | 0.3 | 0.3 |
| 新药001| 40 | 8 | 300 | 0.0 | 0.0 | 0.3 | 0.0 | 0.0 |
| 新药001| 120 | 5 | 300 | 0.0 | 0.0 | 0.0 | 0.7 | 0.7 |
| 新药001| 200 | 29 | 300 | 0.0 | 0.0 | 0.7 | 0.3 | 0.3 |
| 新药001| 240 | 54 | 300 | 0.3 | 1.0 | 0.0 | 1.7 | 1.3 |
| MMC 0.5 | MMC 0.5 | ND | 300 | 0.0 | 0.3 | 0.3 | 18.0 | 16.7** |
| 非代谢活化给药20小时系列 | 非代谢活化给药20小时系列 | 非代谢活化给药20小时系列 | 非代谢活化给药20小时系列 | 非代谢活化给药20小时系列 | 非代谢活化给药20小时系列 | 非代谢活化给药20小时系列 | 非代谢活化给药20小时系列 | 非代谢活化给药20小时系列 |
| DMSO (1%) | DMSO (1%) | 0 | 300 | 0.0 | 0.0 | 0.3 | 0.3 | 0.3 |
| 新药001| 40 | 2 | 300 | 0.0 | 0.0 | 0.0 | 0.0 | 0.0 |
| 新药001| 100 | 17 | 300 | 0.0 | 0.0 | 0.0 | 0.3 | 0.3 |
| 新药001| 170 | 27 | 300 | 0.3 | 0.0 | 0.3 | 0.3 | 0.3 |
| 新药001| 200 | 51 | 300 | 0.0 | 0.0 | 0.3 | 0.3 | 0.3 |
| MMC 0.15 | MMC 0.15 | ND | 300 | 0.0 | 0.0 | 0.3 | 18.3 | 18.0** |''',
            )
            == [
                (
                    '受试物',
                    '给药（μg/mL）',
                    '细胞毒性（%）',
                    '计数细胞总数',
                    '含有多倍体细胞（%）',
                    '含有核内复制细胞（%）',
                    '裂隙（%）',
                    '畸变染色体（%）',
                    '畸变细胞（%）',
                ),
                (
                    'S9代谢活化给药3小时系列',
                    'S9代谢活化给药3小时系列',
                    'S9代谢活化给药3小时系列',
                    'S9代谢活化给药3小时系列',
                    'S9代谢活化给药3小时系列',
                    'S9代谢活化给药3小时系列',
                    'S9代谢活化给药3小时系列',
                    'S9代谢活化给药3小时系列',
                    'S9代谢活化给药3小时系列',
                ),
                ('DMSO (1%)', 'DMSO (1%)', '0', '300', '0.0', '0.0', '0.0', '0.7', '0.7'),
                ('新药001', '40', '3', '300', '0.0', '0.0', '0.0', '0.3', '0.3'),
                ('新药001', '120', '11', '300', '0.0', '0.0', '0.3', '0.0', '0.0'),
                ('新药001', '200', '31', '300', '0.0', '0.0', '0.0', '0.0', '0.0'),
                ('新药001', '240', '47', '300', '0.0', '1.0', '0.7', '1.7', '1.7'),
                ('CP 5', 'CP 5', 'ND', '300', '0.0', '0.7', '0.3', '13.7', '13.0**'),
                (
                    '非代谢活化给药3小时系列',
                    '非代谢活化给药3小时系列',
                    '非代谢活化给药3小时系列',
                    '非代谢活化给药3小时系列',
                    '非代谢活化给药3小时系列',
                    '非代谢活化给药3小时系列',
                    '非代谢活化给药3小时系列',
                    '非代谢活化给药3小时系列',
                    '非代谢活化给药3小时系列',
                ),
                ('DMSO (1%)', 'DMSO (1%)', '0', '300', '0.0', '0.0', '0.3', '0.3', '0.3'),
                ('新药001', '40', '8', '300', '0.0', '0.0', '0.3', '0.0', '0.0'),
                ('新药001', '120', '5', '300', '0.0', '0.0', '0.0', '0.7', '0.7'),
                ('新药001', '200', '29', '300', '0.0', '0.0', '0.7', '0.3', '0.3'),
                ('新药001', '240', '54', '300', '0.3', '1.0', '0.0', '1.7', '1.3'),
                ('MMC 0.5', 'MMC 0.5', 'ND', '300', '0.0', '0.3', '0.3', '18.0', '16.7**'),
                (
                    '非代谢活化给药20小时系列',
                    '非代谢活化给药20小时系列',
                    '非代谢活化给药20小时系列',
                    '非代谢活化给药20小时系列',
                    '非代谢活化给药20小时系列',
                    '非代谢活化给药20小时系列',
                    '非代谢活化给药20小时系列',
                    '非代谢活化给药20小时系列',
                    '非代谢活化给药20小时系列',
                ),
                ('DMSO (1%)', 'DMSO (1%)', '0', '300', '0.0', '0.0', '0.3', '0.3', '0.3'),
                ('新药001', '40', '2', '300', '0.0', '0.0', '0.0', '0.0', '0.0'),
                ('新药001', '100', '17', '300', '0.0', '0.0', '0.0', '0.3', '0.3'),
                ('新药001', '170', '27', '300', '0.3', '0.0', '0.3', '0.3', '0.3'),
                ('新药001', '200', '51', '300', '0.0', '0.0', '0.3', '0.3', '0.3'),
                ('MMC 0.15', 'MMC 0.15', 'ND', '300', '0.0', '0.0', '0.3', '18.3', '18.0**'),
            ]
        )

        assert (
            CHTable.parse_small_table(
                '''| 受试物 | 给药（μg/mL） | 细胞毒性（%） | 计数细胞总数 | 含有多倍体细胞（%） | 含有核内复制细胞（%） | 裂隙（%） | 畸变染色体（%） | 畸变细胞（%） |
| 代谢活化给药5年系列 | 代谢活化给药5年系列 | 代谢活化给药5年系列 | 代谢活化给药5年系列 | 代谢活化给药5年系列 | 代谢活化给药5年系列 | 代谢活化给药5年系列 | 代谢活化给药5年系列 | 代谢活化给药5年系列 |
| 含5% DMSO和10% Solutol的去离子水溶液 | 含5% DMSO和10% Solutol的去离子水溶液 | 0 | 300 | 0.0 | 0.0 | 0.0 | 0.7 | 0.7 |
| cancer新药005 | 10 | 3 | 300 | 0.0 | 0.0 | 0.0 | 0.3 | 0.3 |
| cancer新药005 | 50 | 11 | 300 | 0.0 | 0.0 | 0.3 | 0.0 | 0.0 |
| CP 5 | CP 5 | NPAA | 300 | 0.0 | 0.7 | 0.3 | 13.7 | 13.0** |''',
            )
            == [
                (
                    '受试物',
                    '给药（μg/mL）',
                    '细胞毒性（%）',
                    '计数细胞总数',
                    '含有多倍体细胞（%）',
                    '含有核内复制细胞（%）',
                    '裂隙（%）',
                    '畸变染色体（%）',
                    '畸变细胞（%）',
                ),
                (
                    '代谢活化给药5年系列',
                    '代谢活化给药5年系列',
                    '代谢活化给药5年系列',
                    '代谢活化给药5年系列',
                    '代谢活化给药5年系列',
                    '代谢活化给药5年系列',
                    '代谢活化给药5年系列',
                    '代谢活化给药5年系列',
                    '代谢活化给药5年系列',
                ),
                (
                    '含5% DMSO和10% Solutol的去离子水溶液',
                    '含5% DMSO和10% Solutol的去离子水溶液',
                    '0',
                    '300',
                    '0.0',
                    '0.0',
                    '0.0',
                    '0.7',
                    '0.7',
                ),
                ('cancer新药005', '10', '3', '300', '0.0', '0.0', '0.0', '0.3', '0.3'),
                ('cancer新药005', '50', '11', '300', '0.0', '0.0', '0.3', '0.0', '0.0'),
                ('CP 5', 'CP 5', 'NPAA', '300', '0.0', '0.7', '0.3', '13.7', '13.0**'),
            ]
        )

    def test_merge_tables(self):
        small_table_1 = [
            (
                '受试物',
                '给药（μg/mL）',
                '细胞毒性（%）',
                '计数细胞总数',
                '含有多倍体细胞（%）',
                '含有核内复制细胞（%）',
                '裂隙（%）',
                '畸变染色体（%）',
                '畸变细胞（%）',
            ),
            (
                'S9代谢活化给药3小时系列',
                'S9代谢活化给药3小时系列',
                'S9代谢活化给药3小时系列',
                'S9代谢活化给药3小时系列',
                'S9代谢活化给药3小时系列',
                'S9代谢活化给药3小时系列',
                'S9代谢活化给药3小时系列',
                'S9代谢活化给药3小时系列',
                'S9代谢活化给药3小时系列',
            ),
            ('DMSO (1%)', 'DMSO (1%)', '0', '300', '0.0', '0.0', '0.0', '0.7', '0.7'),
            ('新药001', '40', '3', '300', '0.0', '0.0', '0.0', '0.3', '0.3'),
            ('新药001', '120', '11', '300', '0.0', '0.0', '0.3', '0.0', '0.0'),
            ('新药001', '200', '31', '300', '0.0', '0.0', '0.0', '0.0', '0.0'),
            ('新药001', '240', '47', '300', '0.0', '1.0', '0.7', '1.7', '1.7'),
            ('CP 5', 'CP 5', 'ND', '300', '0.0', '0.7', '0.3', '13.7', '13.0**'),
            (
                '非代谢活化给药3小时系列',
                '非代谢活化给药3小时系列',
                '非代谢活化给药3小时系列',
                '非代谢活化给药3小时系列',
                '非代谢活化给药3小时系列',
                '非代谢活化给药3小时系列',
                '非代谢活化给药3小时系列',
                '非代谢活化给药3小时系列',
                '非代谢活化给药3小时系列',
            ),
            ('DMSO (1%)', 'DMSO (1%)', '0', '300', '0.0', '0.0', '0.3', '0.3', '0.3'),
            ('新药001', '40', '8', '300', '0.0', '0.0', '0.3', '0.0', '0.0'),
            ('新药001', '120', '5', '300', '0.0', '0.0', '0.0', '0.7', '0.7'),
            ('新药001', '200', '29', '300', '0.0', '0.0', '0.7', '0.3', '0.3'),
            ('新药001', '240', '54', '300', '0.3', '1.0', '0.0', '1.7', '1.3'),
            ('MMC 0.5', 'MMC 0.5', 'ND', '300', '0.0', '0.3', '0.3', '18.0', '16.7**'),
            (
                '非代谢活化给药20小时系列',
                '非代谢活化给药20小时系列',
                '非代谢活化给药20小时系列',
                '非代谢活化给药20小时系列',
                '非代谢活化给药20小时系列',
                '非代谢活化给药20小时系列',
                '非代谢活化给药20小时系列',
                '非代谢活化给药20小时系列',
                '非代谢活化给药20小时系列',
            ),
            ('DMSO (1%)', 'DMSO (1%)', '0', '300', '0.0', '0.0', '0.3', '0.3', '0.3'),
            ('新药001', '40', '2', '300', '0.0', '0.0', '0.0', '0.0', '0.0'),
            ('新药001', '100', '17', '300', '0.0', '0.0', '0.0', '0.3', '0.3'),
            ('新药001', '170', '27', '300', '0.3', '0.0', '0.3', '0.3', '0.3'),
            ('新药001', '200', '51', '300', '0.0', '0.0', '0.3', '0.3', '0.3'),
            ('MMC 0.15', 'MMC 0.15', 'ND', '300', '0.0', '0.0', '0.3', '18.3', '18.0**'),
        ]
        small_table_2 = [
            (
                '受试物',
                '给药（μg/mL）',
                '细胞毒性（%）',
                '计数细胞总数',
                '含有多倍体细胞（%）',
                '含有核内复制细胞（%）',
                '裂隙（%）',
                '畸变染色体（%）',
                '畸变细胞（%）',
            ),
            (
                '代谢活化给药5年系列',
                '代谢活化给药5年系列',
                '代谢活化给药5年系列',
                '代谢活化给药5年系列',
                '代谢活化给药5年系列',
                '代谢活化给药5年系列',
                '代谢活化给药5年系列',
                '代谢活化给药5年系列',
                '代谢活化给药5年系列',
            ),
            (
                '含5% DMSO和10% Solutol的去离子水溶液',
                '含5% DMSO和10% Solutol的去离子水溶液',
                '0',
                '300',
                '0.0',
                '0.0',
                '0.0',
                '0.7',
                '0.7',
            ),
            ('cancer新药005', '10', '3', '300', '0.0', '0.0', '0.0', '0.3', '0.3'),
            ('cancer新药005', '50', '11', '300', '0.0', '0.0', '0.3', '0.0', '0.0'),
            ('CP 5', 'CP 5', 'NPAA', '300', '0.0', '0.7', '0.3', '13.7', '13.0**'),
        ]

        small_table_list = [small_table_1, small_table_2]
        assert CHTable.merge_tables(small_table_list) == [
            (
                '给药（μg/mL）',
                '给药（μg/mL）',
                '细胞毒性（%）',
                '计数细胞总数',
                '含有多倍体细胞（%）',
                '含有核内复制细胞（%）',
                '裂隙（%）',
                '畸变染色体（%）',
                '畸变细胞（%）',
            ),
            (
                'S9代谢活化给药3小时系列',
                'S9代谢活化给药3小时系列',
                'S9代谢活化给药3小时系列',
                'S9代谢活化给药3小时系列',
                'S9代谢活化给药3小时系列',
                'S9代谢活化给药3小时系列',
                'S9代谢活化给药3小时系列',
                'S9代谢活化给药3小时系列',
                'S9代谢活化给药3小时系列',
            ),
            ('DMSO (1%)', 'DMSO (1%)', '0', '300', '0.0', '0.0', '0.0', '0.7', '0.7'),
            ('新药001', '40', '3', '300', '0.0', '0.0', '0.0', '0.3', '0.3'),
            ('新药001', '120', '11', '300', '0.0', '0.0', '0.3', '0.0', '0.0'),
            ('新药001', '200', '31', '300', '0.0', '0.0', '0.0', '0.0', '0.0'),
            ('新药001', '240', '47', '300', '0.0', '1.0', '0.7', '1.7', '1.7'),
            ('CP 5', 'CP 5', 'ND', '300', '0.0', '0.7', '0.3', '13.7', '13.0**'),
            (
                '非代谢活化给药3小时系列',
                '非代谢活化给药3小时系列',
                '非代谢活化给药3小时系列',
                '非代谢活化给药3小时系列',
                '非代谢活化给药3小时系列',
                '非代谢活化给药3小时系列',
                '非代谢活化给药3小时系列',
                '非代谢活化给药3小时系列',
                '非代谢活化给药3小时系列',
            ),
            ('DMSO (1%)', 'DMSO (1%)', '0', '300', '0.0', '0.0', '0.3', '0.3', '0.3'),
            ('新药001', '40', '8', '300', '0.0', '0.0', '0.3', '0.0', '0.0'),
            ('新药001', '120', '5', '300', '0.0', '0.0', '0.0', '0.7', '0.7'),
            ('新药001', '200', '29', '300', '0.0', '0.0', '0.7', '0.3', '0.3'),
            ('新药001', '240', '54', '300', '0.3', '1.0', '0.0', '1.7', '1.3'),
            ('MMC 0.5', 'MMC 0.5', 'ND', '300', '0.0', '0.3', '0.3', '18.0', '16.7**'),
            (
                '非代谢活化给药20小时系列',
                '非代谢活化给药20小时系列',
                '非代谢活化给药20小时系列',
                '非代谢活化给药20小时系列',
                '非代谢活化给药20小时系列',
                '非代谢活化给药20小时系列',
                '非代谢活化给药20小时系列',
                '非代谢活化给药20小时系列',
                '非代谢活化给药20小时系列',
            ),
            ('DMSO (1%)', 'DMSO (1%)', '0', '300', '0.0', '0.0', '0.3', '0.3', '0.3'),
            ('新药001', '40', '2', '300', '0.0', '0.0', '0.0', '0.0', '0.0'),
            ('新药001', '100', '17', '300', '0.0', '0.0', '0.0', '0.3', '0.3'),
            ('新药001', '170', '27', '300', '0.3', '0.0', '0.3', '0.3', '0.3'),
            ('新药001', '200', '51', '300', '0.0', '0.0', '0.3', '0.3', '0.3'),
            ('MMC 0.15', 'MMC 0.15', 'ND', '300', '0.0', '0.0', '0.3', '18.3', '18.0**'),
            (
                '代谢活化给药5年系列',
                '代谢活化给药5年系列',
                '代谢活化给药5年系列',
                '代谢活化给药5年系列',
                '代谢活化给药5年系列',
                '代谢活化给药5年系列',
                '代谢活化给药5年系列',
                '代谢活化给药5年系列',
                '代谢活化给药5年系列',
            ),
            (
                '含5% DMSO和10% Solutol的去离子水溶液',
                '含5% DMSO和10% Solutol的去离子水溶液',
                '0',
                '300',
                '0.0',
                '0.0',
                '0.0',
                '0.7',
                '0.7',
            ),
            ('cancer新药005', '10', '3', '300', '0.0', '0.0', '0.0', '0.3', '0.3'),
            ('cancer新药005', '50', '11', '300', '0.0', '0.0', '0.3', '0.0', '0.0'),
            ('CP 5', 'CP 5', 'NPAA', '300', '0.0', '0.7', '0.3', '13.7', '13.0**'),
        ]
