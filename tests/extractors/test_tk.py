import pytest

from app.extractors.tk import TKTable


class TestTKTable:
    @pytest.mark.asyncio(loop_scope='session')
    async def test_extract(self):
        # AB-231646
        assert (
            await TKTable.extract(
                '''| 给药天数 | 组别 | 给药剂量(mg/kg/day) | 性别 | 动物号 | Tmax | Cmax | AUClast | AUC0-24 |
| 给药天数 | 组别 | 给药剂量(mg/kg/day) | 性别 | 动物号 | (hr) | (ng/ml) | (hr*ng/ml) | (hr*ng/ml) |
| 1 | 2 | 5 | 雄 | 8055966 | 1 | 6760 | 65107 | 49020 |
| 1 | 2 | 5 | 雌 | 8046436 | 0.5 | 3670 | 17292 | 20595 |
| 1 | 3 | 15 | 雄 | 8041728 | 1 | 16700 | 336811 | 174880 |
| 1 | 3 | 15 | 雌 | 8040412 | 2 | 23000 | 188949 | 175883 |
| 1 | 4 | 50 | 雄 | 8051898 | 1 | 31600 | 459393 | 273721 |
| 1 | 4 | 50 | 雌 | 8052118 | 4 | 48200 | 1468814 | 694796 |
| 1 | 5 | 100 | 雄 | 8052194 | 4 | 51200 | 1263210 | 845366 |
| 1 | 5 | 100 | 雌 | 8056211 | 4 | 67600 | 2489036 | 1209278 |
| 14 | 2 | 5 | 雄 | 8055966 | 1 | 8280 | 59296 | 59296 |
| 14 | 2 | 5 | 雌 | 8046436 | 1 | 5030 | 24826 | 33977 |
| 14 | 3 | 15 | 雄 | 8041728 | 1 | 31000 | 302480 | 302480 |
| 14 | 3 | 15 | 雌 | 8040412 | 1 | 21400 | 99587 | 126371 |
| 14 | 4 | 50 | 雄 | 8051898 | 4 | 32300 | 331361 | 331361 |
| 14 | 4 | 50 | 雌 | 8052118 | 0.5 | 38000 | 498004 | 413230 |
| 14 | 5 | 100 | 雄 | 8052194 | 4 | 40700 | 404698 | 404698 |
| 注释：Winnonlin数据是使用 ‘linear log Trapezoidal’ 模型进行计算, 权重为1/(Y*Y). T1/2 是选用 "best fit"计算获得。 | 注释：Winnonlin数据是使用 ‘linear log Trapezoidal’ 模型进行计算, 权重为1/(Y*Y). T1/2 是选用 "best fit"计算获得。 | 注释：Winnonlin数据是使用 ‘linear log Trapezoidal’ 模型进行计算, 权重为1/(Y*Y). T1/2 是选用 "best fit"计算获得。 | 注释：Winnonlin数据是使用 ‘linear log Trapezoidal’ 模型进行计算, 权重为1/(Y*Y). T1/2 是选用 "best fit"计算获得。 | 注释：Winnonlin数据是使用 ‘linear log Trapezoidal’ 模型进行计算, 权重为1/(Y*Y). T1/2 是选用 "best fit"计算获得。 | 注释：Winnonlin数据是使用 ‘linear log Trapezoidal’ 模型进行计算, 权重为1/(Y*Y). T1/2 是选用 "best fit"计算获得。 | 注释：Winnonlin数据是使用 ‘linear log Trapezoidal’ 模型进行计算, 权重为1/(Y*Y). T1/2 是选用 "best fit"计算获得。 | 注释：Winnonlin数据是使用 ‘linear log Trapezoidal’ 模型进行计算, 权重为1/(Y*Y). T1/2 是选用 "best fit"计算获得。 | 注释：Winnonlin数据是使用 ‘linear log Trapezoidal’ 模型进行计算, 权重为1/(Y*Y). T1/2 是选用 "best fit"计算获得。 |
| 注释：给药方式为经口灌胃，在WinNonlin里使用 ‘extravascular’ 计算TK参数。 | 注释：给药方式为经口灌胃，在WinNonlin里使用 ‘extravascular’ 计算TK参数。 | 注释：给药方式为经口灌胃，在WinNonlin里使用 ‘extravascular’ 计算TK参数。 | 注释：给药方式为经口灌胃，在WinNonlin里使用 ‘extravascular’ 计算TK参数。 | 注释：给药方式为经口灌胃，在WinNonlin里使用 ‘extravascular’ 计算TK参数。 | 注释：给药方式为经口灌胃，在WinNonlin里使用 ‘extravascular’ 计算TK参数。 | 注释：给药方式为经口灌胃，在WinNonlin里使用 ‘extravascular’ 计算TK参数。 | 注释：给药方式为经口灌胃，在WinNonlin里使用 ‘extravascular’ 计算TK参数。 | 注释：给药方式为经口灌胃，在WinNonlin里使用 ‘extravascular’ 计算TK参数。 |
| *：当Rsq_adjusted<0.85，T1/2不可信。 | *：当Rsq_adjusted<0.85，T1/2不可信。 | *：当Rsq_adjusted<0.85，T1/2不可信。 | *：当Rsq_adjusted<0.85，T1/2不可信。 | *：当Rsq_adjusted<0.85，T1/2不可信。 | *：当Rsq_adjusted<0.85，T1/2不可信。 | *：当Rsq_adjusted<0.85，T1/2不可信。 | *：当Rsq_adjusted<0.85，T1/2不可信。 | *：当Rsq_adjusted<0.85，T1/2不可信。 |
| 下划线：Rsq_adjusted<0.85。 | 下划线：Rsq_adjusted<0.85。 |  |  |  |  |  |  |  |
| NC：Winnonlin无法计算该参数。 | NC：Winnonlin无法计算该参数。 | NC：Winnonlin无法计算该参数。 |  |  |  |  |  |  |'''
            )
            == '''| 性别（雄/雌） | 性别（雄/雌） | 雄 | 雌 |
| 时间 | 剂量(mg/kg) | AUClast (hr*ng/ml) | AUClast (hr*ng/ml) |
| 1 | 5 | 65107 | 17292 |
| 1 | 15 | 336811 | 188949 |
| 1 | 50 | 459393 | 1468814 |
| 1 | 100 | 1263210 | 2489036 |
| 14 | 5 | 59296 | 24826 |
| 14 | 15 | 302480 | 99587 |
| 14 | 50 | 331361 | 498004 |
| 14 | 100 | 404698 | - |'''
        )

        # B-3D3211
        assert (
            await TKTable.extract(
                '''| 给药<br>天数 | 组别 | 性别 | 给药剂量(mg/kg/day) | Cmax<br>(ng/mL) | AUClast (hr*ng/mL) |
| 给药<br>天数 | 组别 | 性别 | 给药剂量(mg/kg/day) | Cmax<br>(ng/mL) | AUClast (hr*ng/mL) |
| 第1天 | 第2组 | 雄性 | 0.3 | 7.97 | 28.1 |
| 第1天 | 第2组 | 雌性 | 0.3 | 32.2 | 56.2 |
| 第1天 | 第3组 | 雄性 | 1.0 | 47.0 | 91.3 |
| 第1天 | 第3组 | 雌性 | 1.0 | 58.6 | 141 |
| 第1天 | 第4组 | 雄性 | 3.0 | 152 | 257 |
| 第1天 | 第4组 | 雌性 | 3.0 | 162 | 226 |
| 第28天 | 第2组 | 雄性 | 0.3 | 6.58 | 21.9 |
| 第28天 | 第2组 | 雌性 | 0.3 | 6.38 | 23.4 |
| 第28天 | 第3组 | 雄性 | 1.0 | 25.4 | 140 |
| 第28天 | 第3组 | 雌性 | 1.0 | 34.8 | 196 |
| 第28天 | 第4组 | 雄性 | 3.0 | 66.1 | 465 |
| 第28天 | 第4组 | 雌性 | 3.0 | 40.6 | 149 |'''
            )
            == '''| 性别（雄/雌） | 性别（雄/雌） | 雄 | 雌 |
| 时间 | 剂量(mg/kg) | AUClast (hr*ng/ml) | AUClast (hr*ng/ml) |
| 1 | 0.3 | 28.1 | 56.2 |
| 1 | 1.0 | 91.3 | 141 |
| 1 | 3.0 | 257 | 226 |
| 28 | 0.3 | 21.9 | 23.4 |
| 28 | 1.0 | 140 | 196 |
| 28 | 3.0 | 465 | 149 |'''
        )

        # B-3R3248
        assert (
            await TKTable.extract(
                '''| 给药<br>天数 | 组别 | 性别 | 给药剂量(mg/kg/day) | Cmax<br>(ng/mL) | AUClast (hr*ng/mL) |
| 给药<br>天数 | 组别 | 性别 | 给药剂量(mg/kg/day) | Cmax<br>(ng/mL) | AUClast (hr*ng/mL) |
| 第1天 | 第2组 | 雄性 | 3 | 141 | 975 |
| 第1天 | 第2组 | 雌性 | 3 | 295 | 2482 |
| 第1天 | 第3组 | 雄性 | 6 | 300 | 1929 |
| 第1天 | 第3组 | 雌性 | 6 | 433 | 3837 |
| 第1天 | 第4组 | 雄性 | 10 | 383 | 1921 |
| 第1天 | 第4组 | 雌性 | 10 | 601 | 5198 |
| 第28天 | 第2组 | 雄性 | 3 | 166 | 1443 |
| 第28天 | 第2组 | 雌性 | 3 | 244 | 3680 |
| 第28天 | 第3组 | 雄性 | 6 | 261 | 2220 |
| 第28天 | 第3组 | 雌性 | 6 | 365 | 4637 |
| 第28天 | 第4组 | 雄性 | 10 | 265 | 1920 |
| 第28天 | 第4组 | 雌性 | 10 | 686 | 7980 |'''
            )
            == '''| 性别（雄/雌） | 性别（雄/雌） | 雄 | 雌 |
| 时间 | 剂量(mg/kg) | AUClast (hr*ng/ml) | AUClast (hr*ng/ml) |
| 1 | 3 | 975 | 2482 |
| 1 | 6 | 1929 | 3837 |
| 1 | 10 | 1921 | 5198 |
| 28 | 3 | 1443 | 3680 |
| 28 | 6 | 2220 | 4637 |
| 28 | 10 | 1920 | 7980 |'''
        )

        # B-23238
        assert (
            await TKTable.extract(
                '''| 天数 | 组别 | 给药剂量 (mg/kg/day) | 性别 | Cmax<br>(ng/mL) | AUClast<br>(hr*ng/mL) |
| 1 | 2 | 10 | 雄性 | 55500 | 1051074 |
| 1 | 2 | 10 | 雌性 | 68867 | 1248594 |
| 1 | 3 | 25 | 雄性 | 108167 | 2020304 |
| 1 | 3 | 25 | 雌性 | 131333 | 2838366 |
| 1 | 4 | 75 | 雄性 | 131333 | 2681692 |
| 1 | 4 | 75 | 雌性 | 152667 | 3417484 |
| 1 | 5 | 150 | 雄性 | 145333 | 3203287 |
| 1 | 5 | 150 | 雌性 | 177333 | 3990086 |
| 14 | 2 | 10 | 雄性 | 86667 | 1999763 |
| 14 | 2 | 10 | 雌性 | 136667 | 5888665 |
| 14 | 3 | 25 | 雄性 | 118333 | 2321910 |
| 14 | 3 | 25 | 雌性 | 152333 | 6223126 |
| 14 | 4 | 75 | 雄性 | 139333 | 2820237 |
| 14 | 4 | 75 | 雌性 | 139667 | 4889237 |
| 14 | 5 | 150 | 雄性 | 158000 | 2542402 |
| 14 | 5 | 150 | 雌性 | 157667 | 4970898 |'''
            )
            == '''| 性别（雄/雌） | 性别（雄/雌） | 雄 | 雌 |
| 时间 | 剂量(mg/kg) | AUClast (hr*ng/ml) | AUClast (hr*ng/ml) |
| 1 | 10 | 1051074 | 1248594 |
| 1 | 25 | 2020304 | 2838366 |
| 1 | 75 | 2681692 | 3417484 |
| 1 | 150 | 3203287 | 3990086 |
| 14 | 10 | 1999763 | 5888665 |
| 14 | 25 | 2321910 | 6223126 |
| 14 | 75 | 2820237 | 4889237 |
| 14 | 150 | 2542402 | 4970898 |'''
        )

        # C-230613
        assert (
            await TKTable.extract(
                '''| 给药天数 | 受试物 | 组别 | 给药剂量<br>(mg/kg/day) | 性别 | Cmax<br>(ng/mL) | AUClast<br>(hr*ng/mL) |
| 1 | NS-041 | 2 | 2 | 雄 | 141 | 1047 |
| 1 | NS-041 | 2 | 2 | 雌 | 168 | 2060 |
| 1 | NS-041 | 3 | 3 | 雄 | 162 | 1197 |
| 1 | NS-041 | 3 | 3 | 雌 | 311 | 3962 |
| 1 | NS-041 | 4 | 5 | 雄 | 225 | 1587 |
| 1 | NS-041 | 4 | 5 | 雌 | 226 | 2674 |
| 28 | NS-041 | 2 | 2 | 雄 | 76.1 | 583 |
| 28 | NS-041 | 2 | 2 | 雌 | 262 | 2945 |
| 28 | NS-041 | 3 | 3 | 雄 | 137 | 952 |
| 28 | NS-041 | 3 | 3 | 雌 | 371 | 4374 |
| 28 | NS-041 | 4 | 5 | 雄 | 143 | 1220 |
| 28 | NS-041 | 4 | 5 | 雌 | 293 | 3344 |'''
            )
            == '''| 性别（雄/雌） | 性别（雄/雌） | 雄 | 雌 |
| 时间 | 剂量(mg/kg) | AUClast (hr*ng/ml) | AUClast (hr*ng/ml) |
| 1 | 2 | 1047 | 2060 |
| 1 | 3 | 1197 | 3962 |
| 1 | 5 | 1587 | 2674 |
| 28 | 2 | 583 | 2945 |
| 28 | 3 | 952 | 4374 |
| 28 | 5 | 1220 | 3344 |'''
        )

        # C-232003
        assert (
            await TKTable.extract(
                '''| 给药天数 | 组别 | 给药剂量(mg/kg/day) | 性别 | Cmax | AUClast |
| 给药天数 | 组别 | 给药剂量(mg/kg/day) | 性别 | (ng/mL) | (hr*ng/mL) |
|  1 | 2 | 1.5 | 雄 | 1578 | 7539 |
|  1 | 2 | 1.5 | 雌 | 2118 | 11670 |
|  1 | 3 | 5 | 雄 | 6476 | 38835 |
|  1 | 3 | 5 | 雌 | 7252 | 37662 |
|  1 | 4 | 15 | 雄 | 22480 | 190826 |
|  1 | 4 | 15 | 雌 | 18660 | 163700 |
|  28 | 2 | 1.5 | 雄 | 1618 | 6854 |
|  28 | 2 | 1.5 | 雌 | 2038 | 9386 |
|  28 | 3 | 5 | 雄 | 5112 | 34451 |
|  28 | 3 | 5 | 雌 | 6264 | 32532 |
|  28 | 4 | 15 | 雄 | 21500 | 216157 |
|  28 | 4 | 15 | 雌 | 22040 | 169215 |'''
            )
            == '''| 性别（雄/雌） | 性别（雄/雌） | 雄 | 雌 |
| 时间 | 剂量(mg/kg) | AUClast (hr*ng/ml) | AUClast (hr*ng/ml) |
| 1 | 1.5 | 7539 | 11670 |
| 1 | 5 | 38835 | 37662 |
| 1 | 15 | 190826 | 163700 |
| 28 | 1.5 | 6854 | 9386 |
| 28 | 5 | 34451 | 32532 |
| 28 | 15 | 216157 | 169215 |'''
        )

        # C-230614
        assert (
            await TKTable.extract(
                '''| 给药天数 | 组别 | 给药剂量<br>(mg/kg/day) | 性别 | Cmax<br>(ng/mL) | AUClast<br>(hr*ng/mL) |
| 1 | 2 | 1 | 雄 | 66.2 | 116 |
| 1 | 2 | 1 | 雌 | 47.5 | 71.6 |
| 1 | 3 | 2 | 雄 | 137 | 261 |
| 1 | 3 | 2 | 雌 | 67.8 | 135 |
| 1 | 4 | 4 | 雄 | 213 | 477 |
| 1 | 4 | 4 | 雌 | 158 | 289 |
| 28 | 2 | 1 | 雄 | 51.4 | 166 |
| 28 | 2 | 1 | 雌 | 31.7 | 107 |
| 28 | 3 | 2 | 雄 | 81.3 | 299 |
| 28 | 3 | 2 | 雌 | 65.3 | 276 |
| 28 | 4 | 4 | 雄 | 81.7 | 455 |
| 28 | 4 | 4 | 雌 | 45.3 | 450 |'''
            )
            == '''| 性别（雄/雌） | 性别（雄/雌） | 雄 | 雌 |
| 时间 | 剂量(mg/kg) | AUClast (hr*ng/ml) | AUClast (hr*ng/ml) |
| 1 | 1 | 116 | 71.6 |
| 1 | 2 | 261 | 135 |
| 1 | 4 | 477 | 289 |
| 28 | 1 | 166 | 107 |
| 28 | 2 | 299 | 276 |
| 28 | 4 | 455 | 450 |'''
        )

        # C-231610
        assert (
            await TKTable.extract(
                '''| 给药天数 | 组别 | 给药剂量(mg/kg/day) | 性别 | Cmax | AUClast |
| 给药天数 | 组别 | 给药剂量(mg/kg/day) | 性别 | (ng/ml) | (hr*ng/ml) |
| 1 | 2 | 5 | 雄性 | 28625 | 517794 |
| 1 | 2 | 2.5 | 雌性 | 19400 | 416645 |
| 1 | 3 | 15 | 雄性 | 127000 | 1492946 |
| 1 | 3 | 7.5 | 雌性 | 112925 | 1379860 |
| 1 | 4 | 75 | 雄性 | 146750 | 2692723 |
| 1 | 4 | 50 | 雌性 | 189250 | 3926221 |
| 28 | 2 | 5 | 雄性 | 44850 | 1291721 |
| 28 | 2 | 2.5 | 雌性 | 78500 | 4027396 |
| 28 | 3 | 15 | 雄性 | 116075 | 3264410 |
| 28 | 3 | 7.5 | 雌性 | 155500 | 6625633 |
| 28 | 4 | 75 | 雄性 | 154750 | 2884819 |
| 28 | 4 | 50 | 雌性 | 179000 | 6920757 |'''
            )
            == '''| 性别（雄/雌） | 性别（雄/雌） | 雄 | 雌 |
| 时间 | 剂量(mg/kg) | AUClast (hr*ng/ml) | AUClast (hr*ng/ml) |
| 1 | 2.5 | - | 416645 |
| 1 | 5 | 517794 | - |
| 1 | 7.5 | - | 1379860 |
| 1 | 15 | 1492946 | - |
| 1 | 50 | - | 3926221 |
| 1 | 75 | 2692723 | - |
| 28 | 2.5 | - | 4027396 |
| 28 | 5 | 1291721 | - |
| 28 | 7.5 | - | 6625633 |
| 28 | 15 | 3264410 | - |
| 28 | 50 | - | 6920757 |
| 28 | 75 | 2884819 | - |'''
        )

    def test_parse_small_table(self):
        with pytest.raises(ValueError):
            TKTable.parse_small_table('')  # 缺少表头

        assert (
            TKTable.parse_small_table(
                '''| 性别（雄/雌） | 性别（雄/雌） | 雄 | 雌 |
| 时间 | 剂量(mg/kg) | AUClast (hr*ng/ml) | AUClast (hr*ng/ml) |'''
            )
            == (
                (
                    [
                        ('性别（雄/雌）', '性别（雄/雌）', '雄', '雌'),
                        ('时间', '剂量(mg/kg)', 'AUClast (hr*ng/ml)', 'AUClast (hr*ng/ml)'),
                    ],
                    {},
                    {},
                ),
                set(),
            )
        )

        with pytest.raises(ValueError):
            TKTable.parse_small_table(
                '''| 性别（雄/雌） | 性别（雄/雌） | 雄 | 雌 |
| 时间 | 剂量(mg/kg) | AUClast (hr*ng/ml) | AUClast (hr*ng/ml) |
| 1.1 | 2.5 | - | 416645 |'''  # 时间不是整数
            )

        with pytest.raises(ValueError):
            TKTable.parse_small_table(
                '''| 性别（雄/雌） | 性别（雄/雌） | 雄 | 雌 |
| 时间 | 剂量(mg/kg) | AUClast (hr*ng/ml) | AUClast (hr*ng/ml) |
| 1 | - | - | 416645 |'''  # 没有剂量
            )

        with pytest.raises(ValueError):
            TKTable.parse_small_table(
                '''| 性别（雄/雌） | 性别（雄/雌） | 雄 | 雌 |
| 时间 | 剂量(mg/kg) | AUClast (hr*ng/ml) | AUClast (hr*ng/ml) |
| 1 | x | - | 416645 |'''  # 剂量不是 float
            )

        assert (
            TKTable.parse_small_table(
                '''| 性别（雄/雌） | 性别（雄/雌） | 雄 | 雌 |
| 时间 | 剂量(mg/kg) | AUClast (hr*ng/ml) | AUClast (hr*ng/ml) |
| 1 | 2.5 | - | 416645 |
| 1 | 5 | 517794 | - |
| 1 | 7.5 | - | 1379860 |
| 1 | 15 | 1492946 | - |
| 1 | 50 | - | 3926221 |
| 1 | 75 | 2692723 | - |'''
            )
            == (
                (
                    (
                        [
                            ('性别（雄/雌）', '性别（雄/雌）', '雄', '雌'),
                            ('时间', '剂量(mg/kg)', 'AUClast (hr*ng/ml)', 'AUClast (hr*ng/ml)'),
                        ],
                        {
                            '2.5': ('-', '416645'),
                            '5': ('517794', '-'),
                            '7.5': ('-', '1379860'),
                            '15': ('1492946', '-'),
                            '50': ('-', '3926221'),
                            '75': ('2692723', '-'),
                        },
                        {},
                    )
                ),
                set(),
            )
        )

        # SD大鼠经口灌胃28天
        assert (
            TKTable.parse_small_table(
                '''| 性别（雄/雌） | 性别（雄/雌） | 雄 | 雌 |
| 时间 | 剂量(mg/kg) | AUClast (hr*ng/ml) | AUClast (hr*ng/ml) |
| 1 | 2.5 | - | 416645 |
| 1 | 5 | 517794 | - |
| 1 | 7.5 | - | 1379860 |
| 1 | 15 | 1492946 | - |
| 1 | 50 | - | 3926221 |
| 1 | 75 | 2692723 | - |
| 28 | 2.5 | - | 4027396 |
| 28 | 5 | 1291721 | - |
| 28 | 7.5 | - | 6625633 |
| 28 | 15 | 3264410 | - |
| 28 | 50 | - | 6920757 |
| 28 | 75 | 2884819 | - |'''
            )
            == (
                (
                    [
                        ('性别（雄/雌）', '性别（雄/雌）', '雄', '雌'),
                        ('时间', '剂量(mg/kg)', 'AUClast (hr*ng/ml)', 'AUClast (hr*ng/ml)'),
                    ],
                    {
                        '2.5': ('-', '416645'),
                        '5': ('517794', '-'),
                        '7.5': ('-', '1379860'),
                        '15': ('1492946', '-'),
                        '50': ('-', '3926221'),
                        '75': ('2692723', '-'),
                    },
                    {
                        '2.5': ('-', '4027396'),
                        '5': ('1291721', '-'),
                        '7.5': ('-', '6625633'),
                        '15': ('3264410', '-'),
                        '50': ('-', '6920757'),
                        '75': ('2884819', '-'),
                    },
                ),
                {'28'},
            )
        )

        # SD大鼠经口灌胃14天剂量范围确定
        assert (
            TKTable.parse_small_table(
                '''| 性别（雄/雌） | 性别（雄/雌） | 雄 | 雌 |
| 时间 | 剂量(mg/kg) | AUClast (hr*ng/ml) | AUClast (hr*ng/ml) |
| 1 | 10 | 1051074 | 1248594 |
| 1 | 25 | 2020304 | 2838366 |
| 1 | 75 | 2681692 | 3417484 |
| 1 | 150 | 3203287 | 3990086 |
| 14 | 10 | 1999763 | 5888665 |
| 14 | 25 | 2321910 | 6223126 |
| 14 | 75 | 2820237 | 4889237 |
| 14 | 150 | 2542402 | 4970898 |'''
            )
            == (
                (
                    [
                        ('性别（雄/雌）', '性别（雄/雌）', '雄', '雌'),
                        ('时间', '剂量(mg/kg)', 'AUClast (hr*ng/ml)', 'AUClast (hr*ng/ml)'),
                    ],
                    {
                        '10': ('1051074', '1248594'),
                        '25': ('2020304', '2838366'),
                        '75': ('2681692', '3417484'),
                        '150': ('3203287', '3990086'),
                    },
                    {
                        '10': ('1999763', '5888665'),
                        '25': ('2321910', '6223126'),
                        '75': ('2820237', '4889237'),
                        '150': ('2542402', '4970898'),
                    },
                ),
                {'14'},
            )
        )

        # 比格犬灌胃28天剂量范围确定
        assert (
            TKTable.parse_small_table(
                '''| 性别（雄/雌） | 性别（雄/雌） | 雄 | 雌 |
| 时间 | 剂量(mg/kg) | AUClast (hr*ng/ml) | AUClast (hr*ng/ml) |
| 1 | 0.3 | 28.1 | 56.2 |
| 1 | 1.0 | 91.3 | 141 |
| 1 | 3.0 | 257 | 226 |
| 28 | 0.3 | 21.9 | 23.4 |
| 28 | 1.0 | 140 | 196 |
| 28 | 3.0 | 465 | 149 |'''
            )
            == (
                (
                    [
                        ('性别（雄/雌）', '性别（雄/雌）', '雄', '雌'),
                        ('时间', '剂量(mg/kg)', 'AUClast (hr*ng/ml)', 'AUClast (hr*ng/ml)'),
                    ],
                    {'0.3': ('28.1', '56.2'), '1.0': ('91.3', '141'), '3.0': ('257', '226')},
                    {'0.3': ('21.9', '23.4'), '1.0': ('140', '196'), '3.0': ('465', '149')},
                ),
                {'28'},
            )
        )

        # 比格犬经口灌胃28天
        assert (
            TKTable.parse_small_table(
                '''| 性别（雄/雌） | 性别（雄/雌） | 雄 | 雌 |
| 时间 | 剂量(mg/kg) | AUClast (hr*ng/ml) | AUClast (hr*ng/ml) |
| 1 | 1.5 | 7539 | 11670 |
| 1 | 5 | 38835 | 37662 |
| 1 | 15 | 190826 | 163700 |
| 28 | 1.5 | 6854 | 9386 |
| 28 | 5 | 34451 | 32532 |
| 28 | 15 | 216157 | 169215 |'''
            )
            == (
                (
                    [
                        ('性别（雄/雌）', '性别（雄/雌）', '雄', '雌'),
                        ('时间', '剂量(mg/kg)', 'AUClast (hr*ng/ml)', 'AUClast (hr*ng/ml)'),
                    ],
                    {'1.5': ('7539', '11670'), '5': ('38835', '37662'), '15': ('190826', '163700')},
                    {'1.5': ('6854', '9386'), '5': ('34451', '32532'), '15': ('216157', '169215')},
                ),
                {'28'},
            )
        )

    def test_generate_medium_header(self):
        assert TKTable.generate_medium_header('SD大鼠', '经口灌胃', '14天') == [
            ('种属', '种属', 'SD大鼠', 'SD大鼠'),
            ('给药途径', '给药途径', '经口灌胃', '经口灌胃'),
            ('试验类型', '试验类型', '14天', '14天'),
        ]

    def test_merge_tables(self):
        # SD大鼠经口灌胃给予新药14天
        small_table1, days1 = TKTable.parse_small_table(
            '''| 性别（雄/雌） | 性别（雄/雌） | 雄 | 雌 |
| 时间 | 剂量(mg/kg) | AUClast (hr*ng/ml) | AUClast (hr*ng/ml) |
| 1 | 10 | 1051074 | 1248594 |
| 1 | 25 | 2020304 | 2838366 |
| 1 | 75 | 2681692 | 3417484 |
| 1 | 150 | 3203287 | 3990086 |
| 14 | 10 | 1999763 | 5888665 |
| 14 | 25 | 2321910 | 6223126 |
| 14 | 75 | 2820237 | 4889237 |
| 14 | 150 | 2542402 | 4970898 |'''
        )
        medium_header_values1 = ('SD大鼠', '经口灌胃', '14天')
        assert TKTable.merge_tables([medium_header_values1], [small_table1], days1) == [
            ['种属', '种属', 'SD大鼠', 'SD大鼠'],
            ['给药途径', '给药途径', '经口灌胃', '经口灌胃'],
            ['试验类型', '试验类型', '14天', '14天'],
            ['性别（雄/雌）', '性别（雄/雌）', '雄', '雌'],
            ['时间', '剂量(mg/kg)', 'AUClast (hr*ng/ml)', 'AUClast (hr*ng/ml)'],
            ['第1天', '10', '1051074', '1248594'],
            ['第1天', '25', '2020304', '2838366'],
            ['第1天', '75', '2681692', '3417484'],
            ['第1天', '150', '3203287', '3990086'],
            ['第14天', '10', '1999763', '5888665'],
            ['第14天', '25', '2321910', '6223126'],
            ['第14天', '75', '2820237', '4889237'],
            ['第14天', '150', '2542402', '4970898'],
        ]

        # 比格犬灌胃给予XXX28天
        small_table2, days2 = TKTable.parse_small_table(
            '''| 性别（雄/雌） | 性别（雄/雌） | 雄 | 雌 |
| 时间 | 剂量(mg/kg) | AUClast (hr*ng/ml) | AUClast (hr*ng/ml) |
| 1 | 0.3 | 28.1 | 56.2 |
| 1 | 1.0 | 91.3 | 141 |
| 1 | 3.0 | 257 | 226 |
| 28 | 0.3 | 21.9 | 23.4 |
| 28 | 1.0 | 140 | 196 |
| 28 | 3.0 | 465 | 149 |'''
        )
        medium_header_values2 = ('比格犬', '灌胃', '28天')
        assert TKTable.merge_tables(
            [medium_header_values1, medium_header_values2], [small_table1, small_table2], days1 | days2
        ) == [
            ['种属', '种属', 'SD大鼠', 'SD大鼠', '比格犬', '比格犬'],
            ['给药途径', '给药途径', '经口灌胃', '经口灌胃', '灌胃', '灌胃'],
            ['试验类型', '试验类型', '14天', '14天', '28天', '28天'],
            ['性别（雄/雌）', '性别（雄/雌）', '雄', '雌', '雄', '雌'],
            [
                '时间',
                '剂量(mg/kg)',
                'AUClast (hr*ng/ml)',
                'AUClast (hr*ng/ml)',
                'AUClast (hr*ng/ml)',
                'AUClast (hr*ng/ml)',
            ],
            ['第1天', '0.3', '-', '-', '28.1', '56.2'],
            ['第1天', '1.0', '-', '-', '91.3', '141'],
            ['第1天', '3.0', '-', '-', '257', '226'],
            ['第1天', '10', '1051074', '1248594', '-', '-'],
            ['第1天', '25', '2020304', '2838366', '-', '-'],
            ['第1天', '75', '2681692', '3417484', '-', '-'],
            ['第1天', '150', '3203287', '3990086', '-', '-'],
            ['第14/28天', '0.3', '-', '-', '21.9', '23.4'],
            ['第14/28天', '1.0', '-', '-', '140', '196'],
            ['第14/28天', '3.0', '-', '-', '465', '149'],
            ['第14/28天', '10', '1999763', '5888665', '-', '-'],
            ['第14/28天', '25', '2321910', '6223126', '-', '-'],
            ['第14/28天', '75', '2820237', '4889237', '-', '-'],
            ['第14/28天', '150', '2542402', '4970898', '-', '-'],
        ]

        # SD大鼠灌胃给予XXX28天
        small_table3, days3 = TKTable.parse_small_table(
            '''| 性别（雄/雌） | 性别（雄/雌） | 雄 | 雌 |
| 时间 | 剂量(mg/kg) | AUClast (hr*ng/ml) | AUClast (hr*ng/ml) |
| 1 | 2.5 | - | 416645 |
| 1 | 5 | 517794 | - |
| 1 | 7.5 | - | 1379860 |
| 1 | 15 | 1492946 | - |
| 1 | 50 | - | 3926221 |
| 1 | 75 | 2692723 | - |
| 28 | 2.5 | - | 4027396 |
| 28 | 5 | 1291721 | - |
| 28 | 7.5 | - | 6625633 |
| 28 | 15 | 3264410 | - |
| 28 | 50 | - | 6920757 |
| 28 | 75 | 2884819 | - |'''
        )
        medium_header_values3 = ('SD大鼠', '灌胃', '28天')
        assert TKTable.merge_tables(
            [medium_header_values1, medium_header_values2, medium_header_values3],
            [small_table1, small_table2, small_table3],
            days1 | days2 | days3,
        ) == [
            ['种属', '种属', 'SD大鼠', 'SD大鼠', 'SD大鼠', 'SD大鼠', '比格犬', '比格犬'],
            ['给药途径', '给药途径', '灌胃', '灌胃', '经口灌胃', '经口灌胃', '灌胃', '灌胃'],
            ['试验类型', '试验类型', '28天', '28天', '14天', '14天', '28天', '28天'],
            ['性别（雄/雌）', '性别（雄/雌）', '雄', '雌', '雄', '雌', '雄', '雌'],
            [
                '时间',
                '剂量(mg/kg)',
                'AUClast (hr*ng/ml)',
                'AUClast (hr*ng/ml)',
                'AUClast (hr*ng/ml)',
                'AUClast (hr*ng/ml)',
                'AUClast (hr*ng/ml)',
                'AUClast (hr*ng/ml)',
            ],
            ['第1天', '0.3', '-', '-', '-', '-', '28.1', '56.2'],
            ['第1天', '1.0', '-', '-', '-', '-', '91.3', '141'],
            ['第1天', '2.5', '-', '416645', '-', '-', '-', '-'],
            ['第1天', '3.0', '-', '-', '-', '-', '257', '226'],
            ['第1天', '5', '517794', '-', '-', '-', '-', '-'],
            ['第1天', '7.5', '-', '1379860', '-', '-', '-', '-'],
            ['第1天', '10', '-', '-', '1051074', '1248594', '-', '-'],
            ['第1天', '15', '1492946', '-', '-', '-', '-', '-'],
            ['第1天', '25', '-', '-', '2020304', '2838366', '-', '-'],
            ['第1天', '50', '-', '3926221', '-', '-', '-', '-'],
            ['第1天', '75', '2692723', '-', '2681692', '3417484', '-', '-'],
            ['第1天', '150', '-', '-', '3203287', '3990086', '-', '-'],
            ['第14/28天', '0.3', '-', '-', '-', '-', '21.9', '23.4'],
            ['第14/28天', '1.0', '-', '-', '-', '-', '140', '196'],
            ['第14/28天', '2.5', '-', '4027396', '-', '-', '-', '-'],
            ['第14/28天', '3.0', '-', '-', '-', '-', '465', '149'],
            ['第14/28天', '5', '1291721', '-', '-', '-', '-', '-'],
            ['第14/28天', '7.5', '-', '6625633', '-', '-', '-', '-'],
            ['第14/28天', '10', '-', '-', '1999763', '5888665', '-', '-'],
            ['第14/28天', '15', '3264410', '-', '-', '-', '-', '-'],
            ['第14/28天', '25', '-', '-', '2321910', '6223126', '-', '-'],
            ['第14/28天', '50', '-', '6920757', '-', '-', '-', '-'],
            ['第14/28天', '75', '2884819', '-', '2820237', '4889237', '-', '-'],
            ['第14/28天', '150', '-', '-', '2542402', '4970898', '-', '-'],
        ]

        # SD大鼠经口灌胃给予新药28天
        small_table4, days4 = TKTable.parse_small_table(
            '''| 性别（雄/雌） | 性别（雄/雌） | 雄 | 雌 |
| 时间 | 剂量(mg/kg) | AUClast (hr*ng/ml) | AUClast (hr*ng/ml) |
| 1 | 2.5 | - | 416645 |
| 1 | 5 | 517794 | - |
| 1 | 7.5 | - | 1379860 |
| 1 | 15 | 1492946 | - |
| 1 | 50 | - | 3926221 |
| 1 | 75 | 2692723 | - |
| 28 | 2.5 | - | 4027396 |
| 28 | 5 | 1291721 | - |
| 28 | 7.5 | - | 6625633 |
| 28 | 15 | 3264410 | - |
| 28 | 50 | - | 6920757 |
| 28 | 75 | 2884819 | - |'''
        )
        medium_header_values4 = ('SD大鼠', '经口灌胃', '28天')

        # 比格犬经口灌胃给予新药28天
        small_table5, days5 = TKTable.parse_small_table(
            '''| 性别（雄/雌） | 性别（雄/雌） | 雄 | 雌 |
| 时间 | 剂量(mg/kg) | AUClast (hr*ng/ml) | AUClast (hr*ng/ml) |
| 1 | 1.5 | 7539 | 11670 |
| 1 | 5 | 38835 | 37662 |
| 1 | 15 | 190826 | 163700 |
| 28 | 1.5 | 6854 | 9386 |
| 28 | 5 | 34451 | 32532 |
| 28 | 15 | 216157 | 169215 |'''
        )
        medium_header_values5 = ('比格犬', '经口灌胃', '28天')
        assert TKTable.merge_tables(
            [
                medium_header_values1,
                medium_header_values2,
                medium_header_values3,
                medium_header_values4,
                medium_header_values5,
            ],
            [small_table1, small_table2, small_table3, small_table4, small_table5],
            days1 | days2 | days3 | days4 | days5,
        ) == [
            [
                '种属',
                '种属',
                'SD大鼠',
                'SD大鼠',
                'SD大鼠',
                'SD大鼠',
                'SD大鼠',
                'SD大鼠',
                '比格犬',
                '比格犬',
                '比格犬',
                '比格犬',
            ],
            [
                '给药途径',
                '给药途径',
                '灌胃',
                '灌胃',
                '经口灌胃',
                '经口灌胃',
                '经口灌胃',
                '经口灌胃',
                '灌胃',
                '灌胃',
                '经口灌胃',
                '经口灌胃',
            ],
            ['试验类型', '试验类型', '28天', '28天', '14天', '14天', '28天', '28天', '28天', '28天', '28天', '28天'],
            ['性别（雄/雌）', '性别（雄/雌）', '雄', '雌', '雄', '雌', '雄', '雌', '雄', '雌', '雄', '雌'],
            [
                '时间',
                '剂量(mg/kg)',
                'AUClast (hr*ng/ml)',
                'AUClast (hr*ng/ml)',
                'AUClast (hr*ng/ml)',
                'AUClast (hr*ng/ml)',
                'AUClast (hr*ng/ml)',
                'AUClast (hr*ng/ml)',
                'AUClast (hr*ng/ml)',
                'AUClast (hr*ng/ml)',
                'AUClast (hr*ng/ml)',
                'AUClast (hr*ng/ml)',
            ],
            ['第1天', '0.3', '-', '-', '-', '-', '-', '-', '28.1', '56.2', '-', '-'],
            ['第1天', '1.0', '-', '-', '-', '-', '-', '-', '91.3', '141', '-', '-'],
            ['第1天', '1.5', '-', '-', '-', '-', '-', '-', '-', '-', '7539', '11670'],
            ['第1天', '2.5', '-', '416645', '-', '-', '-', '416645', '-', '-', '-', '-'],
            ['第1天', '3.0', '-', '-', '-', '-', '-', '-', '257', '226', '-', '-'],
            ['第1天', '5', '517794', '-', '-', '-', '517794', '-', '-', '-', '38835', '37662'],
            ['第1天', '7.5', '-', '1379860', '-', '-', '-', '1379860', '-', '-', '-', '-'],
            ['第1天', '10', '-', '-', '1051074', '1248594', '-', '-', '-', '-', '-', '-'],
            ['第1天', '15', '1492946', '-', '-', '-', '1492946', '-', '-', '-', '190826', '163700'],
            ['第1天', '25', '-', '-', '2020304', '2838366', '-', '-', '-', '-', '-', '-'],
            ['第1天', '50', '-', '3926221', '-', '-', '-', '3926221', '-', '-', '-', '-'],
            ['第1天', '75', '2692723', '-', '2681692', '3417484', '2692723', '-', '-', '-', '-', '-'],
            ['第1天', '150', '-', '-', '3203287', '3990086', '-', '-', '-', '-', '-', '-'],
            ['第14/28天', '0.3', '-', '-', '-', '-', '-', '-', '21.9', '23.4', '-', '-'],
            ['第14/28天', '1.0', '-', '-', '-', '-', '-', '-', '140', '196', '-', '-'],
            ['第14/28天', '1.5', '-', '-', '-', '-', '-', '-', '-', '-', '6854', '9386'],
            ['第14/28天', '2.5', '-', '4027396', '-', '-', '-', '4027396', '-', '-', '-', '-'],
            ['第14/28天', '3.0', '-', '-', '-', '-', '-', '-', '465', '149', '-', '-'],
            ['第14/28天', '5', '1291721', '-', '-', '-', '1291721', '-', '-', '-', '34451', '32532'],
            ['第14/28天', '7.5', '-', '6625633', '-', '-', '-', '6625633', '-', '-', '-', '-'],
            ['第14/28天', '10', '-', '-', '1999763', '5888665', '-', '-', '-', '-', '-', '-'],
            ['第14/28天', '15', '3264410', '-', '-', '-', '3264410', '-', '-', '-', '216157', '169215'],
            ['第14/28天', '25', '-', '-', '2321910', '6223126', '-', '-', '-', '-', '-', '-'],
            ['第14/28天', '50', '-', '6920757', '-', '-', '-', '6920757', '-', '-', '-', '-'],
            ['第14/28天', '75', '2884819', '-', '2820237', '4889237', '2884819', '-', '-', '-', '-', '-'],
            ['第14/28天', '150', '-', '-', '2542402', '4970898', '-', '-', '-', '-', '-', '-'],
        ]

        # 空数据
        medium_header_values6 = ('SD大鼠', '经口灌胃', '28天')
        assert TKTable.merge_tables(
            [medium_header_values1, medium_header_values6], [small_table1, ([], {}, {})], days1 | {'28'}
        ) == [
            ['种属', '种属', 'SD大鼠', 'SD大鼠', 'SD大鼠', 'SD大鼠'],
            ['给药途径', '给药途径', '经口灌胃', '经口灌胃', '经口灌胃', '经口灌胃'],
            ['试验类型', '试验类型', '14天', '14天', '28天', '28天'],
            ['性别（雄/雌）', '性别（雄/雌）', '雄', '雌', '雄', '雌'],
            [
                '时间',
                '剂量(mg/kg)',
                'AUClast (hr*ng/ml)',
                'AUClast (hr*ng/ml)',
                'AUClast (hr*ng/ml)',
                'AUClast (hr*ng/ml)',
            ],
            ['第1天', '10', '1051074', '1248594', '-', '-'],
            ['第1天', '25', '2020304', '2838366', '-', '-'],
            ['第1天', '75', '2681692', '3417484', '-', '-'],
            ['第1天', '150', '3203287', '3990086', '-', '-'],
            ['第14/28天', '10', '1999763', '5888665', '-', '-'],
            ['第14/28天', '25', '2321910', '6223126', '-', '-'],
            ['第14/28天', '75', '2820237', '4889237', '-', '-'],
            ['第14/28天', '150', '2542402', '4970898', '-', '-'],
        ]
