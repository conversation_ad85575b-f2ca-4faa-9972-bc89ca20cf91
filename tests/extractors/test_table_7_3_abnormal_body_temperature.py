import pytest

from app.extractors.table_7_3_abnormal_body_temperature import (
    AbnormalBodyTemperatureTable,
)


class TestAbnormalBodyTemperatureTable:
    @pytest.mark.asyncio(loop_scope='session')
    async def test_extract(self):
        # 重复给药关键: 4232-************, project_id: 296 , doc_id: 1303
        assert (
            await AbnormalBodyTemperatureTable.extract(
                '''| 比较组 | 剂量（mk/kg） | 性别 | 试验日## | 对照组 | 变化幅度# | P* | 毒性反应 | 主要判定依据 |
| 溶媒组 | 0 | M | D1_1h | 阴性对照 | 2%↑ | <0.01 | 否 | 与给药前相比无明显 |
| 溶媒组 | 0 | M | D1_24h | 阴性对照 | 1%↓ | <0.01 | 否 | 均值为39.2°C，在动物正常生理范围内 |
| 溶媒组 | 0 | F | D56 | 阴性对照 | 2%↑ | <0.05 | 否 | 与给药前相比无明显 |
| 供试品低剂量组 | 5 | M | D1_24h | 溶媒对照 | 1%↑ | <0.05 | 否 | 与给药前相比无明显 |
| 供试品中剂量组 | 15 | M | D1_1h | 阴性对照 | 0.4%↓ | ≥0.05 | 是 | 与给药前相比大部分都有下降（0.7~2.6℃），且1/5只动物下降到36.8℃ |
| 供试品中剂量组 | 15 | M | D1_24h | 阴性对照 | 2%↓ | <0.01 | 否 | 与给药前相比无明显 |
| 供试品中剂量组 | 7.5 | F | D1_1h | 阴性对照 | 4%↓ | <0.01 | 是 | 与对照和给药前均下降（0.7~2.6℃），且1/5只动物下降到35.8℃ |
| 供试品中剂量组 | 7.5 | F | D1_1h | 溶媒对照 | 4%↓ | <0.01 | 是 | 与对照和给药前均下降（0.7~2.6℃），且1/5只动物下降到35.8℃ |
| 供试品高剂量组 | 75 | M | D1_1h | 溶媒对照 | 3%↓ | <0.05 | 是 | 与给药前相比均下降（0.5-2.3℃），最低值37.0℃ |
| 供试品高剂量组 | 75 | M | D1_24h | 阴性对照 | 2%↓ | <0.01 | 否 | 37.7-38.4℃，在动物正常生理范围内 |
| 供试品高剂量组 | 75 | M | D28_1h | 溶媒对照 | 3%↑ | <0.05 | 否 | 与给药前相比无明显 |
| 供试品高剂量组 | 50 | F | D1_1h | 阴性对照 | 3%↓ | <0.01 | 是 | 与对照和给药前均下降（1.3-2.2℃），最低至37℃ |
| 供试品高剂量组 | 50 | F | D1_1h | 溶媒对照 | 4%↓ | <0.01 | 是 | 与对照和给药前均下降（1.3-2.2℃），最低至37℃ |''',
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |
| D1_1h | - | - | - | - | - | 4%↓ | 3%↓ | 4%↓ |
| D1_24h | - | - | 1%↑ | - | - | - | - | - |
| D28_1h | - | - | - | - | - | - | 3%↑ | - |'''
        )

        # gpt生成

        assert (
            await AbnormalBodyTemperatureTable.extract(
                '''| 比较组 | 剂量（mk/kg） | 性别 | 试验日## | 对照组 | 变化值 | P* | 毒性反应 | 主要判定依据 |
| 溶媒组 | 0 | M | D1_1h | 阴性对照 | 2%↑ | <0.01 | 否 | 与给药前相比无明显 |
| 溶媒组 | 0 | M | D1_24h | 阴性对照 | 1%↓ | <0.01 | 否 | 均值为39.2°C，在动物正常生理范围内 |
| 溶媒组 | 0 | F | D56 | 阴性对照 | 2%↑ | <0.05 | 否 | 与给药前相比无明显 |
| 供试品低剂量组 | 5 | M | D1_24h | 溶媒对照 | 1%↑ | <0.05 | 否 | 与给药前相比无明显 |
| 供试品低剂量组 | 2.5 | F | D1_24h | 溶媒对照 | 5%↑ | <0.05 | 否 | 与给药前相比无明显 |
| 供试品中剂量组 | 15 | M | D1_1h | 溶媒对照 | 0.4%↓ | ≥0.05 | 是 | 与给药前相比大部分都有下降（0.7~2.6℃），且1/5只动物下降到36.8℃ |
| 供试品中剂量组 | 15 | M | D1_24h | 溶媒对照 | 2%↓ | <0.01 | 否 | 与给药前相比无明显 |
| 供试品中剂量组 | 7.5 | F | D1_24h | 溶媒对照 | 4%↓ | <0.01 | 是 | 与对照和给药前均下降（0.7~2.6℃），且1/5只动物下降到35.8℃ |
| 供试品中剂量组 | 7.5 | F | D1_1h | 溶媒对照 | 4%↓ | <0.01 | 是 | 与对照和给药前均下降（0.7~2.6℃），且1/5只动物下降到35.8℃ |
| 供试品高剂量组 | 75 | M | D1_1h | 溶媒对照 | 3%↓ | <0.05 | 是 | 与给药前相比均下降（0.5-2.3℃），最低值37.0℃ |
| 供试品高剂量组 | 75 | M | D1_24h | 溶媒对照 | 2%↓ | <0.01 | 否 | 37.7-38.4℃，在动物正常生理范围内 |
| 供试品高剂量组 | 75 | M | D28_1h | 溶媒对照 | 3%↑ | <0.05 | 否 | 与给药前相比无明显 |
| 供试品高剂量组 | 50 | F | D28_1h | 溶媒对照 | 3%↓ | <0.01 | 是 | 与对照和给药前均下降（1.3-2.2℃），最低至37℃ |
| 供试品高剂量组 | 50 | F | D1_1h | 溶媒对照 | 4%↓ | <0.01 | 是 | 与对照和给药前均下降（1.3-2.2℃），最低至37℃ |''',
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |
| D1_1h | - | - | - | - | 0.4%↓ | 4%↓ | 3%↓ | 4%↓ |
| D1_24h | - | - | 1%↑ | 5%↑ | 2%↓ | 4%↓ | 2%↓ | - |
| D28_1h | - | - | - | - | - | - | 3%↑ | 3%↓ |'''
        )

        # C-231610，无体温章节，遂无须补充
        # C-232003，有体温章节，但无对应表格，即不补充
        # C-230613，无体温章节，遂无须补充

        # C-230614，7.7体温
        assert (
                await AbnormalBodyTemperatureTable.extract(
                    '''| 给药剂量（mg/kg/day） | Day-9/-8 | Day-9/-8 | Day-5 | Day-5 | Day1 | Day1 | Day24 | Day24 | Day52/Day53 | Day52/Day53 |
| 给药剂量（mg/kg/day） | M | F | M | F | M | F | M | F | M | F | M | F | M | F | M | F |
| 0 | 38.88 | 38.96 | 38.86 | 38.90 | 38.48 | 38.94 | 38.94 | 39.32 | 39.30 | 39.55 |
| 1 | 0.51 | -0.05 | 0.41 | 0.15 | -1.14 | -2.16 | -1.44 | -1.98 | -1.02 | -0.51 |
| 2 | 0.10 | 0.26 | 0.31 | 0.21 | -2.44 | -2.62 | -1.75 | -3.71 | 0.00 | -0.88 |
| 4 | 0.31 | 0.31 | 0.10 | 0.36 | -4.94 | -4.67 | -2.77 | -2.34 | -1.15 | -1.39 |''',
                    '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 1 | 1 | 2 | 2 | 4 | 4 |
    | 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
                )
                == '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 1 | 1 | 2 | 2 | 4 | 4 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |
| Day-9/-8 | - | - | 0.51 | -0.05 | 0.10 | 0.26 | 0.31 | 0.31 |
| Day-5 | - | - | 0.41 | 0.15 | 0.31 | 0.21 | 0.10 | 0.36 |
| Day1 | - | - | -1.14 | -2.16 | -2.44 | -2.62 | -4.94 | -4.67 |
| Day24 | - | - | -1.44 | -1.98 | -1.75 | -3.71 | -2.77 | -2.34 |
| Day52/Day53 | - | - | -1.02 | -0.51 | 0.00 | -0.88 | -1.15 | -1.39 |''')

    def test_parse_table(self):
        # 重复给药关键: 4232-************, project_id: 296 , doc_id: 1303
        header1 = [
            ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
            ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
        ]
        assert (
            AbnormalBodyTemperatureTable.parse_table(
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |
| D1_1h | - | - | - | - | - | 4%↓ | 3%↓ | 4%↓ |
| D1_24h | - | - | 1%↑ | - | - | - | - | - |
| D28_1h | - | - | - | - | - | - | 3%↑ | - |''',
                header1,
            )
            == [
                ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ['体温', '体温', '体温', '体温', '体温', '体温', '体温', '体温', '体温'],
                ['D1_1h\n（%变化幅度）', '-', '-', '-', '-', '-', '4%↓', '3%↓', '4%↓'],
                ['D1_24h\n（%变化幅度）', '-', '-', '1%↑', '-', '-', '-', '-', '-'],
                ['D28_1h\n（%变化幅度）', '-', '-', '-', '-', '-', '-', '3%↑', '-'],
            ]
        )
