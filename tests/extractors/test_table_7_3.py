import pytest

from app.extractors.table_7_3 import (
    HemagglutinationTable,
    generate_clinical_observation_from_table,
    generate_table_clinical_observation,
    generate_table_food_intake,
    generate_table_gross_pathology,
    generate_table_near_death_or_dying,
    generate_table_urinalysis,
)

'''
267.7 续表
'''


# 267.7 续表 - 摄食量 LLM请求方法测试
@pytest.mark.asyncio(loop_scope='session')
class TestGenerateTableFoodIntake:
    async def test_generate_table_food_intake(self):
        assert (
            await generate_table_food_intake(
                '''摄食量个体数据见附录1。
给药期，与同期对照组相比，50 mg/kg/day剂量组2只雄性动物与对照组雄性动物相比摄食情况一般/不良（一般, 饲料剩余1/3至2/3，不良, 饲料剩余2/3以上）发生频率高，该剂量组其他动物以及其他剂量组动物摄食量无显著变化。
恢复期，与同期对照组相比，各给药组雌雄动物摄食量基本一致。''',
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '10', '10', '25', '25', '50', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 10 | 10 | 25 | 25 | 50 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '10', '10', '25', '25', '50', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ['摄食量', '-', '-', '-', '-', '-', '-', '一般/不良', '-'],
                ],
                [],
            )
        )

        assert (
            await generate_table_food_intake(
                '''摄食量个体数据见附录2。
给药期，与同期对照组相比，15 mg/kg/day剂量组3只雌性动物与对照组动物相比摄食情况严重，发生频率较高，该剂量组其他动物以及其他剂量组动物摄食量无显著变化。
恢复期，与同期对照组相比，5 mg/kg/day剂量组1只雌性动物与对照组动物相比摄食情况一般/不良， 15 mg/kg/day剂量组1只雄性动物与对照组动物相比摄食情况严重。''',
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '5', '15', '15'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 5 | 15 | 15 |
| 动物数量 | M: | F: | M: | F: | M: | F: |''',
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '5', '15', '15'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ['摄食量', '-', '-', '-', '-', '-', '严重'],
                ],
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '5', '15', '15'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ['摄食量', '-', '-', '-', '一般/不良', '严重', '-'],
                ],
            )
        )

        assert (
            await generate_table_food_intake(
                '''摄食量个体数据见附录3。
给药期，该剂量组动物以及其他剂量组动物摄食量无显著变化。
恢复期，与同期对照组相比，100 mg/kg/day剂量组1只雌性动物与对照组动物相比摄食情况一般/不良， 15 mg/kg/day剂量组1只雄性动物与对照组动物相比摄食情况严重。''',
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '5', '15', '15', '100', '100'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 5 | 15 | 15 | 100 | 100 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '5', '15', '15', '100', '100'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ['摄食量', '-', '-', '-', '-', '-', '-', '-', '-'],
                ],
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '5', '15', '15', '100', '100'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ['摄食量', '-', '-', '-', '-', '严重', '-', '-', '一般/不良'],
                ],
            )
        )

        # C-231610，7.5摄食量
        assert (
                await generate_table_food_intake(
                    '''摄食量数据总结见附表7（给药期）和附表8（恢复期），个体摄食量数据见附录8。给药期和恢复期均未见与受试物新药001相关的摄食量变化。''',
                    [
                        ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '5', '15', '15', '100', '100'],
                        ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ],
                    '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 5 | 15 | 15 | 100 | 100 |
    | 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
                )
                == (
                    [
                        ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '5', '15', '15', '100', '100'],
                        ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                        ['摄食量', '-', '-', '-', '-', '-', '-', '-', '-'],
                    ],
                    [],
                )
        )

        # C-232003，7.5摄食量
        assert (
                await generate_table_food_intake(
                    '''给药期，与同期对照组相比，除15mg/kg/day剂量组1只雄性动物（#23819）与对照组雄性动物相比摄食情况一般/不良（一般,饲料剩余1/3至2/3，不良,饲料剩余2/3以上）发生频率较高，该剂量组其他动物以及其他剂量组动物摄食量无显著变化。
                    恢复期，与同期对照组相比，各给药组雌雄动物摄食量基本一致。''',
                    [
                        ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '5', '15', '15', '100', '100'],
                        ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ],
                    '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 5 | 15 | 15 | 100 | 100 |
    | 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
                )
                == (
                    [
                        ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '5', '15', '15', '100', '100'],
                        ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                        ['摄食量', '-', '-', '-', '-', '一般/不良', '-', '-', '-'],
                    ],
                    [],
                )
        )

        # C-230614，7.5摄食量
        assert (
                await generate_table_food_intake(
                    '''摄食量个体数据见附录9。
                    给药期，与同期对照组相比，除2mg/kg/day一只雌性动物（#22491）摄食量情况与对照组动物摄食量情况相比较差外，给药期各给药剂量组雌雄动物平均摄食量无显著变化。
                    恢复期，与同期对照组相比，各给药组雌雄动物平均摄食量基本一致。''',
                    [
                        ['日剂量（mg/kg）', '0（对照）', '0（对照）', '2', '2', '15', '15', '100', '100'],
                        ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ],
                    '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 2 | 2 | 15 | 15 | 100 | 100 |
    | 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
                )
                == (
                    [
                        ['日剂量（mg/kg）', '0（对照）', '0（对照）', '2', '2', '15', '15', '100', '100'],
                        ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                        ['摄食量', '-', '-', '-', '较差', '-', '-', '-', '-'],
                    ],
                    [],
                )
        )

        # C-230613，7.5摄食量
        assert (
                await generate_table_food_intake(
                    '''摄食量数据总结见附表7（给药期）和附表8（恢复期），个体摄食量数据见附录8。
                    给药期，与同期对照组相比，与NS-041相关的摄食量变化包括：≥2mg/kg/day剂量组雄性和雌性动物的摄食量在Days1-8减少，≥3mg/kg/day剂量组雄性和雌性动物的摄食量在Days8-15减少，≥3mg/kg/day剂量组雄性动物的摄食量在Days15-22减少，≥3mg/kg/day剂量组雄性动物和≥2mg/kg/day剂量组雌性动物的摄食量在Days22-28减少，见正文表17。
                    恢复期未见与NS-041相关的摄食量变化。''',
                    [
                        ['日剂量（mg/kg）', '0（对照）', '0（对照）', '2', '2', '3', '3', '5', '5'],
                        ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ],
                    '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 2 | 2 | 3 | 3 | 5 | 5 |
    | 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
                )
                == (
                    [
                        ['日剂量（mg/kg）', '0（对照）', '0（对照）', '2', '2', '3', '3', '5', '5'],
                        ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                        ['摄食量', '-', '-', '减少', '减少', '减少', '减少', '减少', '减少'],
                    ],
                    [],
                )
        )



# 267.7 续表 - 临床观察-表格输入测试 LLM请求方法测试
@pytest.mark.asyncio(loop_scope='session')
class TestGenerateTableClinicalObservationDataFromTable:
    async def test_generate_table_clinical_observation_data_from_table(self):
        # 大鼠经口灌胃给予NS-041连续28天及恢复28天的毒理学及毒代动力学试验 33403-230613   7.2.2临床观察   正文表15 异常临床症状
        assert (
            (
                await generate_clinical_observation_from_table(
                    # 以下表为表中部分字段（非完整表，只是从完整表中截取）
                    '''| 阶段 | 临床症状 | 剂量(mg/kg/day) | 雄性 | 雄性 | 雌性 | 雌性 |
           | 阶段 | 临床症状 | 剂量(mg/kg/day) | 发生率 | 症状起止日 | 发生率 | 症状起止日 |
           | 给药期 | 沾染（左右前爪/眼睛/鼻子/下巴/嘴巴，浅红色/红色/暗红色/清澈沾染） | 0 | 1/15 | Days 13-22 | - | - |
           | 给药期 | 沾染（左右前爪/眼睛/鼻子/下巴/嘴巴，浅红色/红色/暗红色/清澈沾染） | 2 | 12/15 | Days 8-27 | 11/15 | Days 12-26 |
           | 给药期 | 沾染（左右前爪/眼睛/鼻子/下巴/嘴巴，浅红色/红色/暗红色/清澈沾染） | 3 | 15/15 | Days 8-27 | 8/15 | Days 12-26 |
           | 给药期 | 沾染（左右前爪/眼睛/鼻子/下巴/嘴巴，浅红色/红色/暗红色/清澈沾染） | 5 | 12/15 | Days 8-27 | 8/15 | Days 12-26 |
           | 给药期 | 流涎 | 2 | 2/15 | Days 13-20 | 1/15 | Days 15-19 |
           | 给药期 | 流涎 | 3 | 6/15 | Days 8-29 | - | - |
           | 给药期 | 流涎 | 5 | 3/15 | Days 8-20 | 2/15 | Days 15-29 |
           | 恢复期 | 流涎 | 3 | - | - | 1/5 | Day 35 |
           | 恢复期 | 流涎 | 5 | - | - | 1/5 | Day 35 |''',
                    [
                        ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1', '1', '2', '2', '3', '3', '4', '4', '5', '5'],
                        ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ],
                    '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 1 | 1 | 2 | 2 | 3 | 3 | 4 | 4 | 5 | 5 |
           | 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: | M: | F: | M: | F: |''',
                )
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1', '1', '2', '2', '3', '3', '4', '4', '5', '5'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    [
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                    ],
                    ['沾染', '1/15', '-', '-', '-', '12/15', '11/15', '15/15', '8/15', '-', '-', '12/15', '8/15'],
                    ['流涎', '-', '-', '-', '-', '2/15', '1/15', '6/15', '-', '-', '-', '3/15', '2/15'],
                ],
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1', '1', '2', '2', '3', '3', '4', '4', '5', '5'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    [
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                    ],
                    ['流涎', '-', '-', '-', '-', '-', '-', '-', '1/5', '-', '-', '-', '1/5'],
                ],
            )
        )

        # C-230613，7.2.2临床观察表格内容（因为表格太大，所以从表格数据截取了一部分充当样例）
        assert (
            (
                await generate_clinical_observation_from_table(
                    '''| 阶段 | 临床症状 | 剂量(mg/kg/day) | 雄性 | 雄性 | 雌性 | 雌性 |
           | 阶段 | 临床症状 | 剂量(mg/kg/day) | 发生率 | 症状起止日 | 发生率 | 症状起止日 |
           | 给药期 | 沾染（左右前爪/眼睛/鼻子/下巴/嘴巴，浅红色/红色/暗红色/清澈沾染） | 0 | 1/15 | Days 13-22 | - | - |
           | 给药期 | 沾染（左右前爪/眼睛/鼻子/下巴/嘴巴，浅红色/红色/暗红色/清澈沾染） | 2 | 12/15 | Days 8-27 | 11/15 | Days 12-26 |
           | 给药期 | 沾染（左右前爪/眼睛/鼻子/下巴/嘴巴，浅红色/红色/暗红色/清澈沾染） | 3 | 15/15 | Days 8-27 | 8/15 | Days 12-26 |
           | 给药期 | 沾染（左右前爪/眼睛/鼻子/下巴/嘴巴，浅红色/红色/暗红色/清澈沾染） | 5 | 12/15 | Days 8-27 | 8/15 | Days 12-26 |
           | 给药期 | 流涎 | 2 | 2/15 | Days 13-20 | 1/15 | Days 15-19 |
           | 给药期 | 流涎 | 3 | 6/15 | Days 8-29 | - | - |
           | 给药期 | 流涎 | 5 | 3/15 | Days 8-20 | 2/15 | Days 15-29 |
           | 恢复期 | 流涎 | 3 | - | - | 1/5 | Day 35 |
           | 恢复期 | 流涎 | 5 | - | - | 1/5 | Day 35 |''',
                    [
                        ['日剂量（mg/kg）', '0（对照）', '0（对照）', '2', '2', '3', '3', '5', '5'],
                        ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ],
                    '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 2 | 2 | 3 | 3 | 5 | 5 |
           | 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
                )
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '2', '2', '3', '3', '5', '5'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    [
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                    ],
                    ['沾染', '1/15', '-', '12/15', '11/15', '15/15', '8/15', '12/15', '8/15'],
                    ['流涎', '-', '-', '2/15', '1/15', '6/15', '-', '3/15', '2/15'],
                ],
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '2', '2', '3', '3', '5', '5'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    [
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                    ],
                    ['流涎', '-', '-', '-', '-', '-', '1/5', '-', '1/5'],
                ],
            )
        )


# # 267.7 续表 - 临床观察 LLM请求方法测试
@pytest.mark.asyncio(loop_scope='session')
class TestGenerateTableClinicalObservation:
    async def test_generate_table_clinical_observation(self):
        assert (
            await generate_table_clinical_observation(
                '''临床观察数据总结见附表1。
            给药期，与同期对照组相比，10 mg/kg/day剂量组2只雌性动物与对照组动物相比观察到软便，该剂量组其他动物无异常。
            恢复期，与同期对照组相比，25 mg/kg/day剂量组5只雄性动物与对照组动物相比出现稀便，50 mg/kg/day剂量组2只雌性动物与对照组动物相比观察到分泌物， ''',
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '10', '10', '25', '25', '50', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 10 | 10 | 25 | 25 | 50 | 50 |
        | 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '10', '10', '25', '25', '50', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    [
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                    ],
                    ['软便', '-', '-', '-', '2', '-', '-', '-', '-'],
                ],
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '10', '10', '25', '25', '50', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    [
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                    ],
                    ['稀便', '-', '-', '-', '-', '5', '-', '-', '-'],
                    ['分泌物', '-', '-', '-', '-', '-', '-', '-', '2'],
                ],
            )
        )

        assert (
            await generate_table_clinical_observation(
                '''摄食量个体数据见附录2。
        给药期，与同期对照组相比，3 mg/kg/day剂量组7只雄性动物与对照组动物相比观察到便秘，该剂量组其他动物无异常。
        恢复期，与同期对照组相比，5 mg/kg/day剂量组1只雌性动物与对照组动物相比观察到稀便， 10 mg/kg/day剂量组13只雄性动物与对照组动物相比出现分泌物。''',
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '3', '5', '10', '15'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 3 | 5 | 10 | 15 |
        # | 动物数量 | M: | F: | M: | F: | M: | F: |''',
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '3', '5', '10', '15'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ['临床观察', '临床观察', '临床观察', '临床观察', '临床观察', '临床观察', '临床观察'],
                    ['便秘', '-', '-', '7', '-', '-', '-'],
                ],
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '3', '5', '10', '15'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ['临床观察', '临床观察', '临床观察', '临床观察', '临床观察', '临床观察', '临床观察'],
                    ['稀便', '-', '-', '-', '1', '-', '-'],
                    ['分泌物', '-', '-', '-', '-', '13', '-'],
                ],
            )
        )

        assert (
            await generate_table_clinical_observation(
                '''摄食量个体数据见附录3。
        给药期：异常临床症状包括：脱毛、分泌物（左眼）、软便和稀便。
        恢复期：在试验第35天，15 mg/kg/day剂量组仅1只雄性动物观察到脸部结痂。''',
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 |
        | 动物数量 | M: | F: | M: | F: | M: |''',
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:'],
                    ['临床观察', '-', '-', '-', '-', '-'],
                ],
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:'],
                    ['临床观察', '临床观察', '临床观察', '临床观察', '临床观察', '临床观察'],
                    ['结痂', '-', '-', '-', '-', '1'],
                ],
            )
        )

        assert (
            await generate_table_clinical_observation(
                '''摄食量个体数据见附录4。
        给药期，异常临床症状包括：呕吐、软便、稀便、分泌物（左眼）、白沫（流涎）、肿块（左/右前后爪或尾巴）、肿胀（外阴）、皮疹（左/右耳或右腹股沟或右眼）和颜色改变（左/右耳）。基于以上改变发生率与对照组相当、缺乏剂量反应关系、和/或是常见的背景改变，认为与新药001无关。
        恢复期，异常临床症状包括：呕吐、软便、白沫（流涎）、肿块（尾）、肿胀（外阴）、皮疹（左/右耳）和颜色改变（左/右耳），上述症状发生只数少、无剂量相关性和/或对照组也有发生，认为与新药001无关。''',
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 1.5 | 1.5 | 5 | 5 |
                        | 动物数量 | M: | F: | M: | F: | M: |''',
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ['临床观察', '-', '-', '-', '-', '-', '-'],
                ],
                [],
            )
        )

        # C-230614，7.2.2临床观察，这用例不再重复补充
        assert (
            await generate_table_clinical_observation(
                '''给药期，认为与NS-041相关的异常临床症状包括：分泌物（左/右眼）、呕吐、精神沉郁、虚脱、震颤、白沫（流涎）、流涎、活动减少。分泌物（左/右眼）见于≥2 mg/kg/day雌雄性动物（5/20）；呕吐见于给药组≥1 mg/kg/day雌雄性动物（30/30），对照组雌雄性动物（3/10）也可见呕吐，但发生频率和出现的动物只数少于给药组，认为与受试物相关；精神沉郁见于4 mg/kg/day雄性动物（3/5）；虚脱见于4 mg/kg/day雄性动物（2/5）；震颤（左/右后肢、全身）见于2 mg/kg/day雄性动物（3/5）和4 mg/kg/day雌性动物；白沫（流涎）≥1 mg/kg/day雌雄动物（30/30）；流涎见于给药组≥1 mg/kg/day雌雄性动物（30/30），对照组雄性动物（1/10）也可见流涎，但发生频率和出现的动物只数少于给药组；活动减少见于≥1 mg/kg/day雄性动物（11/15）和≥2 mg/kg/day雌性动物（5/10）。此外，消瘦见于第29天2 mg/kg/day雌性动物（#22491, 1/5）由于发生动物只数少频率低，仅出现在雌性动物，且该动物第一天体重与其他动物体重比相对较低，不排除个体差异，与受试物NS-041相关性不确定。
        恢复期，2 mg/kg/day雄性动物（1/5）出现呕吐、1和4 mg/kg/day雌雄动物（4/20）出现流涎、2 mg/kg/day雄性动物（1/5）出现白沫（流涎），上述症状无剂量相关性且随机偶发，与受试物NS-041相关性不确定。此外，其他无明显异常症状出现。
        其它异常临床观察可见脱毛、软便、结痂、肿块、皮疹、颜色改变（左/右耳、嘴）、粪便含异物、稀便、水样便，基于其发生率低、缺乏剂量反应关系、对照组动物可见，和/或是常见的背景病变，认为与NS-041给药无关。''',
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1', '1', '2', '2', '4', '4'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 1 | 1 | 2 | 2 | 4 | 4 |
                        | 动物数量 | M: | F: | M: | F: | M: | F: | M: |''',
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1', '1', '2', '2', '4', '4'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    [
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                        '临床观察',
                    ],
                    ['分泌物（左/右眼）', '-', '-', '5/20', '5/20', '5/20', '5/20', '5/20', '5/20'],
                    ['呕吐', '3/10', '3/10', '30/30', '30/30', '30/30', '30/30', '30/30', '30/30'],
                    ['精神沉郁', '-', '-', '-', '-', '-', '-', '3/5', '-'],
                    ['虚脱', '-', '-', '-', '-', '-', '-', '2/5', '-'],
                    ['震颤', '-', '-', '3/5', '-', '3/5', '-', '-', '-'],
                    ['白沫（流涎）', '-', '-', '30/30', '30/30', '30/30', '30/30', '30/30', '30/30'],
                    ['流涎', '1/10', '-', '30/30', '30/30', '30/30', '30/30', '30/30', '30/30'],
                    ['活动减少', '-', '-', '11/15', '-', '11/15', '5/10', '11/15', '5/10'],
                ],
                [],
            )
        )

        # C-231610，7.2.2临床观察
        assert (
                await generate_table_clinical_observation(
                    '''临床观察数据总结见附表1（给药期）和附表2（恢复期）。个体笼旁观察和个体详细临床观察分别见附录4和附录5。
                    给药期：异常临床症状包括：脱毛、分泌物（左眼）、软便和稀便。由于这些症状只在雄性动物中出现，发生率低，且无剂量反应关系，故不认为与新药001相关。
                    恢复期：在试验第35天，15mg/kg/day剂量组仅1只雄性动物（#274804）观察到脸部结痂，随后消失，故不认为与新药001相关。''',
                    [
                        ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '5', '15', '15', '100', '100'],
                        ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ],
                    '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 5 | 15 | 15 | 100 | 100 |
    | 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
                )
                == (
                    [
                        ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '5', '15', '15', '100', '100'],
                        ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                        ['临床观察', '-', '-', '-', '-', '-', '-', '-', '-'],
                    ],
                    [],
                )
        )

        # C-231610，7.2.2临床观察
        assert (
                await generate_table_clinical_observation(
                    '''临床观察总结（包括笼旁观察和详细观察）见附表1（给药期）和附表2（恢复期）。个体笼旁观察结果和详细观察见附录4、附录5和附录6。
                    给药期，异常临床症状包括：呕吐、软便、稀便、分泌物（左眼）、白沫（流涎）、肿块（左/右前后爪或尾巴）、肿胀（外阴）、皮疹（左/右耳或右腹股沟或右眼）和颜色改变（左/右耳）。基于以上改变发生率与对照组相当、缺乏剂量反应关系、和/或是常见的背景改变，认为与新药001无关。
                    恢复期，异常临床症状包括：呕吐、软便、白沫（流涎）、肿块（尾）、肿胀（外阴）、皮疹（左/右耳）和颜色改变（左/右耳），上述症状发生只数少、无剂量相关性和/或对照组也有发生，认为与新药001无关。''',
                    [
                        ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '5', '15', '15', '100', '100'],
                        ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ],
                    '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 5 | 15 | 15 | 100 | 100 |
    | 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
                )
                == (
                    [
                        ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '5', '15', '15', '100', '100'],
                        ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                        ['临床观察', '-', '-', '-', '-', '-', '-', '-', '-']
                    ],
                    [],
                )
        )


# 267.7 续表 - 大体病理学 LLM请求方法测试
@pytest.mark.asyncio(loop_scope='session')
class TestGenerateTableGrossPathology:
    async def test_generate_table_gross_pathology(self):
        assert (
            # 与4232-31003-231610.docx数据相似
            # 但在该基础上修改了一些数据，比如恢复期解剖中增加：1.5 mg/kg/day剂量组1只雌性动物尾皮肤局灶性肿块，用于测试数据所对应剂量与表头不关联的情况
            await generate_table_gross_pathology(
                '''大体病理学检查数据总结见附表1。
终末期解剖（第29天）
与新药001相关的大体病理学改变包括：75 mg/kg/day剂量组5只雄性动物胸腺体积减小，20 mg/kg/day剂量组2只雌性动物胸腺体积减小，2.5 mg/kg/day剂量组2只雌性动物尾皮肤局灶性肿块，7.5 mg/kg/day剂量组2只雌性动物和50 mg/kg/day剂量组7只雌性动物肝所有叶体积增大，5 mg/kg/day剂量组3只雄性动物胸腺体积增大。
恢复期解剖（第57天）
与新药001相关的大体病理学改变包括：1.5 mg/kg/day剂量组1只雌性动物尾皮肤局灶性肿块。15 mg/kg/day剂量组3只雄性动物尾皮肤局灶性肿块。''',
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    [
                        '大体病理学（第29天）',
                        '大体病理学（第29天）',
                        '大体病理学（第29天）',
                        '大体病理学（第29天）',
                        '大体病理学（第29天）',
                        '大体病理学（第29天）',
                        '大体病理学（第29天）',
                        '大体病理学（第29天）',
                        '大体病理学（第29天）',
                    ],
                    ['胸腺', '胸腺', '胸腺', '胸腺', '胸腺', '胸腺', '胸腺', '胸腺', '胸腺'],
                    ['体积减小', '-', '-', '-', '-', '-', '-', '5', '-'],
                    ['体积增大', '-', '-', '3', '-', '-', '-', '-', '-'],
                    ['尾皮肤', '尾皮肤', '尾皮肤', '尾皮肤', '尾皮肤', '尾皮肤', '尾皮肤', '尾皮肤', '尾皮肤'],
                    ['局灶性肿块', '-', '-', '-', '2', '-', '-', '-', '-'],
                    ['肝脏', '肝脏', '肝脏', '肝脏', '肝脏', '肝脏', '肝脏', '肝脏', '肝脏'],
                    ['体积增大', '-', '-', '-', '-', '-', '2', '-', '7'],
                ],
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    [
                        '大体病理学（第57天）',
                        '大体病理学（第57天）',
                        '大体病理学（第57天）',
                        '大体病理学（第57天）',
                        '大体病理学（第57天）',
                        '大体病理学（第57天）',
                        '大体病理学（第57天）',
                        '大体病理学（第57天）',
                        '大体病理学（第57天）',
                    ],
                    ['尾皮肤', '尾皮肤', '尾皮肤', '尾皮肤', '尾皮肤', '尾皮肤', '尾皮肤', '尾皮肤', '尾皮肤'],
                    ['局灶性肿块', '-', '-', '-', '-', '3', '-', '-', '-'],
                ],
            )
        )

        # C-230613，7.9大体病理学检查（文字类似，只不过以下新药001即为NS-041）
        assert (
            await generate_table_gross_pathology(
                '''大体病理学检查数据总结见附表2。
终末期解剖（第29天），未见与新药001相关的大体病理学改变。
恢复期解剖（第57天），未见与新药001相关的大体病理学改变。''',
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ['大体病理学', '-', '-', '-', '-', '-', '-', '-', '-'],
                ],
                [],
            )
        )

        assert (
            await generate_table_gross_pathology(
                '''大体病理学检查数据总结见附表2。
终末期解剖（第29天），未见与新药001相关的大体病理学改变。
恢复期解剖（第57天）
与新药001相关的大体病理学改变包括：15 mg/kg/day剂量组3只雄性动物尾皮肤局灶性肿块。''',
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ['大体病理学', '-', '-', '-', '-', '-', '-', '-', '-'],
                ],
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    [
                        '大体病理学（第57天）',
                        '大体病理学（第57天）',
                        '大体病理学（第57天）',
                        '大体病理学（第57天）',
                        '大体病理学（第57天）',
                        '大体病理学（第57天）',
                        '大体病理学（第57天）',
                        '大体病理学（第57天）',
                        '大体病理学（第57天）',
                    ],
                    ['尾皮肤', '尾皮肤', '尾皮肤', '尾皮肤', '尾皮肤', '尾皮肤', '尾皮肤', '尾皮肤', '尾皮肤'],
                    ['局灶性肿块', '-', '-', '-', '-', '3', '-', '-', '-'],
                ],
            )
        )

        assert (
            await generate_table_gross_pathology(
                '''大体病理学检查数据总结见附表2。
终末期解剖（第29天），与新药001相关的大体病理学改变包括：15 mg/kg/day剂量组3只雄性动物尾皮肤局灶性肿块。
恢复期解剖（第57天），未见与新药001相关的大体病理学改变。''',
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    [
                        '大体病理学（第29天）',
                        '大体病理学（第29天）',
                        '大体病理学（第29天）',
                        '大体病理学（第29天）',
                        '大体病理学（第29天）',
                        '大体病理学（第29天）',
                        '大体病理学（第29天）',
                        '大体病理学（第29天）',
                        '大体病理学（第29天）',
                    ],
                    ['尾皮肤', '尾皮肤', '尾皮肤', '尾皮肤', '尾皮肤', '尾皮肤', '尾皮肤', '尾皮肤', '尾皮肤'],
                    ['局灶性肿块', '-', '-', '-', '-', '3', '-', '-', '-'],
                ],
                [],
            )
        )

        # 4232-31003-231610.docx
        # C-231610，7.9大体病理学与这相同，无须补充
        assert (
                await generate_table_gross_pathology(
                    '''大体病理学检查数据总结见附表15，个体数据见附录17，综合病理报告见附录19。
    终末期解剖（第29天），与新药001相关的大体病理学改变包括：75 mg/kg/day剂量组5只雄性动物、2.5 mg/kg/day剂量组2只雌性动物（动物号274813和274814）、7.5 mg/kg/day剂量组2只雌性动物（动物号274838和274845）以及50 mg/kg/day剂量组7只雌性动物肝所有叶体积增大，其中50 mg/kg/day剂量组雌性动物和75 mg/kg/day剂量组雄性动物肝所有叶体积增大与镜检肝轻微小叶中央肝细胞肥大相关。
    恢复期解剖（第57天），未见与新药001相关的大体病理学改变。''',
                    [
                        ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                        ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ],
                    '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
    | 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
                )
                == (
                    [
                        ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                        ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                        [
                            '大体病理学（第29天）',
                            '大体病理学（第29天）',
                            '大体病理学（第29天）',
                            '大体病理学（第29天）',
                            '大体病理学（第29天）',
                            '大体病理学（第29天）',
                            '大体病理学（第29天）',
                            '大体病理学（第29天）',
                            '大体病理学（第29天）',
                        ],
                        ['肝脏', '肝脏', '肝脏', '肝脏', '肝脏', '肝脏', '肝脏', '肝脏', '肝脏'],
                        ['体积增大', '-', '-', '-', '2', '-', '2', '5', '7'],
                    ],
                    [],
                )
        )

        # C-232003，7.12大体病理学
        assert (
                await generate_table_gross_pathology(
                    '''大体病理学检查结果总结和个体数据分别见附表16和附录21。综合病理报告见附录23。
                    终末期解剖（第29天）未见与新药001相关的大体病理学变化。其他大体病理学改变包括：1.5mg/kg/day剂量组1只雌性动物（动物号23826）胸腺体积减小，与镜检胸腺生理性退化一致。该改变基于发生率较低、缺乏剂量反应关系、仅在单一性别中存在，考虑为该年龄段比格犬常见的偶发性和/或背景性改变，不考虑与受试物相关。
                    恢复期解剖（第57天）未见与新药001相关的大体病理学改变。
                    其他大体病理学改变包括：1.5mg/kg/day剂量组1只雌性动物（动物号23829）尾皮肤局灶性肿块，与镜检尾表皮囊肿一致，该改变基于发生率较低、缺乏剂量反应关系、仅在单一性别中存在，考虑为该年龄段比格犬常见的偶发性和/或背景性改变，不考虑与受试物相关。''',
                    [
                        ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                        ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ],
                    '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
    | 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
                )
                == (
                    [
                        ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                        ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                        ['大体病理学', '-', '-', '-', '-', '-', '-', '-', '-'],
                    ],
                    [],
                )
        )

        # C-230614，7.12大体病理学
        assert (
                await generate_table_gross_pathology(
                    '''大体病理学检查结果总结和个体数据分别见附表15和附录20。综合病理报告见附录23。
                    终末期解剖（第29天）与NS-041相关的大体病理学改变包括：4mg/kg/day剂量组雄性动物胸腺体积减小，与镜检皮质和髓质淋巴细胞减少相一致。
                    恢复期解剖（第57天）未见与NS-041相关的大体病理学改变。所有其他大体病理学改变考虑为偶发的且与NS-041不相关，基于发生率较低、缺乏剂量反应关系，和/或仅在单一性别中存在。
                    所有其他大体病理学改变考虑为偶发的且与NS-041不相关，基于发生率较低、缺乏剂量反应关系，和/或仅在单一性别中存在。''',
                    [
                        ['日剂量（mg/kg）', '0（对照）', '0（对照）', '4', '2.5', '15', '7.5', '75', '50'],
                        ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ],
                    '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 4 | 2.5 | 15 | 7.5 | 75 | 50 |
    | 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
                )
                == (
                    [
                        ['日剂量（mg/kg）', '0（对照）', '0（对照）', '4', '2.5', '15', '7.5', '75', '50'],
                        ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                        [
                            '大体病理学（第29天）',
                            '大体病理学（第29天）',
                            '大体病理学（第29天）',
                            '大体病理学（第29天）',
                            '大体病理学（第29天）',
                            '大体病理学（第29天）',
                            '大体病理学（第29天）',
                            '大体病理学（第29天）',
                            '大体病理学（第29天）',
                        ],
                        ['胸腺', '胸腺', '胸腺', '胸腺', '胸腺', '胸腺', '胸腺', '胸腺', '胸腺'],
                        ['体积减小', '-', '-', '-', '-', '-', '-', '-', '-']
                    ],
                    [],
                )
        )


# 267.7 续表 - 濒死/死亡 LLM请求方法测试
@pytest.mark.asyncio(loop_scope='session')
class TestGenerateTableNearDeathOrDying:
    async def test_generate_table_near_death_or_dying(self):
        assert (
            await generate_table_near_death_or_dying(
                '',
                '''| 死亡编码 | 给药剂量 | 死亡日 | 组别/性别 | 动物号 |
| 人道终点安乐死 | 50 mg/kg/day | 7 | 4/雌性 | 25340 |
| 人道终点安乐死 | 50 mg/kg/day | 15 | 4/雌性 | 25312 |
| 发现死亡 | 50mg/kg/day | 15 | 4/雌性 | 25338 |
| 发现死亡 | 50 mg/kg/day | 16 | 4/雄性 | 25339 |
| 发现死亡 | 15 mg/kg/day | 17 | 4/雄性 | 25555 |
| 发现死亡 | 5 mg/kg/day | 18 | 4/雌性 | 25666 |''',
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '5', '15', '15', '50', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 5 | 15 | 15 | 50 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '5', '15', '15', '50', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ['濒死/死亡', '0', '0', '0', '1', '1', '0', '1', '1'],
                ],
                [],
            )
        )

        assert (
            await generate_table_near_death_or_dying(
                '',
                '''| 死亡编码 | 给药剂量 | 死亡日 | 组别/性别 | 动物号 |
| 人道终点安乐死 | 50 mg/kg/day | 7 | 4/雌性 | 25340 |
| 人道终点安乐死 | 50 mg/kg/day | 15 | 4/雌性 | 25312 | ''',
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '5', '15', '15', '50', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 5 | 15 | 15 | 50 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '5', '15', '15', '50', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ['濒死/死亡', '0', '0', '0', '0', '0', '0', '0', '0'],
                ],
                [],
            )
        )

        assert (
            await generate_table_near_death_or_dying(
                '',
                '''| 死亡编码 | 给药剂量 | 死亡日 | 组别/性别 | 动物号 |
| 人道终点安乐死 | 50 mg/kg/day | 7 | 4/雌性 | 25340 |
| 人道终点安乐死 | 50 mg/kg/day | 15 | 4/雌性 | 25312 |
| 发现死亡 | 50mg/kg/day | 15 | 4/雌性 | 25338 |
| 发现死亡 | 50mg/kg/day | 15 | 4/雌性 | 25338 |
| 发现死亡 | 50 mg/kg/day | 16 | 4/雄性 | 25339 |
| 发现死亡 | 15 mg/kg/day | 17 | 4/雄性 | 25555 |
| 发现死亡 | 15 mg/kg/day | 17 | 4/雄性 | 25555 |
| 发现死亡 | 15 mg/kg/day | 17 | 4/雄性 | 25555 |
| 发现死亡 | 15 mg/kg/day | 17 | 4/雄性 | 25555 |
| 发现死亡 | 5 mg/kg/day | 18 | 4/雌性 | 25666 |''',
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '5', '15', '15', '50', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 5 | 15 | 15 | 50 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '5', '15', '15', '50', '50'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ['濒死/死亡', '0', '0', '0', '1', '4', '0', '1', '2'],
                ],
                [],
            )
        )

        # C-231610，7.2.1非计划死亡内表格数据
        assert (
                await generate_table_near_death_or_dying(
                    '',
                    '''| 死亡编码 | 给药剂量 | 死亡日 | 组别/性别 | 动物号 |
    | 人道终点安乐死 | 50 mg/kg/day | 7 | 4/雌性 | 25340 |
    | 人道终点安乐死 | 50 mg/kg/day | 8 | 4/雌性 | 25339 |
    | 人道终点安乐死 | 50 mg/kg/day | 12 | 4/雌性 | 25320 |
    | 人道终点安乐死 | 50 mg/kg/day | 15 | 4/雌性 | 25312 |
    | 发现死亡 | 50mg/kg/day | 15 | 4/雌性 | 25338 |
    | 发现死亡 | 50 mg/kg/day | 16 | 4/雌性 | 25339 |
    | 发现死亡 | 15 mg/kg/day | 17 | 4/雄性 | 25555 |
    | 发现死亡 | 2.5 mg/kg/day | 18 | 4/雌性 | 25666 |''',
                    [
                        ['日剂量（mg/kg）', '0（对照）', '0（对照）', '2.5', '2.5', '15', '15', '50', '50'],
                        ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ],
                    '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 2.5 | 2.5 | 15 | 15 | 50 | 50 |
    | 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
                )
                == (
                    [
                        ['日剂量（mg/kg）', '0（对照）', '0（对照）', '2.5', '2.5', '15', '15', '50', '50'],
                        ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                        ['濒死/死亡', '0', '0', '0', '1', '1', '0', '0', '2'],
                    ],
                    [],
                )
        )

        # C-232003，7.2.1死亡动物情况(无表格)
        # C-230614，7.2.1死亡动物情况(无表格)
        # C-230613，7.2.1死亡动物情况(无表格)
        assert (
                await generate_table_near_death_or_dying(
                    '',
                    '',
                    [
                        ['日剂量（mg/kg）', '0（对照）', '0（对照）', '2.5', '2.5', '15', '15', '50', '50'],
                        ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ],
                    '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 2.5 | 2.5 | 15 | 15 | 50 | 50 |
    | 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
                )
                == (
                    [
                        ['日剂量（mg/kg）', '0（对照）', '0（对照）', '2.5', '2.5', '15', '15', '50', '50'],
                        ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                        ['濒死/死亡', '0', '0', '0', '0', '0', '0', '0', '0'],
                    ],
                    [],
                )
        )


# 267.7 续表 - 尿液分析 LLM请求方法测试
@pytest.mark.asyncio(loop_scope='session')
class TestGenerateTableUrinalysis:
    async def test_generate_table_urinalysis(self):
        assert (
            await generate_table_urinalysis(
                '''尿液分析个体数据见附录1。
终末期解剖(第29天)
与新药001相关的的尿液分析参数改变为:给予≥5mg/kg/day剂量组雄性动物和给予50mg/kg/day剂量组雌性动物PRO及LEU升高;
恢复期解剖(第57天)
与新药001相关的尿液分析参数改变为：给予≥5 mg/kg/day剂量组雌性动物和给予15 mg/kg/day剂量组雄性动物尿胆原URO降低''',
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '5', '10', '10', '15', '15'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 5 | 10 | 10 | 15 | 15 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '5', '10', '10', '15', '15'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    [
                        '尿液分析（第29天）',
                        '尿液分析（第29天）',
                        '尿液分析（第29天）',
                        '尿液分析（第29天）',
                        '尿液分析（第29天）',
                        '尿液分析（第29天）',
                        '尿液分析（第29天）',
                        '尿液分析（第29天）',
                        '尿液分析（第29天）',
                    ],
                    ['PRO', '-', '-', '↑', '-', '↑', '-', '↑', '-'],
                    ['LEU', '-', '-', '↑', '-', '↑', '-', '↑', '-'],
                ],
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '5', '10', '10', '15', '15'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    [
                        '尿液分析（第57天）',
                        '尿液分析（第57天）',
                        '尿液分析（第57天）',
                        '尿液分析（第57天）',
                        '尿液分析（第57天）',
                        '尿液分析（第57天）',
                        '尿液分析（第57天）',
                        '尿液分析（第57天）',
                        '尿液分析（第57天）',
                    ],
                    ['URO', '-', '-', '-', '↓', '-', '↓', '↓', '↓'],
                ],
            )
        )

        # C-230613，7.7.4尿液分析（只不过药名：NS-041和谐为新药001）
        assert (
            await generate_table_urinalysis(
                '''尿液分析个体数据见附录2。
终末期解剖(第29天)，未见与新药001相关的的尿液分析参数改变;
恢复期解剖(第57天)，未见与新药001相关的的尿液分析参数改变''',
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '5', '10', '10', '15', '15'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 5 | 10 | 10 | 15 | 15 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '5', '10', '10', '15', '15'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ['尿液分析', '-', '-', '-', '-', '-', '-', '-', '-'],
                ],
                [],
            )
        )

        assert (
            await generate_table_urinalysis(
                '''液分析个体数据见附录2。
终末期解剖(第29天)，与新药001相关的的尿液分析参数改变为:给予≥10 mg/kg/day剂量组雄性动物和给予5 mg/kg/day剂量组雌性动物，尿液中的葡萄糖（GLU）和蛋白（PRO）水平显著升高;
恢复期解剖(第57天)，未见与新药001相关的的尿液分析参数改变''',
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '5', '10', '10', '15', '15'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 5 | 10 | 10 | 15 | 15 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == (
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '5', '10', '10', '15', '15'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    [
                        '尿液分析（第29天）',
                        '尿液分析（第29天）',
                        '尿液分析（第29天）',
                        '尿液分析（第29天）',
                        '尿液分析（第29天）',
                        '尿液分析（第29天）',
                        '尿液分析（第29天）',
                        '尿液分析（第29天）',
                        '尿液分析（第29天）',
                    ],
                    ['GLU', '-', '-', '-', '↑', '↑', '-', '↑', '-'],
                    ['PRO', '-', '-', '-', '↑', '↑', '-', '↑', '-'],
                ],
                [],
            )
        )

        assert (
            await generate_table_urinalysis(
                '''液分析个体数据见附录2。
终末期解剖(第29天)，未见与新药001相关的的尿液分析参数改变;
恢复期解剖(第57天)，与新药001相关的的尿液分析参数改变为:给予≥10 mg/kg/day剂量组雄性动物和给予5 mg/kg/day剂量组雌性动物，尿液中的葡萄糖（GLU）和蛋白（PRO）水平显著升高''',
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '5', '10', '10', '15', '15'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ],
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 5 | 10 | 10 | 15 | 15 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == (
                [],
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '5', '10', '10', '15', '15'],
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    [
                        '尿液分析（第57天）',
                        '尿液分析（第57天）',
                        '尿液分析（第57天）',
                        '尿液分析（第57天）',
                        '尿液分析（第57天）',
                        '尿液分析（第57天）',
                        '尿液分析（第57天）',
                        '尿液分析（第57天）',
                        '尿液分析（第57天）',
                    ],
                    ['GLU', '-', '-', '-', '↑', '↑', '-', '↑', '-'],
                    ['PRO', '-', '-', '-', '↑', '↑', '-', '↑', '-'],
                ],
            )
        )

        # C-231610，7.7.4尿液分析
        assert (
                await generate_table_urinalysis(
                    '''终末期解剖(第29天)
    与同期溶媒对照组相比,与新药001相关的的尿液分析参数改变为:给予≥5mg/kg/day剂量组雄性动物和给予50mg/kg/day剂量组雌性动物PRO及LEU升高;给予≥7.5mg/kg/day剂量组雌性动物和给予15mg/kg/day剂量组雄性动物尿胆原URO升高。以上改变均考虑与肾功能损伤有关,与镜检皮质和/或髓质肾小管变性及单个细胞坏死一致。
                       恢复期解剖(第57天)
    与同期溶媒对照组相比,未见与新药001相关的尿液分析参数改变,表明已完全恢复。所有其他临床病理学参数改变无论是否具有统计学意义、或基于严重程度可忽略不计、和缺乏剂量反应关系、个体值相差较大和/或仅在单一性别中存在,均考虑为偶发的且与新药001不相关。''',
                    [
                        ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '5', '7.5', '7.5', '15', '15', '50', '50'],
                        ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ],
                    '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 5 | 7.5 | 7.5 | 15 | 15 | 50 | 50 |
    | 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: | M: | F: |''',
                )
                == (
                    [
                        ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '5', '7.5', '7.5', '15', '15', '50', '50'],
                        ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                        [
                            '尿液分析（第29天）',
                            '尿液分析（第29天）',
                            '尿液分析（第29天）',
                            '尿液分析（第29天）',
                            '尿液分析（第29天）',
                            '尿液分析（第29天）',
                            '尿液分析（第29天）',
                            '尿液分析（第29天）',
                            '尿液分析（第29天）',
                            '尿液分析（第29天）',
                            '尿液分析（第29天）',
                        ],
                        ['PRO', '-', '-', '↑', '-', '↑', '-', '↑', '-', '↑', '↑'],
                        ['LEU', '-', '-', '↑', '-', '↑', '-', '↑', '-', '↑', '↑'],
                        ['URO', '-', '-', '-', '-', '-', '↑', '↑', '↑', '-', '↑'],
                    ],
                    [],
                )
        )

        # C-232003，7.10.4尿液分析
        assert (
                await generate_table_urinalysis(
                    '''个体尿液检查结果见附录17。
                    终末期解剖（第29天）和恢复期解剖（第57天）与给药前基线值（第-3天）和同期溶媒对照组相比，未见与新药001相关的尿液分析参数变化。
                    终末期和恢复期解剖动物其他所有临床病理学参数的改变，由于变化幅度较小、缺乏剂量反应关系和/或个体差异较大，均考虑为偶发的且与受试物不相关。''',
                    [
                        ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '5', '7.5', '7.5', '15', '15', '50', '50'],
                        ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ],
                    '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 5 | 7.5 | 7.5 | 15 | 15 | 50 | 50 |
    | 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: | M: | F: |''',
                )
                == (
                    [
                        ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '5', '7.5', '7.5', '15', '15', '50', '50'],
                        ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                        ['尿液分析', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-'],
                    ],
                    [],
                )
        )

        # C-230614，7.10.4尿液分析
        assert (
                await generate_table_urinalysis(
                    '''尿液检查结果见附录16。
                    终末期解剖（第29天）与给药前基线值（-5天）以及同期溶媒对照组相比，未见与NS-041相关的尿液分析参数改变。
                    恢复期解剖（第57天）与给药前基线值（-5天）以及同期溶媒对照组相比，未见与NS-041相关的尿液分析参数改变。
                    以上所有与NS-041相关的临床病理学参数改变由于严重程度低，均不考虑为不良改变。所有其他临床病理学参数改变无论是否具有统计学意义、或基于严重程度可忽略不计、和缺乏剂量反应关系、个体值相差较大和/或仅在单一性别中存在，均考虑为偶发的且与NS-041不相关。''',
                    [
                        ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '5', '7.5', '7.5', '15', '15', '50', '50'],
                        ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                    ],
                    '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 5 | 7.5 | 7.5 | 15 | 15 | 50 | 50 |
    | 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: | M: | F: |''',
                )
                == (
                    [
                        ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '5', '7.5', '7.5', '15', '15', '50', '50'],
                        ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                        ['尿液分析', '-', '-', '-', '-', '-', '-', '-', '-', '-', '-'],
                    ],
                    [],
                )
        )


class TestHemagglutinationTable:
    @pytest.mark.asyncio(loop_scope='session')
    async def test_extract(self):
        assert (
            await HemagglutinationTable.extract(
                '''| 性别 | 雄性 | 雄性 | 雄性 | 雄性 | 雌性 | 雌性 | 雌性 | 雌性 |
| 组别 | 1 | 2 | 3 | 4 | 1 | 2 | 3 | 4 |
| 受试物 | 溶媒 | 新药001 | 新药001 | 新药001 | 溶媒 | 新药001 | 新药001 | 新药001 |
| 剂量（mg/kg/day） | 0 | 5 | 15 | 75 | 0 | 2.5 | 7.5 | 50 |
| 检查动物数 | 10 | 10 | 10 | 10 | 10 | 10 | 10 | 10 |
| ALB（g/L） | 46.81 | 46.82 | 46.53 | 46.93 | 52.2 | 50.72 | 49.13* | 45.94*** |
| (% 差值) | - | 0 | -1 | 0 | - | -3 | -6 | -12 |
| GLO （g/L） | 17.04 | 16.24 | 16.75 | 18.84** | 14.4 | 14.54 | 14.87 | 17.14*** |
| (% 差值) | - | -5 | -2 | +11 | - | +1 | +3 | +19 |
| A/G （Ratio） | 2.764 | 2.896 | 2.789 | 2.503* | 3.645 | 3.51 | 3.329 | 2.696*** |
| (% 差值) | - | +5 | +1 | -9 | - | -4 | -9 | -26 |
| 注：表中所示结果为组均值，粗体数值考虑受试物相关。 “–”= 不适用；“+/-” = 升高/降低。（%差值）= 给药组均值与对照组均值之差相对对照组均值的百分比，四舍五入到整数。统计分析：Dunnett Test：*- 与对照组有显著差异（P < 0.05）；** - 与对照组有显著差异（P < 0.01）；*** - 与对照组有显著差异（P < 0.001）。 | 注：表中所示结果为组均值，粗体数值考虑受试物相关。 “–”= 不适用；“+/-” = 升高/降低。（%差值）= 给药组均值与对照组均值之差相对对照组均值的百分比，四舍五入到整数。统计分析：Dunnett Test：*- 与对照组有显著差异（P < 0.05）；** - 与对照组有显著差异（P < 0.01）；*** - 与对照组有显著差异（P < 0.001）。 | 注：表中所示结果为组均值，粗体数值考虑受试物相关。 “–”= 不适用；“+/-” = 升高/降低。（%差值）= 给药组均值与对照组均值之差相对对照组均值的百分比，四舍五入到整数。统计分析：Dunnett Test：*- 与对照组有显著差异（P < 0.05）；** - 与对照组有显著差异（P < 0.01）；*** - 与对照组有显著差异（P < 0.001）。 | 注：表中所示结果为组均值，粗体数值考虑受试物相关。 “–”= 不适用；“+/-” = 升高/降低。（%差值）= 给药组均值与对照组均值之差相对对照组均值的百分比，四舍五入到整数。统计分析：Dunnett Test：*- 与对照组 有显著差异（P < 0.05）；** - 与对照组有显著差异（P < 0.01）；*** - 与对照组有显著差异（P < 0.001）。 | 注：表中所示结果为组均值，粗体数值考虑受试物相关。 “–”= 不适用；“+/-” = 升高/降低。（%差值）= 给药组均值与对照组均值之差相对对照组均值的百分比，四舍五入到整数。统计分析：Dunnett Test：*- 与对照组有显著差异（P < 0.05）；** - 与对照组有显著差异（P < 0.01）；*** - 与对照组有显著差异（P < 0.001）。 | 注：表中所示结果为组均值，粗体数值考虑受试物相关。 “–”= 不适用；“+/-” = 升高/降低。（%差值）= 给药组均值与对照组均值之差相对对照组均值的百分比，四舍五入到整数。统计分析：Dunnett Test：*- 与对照组有显著差异（P < 0.05）；** - 与对照组有显著差异（P < 0.01）；*** - 与对照组有显著差异（P < 0.001）。 | 注：表中所示结果为组均值，粗体数值考虑受试物相关。 “–”= 不适用；“+/-” = 升高/降低。（%差值）= 给药组均值与对照组均值之差相对对照组均值的百分比，四舍五入到整数。统计分析：Dunnett Test：*- 与对照组有显著差异（P < 0.05）；** - 与对照组有显 著差异（P < 0.01）；*** - 与对照组有显著差异（P < 0.001）。 | 注：表中所示结果为组均值，粗体数值考虑受试物相关。 “–”= 不适用；“+/-” = 升高/降低。（%差值）= 给药组均值与对照组均值之差相对对照组均值的百分比，四舍五入到整数。统计分析：Dunnett Test：*- 与对照组有显著差异（P < 0.05）；** - 与对照组有显著差异（P < 0.01）；*** - 与对照组有显著差异（P < 0.001）。 | 注：表中所示结果为组均值，粗体数值考虑受试物相关。 “–”= 不适用；“+/-” = 升高/降低。（%差值）= 给药组均值与对照组均值之差相对对照组均值的百分比，四舍五入到整数。统计分析：Dunnett Test：*- 与对照组有显著差异（P < 0.05）；** - 与对照组有显著差异（P < 0.01）；*** - 与对照组有显著差异（P < 0.001）。 |''',
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M:10 | F:10 | M:10 | F:10 | M:10 | F:10 | M:10 | F:10 |
| ALB（g/L） | 46.81 | 52.2 | 46.82 | 50.72 | 46.53 | 49.13* | 46.93 | 45.94*** |
| (% 差值) | - | - | 0 | -3 | -1 | -6 | 0 | -12 |
| GLO （g/L） | 17.04 | 14.4 | 16.24 | 14.54 | 16.75 | 14.87 | 18.84** | 17.14*** |
| (% 差值) | - | - | -5 | +1 | -2 | +3 | +11 | +19 |
| A/G （Ratio） | 2.764 | 3.645 | 2.896 | 3.51 | 2.789 | 3.329 | 2.503* | 2.696*** |
| (% 差值) | - | - | +5 | -4 | +1 | -9 | -9 | -26 |'''
        )

        assert (
            await HemagglutinationTable.extract(
                '''| 性别 | 雄性 | 雄性 | 雄性 | 雄性 | 雌性 | 雌性 | 雌性 | 雌性 |
| 受试物 | 溶媒 | 新药001 | 新药001 | 新药001 | 溶媒 | 新药001 | 新药001 | 新药001 |
| 剂量（mg/kg/day) | 0 | 1.5 | 5 | 15 | 0 | 1.5 | 5 | 15 |
| 检查动物数 | 5 | 5 | 5 | 5 | 5 | 5 | 5 | 5 |
| APTT（seconds） | 147.22 | 146.46 | 145.84 | 141.84 | 146.14 | 147.04 | 145.86 | 144.66 |
| （%差值） | - | -1 | -1 | -4 | - | +1 | 0 | -1 |
| PT (mmol/L) | 108.2 | 108.68 | 108.4 | 103.72 | 107.86 | 109.12 | 108.16 | 106.1 |
| （%差值） | - | 0 | 0 | -4 | - | +1 | 0 | -2 |''',
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 1.5 | 1.5 | 5 | 5 | 15 | 15 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 1.5 | 1.5 | 5 | 5 | 15 | 15 |
| 动物数量 | M:5 | F:5 | M:5 | F:5 | M:5 | F:5 | M:5 | F:5 |
| APTT（seconds） | 147.22 | 146.14 | 146.46 | 147.04 | 145.84 | 145.86 | 141.84 | 144.66 |
| （%差值） | - | - | -1 | +1 | -1 | 0 | -4 | -1 |
| PT (mmol/L) | 108.2 | 107.86 | 108.68 | 109.12 | 108.4 | 108.16 | 103.72 | 106.1 |
| （%差值） | - | - | 0 | +1 | 0 | 0 | -4 | -2 |'''
        )

        # C-231610，7.7.3凝血表格终末期解剖数据
        assert (
                await HemagglutinationTable.extract(
                    '''| 性别 | 雄性 | 雄性 | 雄性 | 雄性 | 雌性 | 雌性 | 雌性 | 雌性 |
| 组别 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 |
| 受试物 | 溶媒 | KC1086 | KC1086 | KC1086 | 溶媒 | KC1086 | KC1086 | KC1086 |
| 剂量（mg/kg/day） | 0 | 5 | 15 | 75 | 0 | 2.5 | 7.5 | 50 |
| 检查动物数 | 10 | 10 | 10 | 10 | 10 | 10 | 10 | 10 |
| APTT（seconds） | 23.93 | 23.90 | 22.09 | 20.52 | 20.16 | 18.41 | 19.38 | 17.65 |
| (% 差值) | - | -0.13 | -7.69 | -14.25 | - | -8.67 | -3.87 | -12.46 |
| PT （seconds） | 22.95 | 21.30 | 20.55 | 19.22 | 20.88 | 18.78 | 19.43 | 18.56 |
| (% 差值) | - | -7.19 | -10.46 | -16.25 | - | -10.06 | -6.94 | -11.11 |''',
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
                )
                == '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M:10 | F:10 | M:10 | F:10 | M:10 | F:10 | M:10 | F:10 |
| APTT（seconds） | 23.93 | 20.16 | 23.90 | 18.41 | 22.09 | 19.38 | 20.52 | 17.65 |
| (% 差值) | - | - | -0.13 | -8.67 | -7.69 | -3.87 | -14.25 | -12.46 |
| PT （seconds） | 22.95 | 20.88 | 21.30 | 18.78 | 20.55 | 19.43 | 19.22 | 18.56 |
| (% 差值) | - | - | -7.19 | -10.06 | -10.46 | -6.94 | -16.25 | -11.11 |'''
        )

        # C-232003，7.10.3凝血功能测试当中无表格
        # C-230614，7.10.3凝血功能测试当中无表格
        # C-230613，7.10.3凝血功能测试当中无表格

    def test_parse_table(self):
        header1 = [
            ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
            ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
        ]

        assert (
            HemagglutinationTable.parse_table(
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M:10 | F:10 | M:10 | F:10 | M:10 | F:10 | M:10 | F:10 |
| ALB（g/L） | 46.81 | 52.2 | 46.82 | 50.72 | 46.53 | 49.13* | 46.93 | 45.94*** |
| (% 差值) | - | - | 0 | -3 | -1 | -6 | 0 | -12 |
| GLO （g/L） | 17.04 | 14.4 | 16.24 | 14.54 | 16.75 | 14.87 | 18.84** | 17.14*** |
| (% 差值) | - | - | -5 | +1 | -2 | +3 | +11 | +19 |
| A/G （Ratio） | 2.764 | 3.645 | 2.896 | 3.51 | 2.789 | 3.329 | 2.503* | 2.696*** |
| (% 差值) | - | - | +5 | -4 | +1 | -9 | -9 | -26 |''',
                header1,
            )
            == [
                ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                [
                    '血凝',
                    '血凝',
                    '血凝',
                    '血凝',
                    '血凝',
                    '血凝',
                    '血凝',
                    '血凝',
                    '血凝',
                ],
                [
                    'ALB（g/L）\n（%变化百分比）',
                    '46.81',
                    '52.2',
                    '46.82\n（0%）',
                    '50.72\n（3%↓）',
                    '46.53\n（1%↓）',
                    '49.13*\n（6%↓）',
                    '46.93\n（0%）',
                    '45.94***\n（12%↓）',
                ],
                [
                    'GLO （g/L）\n（%变化百分比）',
                    '17.04',
                    '14.4',
                    '16.24\n（5%↓）',
                    '14.54\n（1%↑）',
                    '16.75\n（2%↓）',
                    '14.87\n（3%↑）',
                    '18.84**\n（11%↑）',
                    '17.14***\n（19%↑）',
                ],
                [
                    'A/G （Ratio）\n（%变化百分比）',
                    '2.764',
                    '3.645',
                    '2.896\n（5%↑）',
                    '3.51\n（4%↓）',
                    '2.789\n（1%↑）',
                    '3.329\n（9%↓）',
                    '2.503*\n（9%↓）',
                    '2.696***\n（26%↓）',
                ],
            ]
        )

        header2 = [
            ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'],
            ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
        ]

        assert (
            HemagglutinationTable.parse_table(
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 1.5 | 1.5 | 5 | 5 | 15 | 15 |
| 动物数量 | M:5 | F:5 | M:5 | F:5 | M:5 | F:5 | M:5 | F:5 |
| Na（mmol/L） | 147.22 | 146.14 | 146.46 | 147.04 | 145.84 | 145.86 | 141.84<br>*** | 144.66 |
| （% 差值） | - | - | -1 | +1 | -1 | 0 | -4 | -1 |
| Cl (mmol/L) | 108.2 | 107.86 | 108.68 | 109.12 | 108.4 | 108.16 | 103.72<br>** | 106.1 |
| （% 差值） | - | - | 0 | +1 | 0 | 0 | -4 | -2 |''',
                header2,
            )
            == [
                ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'],
                ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                [
                    '血凝',
                    '血凝',
                    '血凝',
                    '血凝',
                    '血凝',
                    '血凝',
                    '血凝',
                    '血凝',
                    '血凝',
                ],
                [
                    'Na（mmol/L）\n（%变化百分比）',
                    '147.22',
                    '146.14',
                    '146.46\n（1%↓）',
                    '147.04\n（1%↑）',
                    '145.84\n（1%↓）',
                    '145.86\n（0%）',
                    '141.84\n***\n（4%↓）',
                    '144.66\n（1%↓）',
                ],
                [
                    'Cl (mmol/L)\n（%变化百分比）',
                    '108.2',
                    '107.86',
                    '108.68\n（0%）',
                    '109.12\n（1%↑）',
                    '108.4\n（0%）',
                    '108.16\n（0%）',
                    '103.72\n**\n（4%↓）',
                    '106.1\n（2%↓）',
                ],
            ]
        )
