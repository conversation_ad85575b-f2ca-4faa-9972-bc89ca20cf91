import pytest

from app.extractors.table_7_3_abnormal_electrocardiogram import (
    AbnormalElectrocardiogramTable,
)


class TestAbnormalElectrocardiogramTable:
    @pytest.mark.asyncio(loop_scope='session')
    async def test_extract(self):
        # 重复给药关键: 4232-************, project_id: 328 , doc_id: 1442
        assert (
            await AbnormalElectrocardiogramTable.extract(
                '''| 指标 | 比较组 | 剂量（mg/kg） | 性别 | 实验日## | 对照组 | 变化幅度# | P* | 毒性反应 | 主要判定依据 | 
| 心率（HR） | 供试品中剂量组 | 15 | M | D1_1h | 溶媒对照 | 3%↓ | ≥0.05 | 可能 | 相比对照和给药前均有降低 | 
| 心率（HR） | 供试品中剂量组 | 7.5 | F | D1_1h | 溶媒对照 | 15%↓ | <0.05 | 是 | 相比对照和给药前均有降低 | 
| 心率（HR） | 供试品高剂量组 | 75 | M | D1_1h | 溶媒对照 | 9%↓ | ≥0.05 | 可能 | 相比对照和给药前均有降低 | 
| 心率（HR） | 供试品高剂量组 | 50 | F | D1_1h | 溶媒对照 | 17%↓ | <0.05 | 是 | 相比对照和给药前均有降低 | 
| QRS波群时限（QRS） | 溶媒对照 | 0 | F | D8_1h | 阴性对照 | 26%↑ | <0.05 | 否 | 与给药前相比无明显差异 | 
| QRS波群时限（QRS） | 供试品低剂量组 | 5 | M | D1_1h | 阴性对照 | 16%↓ | <0.05 | 否 | 与给药前相比无明显差异 | 
| QRS波群时限（QRS） | 供试品低剂量组 | 2.5 | F | D28_1h | 溶媒对照 | 24%↓ | <0.01 | 否 | 与给药前相比无明显差异 | 
| QRS波群时限（QRS） | 供试品中剂量组 | 7.5 | F | D1_24h | 阴性对照 | 24%↓ | <0.01 | 否 | 动物正常范围内 | 
| QRS波群时限（QRS） | 供试品中剂量组 | 7.5 | F | D28_1h | 阴性对照 | 33%↑ | <0.05 | 否 | 动物正常范围内 | 
| QRS波群时限（QRS） | 供试品高剂量组 | 50 | F | D8_1h | 溶媒对照 | 22%↓ | <0.05 | 否 | 与给药前相比无明显差异 | 
| QRS波群时限（QRS） | 供试品高剂量组 | 50 | F | D28_1h | 阴性对照 | 29%↑ | <0.05 | 否 | 与给药前相比无明显差异 | 
| QT期间（QT） | 供试品中剂量组 | 15 | M | D1_1h | 阴性对照 | 18%↑ | <0.01 | 是 | 相对对照和给药前均有明显延长（分别32ms、19s） | 
| QT期间（QT） | 供试品中剂量组 | 15 | M | D28_1h | 阴性对照 | 16%↑ | <0.01 | 否 | 与给药期相比无差异 | 
| QT期间（QT） | 供试品中剂量组 | 7.5 | F | D1_1h | 阴性对照 | 28%↑ | <0.01 | 是 | 相比对照和给药前均有明显延长（Day1，分别延长46-47ms、28ms、Day8，分别延长29-34ms、6ms） | 
| QT期间（QT） | 供试品中剂量组 | 7.5 | F | D1_1h | 溶媒对照 | 27%↑ | <0.01 | 是 | 相比对照和给药前均有明显延长（Day1，分别延长46-47ms、28ms、Day8，分别延长29-34ms、6ms） | 
| QT期间（QT） | 供试品中剂量组 | 7.5 | F | D8_1h | 阴性对照 | 21%↑ | <0.05 | 是 | 相比对照和给药前均有明显延长（Day1，分别延长46-47ms、28ms、Day8，分别延长29-34ms、6ms） | 
| QT期间（QT） | 供试品中剂量组 | 7.5 | F | D8_1h | 溶媒对照 | 18%↑ | <0.05 | 是 | 相比对照和给药前均有明显延长（Day1，分别延长46-47ms、28ms、Day8，分别延长29-34ms、6ms） | 
| QT期间（QT） | 供试品高剂量组 | 75 | M | D1_1h | 阴性对照 | 22%↑ | <0.01 | 是 | 相比对照和给药器前均有名称延长（分别延长32-40ms、37ms） | 
| QT期间（QT） | 供试品高剂量组 | 75 | M | D1_1h | 溶媒对照 | 17%↑ | <0.05 | 是 | 相比对照和给药器前均有名称延长（分别延长32-40ms、37ms） | 
| QT期间（QT） | 供试品高剂量组 | 75 | M | D1_1h | 阴性对照 | 15%↑ | <0.05 | 否 | 与给药前相比无差异 | 
| QT期间（QT） | 供试品高剂量组 | 75 | M | D1_24h | 阴性对照 | 15%↑ | <0.05 | 否 | 与给药前相比无差异 | 
| QT期间（QT） | 供试品高剂量组 | 50 | F | D28_1h | 阴性对照 | 37%↑ | <0.01 | 是 | 相比对照和给药器前均有名称延长（分别延长64-66ms、72ms） | 
| QT期间（QT） | 供试品高剂量组 | 50 | F | D1_1h | 溶媒对照 | 38%↑ | <0.01 | 是 | 相比对照和给药器前均有名称延长（分别延长64-66ms、72ms） | ''',
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |
| 心率（HR） | 心率（HR） | 心率（HR） | 心率（HR） | 心率（HR） | 心率（HR） | 心率（HR） | 心率（HR） | 心率（HR） |
| D1_1h | - | - | - | - | 3%↓ | 15%↓ | 9%↓ | 17%↓ |
| QRS波群时限（QRS） | QRS波群时限（QRS） | QRS波群时限（QRS） | QRS波群时限（QRS） | QRS波群时限（QRS） | QRS波群时限（QRS） | QRS波群时限（QRS） | QRS波群时限（QRS） | QRS波群时限（QRS） |
| D28_1h | - | - | - | 24%↓ | - | - | - | - |
| D8_1h | - | - | - | - | - | - | - | 22%↓ |
| QT期间（QT） | QT期间（QT） | QT期间（QT） | QT期间（QT） | QT期间（QT） | QT期间（QT） | QT期间（QT） | QT期间（QT） | QT期间（QT） |
| D1_1h | - | - | - | - | - | 27%↑ | 17%↑ | 38%↑ |
| D8_1h | - | - | - | - | - | 18%↑ | - | - |'''
        )

        # C-231610，无心电图章节，遂无须补充
        # C-232003，7.9ECG参数，无对应表格，遂无须补充
        # C-230614，7.9ECG参数，无对应表格，遂无须补充
        # C-230613，无心电图章节，遂无须补充

        # gpt生成

    def test_parse_table(self):
        # 重复给药关键: 4232-************, project_id: 328 , doc_id: 1442
        header1 = [
            ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
            ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
        ]
        assert (
            AbnormalElectrocardiogramTable.parse_table(
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |
| 心率（HR） | 心率（HR） | 心率（HR） | 心率（HR） | 心率（HR） | 心率（HR） | 心率（HR） | 心率（HR） | 心率（HR） |
| D1_1h | - | - | - | - | 3%↓ | 15%↓ | 9%↓ | 17%↓ |
| QRS波群时限（QRS） | QRS波群时限（QRS） | QRS波群时限（QRS） | QRS波群时限（QRS） | QRS波群时限（QRS） | QRS波群时限（QRS） | QRS波群时限（QRS） | QRS波群时限（QRS） | QRS波群时限（QRS） |
| D28_1h | - | - | - | 24%↓ | - | - | - | - |
| D8_1h | - | - | - | - | - | - | - | 22%↓ |
| QT期间（QT） | QT期间（QT） | QT期间（QT） | QT期间（QT） | QT期间（QT） | QT期间（QT） | QT期间（QT） | QT期间（QT） | QT期间（QT） |
| D1_1h | - | - | - | - | - | 27%↑ | 17%↑ | 38%↑ |
| D8_1h | - | - | - | - | - | 18%↑ | - | - |''',
                header1,
            )
            == [
                ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                ['心电图', '心电图', '心电图', '心电图', '心电图', '心电图', '心电图', '心电图', '心电图'],
                [
                    '心率（HR）',
                    '心率（HR）',
                    '心率（HR）',
                    '心率（HR）',
                    '心率（HR）',
                    '心率（HR）',
                    '心率（HR）',
                    '心率（HR）',
                    '心率（HR）',
                ],
                ['D1_1h\n（%变化幅度）', '-', '-', '-', '-', '3%↓', '15%↓', '9%↓', '17%↓'],
                [
                    'QRS波群时限（QRS）',
                    'QRS波群时限（QRS）',
                    'QRS波群时限（QRS）',
                    'QRS波群时限（QRS）',
                    'QRS波群时限（QRS）',
                    'QRS波群时限（QRS）',
                    'QRS波群时限（QRS）',
                    'QRS波群时限（QRS）',
                    'QRS波群时限（QRS）',
                ],
                ['D28_1h\n（%变化幅度）', '-', '-', '-', '24%↓', '-', '-', '-', '-'],
                ['D8_1h\n（%变化幅度）', '-', '-', '-', '-', '-', '-', '-', '22%↓'],
                [
                    'QT期间（QT）',
                    'QT期间（QT）',
                    'QT期间（QT）',
                    'QT期间（QT）',
                    'QT期间（QT）',
                    'QT期间（QT）',
                    'QT期间（QT）',
                    'QT期间（QT）',
                    'QT期间（QT）',
                ],
                ['D1_1h\n（%变化幅度）', '-', '-', '-', '-', '-', '27%↑', '17%↑', '38%↑'],
                ['D8_1h\n（%变化幅度）', '-', '-', '-', '-', '-', '18%↑', '-', '-'],
            ]
        )
