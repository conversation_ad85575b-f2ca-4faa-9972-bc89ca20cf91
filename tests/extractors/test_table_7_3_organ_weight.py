import pytest

from app.extractors.table_7_3_organ_weight import OrganWeightTable


class TestOrganWeightTable:
    @pytest.mark.asyncio(loop_scope='session')
    async def test_extract(self):
        # 重复给药关键: 4232-31003-231610, project_id: 172 , doc_id: 902
        # C-231610，7.8脏器重量，与下表一致
        assert (
            await OrganWeightTable.extract(
                '''| 性别 | 雄性 | 雄性 | 雄性 | 雄性 | 雌性 | 雌性 | 雌性 | 雌性 |
| 组别 | 1 | 2 | 3 | 4 | 1 | 2 | 3 | 4 |
| 受试物 | 溶媒 | 新药001 | 新药001 | 新药001 | 溶媒 | 新药001 | 新药001 | 新药001 |
| 剂量（mg/kg/day） | 0 | 5 | 15 | 75 | 0 | 2.5 | 7.5 | 50 |
| 检查动物数 | 10 | 10 | 10 | 10 | 10 | 10 | 10 | 10 |
| 脏器重量（g） | 12.6931 | 12.6014 | 12.4500 | 14.6611** | 7.2643 | 7.9534 | 8.3677* | 9.4006*** |
| （% 差值） | -- | -1 | -2 | +16 | -- | +9a | +15b | +29 |
| 脏体比（%） | 3.0781 | 3.0022 | 2.9971 | 3.5400*** | 3.0248 | 3.1041 | 3.3305** | 3.8423*** |
| （% 差值） | -- | -2 | -3 | +15 | -- | +3 | +10 | +27 |
| 脏脑比（%） | 600.75 | 598.457 | 585.512 | 701.401** | 369.603 | 407.107 | 422.406* | 486.713*** |
| （% 差值） | -- | 0 | -3 | +17 | -- | +10a | +14b | +32 |''',
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
                '正文表18：与新药001相关的脏器重量变化（肝脏）',
            )
            == '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M:10 | F:10 | M:10 | F:10 | M:10 | F:10 | M:10 | F:10 |
| 肝脏 | 肝脏 | 肝脏 | 肝脏 | 肝脏 | 肝脏 | 肝脏 | 肝脏 | 肝脏 |
| 脏器重量（g） | 12.6931 | 7.2643 | 12.6014 | 7.9534 | 12.4500 | 8.3677* | 14.6611** | 9.4006*** |
| （% 差值） | -- | -- | -1 | +9a | -2 | +15b | +16 | +29 |
| 脏体比（%） | 3.0781 | 3.0248 | 3.0022 | 3.1041 | 2.9971 | 3.3305** | 3.5400*** | 3.8423*** |
| （% 差值） | -- | -- | -2 | +3 | -3 | +10 | +15 | +27 |
| 脏脑比（%） | 600.75 | 369.603 | 598.457 | 407.107 | 585.512 | 422.406* | 701.401** | 486.713*** |
| （% 差值） | -- | -- | 0 | +10a | -3 | +14b | +17 | +32 |'''
        )

        # 重复给药关键: 4232-31003-232003, project_id: 172 , doc_id: 903, 没有表格
        # C-232003，7.11脏器重量，无表格
        # C-230614，7.11脏器重量，无表格

        # C-230613，7.8脏器重量
        assert (
                await OrganWeightTable.extract(
                    '''| 性别 | 雄性 | 雄性 | 雄性 | 雄性 | 雌性 | 雌性 | 雌性 | 雌性 |
| 剂量（mg/kg/day） | 0 | 2 | 3 | 5 | 0 | 2 | 3 | 5 |
| 脏器重量（g） | 12.9 | 12.75 | 12.66 | 13.07 | 7.75 | 8.46 | 8.77* | 8.67* |
| （% 差值） | -- | -1.13 | -1.86 | +1.31 | -- | +9.14 | +13.15 | +11.87 |
| 脏体比（%） | 3.1 | 3.13 | 3.23 | 3.31** | 3.13 | 3.36* | 3.54*** | 3.58*** |
| （% 差值） | -- | +1.01 | +4.26 | +6.93 | -- | +7.61 | +13.13 | +14.55 |
| 脏脑比（%） | 618.22 | 617.84 | 599.48 | 642.39 | 379.87 | 426.34* | 448.08** | 449.42** |
| （% 差值） | -- | -0.06 | -3.03 | +3.91 | -- | +12.23 | +17.96 | +18.31 |''',
            '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 2 | 2 | 3 | 3 | 5 | 5 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
                    '正文表18：与新药001相关的脏器重量变化（肝脏）',
                )
                == '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 2 | 2 | 3 | 3 | 5 | 5 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |
| 肝脏 | 肝脏 | 肝脏 | 肝脏 | 肝脏 | 肝脏 | 肝脏 | 肝脏 | 肝脏 |
| 脏器重量（g） | 12.9 | 7.75 | 12.75 | 8.46 | 12.66 | 8.77* | 13.07 | 8.67* |
| （% 差值） | -- | -- | -1.13 | +9.14 | -1.86 | +13.15 | +1.31 | +11.87 |
| 脏体比（%） | 3.1 | 3.13 | 3.13 | 3.36* | 3.23 | 3.54*** | 3.31** | 3.58*** |
| （% 差值） | -- | -- | +1.01 | +7.61 | +4.26 | +13.13 | +6.93 | +14.55 |
| 脏脑比（%） | 618.22 | 379.87 | 617.84 | 426.34* | 599.48 | 448.08** | 642.39 | 449.42** |
| （% 差值） | -- | -- | -0.06 | +12.23 | -3.03 | +17.96 | +3.91 | +18.31 |''')

        # 生成的用例
        assert (
            await OrganWeightTable.extract(
                '''| 性别 | 雄性 | 雄性 | 雄性 | 雄性 | 雌性 | 雌性 | 雌性 | 雌性 |
| 组别 | 1 | 2 | 3 | 4 | 1 | 2 | 3 | 4 |
| 受试物 | 溶媒 | 新药001 | 新药001 | 新药001 | 溶媒 | 新药001 | 新药001 | 新药001 |
| 剂量（mg/kg/day） | 0 | 5 | 15 | 75 | 0 | 2.5 | 7.5 | 50 |
| 检查动物数 | 10 | 10 | 10 | 10 | 10 | 10 | 10 | 10 |
| 脏器重量（g） | 12.6931 | 12.6014 | 12.4500 | 14.6611** | 7.2643 | 7.9534 | 8.3677* | 9.4006*** |
| （% 差值） | -- | -1 | -2 | +16 | -- | +9a | +15b | +29 |''',
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
                '正文表18：与新药001相关的脏器重量变化',
            )
            == '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M:10 | F:10 | M:10 | F:10 | M:10 | F:10 | M:10 | F:10 |
| 无内脏 | 无内脏 | 无内脏 | 无内脏 | 无内脏 | 无内脏 | 无内脏 | 无内脏 | 无内脏 |
| 脏器重量（g） | 12.6931 | 7.2643 | 12.6014 | 7.9534 | 12.4500 | 8.3677* | 14.6611** | 9.4006*** |
| （% 差值） | -- | -- | -1 | +9a | -2 | +15b | +16 | +29 |'''
        )

    def test_parse_table(self):
        # 重复给药关键: 4232-31003-231610, project_id: 172 , doc_id: 902
        header1 = [
            ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
            ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
        ]
        assert (
            OrganWeightTable.parse_table(
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M:10 | F:10 | M:10 | F:10 | M:10 | F:10 | M:10 | F:10 |
| 肝脏 | 肝脏 | 肝脏 | 肝脏 | 肝脏 | 肝脏 | 肝脏 | 肝脏 | 肝脏 |
| 脏器重量（g） | 12.6931 | 7.2643 | 12.6014 | 7.9534 | 12.4500 | 8.3677* | 14.6611** | 9.4006*** |
| （% 差值） | -- | -- | -1 | +9a | -2 | +15b | +16 | +29 |
| 脏体比（%） | 3.0781 | 3.0248 | 3.0022 | 3.1041 | 2.9971 | 3.3305** | 3.5400*** | 3.8423*** |
| （% 差值） | -- | -- | -2 | +3 | -3 | +10 | +15 | +27 |
| 脏脑比（%） | 600.75 | 369.603 | 598.457 | 407.107 | 585.512 | 422.406* | 701.401** | 486.713*** |
| （% 差值） | -- | -- | 0 | +10a | -3 | +14b | +17 | +32 |''',
                header1,
            )
            == [
                ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                [
                    '器官重量',
                    '器官重量',
                    '器官重量',
                    '器官重量',
                    '器官重量',
                    '器官重量',
                    '器官重量',
                    '器官重量',
                    '器官重量',
                ],
                [' 肝脏 ', ' 肝脏 ', ' 肝脏 ', ' 肝脏 ', ' 肝脏 ', ' 肝脏 ', ' 肝脏 ', ' 肝脏 ', ' 肝脏 '],
                [
                    '脏器重量（g）\n（%变化百分比）',
                    '12.6931',
                    '7.2643',
                    '12.6014\n（1%↓）',
                    '7.9534\n（9%↑）',
                    '12.4500\n（2%↓）',
                    '8.3677*\n（15%↑）',
                    '14.6611**\n（16%↑）',
                    '9.4006***\n（29%↑）',
                ],
                [
                    '脏体比（%）\n（%变化百分比）',
                    '3.0781',
                    '3.0248',
                    '3.0022\n（2%↓）',
                    '3.1041\n（3%↑）',
                    '2.9971\n（3%↓）',
                    '3.3305**\n（10%↑）',
                    '3.5400***\n（15%↑）',
                    '3.8423***\n（27%↑）',
                ],
                [
                    '脏脑比（%）\n（%变化百分比）',
                    '600.75',
                    '369.603',
                    '598.457\n（0%）',
                    '407.107\n（10%↑）',
                    '585.512\n（3%↓）',
                    '422.406*\n（14%↑）',
                    '701.401**\n（17%↑）',
                    '486.713***\n（32%↑）',
                ],
            ]
        )

        # 重复给药关键: 4232-31003-232003, project_id: 172 , doc_id: 903  这篇文章没数据
