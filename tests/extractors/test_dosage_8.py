import pytest

from app.extractors.dosage_8 import (
    NumberOfParallelCultures,
    PositiveControlSample,
    MetabolicSystem,
    CytotoxicEffects8,
    GenotoxicEffects8,
)

@pytest.mark.asyncio(loop_scope='session')
class TestNumberOfParallelCultures:
    async def test_extract(self):

        # 遗传毒性-回复突变: 42331-31006-231925.docx
        # D-231925，8.2.2阳性对照值
        assert (
            await NumberOfParallelCultures.extract(
                "测试菌株阳性对照组的平均回复突变菌落均数至少是平行溶媒对照组回复突变菌落均数的3倍以上。"
            )
            == "3倍以上"
        )

        # GPT生成的案例
        assert (
            await NumberOfParallelCultures.extract(
                "输入: 试验中阳性对照组的平均微核率显著高于溶媒对照组，且在统计学上具有显著差异，为平行溶媒对照组的4倍以上。"
            )
            == "4倍以上"
        )
        
        # GPT生成的案例
        assert (
            await NumberOfParallelCultures.extract(
                "阳性对照组中，DNA损伤率的平均值达到了平行溶媒对照组的2.5倍以上，并且具有统计学显著性。"
            )
            == "2.5倍以上"
        )

        # GPT生成的案例
        assert (
            await NumberOfParallelCultures.extract(
                "在体外基因突变试验中，阳性对照组的基因突变频率是溶媒对照组的5倍以上，显示阳性反应明显。"
            )
            == "5倍以上"
        )


@pytest.mark.asyncio(loop_scope='session')
class TestPositiveControlSample:
    async def test_extract(self):

        # 遗传毒性-回复突变: 42331-31006-231925.docx
        # D-231925，3.3阳性对照品信息
        assert (
            await PositiveControlSample.extract(
                """所有菌株（TA98、TA100、TA1535、TA1537和WP2 uvrA）加代谢活化系统时使用的阳性对照品为：
名称：	2-氨基蒽（2-AA）
批号：	STBJ3963
CAS号：	613-13-8
失效日期：	2024年01月03日
保存条件：	5±3C，避光
性状：	粉末
供应商：	Sigma-Aldrich

名称：	2-氨基蒽（2-AA）
批号：	STBJ3963
CAS 号:	613-13-8
失效日期：	2025年03月31日
保存条件：	5±3C，避光
性状：	粉末
供应商：	Sigma-Aldrich

菌株TA98不加代谢活化系统时使用的阳性对照品为：
名称：	2-硝基芴（2-NF）
批号：	STBF2425V
CAS 号:	607-57-8
失效日期：	2025年06月05日
保存条件：	室温，避光
性状：	粉末
供应商：	Sigma-Aldrich
	
菌株TA1537不加代谢活化系统时使用的阳性对照品为：
名称：	ICR-191
批号：	SLCM3877
CAS号：	17070-45-0
失效日期：	2025年08月25日
保存条件：	5±3C，避光
性状：	黄色粉末
供应商：	Sigma-Aldrich
菌株TA100、TA1535和WP2 uvrA不加代谢活化系统时使用的阳性对照品为：
名称：	N-甲基-N-硝基-N亚硝基胍（MNNG）
批号：	K21171191
CAS号：	70-25-7
失效日期：	2025年11月19日
保存条件：	5±3℃，避光
性状：	固体
供应商：	上海阿拉丁生化科技股份有限公司"""
            )
            == "2-氨基蒽（2-AA）、2-硝基芴（2-NF）、ICR-191、N-甲基-N-硝基-N亚硝基胍（MNNG）"
        )

        # GPT生成的案例
        assert (
            await PositiveControlSample.extract(
                """所有菌株（TA98、TA100、TA1535、TA1537和WP2 uvrA）加代谢活化系统时使用的阳性对照品为：  
名称：	氨基比利霉素（AB）  
批号：	AB123456  
CAS号：	123-45-6  
失效日期：	2024年06月15日  
保存条件：	2-8℃  
性状：	粉末  
供应商：	Sigma-Aldrich  

菌株TA98不加代谢活化系统时使用的阳性对照品为：  
名称：	苯并芘（BaP）  
批号：	BP987654  
CAS号：	50-32-8  
失效日期：	2025年09月10日  
保存条件：	避光，常温  
性状：	粉末  
供应商：	Sigma-Aldrich  

菌株TA100不加代谢活化系统时使用的阳性对照品为：  
名称：	间苯二酚（Benzene-1,2-diol）  
批号：	BD123123  
CAS号：	123-45-6  
失效日期：	2025年02月20日  
保存条件：	避光，2-8℃  
性状：	粉末  
供应商：	Sigma-Aldrich  
"""
            )
            == "氨基比利霉素（AB）、苯并芘（BaP）、间苯二酚（Benzene-1,2-diol）"
        )

        # GPT生成的案例
        assert (
            await PositiveControlSample.extract(
                """用于溶媒对照的阳性对照品如下：  
名称：	二苯基嘧啶（DPPH）  
批号：	DP654321  
CAS号：	112-39-0  
失效日期：	2025年03月12日  
保存条件：	常温  
性状：	晶体  
供应商：	阿尔法公司  

菌株TA1537加代谢活化系统时使用的阳性对照品：  
名称：	氯硝柳胺（Cl-3NH）  
批号：	CL567890  
CAS号：	1691-20-3  
失效日期：	2024年07月30日  
保存条件：	避光，低温  
性状：	结晶  
供应商：	上海化学试剂公司  
"""
            )
            == "二苯基嘧啶（DPPH）、氯硝柳胺（Cl-3NH）"
        )

        # GPT生成的案例
        assert (
            await PositiveControlSample.extract(
                """用于染色实验的阳性对照品为：  
名称：	二氯乙烯（DCE）  
批号：	DCE20034  
CAS号：	156-60-5  
失效日期：	2024年09月01日  
保存条件：	-20℃，避光  
性状：	液体  
供应商：	阿拉丁公司  

用于骨髓损伤实验的阳性对照品为：  
名称：	甲醛（Formaldehyde）  
批号：	FA123987  
CAS号：	50-00-0  
失效日期：	2025年05月22日  
保存条件：	常温  
性状：	液体  
供应商：	辉南化学  
"""
            )
            == "二氯乙烯（DCE）、甲醛（Formaldehyde）"
        )


@pytest.mark.asyncio(loop_scope='session')
class TestMetabolicSystem:
    async def test_extract(self):

        # 遗传毒性-回复突变: 42331-31006-231925.docx
        # 1 研究目的
        assert (
            await MetabolicSystem.extract(
                """本次试验通过检测新药001在添加或不添加外源性代谢活化系统（β-萘黄酮和苯巴比妥诱导的大鼠肝脏S9）条件下诱导组氨酸营养缺陷型鼠伤寒沙门氏菌（TA98、TA100、TA1535和TA1537）和色氨酸营养缺陷型大肠埃希杆菌（WP2 uvrA）产生回复突变的能力，评价受试物潜在的致突变性。"""
            )
            == "代谢活化系统（β-萘黄酮和苯巴比妥诱导的大鼠肝脏S9）"
        )

        # GPT生成的案例
        assert (
            await MetabolicSystem.extract(
                """本试验通过检测新药002在添加或不添加外源性代谢活化系统（烟酰胺腺嘌呤二核苷酸（NADH）和乙酸乙酯诱导的小鼠肝脏S9）条件下诱导大肠杆菌（TA1537、TA98）和鼠伤寒沙门氏菌（TA100）产生突变的能力，评估受试物的潜在致突变性。"""
            )
            == "代谢活化系统（烟酰胺腺嘌呤二核苷酸（NADH）和乙酸乙酯诱导的小鼠肝脏S9）"
        )

        # GPT生成的案例
        assert (
            await MetabolicSystem.extract(
                """在本次试验中，评估新药003在添加或不添加外源性代谢活化系统（丙酮和4-甲基氨基苯）的条件下对金黄色葡萄球菌（TA1535和WP2 uvrA）及大肠杆菌（TA98、TA100）产生突变的影响，旨在确定新药003的致突变性。"""
            )
            == "代谢活化系统（丙酮和4-甲基氨基苯）"
        )

        # GPT生成的案例
        assert (
            await MetabolicSystem.extract(
                """此实验评估了新药004在添加或不添加外源性代谢活化系统（氯苯那敏和苯基苯并呋喃）条件下，对小鼠大肠杆菌（WP2 uvrA、TA100、TA1535）进行突变诱导的能力。"""
            )
            == "代谢活化系统（氯苯那敏和苯基苯并呋喃）"
        )


@pytest.mark.asyncio(loop_scope='session')
class TestCytotoxicEffects8:
    async def test_extract(self):

        # 遗传毒性-回复突变: 42331-31006-231925.docx
        # D-231925，第7页 试用摘要
        assert (
            await CytotoxicEffects8.extract(
                """在加或不加S9混合物的条件下，所有给药剂量均未观察到细菌毒性。"""
            )
            == "无"
        )

        # GPT生成的案例
        assert (
            await CytotoxicEffects8.extract(
                """该实验表明，新药001在200μg/皿和500μg/皿剂量下未表现出细菌毒性反应。"""
            )
            == "无"
        )

        # GPT生成的案例
        assert (
            await CytotoxicEffects8.extract(
                """在2000 μg/皿时，细胞的活性低于20%，表明此浓度下具有细胞毒性"""
            )
            == "在2000 μg/皿时，细胞的活性低于20%"
        )

        # GPT生成的案例
        assert (
            await CytotoxicEffects8.extract(
                """对于新药002，所有剂量的细菌毒性均为阴性，未引起细菌毒性反应。"""
            )
            == "无"
        )


@pytest.mark.asyncio(loop_scope='session')
class TestGenotoxicEffects8:
    async def test_extract(self):

        # 遗传毒性-回复突变: 42331-31006-231925.docx
        # D-231925，11 结论
        assert (
            await GenotoxicEffects8.extract(
                """本次Ames试验有效，受试物新药001在本次试验中的测试结果为阴性。"""
            )
            == "无"
        )

        # GPT生成的案例
        assert (
            await GenotoxicEffects8.extract(
                """该药物在高剂量下表现出明显的遗传毒性，导致基因突变。"""
            )
            == "该药物在高剂量下表现出明显的遗传毒性，导致基因突变。"
        )

        # GPT生成的案例
        assert (
            await GenotoxicEffects8.extract(
                """受试物新药003在Ames试验中的测试结果为阴性，未显示出突变性。"""
            )
            == "无"
        )

        # GPT生成的案例
        assert (
            await GenotoxicEffects8.extract(
                """研究表明，该药物具有遗传毒性，可引发染色体畸变和基因突变。"""
            )
            == "该药物具有遗传毒性，可引发染色体畸变和基因突变。"
        )

