from pytest import raises

from app.extractors.base import extract_answer, trim_lines
from app.utils.exception import ExtractError


def test_extract_answer():
    with raises(ExtractError):
        extract_answer('')
    with raises(ExtractError):
        extract_answer('1')
    with raises(ExtractError):
        extract_answer('<ANSWER>1<ANSWER>')

    assert extract_answer('<ANSWER>1</ANSWER>') == '1'
    assert extract_answer('<ANSWER> 1</ANSWER>') == ' 1'
    assert extract_answer('<ANSWER>1\n2 </ANSWER>') == '1\n2 '


def test_trim_lines():
    assert trim_lines('') == ''
    assert trim_lines(' ') == ''
    assert trim_lines('1') == '1'
    assert trim_lines('1 ') == '1'
    assert trim_lines(' 1\n2\n3 ') == '1\n2\n3'
