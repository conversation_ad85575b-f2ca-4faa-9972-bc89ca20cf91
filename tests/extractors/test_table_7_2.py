import pytest

from app.extractors.table_7_2 import (
    generate_table_auc_last_c_max_data,
    generate_table_header,
)


@pytest.mark.asyncio(loop_scope='session')
class TestGenerateTableHeader:
    async def test_generate_table_header(self):
        assert (
            await generate_table_header(
                '''| 组别 | 受试物 | 给药剂量<br>(雄/雌)<br>(mg/kg/day) | 浓度<br>(雄/雌)<br>(mg/mL) | 动物数 (雄/雌) | 动物数 (雄/雌) | 动物数 (雄/雌) |
| 组别 | 受试物 | 给药剂量<br>(雄/雌)<br>(mg/kg/day) | 浓度<br>(雄/雌)<br>(mg/mL) | 主试验动物 | 主试验动物 | TK |
| 组别 | 受试物 | 给药剂量<br>(雄/雌)<br>(mg/kg/day) | 浓度<br>(雄/雌)<br>(mg/mL) | TSb | RSc | TK |
| 1 | 溶媒a | 0/0 | 0/0 | 10/10 | 5/5 | 4/4 |
| 2 | 新药001 | 5/2.5 | 0.5/0.25 | 10/10 | 5/5 | 4/4 |
| 3 | 新药001 | 15/7.5 | 1.5/0.75 | 10/10 | 5/5 | 4/4 |
| 4 | 新药001 | 75/50 | 7.5/5.0 | 10/10 | 5/5 | 4/4 |''',
                '雄性：5、15、75 mg/kg/day\n雌性：2.5、7.5、50 mg/kg/day',
            )
            == [
                ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                ['动物数量', 'M:4', 'F:4', 'M:4', 'F:4', 'M:4', 'F:4', 'M:4', 'F:4'],
            ]
        )

        assert (
            await generate_table_header(
                '''| 组别 | 受试物 | 给药剂量(mg/kg/day) | 浓度<br>(mg/mL) | 动物数 (M/F) | 动物数 (M/F) |
| 组别 | 受试物 | 给药剂量(mg/kg/day) | 浓度<br>(mg/mL) | TSa | RSb |
| 1 | 溶媒c | 0 | 0 | 3/3 | 2/2 |
| 2 | 新药001 | 1.5 | 0.3 | 3/3 | 2/2 |
| 3 | 新药001 | 5 | 1 | 3/3 | 2/2 |
| 4 | 新药001 | 15 | 3 | 3/3 | 2/2 |''',
                '1.5、5、15 mg/kg/day',
            )
            == [
                ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'],
                ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
            ]
        )

        assert (
            await generate_table_header(
                '''| 组别 | 受试物 | 给药剂量<br>(雄/雌)<br>(mg/kg/day) | 浓度<br>(雄/雌)<br>(mg/mL) | 动物数 (雄/雌) | 动物数 (雄/雌) | 动物数 (雄/雌) |
| 组别 | 受试物 | 给药剂量<br>(雄/雌)<br>(mg/kg/day) | 浓度<br>(雄/雌)<br>(mg/mL) | 主试验动物 | 主试验动物 | TK |
| 组别 | 受试物 | 给药剂量<br>(雄/雌)<br>(mg/kg/day) | 浓度<br>(雄/雌)<br>(mg/mL) | TSb | RSc | TK |
| 1 | 溶媒a | 0/0 | 0/0 | 10/10 | 5/5 | 1/2 |
| 2 | 新药001 | 5/2.5 | 0.5/0.25 | 10/10 | 5/5 | 3/4 |
| 3 | 新药001 | 15/7.5 | 1.5/0.75 | 10/10 | 5/5 | 5/6 |
| 4 | 新药001 | 75/50 | 7.5/5.0 | 10/10 | 5/5 | 7/8 |''',
                '雄性：5、15、75 mg/kg/day\n雌性：2.5、7.5、50 mg/kg/day',
            )
            == [
                ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                ['动物数量', 'M:1', 'F:2', 'M:3', 'F:4', 'M:5', 'F:6', 'M:7', 'F:8'],
            ]
        )


@pytest.mark.asyncio(loop_scope='session')
class TestGenerateTableAUClastCmaxData:
    async def test_generate_table_AUClast_Cmax_data(self):
        assert (
            await generate_table_auc_last_c_max_data(
                '''| 给药天数 | 组别 | 给药剂量(mg/kg/day) | 性别 | Cmax | AUClast | 
| 给药天数 | 组别 | 给药剂量(mg/kg/day) | 性别 | (ng/ml) | (hr*ng/ml) | 
| 1 | 2 | 5 | 雄性 | 28625 | 517794 | 
| 1 | 2 | 2.5 | 雌性 | 19400 | 416645 | 
| 1 | 3 | 15 | 雄性 | 127000 | 1492946 | 
| 1 | 3 | 7.5 | 雌性 | 112925 | 1379860 | 
| 1 | 4 | 75 | 雄性 | 146750 | 2692723 | 
| 1 | 4 | 50 | 雌性 | 189250 | 3926221 | 
| 28 | 2 | 5 | 雄性 | 44850 | 1291721 | 
| 28 | 2 | 2.5 | 雌性 | 78500 | 4027396 | 
| 28 | 3 | 15 | 雄性 | 116075 | 3264410 | 
| 28 | 3 | 7.5 | 雌性 | 155500 | 6625633 | 
| 28 | 4 | 75 | 雄性 | 154750 | 2884819 | 
| 28 | 4 | 50 | 雌性 | 179000 | 6920757 | ''',
                '28天',
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'], 
                    ['动物数量', 'M:4', 'F:4', 'M:4', 'F:4', 'M:4', 'F:4', 'M:4', 'F:4']
                ]
            )
            == [
                ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'], 
                ['动物数量', 'M:4', 'F:4', 'M:4', 'F:4', 'M:4', 'F:4', 'M:4', 'F:4'],
                [
                    '毒代动力学AUClast(hr*ng/ml)',
                    '毒代动力学AUClast(hr*ng/ml)',
                    '毒代动力学AUClast(hr*ng/ml)',
                    '毒代动力学AUClast(hr*ng/ml)',
                    '毒代动力学AUClast(hr*ng/ml)',
                    '毒代动力学AUClast(hr*ng/ml)',
                    '毒代动力学AUClast(hr*ng/ml)',
                    '毒代动力学AUClast(hr*ng/ml)',
                    '毒代动力学AUClast(hr*ng/ml)',
                ],
                ['第1天', '-', '-', '517794', '416645', '1492946', '1379860', '2692723', '3926221'],
                ['第28天', '-', '-', '1291721', '4027396', '3264410', '6625633', '2884819', '6920757'],
                [
                    '毒代动力学Cmax(ng/ml)',
                    '毒代动力学Cmax(ng/ml)',
                    '毒代动力学Cmax(ng/ml)',
                    '毒代动力学Cmax(ng/ml)',
                    '毒代动力学Cmax(ng/ml)',
                    '毒代动力学Cmax(ng/ml)',
                    '毒代动力学Cmax(ng/ml)',
                    '毒代动力学Cmax(ng/ml)',
                    '毒代动力学Cmax(ng/ml)',
                ],
                ['第1天', '-', '-', '28625', '19400', '127000', '112925', '146750', '189250'],
                ['第28天', '-', '-', '44850', '78500', '116075', '155500', '154750', '179000'],
            ]
        )

        assert (
            await generate_table_auc_last_c_max_data(
                '''| 给药天数 | 组别 | 给药剂量(mg/kg/day) | 性别 | Cmax | AUClast | 
| 给药天数 | 组别 | 给药剂量(mg/kg/day) | 性别 | (ng/mL) | (hr*ng/mL) | 
|  1 | 2 | 1.5 | 雄 | 1578 | 7539 | 
|  1 | 2 | 1.5 | 雌 | 2118 | 11670 | 
|  1 | 3 | 5 | 雄 | 6476 | 38835 | 
|  1 | 3 | 5 | 雌 | 7252 | 37662 | 
|  1 | 4 | 15 | 雄 | 22480 | 190826 | 
|  1 | 4 | 15 | 雌 | 18660 | 163700 | 
|  28 | 2 | 1.5 | 雄 | 1618 | 6854 | 
|  28 | 2 | 1.5 | 雌 | 2038 | 9386 | 
|  28 | 3 | 5 | 雄 | 5112 | 34451 | 
|  28 | 3 | 5 | 雌 | 6264 | 32532 | 
|  28 | 4 | 15 | 雄 | 21500 | 216157 | 
|  28 | 4 | 15 | 雌 | 22040 | 169215 | ''',
                '28天',
                [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'], 
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:']
                ]
            )
            == [
                    ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'], 
                    ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                [
                    '毒代动力学AUClast(hr*ng/mL)',
                    '毒代动力学AUClast(hr*ng/mL)',
                    '毒代动力学AUClast(hr*ng/mL)',
                    '毒代动力学AUClast(hr*ng/mL)',
                    '毒代动力学AUClast(hr*ng/mL)',
                    '毒代动力学AUClast(hr*ng/mL)',
                    '毒代动力学AUClast(hr*ng/mL)',
                    '毒代动力学AUClast(hr*ng/mL)',
                    '毒代动力学AUClast(hr*ng/mL)',
                ],
                ['第1天', '-', '-', '7539', '11670', '38835', '37662', '190826', '163700'],
                ['第28天', '-', '-', '6854', '9386', '34451', '32532', '216157', '169215'],
                [
                    '毒代动力学Cmax(ng/mL)',
                    '毒代动力学Cmax(ng/mL)',
                    '毒代动力学Cmax(ng/mL)',
                    '毒代动力学Cmax(ng/mL)',
                    '毒代动力学Cmax(ng/mL)',
                    '毒代动力学Cmax(ng/mL)',
                    '毒代动力学Cmax(ng/mL)',
                    '毒代动力学Cmax(ng/mL)',
                    '毒代动力学Cmax(ng/mL)',
                ],
                ['第1天', '-', '-', '1578', '2118', '6476', '7252', '22480', '18660'],
                ['第28天', '-', '-', '1618', '2038', '5112', '6264', '21500', '22040'],
            ]
        )
