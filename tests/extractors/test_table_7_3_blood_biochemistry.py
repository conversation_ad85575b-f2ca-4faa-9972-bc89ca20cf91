import pytest

from app.extractors.table_7_3_blood_biochemistry import BloodBiochemistryTable


class TestBloodBiochemistryTable:
    @pytest.mark.asyncio(loop_scope='session')
    async def test_extract(self):
        # 重复给药关键: 4232-31003-231610, project_id: 172 , doc_id: 902
        # C-231610，7.7.1血生化，与以下表格一致
        assert (
            await BloodBiochemistryTable.extract(
                '''| 性别 | 雄性 | 雄性 | 雄性 | 雄性 | 雌性 | 雌性 | 雌性 | 雌性 |
| 组别 | 1 | 2 | 3 | 4 | 1 | 2 | 3 | 4 |
| 受试物 | 溶媒 | 新药001 | 新药001 | 新药001 | 溶媒 | 新药001 | 新药001 | 新药001 |
| 剂量（mg/kg/day） | 0 | 5 | 15 | 75 | 0 | 2.5 | 7.5 | 50 |
| 检查动物数 | 10 | 10 | 10 | 10 | 10 | 10 | 10 | 10 |
| ALB（g/L） | 46.81 | 46.82 | 46.53 | 46.93 | 52.2 | 50.72 | 49.13* | 45.94*** |
| (% 差值) | - | 0 | -1 | 0 | - | -3 | -6 | -12 |
| GLO （g/L） | 17.04 | 16.24 | 16.75 | 18.84** | 14.4 | 14.54 | 14.87 | 17.14*** |
| (% 差值) | - | -5 | -2 | +11 | - | +1 | +3 | +19 |
| A/G （Ratio） | 2.764 | 2.896 | 2.789 | 2.503* | 3.645 | 3.51 | 3.329 | 2.696*** |
| (% 差值) | - | +5 | +1 | -9 | - | -4 | -9 | -26 |
| 注：表中所示结果为组均值，粗体数值考虑受试物相关。 “–”= 不适用；“+/-” = 升高/降低。（%差值）= 给药组均值与对照组均值之差相对对照组均值的百分比，四舍五入到整数。统计分析：Dunnett Test：*- 与对照组有显著差异（P < 0.05）；** - 与对照组有显著差异（P < 0.01）；*** - 与对照组有显著差异（P < 0.001）。 | 注：表中所示结果为组均值，粗体数值考虑受试物相关。 “–”= 不适用；“+/-” = 升高/降低。（%差值）= 给药组均值与对照组均值之差相对对照组均值的百分比，四舍五入到整数。统计分析：Dunnett Test：*- 与对照组有显著差异（P < 0.05）；** - 与对照组有显著差异（P < 0.01）；*** - 与对照组有显著差异（P < 0.001）。 | 注：表中所示结果为组均值，粗体数值考虑受试物相关。 “–”= 不适用；“+/-” = 升高/降低。（%差值）= 给药组均值与对照组均值之差相对对照组均值的百分比，四舍五入到整数。统计分析：Dunnett Test：*- 与对照组有显著差异（P < 0.05）；** - 与对照组有显著差异（P < 0.01）；*** - 与对照组有显著差异（P < 0.001）。 | 注：表中所示结果为组均值，粗体数值考虑受试物相关。 “–”= 不适用；“+/-” = 升高/降低。（%差值）= 给药组均值与对照组均值之差相对对照组均值的百分比，四舍五入到整数。统计分析：Dunnett Test：*- 与对照组 有显著差异（P < 0.05）；** - 与对照组有显著差异（P < 0.01）；*** - 与对照组有显著差异（P < 0.001）。 | 注：表中所示结果为组均值，粗体数值考虑受试物相关。 “–”= 不适用；“+/-” = 升高/降低。（%差值）= 给药组均值与对照组均值之差相对对照组均值的百分比，四舍五入到整数。统计分析：Dunnett Test：*- 与对照组有显著差异（P < 0.05）；** - 与对照组有显著差异（P < 0.01）；*** - 与对照组有显著差异（P < 0.001）。 | 注：表中所示结果为组均值，粗体数值考虑受试物相关。 “–”= 不适用；“+/-” = 升高/降低。（%差值）= 给药组均值与对照组均值之差相对对照组均值的百分比，四舍五入到整数。统计分析：Dunnett Test：*- 与对照组有显著差异（P < 0.05）；** - 与对照组有显著差异（P < 0.01）；*** - 与对照组有显著差异（P < 0.001）。 | 注：表中所示结果为组均值，粗体数值考虑受试物相关。 “–”= 不适用；“+/-” = 升高/降低。（%差值）= 给药组均值与对照组均值之差相对对照组均值的百分比，四舍五入到整数。统计分析：Dunnett Test：*- 与对照组有显著差异（P < 0.05）；** - 与对照组有显 著差异（P < 0.01）；*** - 与对照组有显著差异（P < 0.001）。 | 注：表中所示结果为组均值，粗体数值考虑受试物相关。 “–”= 不适用；“+/-” = 升高/降低。（%差值）= 给药组均值与对照组均值之差相对对照组均值的百分比，四舍五入到整数。统计分析：Dunnett Test：*- 与对照组有显著差异（P < 0.05）；** - 与对照组有显著差异（P < 0.01）；*** - 与对照组有显著差异（P < 0.001）。 | 注：表中所示结果为组均值，粗体数值考虑受试物相关。 “–”= 不适用；“+/-” = 升高/降低。（%差值）= 给药组均值与对照组均值之差相对对照组均值的百分比，四舍五入到整数。统计分析：Dunnett Test：*- 与对照组有显著差异（P < 0.05）；** - 与对照组有显著差异（P < 0.01）；*** - 与对照组有显著差异（P < 0.001）。 |''',
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M:10 | F:10 | M:10 | F:10 | M:10 | F:10 | M:10 | F:10 |
| ALB（g/L） | 46.81 | 52.2 | 46.82 | 50.72 | 46.53 | 49.13* | 46.93 | 45.94*** |
| (% 差值) | - | - | 0 | -3 | -1 | -6 | 0 | -12 |
| GLO （g/L） | 17.04 | 14.4 | 16.24 | 14.54 | 16.75 | 14.87 | 18.84** | 17.14*** |
| (% 差值) | - | - | -5 | +1 | -2 | +3 | +11 | +19 |
| A/G （Ratio） | 2.764 | 3.645 | 2.896 | 3.51 | 2.789 | 3.329 | 2.503* | 2.696*** |
| (% 差值) | - | - | +5 | -4 | +1 | -9 | -9 | -26 |'''
        )

        # 重复给药关键: 4232-31003-232003, project_id: 172 , doc_id: 903
        # C-232003，7.10.1血生化，与以下表格一致
        assert (
            await BloodBiochemistryTable.extract(
                '''| 性别 | 雄性 | 雄性 | 雄性 | 雄性 | 雌性 | 雌性 | 雌性 | 雌性 |
| 受试物 | 溶媒 | 新药001 | 新药001 | 新药001 | 溶媒 | 新药001 | 新药001 | 新药001 |
| 剂量（mg/kg/day) | 0 | 1.5 | 5 | 15 | 0 | 1.5 | 5 | 15 |
| 检查动物数 | 5 | 5 | 5 | 5 | 5 | 5 | 5 | 5 |
| Na（mmol/L） | 147.22 | 146.46 | 145.84 | 141.84<br>*** | 146.14 | 147.04 | 145.86 | 144.66 |
| （% 差值） | - | -1 | -1 | -4 | - | +1 | 0 | -1 |
| Cl (mmol/L) | 108.2 | 108.68 | 108.4 | 103.72<br>** | 107.86 | 109.12 | 108.16 | 106.1 |
| （% 差值） | - | 0 | 0 | -4 | - | +1 | 0 | -2 |''',
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 1.5 | 1.5 | 5 | 5 | 15 | 15 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''',
            )
            == '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 1.5 | 1.5 | 5 | 5 | 15 | 15 |
| 动物数量 | M:5 | F:5 | M:5 | F:5 | M:5 | F:5 | M:5 | F:5 |
| Na（mmol/L） | 147.22 | 146.14 | 146.46 | 147.04 | 145.84 | 145.86 | 141.84<br>*** | 144.66 |
| （% 差值） | - | - | -1 | +1 | -1 | 0 | -4 | -1 |
| Cl (mmol/L) | 108.2 | 107.86 | 108.68 | 109.12 | 108.4 | 108.16 | 103.72<br>** | 106.1 |
| （% 差值） | - | - | 0 | +1 | 0 | 0 | -4 | -2 |'''
        )

        # C-230614，7.10.1血生化
        assert (
                await BloodBiochemistryTable.extract(
                    '''| 性别 | 雄性 | 雄性 | 雄性 | 雄性 | 雌性 | 雌性 | 雌性 | 雌性 |
| 组别 | 1 | 2 | 3 | 4 | 1 | 2 | 3 | 4 |
| 受试物 | 溶媒 | 新药001 | 新药001 | 新药001 | 溶媒 | 新药001 | 新药001 | 新药001 |
| 剂量（mg/kg/day) | 0 | 1 | 2 | 4 | 0 | 1 | 2 | 4 |
| 检查动物数 | 5 | 5 | 5 | 5 | 5 | 5 | 5 | 5 |
| ALP（mmol/L） | 112.2 | 126.8 | 146.6 | 185.8 | 92.2 | 132.6 | 125.0 | 179.4* |
| （% 差值） | - | +13 | +31 | +66 | - | +44 | +36 | +95 |
| CHO (mmol/L) | 3.674 | 4.636 | 4.702 | 5.698*** | 3.926 | 4.028 | 4.792 | 4.668 |
| （% 差值） | - | +26 | +28 | +55 | - | +3 | +22 | +19 |
| TP (g/L) | 54.26 | 57.36 | 58.98 | 61.86* | 55.18 | 59.94 | 55.68 | 59.34 |
| （% 差值） | - | +6 | +9 | +14 | - | +9 | +1 | +8 |
| ALB (g/L) | 31.62 | 32.58 | 32.24 | 35.78 | 23.64 | 33.84 | 32.32 | 34.60 |
| （% 差值） | - | +3 | +2 | +13 | - | +4 | -1 | +6 |
| GLO (g/L) | 22.64 | 24.78 | 26.74** | 26.08* | 22.54 | 26.10 | 23.36 | 24.74 |
| （% 差值） | - | +9 | +18 | +15 | - | +16 | +4 | +10 |
| TG (mmol/L) | 0.288 | 0.352 | 0.466 | 0.488 | 0.308 | 0.406 | 0.508 | 0.350 |
| （% 差值） | - | +22 | +62 | +69 | - | +32 | +65 | +14 |
| Cl (mmol/L) | 108.34 | 109.28 | 110.72 | 109.14 | 108.70 | 109.44 | 110.40 | 105.28 |
| （% 差值） | - | +1 | +2 | +1 | - | +1 | +2 | -3 |
| K (mmol/L) | 4.616 | 4.196* | 4.220 | 4.168* | 4.738 | 4.422 | 4.528 | 4.216** |
| （% 差值） | - | -9 | -9 | -10 | - | -7 | -4 | -11 |''',
                    '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 1 | 1 | 2 | 2 | 4 | 4 |
    | 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''')
                == '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 1 | 1 | 2 | 2 | 4 | 4 |
| 动物数量 | M:5 | F:5 | M:5 | F:5 | M:5 | F:5 | M:5 | F:5 |
| ALP（mmol/L） | 112.2 | 92.2 | 126.8 | 132.6 | 146.6 | 125.0 | 185.8 | 179.4* |
| （% 差值） | - | - | +13 | +44 | +31 | +36 | +66 | +95 |
| CHO (mmol/L) | 3.674 | 3.926 | 4.636 | 4.028 | 4.702 | 4.792 | 5.698*** | 4.668 |
| （% 差值） | - | - | +26 | +3 | +28 | +22 | +55 | +19 |
| TP (g/L) | 54.26 | 55.18 | 57.36 | 59.94 | 58.98 | 55.68 | 61.86* | 59.34 |
| （% 差值） | - | - | +6 | +9 | +9 | +1 | +14 | +8 |
| ALB (g/L) | 31.62 | 23.64 | 32.58 | 33.84 | 32.24 | 32.32 | 35.78 | 34.60 |
| （% 差值） | - | - | +3 | +4 | +2 | -1 | +13 | +6 |
| GLO (g/L) | 22.64 | 22.54 | 24.78 | 26.10 | 26.74** | 23.36 | 26.08* | 24.74 |
| （% 差值） | - | - | +9 | +16 | +18 | +4 | +15 | +10 |
| TG (mmol/L) | 0.288 | 0.308 | 0.352 | 0.406 | 0.466 | 0.508 | 0.488 | 0.350 |
| （% 差值） | - | - | +22 | +32 | +62 | +65 | +69 | +14 |
| Cl (mmol/L) | 108.34 | 108.70 | 109.28 | 109.44 | 110.72 | 110.40 | 109.14 | 105.28 |
| （% 差值） | - | - | +1 | +1 | +2 | +2 | +1 | -3 |
| K (mmol/L) | 4.616 | 4.738 | 4.196* | 4.422 | 4.220 | 4.528 | 4.168* | 4.216** |
| （% 差值） | - | - | -9 | -7 | -9 | -4 | -10 | -11 |''')

        # C-230613，7.7.1血生化
        assert (
                await BloodBiochemistryTable.extract(
                    '''| 性别 | 雄 | 雄 | 雄 | 雄 | 雌 | 雌 | 雌 | 雌 |
| 组别 | 1 | 2 | 3 | 4 | 1 | 2 | 3 | 4 |
| 受试物 | 溶媒 | 新药001 | 新药001 | 新药001 | 溶媒 | 新药001 | 新药001 | 新药001 |
| 剂量（mg/kg/day） | 0 | 2 | 3 | 5 | 0 | 2 | 3 | 5 |
| 检查动物数 | 10 | 10 | 10 | 10 | 10 | 10 | 10 | 10 |
| ALB （g/L） | 63.40 | 63.05 | 63.28 | 63.64 | 73.09 | 71.55 | 68.76 | 68.66 |
| (% 差值) | - | -1 | - | - | - | -2 | -6 | -6 |
| TP（g/L） | 44.09 | 44.56 | 44.94 | 45.27 | 54.46 | 53.40 | 51.07 | 50.80 |
| (% 差值) | - | +1 | +2 | +3 | - | -2 | -6 | -7 |
| TG （mmol/L） | 0.370 | 0.430 | 0.547 | 0.602 | 0.190 | 0.274 | 0.356* | 0.442** |
| (% 差值) | - | +16 | +48 | +63 | - | +44 | +87 | +133 |''',
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 2 | 2 | 3 | 3 | 5 | 5 |
| 动物数量 | M: | F: | M: | F: | M: | F: | M: | F: |''')
                == '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 2 | 2 | 3 | 3 | 5 | 5 |
| 动物数量 | M:10 | F:10 | M:10 | F:10 | M:10 | F:10 | M:10 | F:10 |
| ALB （g/L） | 63.40 | 73.09 | 63.05 | 71.55 | 63.28 | 68.76 | 63.64 | 68.66 |
| (% 差值) | - | - | -1 | -2 | - | -6 | - | -6 |
| TP（g/L） | 44.09 | 54.46 | 44.56 | 53.40 | 44.94 | 51.07 | 45.27 | 50.80 |
| (% 差值) | - | - | +1 | -2 | +2 | -6 | +3 | -7 |
| TG （mmol/L） | 0.370 | 0.190 | 0.430 | 0.274 | 0.547 | 0.356* | 0.602 | 0.442** |
| (% 差值) | - | - | +16 | +44 | +48 | +87 | +63 | +133 |''')

    def test_parse_table(self):
        # 重复给药关键: 4232-31003-231610, project_id: 172 , doc_id: 902
        header1 = [
            ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
            ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
        ]
        assert (
            BloodBiochemistryTable.parse_table(
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 5 | 2.5 | 15 | 7.5 | 75 | 50 |
| 动物数量 | M:10 | F:10 | M:10 | F:10 | M:10 | F:10 | M:10 | F:10 |
| ALB（g/L） | 46.81 | 52.2 | 46.82 | 50.72 | 46.53 | 49.13* | 46.93 | 45.94*** |
| (% 差值) | - | - | 0 | -3 | -1 | -6 | 0 | -12 |
| GLO （g/L） | 17.04 | 14.4 | 16.24 | 14.54 | 16.75 | 14.87 | 18.84** | 17.14*** |
| (% 差值) | - | - | -5 | +1 | -2 | +3 | +11 | +19 |
| A/G （Ratio） | 2.764 | 3.645 | 2.896 | 3.51 | 2.789 | 3.329 | 2.503* | 2.696*** |
| (% 差值) | - | - | +5 | -4 | +1 | -9 | -9 | -26 |''',
                header1,
            )
            == [
                ['日剂量（mg/kg）', '0（对照）', '0（对照）', '5', '2.5', '15', '7.5', '75', '50'],
                ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                [
                    '血生化参数',
                    '血生化参数',
                    '血生化参数',
                    '血生化参数',
                    '血生化参数',
                    '血生化参数',
                    '血生化参数',
                    '血生化参数',
                    '血生化参数',
                ],
                [
                    'ALB（g/L）\n（%变化百分比）',
                    '46.81',
                    '52.2',
                    '46.82\n（0%）',
                    '50.72\n（3%↓）',
                    '46.53\n（1%↓）',
                    '49.13*\n（6%↓）',
                    '46.93\n（0%）',
                    '45.94***\n（12%↓）',
                ],
                [
                    'GLO （g/L）\n（%变化百分比）',
                    '17.04',
                    '14.4',
                    '16.24\n（5%↓）',
                    '14.54\n（1%↑）',
                    '16.75\n（2%↓）',
                    '14.87\n（3%↑）',
                    '18.84**\n（11%↑）',
                    '17.14***\n（19%↑）',
                ],
                [
                    'A/G （Ratio）\n（%变化百分比）',
                    '2.764',
                    '3.645',
                    '2.896\n（5%↑）',
                    '3.51\n（4%↓）',
                    '2.789\n（1%↑）',
                    '3.329\n（9%↓）',
                    '2.503*\n（9%↓）',
                    '2.696***\n（26%↓）',
                ],
            ]
        )

        # 重复给药关键: 4232-31003-232003, project_id: 172 , doc_id: 903

        header2 = [
            ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'],
            ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
        ]

        assert (
            BloodBiochemistryTable.parse_table(
                '''| 日剂量（mg/kg） | 0（对照） | 0（对照） | 1.5 | 1.5 | 5 | 5 | 15 | 15 |
| 动物数量 | M:5 | F:5 | M:5 | F:5 | M:5 | F:5 | M:5 | F:5 |
| Na（mmol/L） | 147.22 | 146.14 | 146.46 | 147.04 | 145.84 | 145.86 | 141.84<br>*** | 144.66 |
| （% 差值） | - | - | -1 | +1 | -1 | 0 | -4 | -1 |
| Cl (mmol/L) | 108.2 | 107.86 | 108.68 | 109.12 | 108.4 | 108.16 | 103.72<br>** | 106.1 |
| （% 差值） | - | - | 0 | +1 | 0 | 0 | -4 | -2 |''',
                header2,
            )
            == [
                ['日剂量（mg/kg）', '0（对照）', '0（对照）', '1.5', '1.5', '5', '5', '15', '15'],
                ['动物数量', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:', 'M:', 'F:'],
                [
                    '血生化参数',
                    '血生化参数',
                    '血生化参数',
                    '血生化参数',
                    '血生化参数',
                    '血生化参数',
                    '血生化参数',
                    '血生化参数',
                    '血生化参数',
                ],
                [
                    'Na（mmol/L）\n（%变化百分比）',
                    '147.22',
                    '146.14',
                    '146.46\n（1%↓）',
                    '147.04\n（1%↑）',
                    '145.84\n（1%↓）',
                    '145.86\n（0%）',
                    '141.84\n***\n（4%↓）',
                    '144.66\n（1%↓）',
                ],
                [
                    'Cl (mmol/L)\n（%变化百分比）',
                    '108.2',
                    '107.86',
                    '108.68\n（0%）',
                    '109.12\n（1%↑）',
                    '108.4\n（0%）',
                    '108.16\n（0%）',
                    '103.72<\n**\n（4%↓）',
                    '106.1\n（2%↓）',
                ],
            ]
        )
