import pytest
from sqlmodel import col

from app.clients.mysql import get_session
from app.models.doc_chunk import Doc<PERSON>hunk


@pytest.mark.asyncio(loop_scope='session')
class TestDocChunk:
    async def test_query(self):
        async with get_session() as mysql:
            # TODO: 这里是硬编码的
            doc_chunks = await DocChunk.query(mysql, 34, title='动物')
            assert len(doc_chunks) == 1

            titles = await DocChunk.query(mysql, 34, title='动物%', columns=col(DocChunk.title))
            assert len(titles) == 2
            assert titles[0] == '动物'

            chunks = await DocChunk.query(mysql, 34, title='%动物%', columns=(DocChunk.title, DocChunk.paragraph))
            assert len(chunks) == 3
            assert chunks[1].title == '动物'

    async def test_query_first(self):
        async with get_session() as mysql:
            # TODO: 这里是硬编码的
            doc_chunk = await DocChunk.query_first(mysql, 34, title='动物')
            assert doc_chunk.title == '动物'

            title = await DocChunk.query_first(mysql, 34, title='动物%', columns=col(DocChunk.title))
            assert title == '动物'

            chunk = await DocChunk.query_first(mysql, 34, title='%动物%', columns=(DocChunk.title, DocChunk.paragraph))
            assert '动物' in chunk.title

    # 测试所有重复关键文章的 体重异常字段
    async def test_query_catalog_abnormal_weight(self):
        async with get_session() as mysql:
            # 重复给药关键: 4232-************, project_id: 172 , doc_id: 902
            doc_chunk = await DocChunk.query_sub(
                mysql,
                902,
                39143,
                title='%体重变化%',
                columns=(DocChunk.id, DocChunk.parent_id, DocChunk.title),
            )
            assert doc_chunk.title == '体重和体重变化'
            assert doc_chunk.id == 39149

            # 重复给药关键: 4232-************, project_id: 172 , doc_id: 903
            doc_chunk = await DocChunk.query_sub(
                mysql, 903, 39027, title='%体重变化%', columns=(DocChunk.id, DocChunk.parent_id, DocChunk.title)
            )
            assert doc_chunk.title == '体重和体重变化'
            assert doc_chunk.id == 39036

            # 重复给药关键: 大鼠经口重复给药28天毒性试验, project_id: 172 , doc_id: 900
            doc_chunk = await DocChunk.query_sub(
                mysql, 900, 38897, title='%体重变化%', columns=(DocChunk.id, DocChunk.parent_id, DocChunk.title)
            )
            assert doc_chunk is None

            # 重复给药关键: 比格犬经口重复给药4周毒性试验, project_id: 172 , doc_id: 901  文档格式需要修改

    # 测试所有重复关键文章的 摄食量字段
    async def test_query_catalog_food_intake(self):
        async with get_session() as mysql:
            # 重复给药关键: 4232-************, project_id: 172 , doc_id: 902
            doc_chunk = await DocChunk.query_sub(
                mysql, 902, 39143, title='%摄食量%', columns=(DocChunk.id, DocChunk.parent_id, DocChunk.title)
            )
            assert doc_chunk.title == '摄食量'
            assert doc_chunk.id == 39150

            # 重复给药关键: 4232-************, project_id: 172 , doc_id: 903
            doc_chunk = await DocChunk.query_sub(
                mysql, 903, 39027, title='%摄食量%', columns=(DocChunk.id, DocChunk.parent_id, DocChunk.title)
            )
            assert doc_chunk.title == '摄食量'
            assert doc_chunk.id == 39037

            # 重复给药关键: 大鼠经口重复给药28天毒性试验, project_id: 172 , doc_id: 900
            doc_chunk = await DocChunk.query_sub(
                mysql, 900, 38897, title='%摄食量%', columns=(DocChunk.id, DocChunk.parent_id, DocChunk.title)
            )
            assert doc_chunk.title == '8.4摄食量'
            assert doc_chunk.id == 38903

            # 重复给药关键: 比格犬经口重复给药4周毒性试验, project_id: 172 , doc_id: 901  文档格式需要修改

    # 测试所有重复关键文章的 临床观察异常字段
    async def test_query_catalog_clinical_observation(self):
        async with get_session() as mysql:
            # 重复给药关键: 4232-************, project_id: 172 , doc_id: 902
            doc_chunk = await DocChunk.query_sub(
                mysql, 902, 39143, title='%临床观察%', columns=(DocChunk.id, DocChunk.parent_id, DocChunk.title)
            )
            assert doc_chunk.title == '临床观察'
            assert doc_chunk.id == 39147

            # 重复给药关键: 4232-************, project_id: 172 , doc_id: 903
            doc_chunk = await DocChunk.query_sub(
                mysql, 903, 39027, title='%临床观察%', columns=(DocChunk.id, DocChunk.parent_id, DocChunk.title)
            )
            assert doc_chunk.title == '临床观察'
            assert doc_chunk.id == 39034

            # 重复给药关键: 大鼠经口重复给药28天毒性试验, project_id: 172 , doc_id: 900
            doc_chunk = await DocChunk.query_sub(
                mysql, 900, 38897, title='%临床观察%', columns=(DocChunk.id, DocChunk.parent_id, DocChunk.title)
            )
            assert doc_chunk.title == '8.2临床观察'
            assert doc_chunk.id == 38901

            # 重复给药关键: 比格犬经口重复给药4周毒性试验, project_id: 172 , doc_id: 901  文档格式需要修改

    # 测试所有重复关键文章的 血生化异常字段
    async def test_query_catalog_blood_biochemistry(self):
        async with get_session() as mysql:
            # 重复给药关键: 4232-************, project_id: 172 , doc_id: 902
            doc_chunk = await DocChunk.query_sub(
                mysql, 902, 39143, title='%血生化%', columns=(DocChunk.id, DocChunk.parent_id, DocChunk.title)
            )
            assert doc_chunk.title == '血生化'
            assert doc_chunk.id == 39153

            # 重复给药关键: 4232-************, project_id: 172 , doc_id: 903
            doc_chunk = await DocChunk.query_sub(
                mysql, 903, 39027, title='%血生化%', columns=(DocChunk.id, DocChunk.parent_id, DocChunk.title)
            )
            assert doc_chunk.title == '血生化'
            assert doc_chunk.id == 39043

            # 重复给药关键: 大鼠经口重复给药28天毒性试验, project_id: 172 , doc_id: 900
            doc_chunk = await DocChunk.query_sub(
                mysql, 900, 38897, title='%血%生化%', columns=(DocChunk.id, DocChunk.parent_id, DocChunk.title)
            )
            assert doc_chunk.title == '8.5.2血清生化'
            assert doc_chunk.id == 38906

            # 重复给药关键: 比格犬经口重复给药4周毒性试验, project_id: 172 , doc_id: 901  文档格式需要修改

    # 测试所有重复关键文章的 血液学字段
    async def test_query_catalog_hematology(self):
        async with get_session() as mysql:
            # 重复给药关键: 4232-************, project_id: 172 , doc_id: 902
            doc_chunk = await DocChunk.query_sub(
                mysql, 902, 39143, title='%血液学%', columns=(DocChunk.id, DocChunk.parent_id, DocChunk.title)
            )
            assert doc_chunk.title == '血液学'
            assert doc_chunk.id == 39154

            # 重复给药关键: 4232-************, project_id: 172 , doc_id: 903
            doc_chunk = await DocChunk.query_sub(
                mysql, 903, 39027, title='%血液学%', columns=(DocChunk.id, DocChunk.parent_id, DocChunk.title)
            )
            assert doc_chunk.title == '血液学'
            assert doc_chunk.id == 39044

            # 重复给药关键: 大鼠经口重复给药28天毒性试验, project_id: 172 , doc_id: 900
            doc_chunk = await DocChunk.query_sub(
                mysql, 900, 38897, title='%血液学%', columns=(DocChunk.id, DocChunk.parent_id, DocChunk.title)
            )
            assert doc_chunk.title == '8.5.1血液学'
            assert doc_chunk.id == 38905

            # 重复给药关键: 比格犬经口重复给药4周毒性试验, project_id: 172 , doc_id: 901  文档格式需要修改

    # 测试所有重复关键文章的 异常器官重量字段
    async def test_query_catalog_abnormal_organ_weight(self):
        async with get_session() as mysql:
            # 重复给药关键: 4232-************, project_id: 172 , doc_id: 902
            doc_chunk = await DocChunk.query_sub(
                mysql, 902, 39143, title='%脏器重量%', columns=(DocChunk.id, DocChunk.parent_id, DocChunk.title)
            )
            assert doc_chunk.title == '脏器重量'
            assert doc_chunk.id == 39157

            # 重复给药关键: 4232-************, project_id: 172 , doc_id: 903
            doc_chunk = await DocChunk.query_sub(
                mysql, 903, 39027, title='%脏器重量%', columns=(DocChunk.id, DocChunk.parent_id, DocChunk.title)
            )
            assert doc_chunk.title == '脏器重量'
            assert doc_chunk.id == 39047

            # 重复给药关键: 大鼠经口重复给药28天毒性试验, project_id: 172 , doc_id: 900
            doc_chunk = await DocChunk.query_sub(
                mysql, 900, 38897, title='%脏器重量%', columns=(DocChunk.id, DocChunk.parent_id, DocChunk.title)
            )
            assert doc_chunk.title == '8.6.2脏器重量和脏器系数'
            assert doc_chunk.id == 38912

            # 重复给药关键: 比格犬经口重复给药4周毒性试验, project_id: 172 , doc_id: 901  文档格式需要修改

    # 测试所有重复关键文章的 大体病理学字段
    async def test_query_catalog_gross_pathology(self):
        async with get_session() as mysql:
            # 重复给药关键: 4232-************, project_id: 172 , doc_id: 902
            doc_chunk = await DocChunk.query_sub(
                mysql, 902, 39143, title='%大体%', columns=(DocChunk.id, DocChunk.parent_id, DocChunk.title)
            )
            assert doc_chunk.title == '大体病理学检查'
            assert doc_chunk.id == 39158

            # 重复给药关键: 4232-************, project_id: 172 , doc_id: 903
            doc_chunk = await DocChunk.query_sub(
                mysql, 903, 39027, title='%大体%', columns=(DocChunk.id, DocChunk.parent_id, DocChunk.title)
            )
            assert doc_chunk.title == '大体病理学'
            assert doc_chunk.id == 39048

            # 重复给药关键: 大鼠经口重复给药28天毒性试验, project_id: 172 , doc_id: 900
            doc_chunk = await DocChunk.query_sub(
                mysql, 900, 38897, title='%大体%', columns=(DocChunk.id, DocChunk.parent_id, DocChunk.title)
            )
            assert doc_chunk.title == '8.6.1大体观察'
            assert doc_chunk.id == 38911

            # 重复给药关键: 比格犬经口重复给药4周毒性试验, project_id: 172 , doc_id: 901  文档格式需要修改

    # 测试所有重复关键文章的 组织病理学字段
    async def test_query_catalog_histopathology(self):
        async with get_session() as mysql:
            # 重复给药关键: 4232-************, project_id: 172 , doc_id: 902
            doc_chunk = await DocChunk.query_sub(
                mysql, 902, 39143, title='%组织病理学%', columns=(DocChunk.id, DocChunk.parent_id, DocChunk.title)
            )
            assert doc_chunk.title == '组织病理学评价'
            assert doc_chunk.id == 39159

            # 重复给药关键: 4232-************, project_id: 172 , doc_id: 903
            doc_chunk = await DocChunk.query_sub(
                mysql, 903, 39027, title='%组织病理学%', columns=(DocChunk.id, DocChunk.parent_id, DocChunk.title)
            )
            assert doc_chunk.title == '组织病理学'
            assert doc_chunk.id == 39049

            # 重复给药关键: 大鼠经口重复给药28天毒性试验, project_id: 172 , doc_id: 900
            doc_chunk = await DocChunk.query_sub(
                mysql, 900, 38897, title='%组织病理学%', columns=(DocChunk.id, DocChunk.parent_id, DocChunk.title)
            )
            assert doc_chunk.title == '8.6.3组织病理学'
            assert doc_chunk.id == 38913

            # 重复给药关键: 比格犬经口重复给药4周毒性试验, project_id: 172 , doc_id: 901  文档格式需要修改

    async def test_get_sub_ids(self):
        async with get_session() as mysql:
            # 测试无parent_id
            sub_ids = await DocChunk.get_sub_ids(mysql, 902, 0)
            assert len(sub_ids) > 0
            assert all(isinstance(id, int) for id in sub_ids)

            # 测试单个parent_id
            sub_ids = await DocChunk.get_sub_ids(mysql, 902, 39064)
            count = len(sub_ids)
            assert count > 0
            assert all(isinstance(id, int) for id in sub_ids)

            # 测试多个parent_ids
            sub_ids = await DocChunk.get_sub_ids(mysql, 902, (39064, 39079))
            count2 = len(sub_ids)
            assert count2 > count
            assert all(isinstance(id, int) for id in sub_ids)

            # 测试列表形式的parent_ids
            sub_ids = await DocChunk.get_sub_ids(mysql, 902, [39064, 39079])
            count3 = len(sub_ids)
            assert count3 == count2
            assert all(isinstance(id, int) for id in sub_ids)

    async def test_query_doc_chunks(self):
        async with get_session() as mysql:
            # 基本查询测试
            doc_chunks = await DocChunk.query_doc_chunks(mysql, 902)
            assert len(doc_chunks) > 0

            # 测试parent_id过滤
            doc_chunks = await DocChunk.query_doc_chunks(mysql, 902, parent_id=39064)
            assert len(doc_chunks) > 0

            # 测试parent_titles过滤 - 字符串
            doc_chunks = await DocChunk.query_doc_chunks(mysql, 902, parent_titles='动物')
            assert len(doc_chunks) > 0

            # 测试parent_titles过滤 - 元组
            doc_chunks = await DocChunk.query_doc_chunks(mysql, 902, parent_titles=('动物', '给药'))
            assert len(doc_chunks) > 0

            # 测试titles过滤 - 字符串
            doc_chunks = await DocChunk.query_doc_chunks(mysql, 902, titles='动物')
            assert len(doc_chunks) > 0
            assert all('动物' in chunk.title for chunk in doc_chunks)

            # 测试titles过滤 - 元组
            doc_chunks = await DocChunk.query_doc_chunks(mysql, 902, titles=('动物', '给药'))
            assert len(doc_chunks) > 0

            # 测试contents过滤 - 字符串
            doc_chunks = await DocChunk.query_doc_chunks(mysql, 902, contents='动物')
            assert len(doc_chunks) > 0

            # 测试contents过滤 - 元组
            doc_chunks = await DocChunk.query_doc_chunks(mysql, 902, contents=('动物', '给药'))
            assert len(doc_chunks) > 0

            # 测试contents过滤 - 嵌套元组(AND条件)
            doc_chunks = await DocChunk.query_doc_chunks(mysql, 902, contents=(('动物', '给药'),))
            assert len(doc_chunks) > 0

            # 测试指定columns
            doc_chunks = await DocChunk.query_doc_chunks(
                mysql, 902, titles='动物', columns=(DocChunk.title, DocChunk.paragraph)
            )
            assert len(doc_chunks) > 0
            assert all(hasattr(chunk, 'title') and hasattr(chunk, 'paragraph') for chunk in doc_chunks)

            # 测试组合查询
            doc_chunks = await DocChunk.query_doc_chunks(
                mysql,
                902,
                parent_id=0,
                parent_titles='实验',
                titles='动物',
                contents='给药',
                columns=(DocChunk.title, DocChunk.paragraph),
            )
            assert len(doc_chunks) >= 0  # 可能没有完全匹配的结果

    async def test_query_v2(self):
        async with get_session() as mysql:
            # 基本查询测试
            doc_chunks = await DocChunk.query_v2(mysql, 902)
            assert doc_chunks == []

            # 测试parent_id过滤
            doc_chunks = await DocChunk.query_v2(mysql, 902, parent_id=39064)
            assert len(doc_chunks) > 0

            # 测试titles过滤 - 字符串
            doc_chunks = await DocChunk.query_v2(mysql, 902, titles='动物')
            assert len(doc_chunks) > 0
            assert all('动物' in chunk.title for chunk in doc_chunks)

            # 测试titles过滤 - 元组
            doc_chunks = await DocChunk.query_v2(mysql, 902, titles=('动物', '给药'))
            assert len(doc_chunks) > 0

            # 测试指定columns
            doc_chunks = await DocChunk.query_v2(
                mysql, 902, titles='动物', columns=(DocChunk.title, DocChunk.paragraph)
            )
            assert len(doc_chunks) > 0
            assert all(hasattr(chunk, 'title') and hasattr(chunk, 'paragraph') for chunk in doc_chunks)

            # 测试组合查询
            doc_chunks = await DocChunk.query_v2(
                mysql,
                902,
                parent_id=39064,
                titles='动物',
                columns=(DocChunk.title, DocChunk.paragraph),
            )
            assert len(doc_chunks) > 0

    async def test_filter_content(self):
        async with get_session() as mysql:
            doc_chunks = await DocChunk.query_v2(mysql, 902, parent_id=39064)
            assert len(doc_chunks) > 0

            filtered_chunks = await DocChunk.filter_content(mysql, 902, doc_chunks, contents='动物')
            assert len(doc_chunks) > len(filtered_chunks) > 0

            filtered_chunks = await DocChunk.filter_content(mysql, 902, [], contents='动物')
            assert len(filtered_chunks) > 0
