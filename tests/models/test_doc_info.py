import pytest
from sqlmodel import col

from app.clients.mysql import get_session
from app.models.doc import Doc
from app.schemas.doc_base_field import TrialMainType, TrialSubType


@pytest.mark.asyncio(loop_scope='session')
class TestDoc:
    async def test_get_by_project(self):
        async with get_session() as mysql:
            # TODO: 这里是硬编码的
            docs = await Doc.get_by_project(mysql, 15)
            assert len(docs) == 6

            doc_ids = await Doc.get_by_project(mysql, 15, columns=col(Doc.id))
            for doc_id in doc_ids:
                assert isinstance(doc_id, int)

            docs = await Doc.get_by_project(mysql, 15, trial_main_type=TrialMainType.REPEATED_DOSE.value)
            assert len(docs) == 2

            docs = await Doc.get_by_project(mysql, 15, trial_subtype=TrialSubType.REPEATED_DOSE.value)
            assert len(docs) == 1
