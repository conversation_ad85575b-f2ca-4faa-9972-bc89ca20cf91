import pytest
from sqlmodel import col

from app.clients.mysql import get_session
from app.models.doc_chunk import Doc<PERSON>hunk
from app.models.table_chunk import TableChunk


@pytest.mark.asyncio(loop_scope='session')
class TestTableChunk:
    async def test_query(self):
        async with get_session() as mysql:
            # TODO: 这里是硬编码的
            doc_chunk_ids = await DocChunk.query(mysql, 34, title='动物', columns=col(DocChunk.id))
            assert len(doc_chunk_ids) == 1
            doc_chunk_id = doc_chunk_ids[0]

            table_chunks = await TableChunk.query(mysql, doc_chunk_id)
            assert len(table_chunks) == 1
            content_md = table_chunks[0].content_md

            content_mds = await TableChunk.query(
                mysql, doc_chunk_id, '正文表3：实验动物', columns=col(TableChunk.content_md)
            )
            assert len(content_mds) == 1
            assert content_mds[0] == content_md

            chunks = await TableChunk.query(
                mysql, doc_chunk_id, '%实验动物', (TableChunk.content_md, TableChunk.content_json)
            )
            assert len(chunks) == 1
            assert chunks[0].content_md == content_md

    async def test_query_first(self):
        async with get_session() as mysql:
            # TODO: 这里是硬编码的
            doc_chunk_id = await DocChunk.query_first(mysql, 34, title='动物', columns=col(DocChunk.id))

            table_chunk = await TableChunk.query_first(mysql, doc_chunk_id)
            content_md = table_chunk.content_md

            content_md2 = await TableChunk.query_first(
                mysql, doc_chunk_id, '正文表3：实验动物', columns=col(TableChunk.content_md)
            )
            assert content_md == content_md2

            chunk = await TableChunk.query_first(
                mysql, doc_chunk_id, '%实验动物', (TableChunk.content_md, TableChunk.content_json)
            )
            assert chunk.content_md == content_md

            chunk = await TableChunk.query_first(
                mysql,
                doc_chunk_id,
                ('%实验动物', '正文表3：实验动物'),
                (TableChunk.content_md, TableChunk.content_json),
            )
            assert chunk.content_md == content_md

    async def test_query_field(self):
        async with get_session() as mysql:
            # TODO: 这里是硬编码的
            doc_chunk_ids = await DocChunk.query(mysql, 34, title='动物', columns=col(DocChunk.id))
            assert len(doc_chunk_ids) == 1
            doc_chunk_id = doc_chunk_ids[0]

            assert await TableChunk.query_field(mysql, doc_chunk_id, '正文表3：实验动物', '种属') is None

            assert (
                await TableChunk.query_field(mysql, doc_chunk_id, '%实验动物', '种属和品种')
                == 'Sprague-Dawley大鼠[Crl:CD(SD)IGS](SPF)，无给药史'
            )

    async def test_query_table_chunks(self):
        async with get_session() as mysql:
            # TODO: 这里是硬编码的
            doc_chunk_ids = await DocChunk.query(mysql, 34, title='动物', columns=col(DocChunk.id))
            assert len(doc_chunk_ids) == 1
            doc_chunk_id = doc_chunk_ids[0]

            # 测试基本查询
            table_chunks = await TableChunk.query_table_chunks(mysql, 34, doc_chunk_ids=[doc_chunk_id])
            assert len(table_chunks) == 1
            content_md = table_chunks[0].content_md

            # 测试带标题的查询
            table_chunks = await TableChunk.query_table_chunks(
                mysql, 34, doc_chunk_ids=[doc_chunk_id], titles='实验动物'
            )
            assert len(table_chunks) == 1
            assert table_chunks[0].content_md == content_md

            # 测试带内容的查询
            table_chunks = await TableChunk.query_table_chunks(
                mysql, 34, doc_chunk_ids=[doc_chunk_id], contents='Sprague-Dawley'
            )
            assert len(table_chunks) == 1
            assert table_chunks[0].content_md == content_md

            # 测试带指定列的查询
            chunks = await TableChunk.query_table_chunks(
                mysql,
                34,
                doc_chunk_ids=[doc_chunk_id],
                titles='实验动物',
                columns=(TableChunk.content_md, TableChunk.content_json),
            )
            assert len(chunks) == 1
            assert chunks[0].content_md == content_md

            # 测试多个标题的查询
            chunks = await TableChunk.query_table_chunks(
                mysql,
                34,
                doc_chunk_ids=[doc_chunk_id],
                titles=('实验动物', '给药'),
                columns=col(TableChunk.content_md),
            )
            assert len(chunks) == 1
            assert chunks[0] == content_md

            # 测试多个内容关键词的查询（元组形式）
            chunks = await TableChunk.query_table_chunks(
                mysql,
                34,
                doc_chunk_ids=[doc_chunk_id],
                contents=('Sprague-Dawley', 'SPF'),
                columns=col(TableChunk.content_md),
            )
            assert len(chunks) == 1
            assert chunks[0] == content_md

            # 测试多个内容关键词的查询（嵌套元组形式）
            chunks = await TableChunk.query_table_chunks(
                mysql,
                34,
                doc_chunk_ids=[doc_chunk_id],
                contents=(('Sprague-Dawley', 'SPF'),),
                columns=col(TableChunk.content_md),
            )
            assert len(chunks) == 1
            assert chunks[0] == content_md

    async def test_query_v2(self):
        async with get_session() as mysql:
            # TODO: 这里是硬编码的
            doc_chunk_ids = await DocChunk.query(mysql, 34, title='动物', columns=col(DocChunk.id))
            assert len(doc_chunk_ids) == 1
            doc_chunk_id = doc_chunk_ids[0]

            # 测试基本查询
            table_chunks = await TableChunk.query_v2(mysql, 34, doc_chunk_ids=[doc_chunk_id])
            assert len(table_chunks) == 1
            content_md = table_chunks[0].content_md

            # 测试带标题的查询
            table_chunks = await TableChunk.query_v2(mysql, 34, doc_chunk_ids=[doc_chunk_id], titles='实验动物')
            assert len(table_chunks) == 1
            assert table_chunks[0].content_md == content_md

            # 测试带内容的查询
            table_chunks = await TableChunk.query_v2(mysql, 34, doc_chunk_ids=[doc_chunk_id], contents='Sprague-Dawley')
            assert len(table_chunks) == 1
            assert table_chunks[0].content_md == content_md

            # 测试带指定列的查询
            chunks = await TableChunk.query_v2(
                mysql,
                34,
                doc_chunk_ids=[doc_chunk_id],
                titles='实验动物',
                columns=(TableChunk.content_md, TableChunk.content_json, TableChunk.table_title),
            )
            assert len(chunks) == 1
            assert chunks[0].content_md == content_md

            # 测试多个标题的查询
            chunks = await TableChunk.query_v2(
                mysql,
                34,
                doc_chunk_ids=[doc_chunk_id],
                titles=('实验动物', '给药'),
                columns=(TableChunk.content_md, TableChunk.table_title),
            )
            assert len(chunks) == 1
            assert chunks[0].content_md == content_md

            # 测试多个内容关键词的查询（元组形式）
            chunks = await TableChunk.query_v2(
                mysql,
                34,
                doc_chunk_ids=[doc_chunk_id],
                contents=('Sprague-Dawley', 'SPF'),
                columns=col(TableChunk.content_md),
            )
            assert len(chunks) == 1
            assert chunks[0] == content_md

            # 测试多个内容关键词的查询（嵌套元组形式）
            chunks = await TableChunk.query_v2(
                mysql,
                34,
                doc_chunk_ids=[doc_chunk_id],
                contents=(('Sprague-Dawley', 'SPF'),),
                columns=col(TableChunk.content_md),
            )
            assert len(chunks) == 1
            assert chunks[0] == content_md

            # 测试不带 doc_chunk_ids 的查询
            table_chunks = await TableChunk.query_v2(mysql, 34, titles='实验动物')
            assert len(table_chunks) == 1
            assert '实验动物' in table_chunks[0].table_title

            # 测试多个内容关键词的查询（嵌套元组形式）
            chunks = await TableChunk.query_v2(
                mysql,
                34,
                contents=(('Sprague-Dawley', 'SPF'),),
                columns=col(TableChunk.content_md),
            )
            assert len(chunks) == 1
            assert chunks[0] == content_md
