services:
  mysql-test:
    image: bitnami/mysql:5.7
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
      - MYSQL_DATABASE=test

  redis-test:
    image: bitnami/redis
    environment:
      - ALLOW_EMPTY_PASSWORD=yes

  rabbitmq-test:
    image: rabbitmq

  app:
    image: purefda/ind267-backend
    pull_policy: build
    build:
      dockerfile: Dockerfile
    ports:
      - '127.0.0.1:8000:8000'
    environment:
      - MYSQL_DSN=mysql+asyncmy://root@mysql-test:3306/test?charset=utf8mb4
      - REDIS_DSN=redis://redis-test:6379/0?protocol=3&decode_responses=True
      - TASK_QUEUE_BROKER=amqp://rabbitmq-test:5672
      - TASK_QUEUE_BACKEND=redis://redis-test:6379/0?protocol=3
    volumes:
      - ./config:/config:ro
    depends_on:
      - mysql-test
      - redis-test

  worker-extract:
    image: purefda/ind267-backend
    command: taskiq worker app.task:broker -w 8 app.tasks.project app.tasks.extract
    environment:
      - MYSQL_DSN=mysql+asyncmy://root@mysql-test:3306/test?charset=utf8mb4
      - REDIS_DSN=redis://redis-test:6379/0?protocol=3&decode_responses=True
      - TASK_QUEUE_BROKER=amqp://rabbitmq-test:5672
      - TASK_QUEUE_BACKEND=redis://redis-test:6379/0?protocol=3
    depends_on:
      - app
      - rabbitmq-test
      - redis-test

  worker-chunk:
    image: purefda/ind267-backend
    command: taskiq worker app.task:broker_chunk -w 4 app.tasks.chunk
    environment:
      - MYSQL_DSN=mysql+asyncmy://root@mysql-test:3306/test?charset=utf8mb4
      - REDIS_DSN=redis://redis-test:6379/0?protocol=3&decode_responses=True
      - TASK_QUEUE_BROKER=amqp://rabbitmq-test:5672
      - TASK_QUEUE_BACKEND=redis://redis-test:6379/0?protocol=3
    depends_on:
      - app
      - rabbitmq-test
      - redis-test

  bce-rerank:
    image: purefda/bce-rerank
    pull_policy: build
    build:
      dockerfile: bce.Dockerfile
    ports:
      - '4000:4000'

  # test:
  #   image: purefda/ind267-backend-test
  #   pull_policy: build
  #   build:
  #     dockerfile: test.Dockerfile
  #   environment:
  #     - MYSQL_DSN=mysql+asyncmy://root@mysql-test:3306/test?charset=utf8mb4
  #     - REDIS_DSN=redis://redis-test:6379/0?protocol=3&decode_responses=True
  #     - TASK_QUEUE_BROKER=amqp://rabbitmq-test:5672
  #     - TASK_QUEUE_BACKEND=redis://redis-test:6379/0?protocol=3
  #   volumes:
  #     - ./config:/config:ro
  #   depends_on:
  #     - app
