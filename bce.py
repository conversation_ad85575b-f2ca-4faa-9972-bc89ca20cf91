from asyncio import Lock

from BCEmbedding import RerankerModel
from fastapi import FastAPI
from pydantic import BaseModel


class RerankParam(BaseModel):
    query: str
    documents: list[str]
    top_n: int = 1


app = FastAPI()

model = RerankerModel(model_name_or_path='/bce-reranker-base_v1')

lock = Lock()


@app.post('/rerank')
async def rerank(param: RerankParam) -> list[str]:
    async with lock:  # 限制并发请求数，避免内存不够导致进程被杀
        result = model.rerank(param.query, param.documents)
    return result['rerank_passages'][: param.top_n]
