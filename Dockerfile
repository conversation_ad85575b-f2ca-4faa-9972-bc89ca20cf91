FROM python:3.12-slim AS base

RUN apt-get update \
    && apt-get install -y --no-install-recommends curl build-essential python3-dev

COPY requirements.txt .

RUN pip install -r requirements.txt


FROM debian:bookworm-slim

RUN apt-get update \
    && apt-get install -y --no-install-recommends ca-certificates \
    && apt-get autoclean \
    && rm -rf /var/lib/apt/lists/*

COPY --from=base /usr/local/bin/python /usr/local/bin/
RUN ln -s /usr/local/bin/python /usr/local/bin/python3.12
# 这个文件和 Python 版本相关
COPY --from=base /usr/local/lib/libpython3.12.so.1.0 /usr/local/lib/
# 这些文件是 Python 的依赖
COPY --from=base /usr/lib/x86_64-linux-gnu/libssl.so.3 /usr/lib/x86_64-linux-gnu/
COPY --from=base /usr/lib/x86_64-linux-gnu/libcrypto.so.3 /usr/lib/x86_64-linux-gnu/
COPY --from=base /usr/lib/x86_64-linux-gnu/libexpat.so.1 /usr/lib/x86_64-linux-gnu/
# COPY --from=base /usr/lib/aarch64-linux-gnu/libssl.so.3 /usr/lib/aarch64-linux-gnu/
# COPY --from=base /usr/lib/aarch64-linux-gnu/libcrypto.so.3 /usr/lib/aarch64-linux-gnu/
# COPY --from=base /usr/lib/aarch64-linux-gnu/libexpat.so.1 /usr/lib/aarch64-linux-gnu/
# 这些是入口程序，可能会升级
COPY --from=base /usr/local/bin/uvicorn /usr/local/bin/
COPY --from=base /usr/local/bin/taskiq /usr/local/bin/
# 这些是其他第三方库，可能会升级或变化
COPY --from=base /usr/local/lib/python3.12/ /usr/local/lib/python3.12/

COPY static /static
COPY app /app

CMD ["uvicorn", "--host", "0.0.0.0", "app.main:app"]
