康龙 IND-267

# API 文档

[https://app.apifox.com/project/4425456](https://app.apifox.com/project/4425456)

# Docker 运行

```bash
docker compose up
```

# macOS 本地运行

## 安装依赖

```bash
brew install mysql redis rabbitmq
brew update
python3.12 -m venv .
bin/pip install -r requirements.txt -r requirements-test.txt
```

## 运行数据库

```bash
brew services run mysql
brew services run redis
```

## 建表

```bash
mysql -uroot < db/test.sql
mysql -uroot test < db/mysql.sql
```

## 运行任务队列

```bash
brew services run rabbitmq
```

## 配置环境变量

在当前目录创建一个 `.env` 文件，配置环境变量

## 运行

### 运行 web app

```bash
bin/uvicorn app.main:app
```

### 运行任务队列 worker

```bash
bin/taskiq worker app.task:broker -fsd -tp "app/tasks/**/*.py"
```

### 运行单元测试

```bash
bin/pytest --ruff app --cov=app tests
```

部份接口会通过 HTTP 请求调用其他 WEB 服务，在单元测试中默认使用 Mock 进行虚假调用。若要测试真实接口，请配置环境变量 `TEST_REAL_HTTP` 为 `ture`

```bash
TEST_REAL_HTTP=true bin/pytest --ruff app --cov=app tests
```

# 注意事项

## 时区

如果 MySQL 的默认时区为东八区，它输出的 TIMESTAMP 或 DATETIME 类型的值都是东八区的时间字符串。asyncmy 会将这些时间字符串直接 parse 为无时区（naive）的 datetime 对象，导致时间错误。

有三种办法可以解决这个问题：
1. 在运行容器时，设置 `TZ=Asia/Shanghai` 环境变量，这样转成的 naive datetime 对象会使用东八区的时区。
2. 在调用 `create_async_engine()` 时，设置 `connect_args={'init_command': "SET SESSION time_zone='+00:00'"}` 参数，并保持容器是 UTC 时区。
3. 使用 `func.unix_timestamp()` 转成整数，这样 MySQL 会根据自己的时区去转换时间戳，不会出现环境配置不一致导致的问题。但是如果要手动设置时间，则不能使用 datetime 对象，而需要用 `text('CURRENT_TIMESTAMP')` 或 `func.from_unixtime()`。
