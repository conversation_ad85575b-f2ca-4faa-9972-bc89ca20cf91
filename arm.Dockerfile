FROM purefda/ind267-backend


FROM python:3.12-slim

RUN apt-get update \
    && apt-get install -y --no-install-recommends ca-certificates \
    && apt-get autoclean \
    && rm -rf /var/lib/apt/lists/*

COPY --from=0 /usr/local/lib/python3.12/site-packages/ /usr/local/lib/python3.12/site-packages/
COPY --from=0 /usr/local/bin/uvicorn /usr/local/bin/uvicorn
COPY app /app

CMD ["uvicorn", "--host", "0.0.0.0", "app.main:app"]
