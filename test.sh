while ! mysq<PERSON><PERSON> ping -h mysql-test -uroot --silent; do
    sleep 1
done
mysql -h mysql-test -uroot < db/test.sql
mysql -h mysql-test -uroot test < db/mysql.sql
while ! /opt/mssql-tools18/bin/sqlcmd -C -S mssql-test -U sa -P 1234qwER -Q "SELECT @@version;"; do
    sleep 1
done
/opt/mssql-tools18/bin/sqlcmd -C -S mssql-test -U sa -P 1234qwER < db/mssql.sql
pytest --ruff app --cov=app tests
